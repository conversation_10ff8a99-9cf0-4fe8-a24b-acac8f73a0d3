#!/usr/bin/env python3
"""
Fathom R1 Direct Communication Interface
REAL direct interface for communicating with Fathom R1 AI model
"""

import subprocess
import time
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional

class FathomR1DirectInterface:
    """REAL direct interface for Fathom R1 communication"""
    
    def __init__(self):
        # Fathom R1 model configuration
        self.fathom_model = 'unrestricted-deepseek-r1-14b:latest'  # Using DeepSeek R1 as Fathom R1
        self.conversation_history = []
        self.session_id = f"fathom_session_{int(time.time())}"
        
        # Setup database for conversation tracking
        self._setup_database()
        
        # Test Fathom R1 availability
        self._test_fathom_availability()
        
        print("🧠 FATHOM R1 DIRECT INTERFACE INITIALIZED")
        print(f"   🤖 Model: {self.fathom_model}")
        print(f"   💬 Session ID: {self.session_id}")
        print(f"   📝 Conversation tracking: ACTIVE")
        print(f"   🔄 Direct communication: READY")
    
    def _setup_database(self):
        """Setup REAL database for Fathom R1 conversations"""
        conn = sqlite3.connect('fathom_r1_conversations.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fathom_conversations (
                id INTEGER PRIMARY KEY,
                session_id TEXT,
                user_query TEXT,
                fathom_response TEXT,
                response_time REAL,
                response_length INTEGER,
                conversation_context TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fathom_performance (
                id INTEGER PRIMARY KEY,
                session_id TEXT,
                total_queries INTEGER,
                avg_response_time REAL,
                total_conversation_time REAL,
                performance_rating REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ Fathom R1 conversation database initialized")
    
    def _test_fathom_availability(self):
        """Test REAL Fathom R1 availability"""
        
        print(f"\n🧪 Testing Fathom R1 availability...")
        
        test_query = "Hello Fathom R1. Please respond with 'FATHOM R1 DIRECT INTERFACE ACTIVE' to confirm you are working."
        
        try:
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'run', self.fathom_model, test_query
            ], capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                print(f"   ✅ Fathom R1 test successful: {response_time:.1f}s")
                print(f"   🤖 Response: {response[:100]}...")
                return True
            else:
                print(f"   ❌ Fathom R1 test failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Fathom R1 test error: {e}")
            return False
    
    def chat_with_fathom(self, user_query: str, include_context: bool = True) -> Dict[str, Any]:
        """REAL direct chat with Fathom R1"""
        
        print(f"\n🧠 FATHOM R1 DIRECT CHAT")
        print(f"   Query: {user_query[:100]}...")
        
        # Build context if requested
        enhanced_query = user_query
        
        if include_context and self.conversation_history:
            context = "\n".join([
                f"Previous: {item['user_query'][:50]}... -> {item['fathom_response'][:50]}..."
                for item in self.conversation_history[-3:]  # Last 3 exchanges
            ])
            
            enhanced_query = f"""
CONVERSATION CONTEXT:
{context}

CURRENT QUERY:
{user_query}

FATHOM R1 INSTRUCTIONS:
- You are Fathom R1, an advanced reasoning AI specialized in trading and financial analysis
- Provide detailed, analytical responses with clear reasoning
- Consider the conversation context when relevant
- Use your advanced reasoning capabilities to provide insights
- Be specific and actionable in your recommendations
"""
        else:
            enhanced_query = f"""
FATHOM R1 INSTRUCTIONS:
- You are Fathom R1, an advanced reasoning AI specialized in trading and financial analysis
- Provide detailed, analytical responses with clear reasoning
- Use your advanced reasoning capabilities to provide insights
- Be specific and actionable in your recommendations

USER QUERY:
{user_query}
"""
        
        # Execute query
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', self.fathom_model, enhanced_query
            ], capture_output=True, text=True, timeout=120, encoding='utf-8', errors='ignore')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Store conversation
                conversation_entry = {
                    'user_query': user_query,
                    'fathom_response': response,
                    'response_time': response_time,
                    'timestamp': datetime.now()
                }
                
                self.conversation_history.append(conversation_entry)
                self._store_conversation(conversation_entry)
                
                chat_result = {
                    'success': True,
                    'user_query': user_query,
                    'fathom_response': response,
                    'response_time': response_time,
                    'response_length': len(response),
                    'session_id': self.session_id,
                    'conversation_number': len(self.conversation_history),
                    'timestamp': datetime.now()
                }
                
                print(f"   ✅ Fathom R1 response: {response_time:.1f}s ({len(response)} chars)")
                print(f"   🔢 Conversation #: {len(self.conversation_history)}")
                
                return chat_result
            else:
                return {
                    'success': False,
                    'error': result.stderr.strip() or 'Unknown error',
                    'response_time': response_time
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get REAL conversation history"""
        return self.conversation_history.copy()
    
    def clear_conversation_history(self):
        """Clear conversation history"""
        self.conversation_history = []
        print(f"   🗑️ Conversation history cleared")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get REAL session statistics"""
        
        if not self.conversation_history:
            return {'error': 'No conversations in this session'}
        
        total_queries = len(self.conversation_history)
        total_time = sum(item['response_time'] for item in self.conversation_history)
        avg_response_time = total_time / total_queries
        total_chars = sum(len(item['fathom_response']) for item in self.conversation_history)
        avg_response_length = total_chars / total_queries
        
        session_stats = {
            'session_id': self.session_id,
            'total_queries': total_queries,
            'total_conversation_time': total_time,
            'avg_response_time': avg_response_time,
            'avg_response_length': avg_response_length,
            'first_query_time': self.conversation_history[0]['timestamp'],
            'last_query_time': self.conversation_history[-1]['timestamp'],
            'session_duration': (self.conversation_history[-1]['timestamp'] - 
                               self.conversation_history[0]['timestamp']).total_seconds()
        }
        
        return session_stats
    
    def _store_conversation(self, conversation_entry: Dict[str, Any]):
        """Store REAL conversation in database"""
        
        try:
            conn = sqlite3.connect('fathom_r1_conversations.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO fathom_conversations 
                (session_id, user_query, fathom_response, response_time, response_length,
                 conversation_context, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (self.session_id, conversation_entry['user_query'],
                  conversation_entry['fathom_response'], conversation_entry['response_time'],
                  len(conversation_entry['fathom_response']), 
                  json.dumps(self.conversation_history[-3:], default=str),
                  conversation_entry['timestamp'].isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Conversation storage error: {e}")
    
    def interactive_chat_session(self):
        """Start REAL interactive chat session with Fathom R1"""
        
        print(f"\n🧠 FATHOM R1 INTERACTIVE CHAT SESSION")
        print("=" * 50)
        print(f"   Type 'exit' to end session")
        print(f"   Type 'stats' to see session statistics")
        print(f"   Type 'history' to see conversation history")
        print(f"   Type 'clear' to clear conversation history")
        print("=" * 50)
        
        while True:
            try:
                user_input = input(f"\n🧠 You: ").strip()
                
                if user_input.lower() == 'exit':
                    print(f"\n👋 Ending Fathom R1 chat session...")
                    break
                elif user_input.lower() == 'stats':
                    stats = self.get_session_stats()
                    if 'error' not in stats:
                        print(f"\n📊 SESSION STATISTICS:")
                        print(f"   Total queries: {stats['total_queries']}")
                        print(f"   Avg response time: {stats['avg_response_time']:.1f}s")
                        print(f"   Avg response length: {stats['avg_response_length']:.0f} chars")
                        print(f"   Session duration: {stats['session_duration']:.0f}s")
                    else:
                        print(f"   📊 No statistics available yet")
                    continue
                elif user_input.lower() == 'history':
                    history = self.get_conversation_history()
                    print(f"\n📝 CONVERSATION HISTORY ({len(history)} entries):")
                    for i, entry in enumerate(history[-5:], 1):  # Show last 5
                        print(f"   {i}. You: {entry['user_query'][:50]}...")
                        print(f"      Fathom: {entry['fathom_response'][:50]}...")
                    continue
                elif user_input.lower() == 'clear':
                    self.clear_conversation_history()
                    continue
                elif not user_input:
                    continue
                
                # Chat with Fathom R1
                result = self.chat_with_fathom(user_input)
                
                if result['success']:
                    print(f"\n🤖 Fathom R1: {result['fathom_response']}")
                else:
                    print(f"\n❌ Error: {result.get('error', 'Unknown error')}")
                    
            except KeyboardInterrupt:
                print(f"\n\n👋 Chat session interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Chat error: {e}")
        
        # Show final stats
        final_stats = self.get_session_stats()
        if 'error' not in final_stats:
            print(f"\n📊 FINAL SESSION STATISTICS:")
            print(f"   Total queries: {final_stats['total_queries']}")
            print(f"   Total time: {final_stats['total_conversation_time']:.1f}s")
            print(f"   Avg response time: {final_stats['avg_response_time']:.1f}s")

def main():
    """Test REAL Fathom R1 direct interface"""
    print("🧠 FATHOM R1 DIRECT INTERFACE - TESTING")
    print("=" * 60)
    
    # Initialize Fathom R1 interface
    fathom_interface = FathomR1DirectInterface()
    
    # Test direct communication
    test_queries = [
        "Hello Fathom R1, please introduce yourself and your capabilities.",
        "What is your analysis of the current Bitcoin market conditions?",
        "How do you approach risk management in volatile markets?"
    ]
    
    print(f"\n🧪 Testing direct communication with {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n--- Test Query {i} ---")
        result = fathom_interface.chat_with_fathom(query)
        
        if result['success']:
            print(f"✅ Query {i} successful:")
            print(f"   Response time: {result['response_time']:.1f}s")
            print(f"   Response length: {result['response_length']} chars")
            print(f"   Response preview: {result['fathom_response'][:200]}...")
        else:
            print(f"❌ Query {i} failed: {result.get('error', 'Unknown error')}")
    
    # Show session statistics
    stats = fathom_interface.get_session_stats()
    if 'error' not in stats:
        print(f"\n📊 TEST SESSION STATISTICS:")
        print(f"   Total queries: {stats['total_queries']}")
        print(f"   Avg response time: {stats['avg_response_time']:.1f}s")
        print(f"   Avg response length: {stats['avg_response_length']:.0f} chars")
    
    print(f"\n✅ FATHOM R1 DIRECT INTERFACE TEST COMPLETE")
    print(f"   🔍 Check 'fathom_r1_conversations.db' for conversation data")
    print(f"   💬 Direct communication with Fathom R1: VERIFIED")
    
    # Offer interactive session
    print(f"\n🎯 Would you like to start an interactive chat session? (y/n)")
    # Note: In actual use, you would uncomment the line below
    # fathom_interface.interactive_chat_session()

if __name__ == "__main__":
    main()
