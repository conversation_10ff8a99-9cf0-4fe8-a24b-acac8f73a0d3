#!/usr/bin/env python3
"""
Expanded AI Team System
REAL implementation of 16+ AI agents with specialized roles and advanced features
"""

import subprocess
import time
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from fathom_r1_direct_interface import FathomR1DirectInterface

class ExpandedAITeamSystem:
    """REAL expanded AI team with 16+ specialized agents"""
    
    def __init__(self):
        # Initialize Fathom R1 direct interface
        self.fathom_interface = FathomR1DirectInterface()
        
        # EXPANDED AI TEAM - 16 SPECIALIZED AGENTS
        self.ai_team = {
            # ORIGINAL ENHANCED AGENTS (6)
            'marco_o1_finance': {
                'model': 'unrestricted-noryon-marco-o1-finance-v2-latest:latest',
                'specialization': 'scalping_momentum',
                'role': 'Senior Scalping & Momentum Specialist',
                'expertise': ['Scalping strategies', 'Momentum analysis', 'Quick profits', 'Short-term trading'],
                'timeframes': ['1m', '5m', '15m'],
                'features': ['technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'deepseek_r1_finance': {
                'model': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
                'specialization': 'fundamental_analysis',
                'role': 'Senior Fundamental Analysis Expert',
                'expertise': ['Company analysis', 'Financial statements', 'Market research', 'Long-term trends'],
                'timeframes': ['1d', '1w', '1M'],
                'features': ['technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'cogito_finance': {
                'model': 'unrestricted-noryon-cogito-finance-v2-latest:latest',
                'specialization': 'risk_management',
                'role': 'Senior Risk Management Specialist',
                'expertise': ['Risk assessment', 'Position sizing', 'Stop-loss strategies', 'Portfolio protection'],
                'timeframes': ['1h', '4h', '1d'],
                'features': ['technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'phi4_finance': {
                'model': 'unrestricted-noryon-phi-4-9b-finance-latest:latest',
                'specialization': 'options_derivatives',
                'role': 'Senior Options & Derivatives Expert',
                'expertise': ['Options strategies', 'Volatility trading', 'Greeks analysis', 'Hedging strategies'],
                'timeframes': ['1h', '1d', '1w'],
                'features': ['technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'qwen3_finance': {
                'model': 'unrestricted-noryon-qwen3-finance-v2-latest:latest',
                'specialization': 'macro_economics',
                'role': 'Senior Macro Economic Analyst',
                'expertise': ['Economic indicators', 'Market cycles', 'Global trends', 'Sector analysis'],
                'timeframes': ['1d', '1w', '1M'],
                'features': ['technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'fathom_r1': {
                'model': 'unrestricted-deepseek-r1-14b:latest',
                'specialization': 'advanced_reasoning',
                'role': 'Advanced Reasoning & Strategy Architect',
                'expertise': ['Advanced reasoning', 'Strategy synthesis', 'Complex analysis', 'Multi-factor decisions'],
                'timeframes': ['1m', '1h', '1d', '1w'],
                'features': ['advanced_reasoning', 'technical_analysis', 'specialization', 'memory', 'collaboration'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            
            # NEW SPECIALIZED AGENTS (10)
            'gemma3_crypto': {
                'model': 'unrestricted-noryon-gemma-3-12b-finance-latest:latest',
                'specialization': 'cryptocurrency',
                'role': 'Cryptocurrency Specialist',
                'expertise': ['Crypto markets', 'DeFi analysis', 'Blockchain trends', 'Altcoin analysis'],
                'timeframes': ['1m', '5m', '1h', '1d'],
                'features': ['crypto_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'falcon3_forex': {
                'model': 'unrestricted-noryon-falcon3-finance-v1-latest:latest',
                'specialization': 'forex_trading',
                'role': 'Forex Trading Expert',
                'expertise': ['Currency pairs', 'Central bank policy', 'Carry trades', 'FX correlations'],
                'timeframes': ['5m', '1h', '4h', '1d'],
                'features': ['forex_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'dolphin3_commodities': {
                'model': 'unrestricted-noryon-dolphin3-finance-v2-latest:latest',
                'specialization': 'commodities',
                'role': 'Commodities Analyst',
                'expertise': ['Gold/Silver', 'Oil/Gas', 'Agricultural commodities', 'Supply/Demand analysis'],
                'timeframes': ['1h', '1d', '1w', '1M'],
                'features': ['commodities_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'exaone_hft': {
                'model': 'unrestricted-noryon-exaone-deep-finance-v2-latest:latest',
                'specialization': 'high_frequency_trading',
                'role': 'High-Frequency Trading Specialist',
                'expertise': ['Microsecond trading', 'Order book analysis', 'Latency optimization', 'Market microstructure'],
                'timeframes': ['1s', '1m', '5m'],
                'features': ['hft_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'granite_behavioral': {
                'model': 'unrestricted-noryon-granite-vision-finance-v1-latest:latest',
                'specialization': 'behavioral_finance',
                'role': 'Behavioral Finance Expert',
                'expertise': ['Market psychology', 'Sentiment analysis', 'Crowd behavior', 'Bias identification'],
                'timeframes': ['1h', '1d', '1w'],
                'features': ['behavioral_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'wizard_quant': {
                'model': 'unrestricted-wizard-math-13b:latest',
                'specialization': 'quantitative_analysis',
                'role': 'Quantitative Analysis Specialist',
                'expertise': ['Mathematical models', 'Statistical analysis', 'Algorithmic trading', 'Backtesting'],
                'timeframes': ['1m', '1h', '1d'],
                'features': ['quantitative_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'deepscaler_microstructure': {
                'model': 'unrestricted-noryon-deepscaler-finance-v2-latest:latest',
                'specialization': 'market_microstructure',
                'role': 'Market Microstructure Analyst',
                'expertise': ['Order flow', 'Bid-ask spreads', 'Market making', 'Liquidity analysis'],
                'timeframes': ['1s', '1m', '5m', '15m'],
                'features': ['microstructure_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'phi4_reasoning_enhanced': {
                'model': 'unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest',
                'specialization': 'advanced_reasoning_enhanced',
                'role': 'Enhanced Reasoning Specialist',
                'expertise': ['Complex reasoning', 'Multi-step analysis', 'Scenario planning', 'Strategic thinking'],
                'timeframes': ['1h', '1d', '1w', '1M'],
                'features': ['enhanced_reasoning', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'qwen3_enhanced': {
                'model': 'unrestricted-qwen3-14b:latest',
                'specialization': 'multi_asset_correlation',
                'role': 'Multi-Asset Correlation Specialist',
                'expertise': ['Cross-asset analysis', 'Correlation trading', 'Portfolio diversification', 'Asset allocation'],
                'timeframes': ['1h', '1d', '1w'],
                'features': ['correlation_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            },
            'granite_enhanced': {
                'model': 'unrestricted-granite3.1-dense-8b:latest',
                'specialization': 'volatility_forecasting',
                'role': 'Volatility Forecasting Specialist',
                'expertise': ['Volatility modeling', 'VIX analysis', 'Volatility trading', 'Risk forecasting'],
                'timeframes': ['1h', '1d', '1w'],
                'features': ['volatility_analysis', 'technical_analysis', 'specialization', 'memory'],
                'performance_score': 0.0,
                'total_queries': 0
            }
        }
        
        # Setup database
        self._setup_database()
        
        print("🚀 EXPANDED AI TEAM SYSTEM INITIALIZED")
        print(f"   🤖 Total AI agents: {len(self.ai_team)}")
        print(f"   📊 Specializations: {len(set(agent['specialization'] for agent in self.ai_team.values()))}")
        print(f"   🧠 Fathom R1 direct interface: ACTIVE")
        print(f"   💾 Database tracking: READY")
    
    def _setup_database(self):
        """Setup REAL database for expanded team"""
        conn = sqlite3.connect('expanded_ai_team.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS expanded_team_members (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                model_name TEXT,
                specialization TEXT,
                role TEXT,
                expertise TEXT,
                timeframes TEXT,
                features TEXT,
                performance_score REAL,
                total_queries INTEGER,
                last_updated DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS team_specialization_queries (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                specialization TEXT,
                query_text TEXT,
                response_text TEXT,
                response_time REAL,
                confidence_score REAL,
                specialization_relevance REAL,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS multi_agent_consensus (
                id INTEGER PRIMARY KEY,
                consensus_id TEXT,
                participating_agents TEXT,
                query_topic TEXT,
                individual_decisions TEXT,
                final_consensus TEXT,
                consensus_strength REAL,
                total_response_time REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ Expanded team database initialized")
    
    def get_agents_by_specialization(self, specialization: str) -> List[str]:
        """Get REAL agents by specialization"""
        
        return [name for name, info in self.ai_team.items() 
                if info['specialization'] == specialization]
    
    def get_all_specializations(self) -> List[str]:
        """Get all REAL specializations"""
        
        return list(set(agent['specialization'] for agent in self.ai_team.values()))
    
    def query_specialist_by_domain(self, specialization: str, query: str, symbol: str = 'BTC-USD') -> Dict[str, Any]:
        """Query REAL specialist by domain"""
        
        specialists = self.get_agents_by_specialization(specialization)
        
        if not specialists:
            return {'error': f'No specialists found for {specialization}'}
        
        # Use the first specialist for the domain
        agent_name = specialists[0]
        agent_info = self.ai_team[agent_name]
        
        print(f"\n🎯 SPECIALIST DOMAIN QUERY: {specialization}")
        print(f"   Agent: {agent_info['role']}")
        print(f"   Query: {query[:100]}...")
        
        # Create specialized prompt
        specialized_prompt = f"""
SPECIALIST DOMAIN: {specialization.upper()}
AGENT ROLE: {agent_info['role']}

EXPERTISE AREAS:
{chr(10).join(f"- {expertise}" for expertise in agent_info['expertise'])}

PREFERRED TIMEFRAMES: {', '.join(agent_info['timeframes'])}

SYMBOL: {symbol}
QUERY: {query}

SPECIALIZED INSTRUCTIONS:
- Focus on your area of expertise: {specialization}
- Provide insights specific to your specialization
- Use your preferred timeframes for analysis
- Give actionable recommendations within your domain

RESPOND WITH:
SPECIALIZATION: {specialization}
DECISION: [BUY/SELL/HOLD]
CONFIDENCE: [1-10]
SPECIALIST_REASONING: [Your specialized analysis]
TIMEFRAME_RECOMMENDATION: [Best timeframe for this trade]
RISK_FACTORS: [Risks specific to your specialization]
"""
        
        # Execute query
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', agent_info['model'], specialized_prompt
            ], capture_output=True, text=True, timeout=120, encoding='utf-8', errors='ignore')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Parse decision
                decision = self._parse_specialist_decision(response)
                
                # Calculate specialization relevance
                relevance = self._calculate_domain_relevance(response, agent_info)
                
                specialist_result = {
                    'agent_name': agent_name,
                    'specialization': specialization,
                    'role': agent_info['role'],
                    'success': True,
                    'response': response,
                    'response_time': response_time,
                    'response_length': len(response),
                    'decision': decision,
                    'specialization_relevance': relevance,
                    'expertise_areas': agent_info['expertise'],
                    'timestamp': datetime.now()
                }
                
                # Store query
                self._store_specialization_query(specialist_result)
                
                print(f"   ✅ Specialist response: {response_time:.1f}s ({len(response)} chars)")
                print(f"   🎯 Relevance: {relevance:.1f}/10")
                if decision:
                    print(f"   📊 Decision: {decision.get('action', 'UNKNOWN')} (confidence: {decision.get('confidence', 0)})")
                
                return specialist_result
            else:
                return {
                    'success': False,
                    'error': result.stderr.strip() or 'Unknown error',
                    'response_time': response_time
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }
    
    def run_multi_specialist_consensus(self, query: str, symbol: str = 'BTC-USD', 
                                     specializations: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run REAL multi-specialist consensus"""
        
        if specializations is None:
            # Select diverse specializations
            specializations = [
                'cryptocurrency', 'risk_management', 'quantitative_analysis', 
                'behavioral_finance', 'advanced_reasoning'
            ]
        
        print(f"\n🤝 MULTI-SPECIALIST CONSENSUS")
        print(f"   Query: {query[:100]}...")
        print(f"   Symbol: {symbol}")
        print(f"   Specializations: {len(specializations)}")
        
        consensus_id = f"consensus_{int(time.time())}"
        specialist_responses = {}
        total_time = 0
        
        for specialization in specializations:
            print(f"\n🎯 Consulting {specialization} specialist...")
            
            specialist_result = self.query_specialist_by_domain(specialization, query, symbol)
            specialist_responses[specialization] = specialist_result
            
            if specialist_result.get('success'):
                total_time += specialist_result.get('response_time', 0)
        
        # Build consensus
        consensus = self._build_specialist_consensus(specialist_responses)
        
        # Store consensus
        self._store_multi_agent_consensus(consensus_id, specializations, query, 
                                        specialist_responses, consensus, total_time)
        
        consensus_result = {
            'consensus_id': consensus_id,
            'query': query,
            'symbol': symbol,
            'participating_specializations': specializations,
            'specialist_responses': specialist_responses,
            'consensus': consensus,
            'total_response_time': total_time,
            'timestamp': datetime.now()
        }
        
        print(f"\n🎉 MULTI-SPECIALIST CONSENSUS COMPLETE")
        if 'error' not in consensus:
            print(f"   🎯 Consensus: {consensus.get('consensus_decision', 'UNKNOWN')}")
            print(f"   💪 Strength: {consensus.get('consensus_strength', 0):.1%}")
            print(f"   🔢 Confidence: {consensus.get('average_confidence', 0):.1f}/10")
            print(f"   ⏱️ Total time: {total_time:.1f}s")
        
        return consensus_result
    
    def chat_with_fathom_direct(self, query: str) -> Dict[str, Any]:
        """Direct chat with Fathom R1 using the direct interface"""
        
        print(f"\n🧠 DIRECT FATHOM R1 CHAT")
        return self.fathom_interface.chat_with_fathom(query)
    
    def get_team_statistics(self) -> Dict[str, Any]:
        """Get REAL team statistics"""
        
        total_agents = len(self.ai_team)
        specializations = self.get_all_specializations()
        
        # Count agents by specialization
        specialization_counts = {}
        for agent_info in self.ai_team.values():
            spec = agent_info['specialization']
            specialization_counts[spec] = specialization_counts.get(spec, 0) + 1
        
        # Calculate performance metrics
        total_queries = sum(agent['total_queries'] for agent in self.ai_team.values())
        avg_performance = sum(agent['performance_score'] for agent in self.ai_team.values()) / total_agents
        
        team_stats = {
            'total_agents': total_agents,
            'total_specializations': len(specializations),
            'specialization_breakdown': specialization_counts,
            'total_queries_processed': total_queries,
            'average_performance_score': avg_performance,
            'available_specializations': specializations,
            'fathom_r1_direct_available': True,
            'timestamp': datetime.now()
        }
        
        return team_stats
    
    def _parse_specialist_decision(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse REAL specialist decision"""
        
        decision = {}
        response_upper = response.upper()
        
        # Extract decision
        if 'DECISION:' in response_upper:
            if 'BUY' in response_upper and 'SELL' not in response_upper:
                decision['action'] = 'BUY'
            elif 'SELL' in response_upper and 'BUY' not in response_upper:
                decision['action'] = 'SELL'
            else:
                decision['action'] = 'HOLD'
        
        # Extract confidence
        import re
        conf_match = re.search(r'CONFIDENCE:\s*(\d+)', response_upper)
        if conf_match:
            decision['confidence'] = int(conf_match.group(1))
        else:
            decision['confidence'] = 5
        
        # Extract timeframe
        timeframe_match = re.search(r'TIMEFRAME_RECOMMENDATION:\s*([A-Z0-9]+)', response_upper)
        if timeframe_match:
            decision['recommended_timeframe'] = timeframe_match.group(1)
        
        return decision if 'action' in decision else None
    
    def _calculate_domain_relevance(self, response: str, agent_info: Dict[str, Any]) -> float:
        """Calculate REAL domain relevance score"""
        
        relevance_score = 0.0
        response_lower = response.lower()
        
        # Check for expertise keywords
        for expertise in agent_info['expertise']:
            if expertise.lower() in response_lower:
                relevance_score += 2.0
        
        # Check for specialization terms
        specialization_terms = {
            'cryptocurrency': ['crypto', 'bitcoin', 'ethereum', 'blockchain', 'defi'],
            'forex_trading': ['forex', 'currency', 'usd', 'eur', 'central bank'],
            'commodities': ['gold', 'silver', 'oil', 'gas', 'commodity'],
            'high_frequency_trading': ['hft', 'microsecond', 'latency', 'order book'],
            'behavioral_finance': ['psychology', 'sentiment', 'behavior', 'crowd'],
            'quantitative_analysis': ['quantitative', 'mathematical', 'statistical', 'algorithm'],
            'market_microstructure': ['microstructure', 'order flow', 'bid-ask', 'liquidity'],
            'volatility_forecasting': ['volatility', 'vix', 'variance', 'risk']
        }
        
        if agent_info['specialization'] in specialization_terms:
            for term in specialization_terms[agent_info['specialization']]:
                if term in response_lower:
                    relevance_score += 1.0
        
        # Normalize to 1-10 scale
        max_possible = len(agent_info['expertise']) * 2 + 5
        normalized_score = min(10.0, (relevance_score / max_possible) * 10)
        
        return round(normalized_score, 1)
    
    def _build_specialist_consensus(self, specialist_responses: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Build REAL specialist consensus"""
        
        decisions = []
        confidences = []
        successful_specialists = []
        
        for specialization, response in specialist_responses.items():
            if response.get('success') and response.get('decision'):
                decision = response['decision']
                decisions.append(decision['action'])
                confidences.append(decision['confidence'])
                successful_specialists.append(specialization)
        
        if not decisions:
            return {'error': 'No valid specialist decisions'}
        
        # Calculate consensus
        decision_counts = {}
        for decision in decisions:
            decision_counts[decision] = decision_counts.get(decision, 0) + 1
        
        consensus_decision = max(decision_counts.items(), key=lambda x: x[1])
        consensus_strength = consensus_decision[1] / len(decisions)
        avg_confidence = sum(confidences) / len(confidences)
        
        return {
            'consensus_decision': consensus_decision[0],
            'consensus_strength': round(consensus_strength, 2),
            'average_confidence': round(avg_confidence, 1),
            'participating_specialists': successful_specialists,
            'total_specialists': len(successful_specialists),
            'decision_breakdown': decision_counts
        }
    
    def _store_specialization_query(self, specialist_result: Dict[str, Any]):
        """Store REAL specialization query"""

        try:
            conn = sqlite3.connect('expanded_ai_team.db')
            cursor = conn.cursor()

            decision = specialist_result.get('decision', {})
            confidence = decision.get('confidence', 0) if decision else 0

            cursor.execute('''
                INSERT INTO team_specialization_queries
                (agent_name, specialization, query_text, response_text, response_time,
                 confidence_score, specialization_relevance, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (specialist_result['agent_name'], specialist_result['specialization'],
                  'Specialist query', specialist_result['response'], specialist_result['response_time'],
                  confidence, specialist_result['specialization_relevance'],
                  specialist_result['timestamp'].isoformat()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"   ❌ Specialization query storage error: {e}")
    
    def _store_multi_agent_consensus(self, consensus_id: str, specializations: List[str],
                                   query: str, responses: Dict[str, Dict[str, Any]],
                                   consensus: Dict[str, Any], total_time: float):
        """Store REAL multi-agent consensus"""
        
        try:
            conn = sqlite3.connect('expanded_ai_team.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO multi_agent_consensus 
                (consensus_id, participating_agents, query_topic, individual_decisions,
                 final_consensus, consensus_strength, total_response_time, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (consensus_id, json.dumps(specializations), query,
                  json.dumps(responses, default=str), json.dumps(consensus),
                  consensus.get('consensus_strength', 0), total_time,
                  datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Consensus storage error: {e}")

def main():
    """Test REAL expanded AI team system"""
    print("🚀 EXPANDED AI TEAM SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize expanded team
    expanded_team = ExpandedAITeamSystem()
    
    # Show team statistics
    stats = expanded_team.get_team_statistics()
    print(f"\n📊 TEAM STATISTICS:")
    print(f"   Total agents: {stats['total_agents']}")
    print(f"   Specializations: {stats['total_specializations']}")
    print(f"   Available domains: {', '.join(stats['available_specializations'][:5])}...")
    
    # Test specialist queries
    test_specializations = ['cryptocurrency', 'risk_management', 'quantitative_analysis']
    test_query = "Should I invest in Bitcoin at current levels?"
    
    for specialization in test_specializations:
        print(f"\n🎯 Testing {specialization} specialist...")
        result = expanded_team.query_specialist_by_domain(specialization, test_query, 'BTC-USD')
        
        if result.get('success'):
            decision = result.get('decision', {})
            if decision:
                print(f"   ✅ {specialization}: {decision.get('action', 'UNKNOWN')} "
                      f"(confidence: {decision.get('confidence', 0)}/10)")
            else:
                print(f"   ⚠️ {specialization}: Response received but no decision parsed")
        else:
            print(f"   ❌ {specialization}: {result.get('error', 'Unknown error')}")
    
    # Test multi-specialist consensus
    print(f"\n🤝 Testing multi-specialist consensus...")
    consensus_result = expanded_team.run_multi_specialist_consensus(
        "Analyze Bitcoin's investment potential for the next month",
        'BTC-USD'
    )
    
    # Test direct Fathom R1 chat
    print(f"\n🧠 Testing direct Fathom R1 communication...")
    fathom_result = expanded_team.chat_with_fathom_direct(
        "What is your assessment of the current market conditions?"
    )
    
    if fathom_result.get('success'):
        print(f"   ✅ Fathom R1 direct chat successful: {fathom_result['response_time']:.1f}s")
    
    print(f"\n✅ EXPANDED AI TEAM SYSTEM TEST COMPLETE")
    print(f"   🔍 Check 'expanded_ai_team.db' for team data")
    print(f"   🔍 Check 'fathom_r1_conversations.db' for Fathom R1 chats")
    print(f"   🤖 {stats['total_agents']} AI agents operational")
    print(f"   🎯 {stats['total_specializations']} specializations active")

if __name__ == "__main__":
    main()
