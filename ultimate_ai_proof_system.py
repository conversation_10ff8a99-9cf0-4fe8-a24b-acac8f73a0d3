#!/usr/bin/env python3
"""
ULTIMATE AI PROOF SYSTEM - NO BULLSHIT
Test ALL AI agents with REAL trading decisions and PROVE everything works
"""

import subprocess
import time
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any
import threading

class UltimateAIProofSystem:
    """PROVE ALL AI AGENTS WORK WITH REAL TRADING"""
    
    def __init__(self):
        self.ai_agents = {
            # TOP FINANCE MODELS
            'deepseek_r1_finance': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
            'phi4_finance': 'unrestricted-noryon-phi-4-9b-finance-latest:latest',
            'qwen3_finance': 'unrestricted-noryon-qwen3-finance-v2-latest:latest',
            'marco_o1_finance': 'unrestricted-noryon-marco-o1-finance-v2-latest:latest',
            'cogito_finance': 'unrestricted-noryon-cogito-finance-v2-latest:latest',
            
            # ENHANCED MODELS
            'phi4_enhanced': 'phase2-unrestricted-noryon-phi-4-9b-finance-latest:latest',
            'qwen3_enhanced': 'phase2-unrestricted-noryon-qwen3-finance-v2-latest:latest',
            'gemma3_enhanced': 'unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest:latest',
            
            # REASONING MODELS
            'phi4_reasoning': 'unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest',
            'deepseek_r1': 'unrestricted-deepseek-r1-14b:latest',
            'marco_o1': 'unrestricted-marco-o1-7b:latest',
            
            # SPECIALIZED MODELS
            'granite_vision': 'unrestricted-noryon-granite-vision-finance-v1-latest:latest',
            'falcon3_finance': 'unrestricted-noryon-falcon3-finance-v1-latest:latest',
            'dolphin3_finance': 'unrestricted-noryon-dolphin3-finance-v2-latest:latest'
        }
        
        self.test_results = {}
        self.trading_decisions = {}
        
        # Setup database
        self._setup_database()
        
        print("🚀 ULTIMATE AI PROOF SYSTEM INITIALIZED")
        print(f"   🤖 AI Agents to test: {len(self.ai_agents)}")
        print(f"   💾 Database: READY")
        print(f"   🎯 PROVING ALL AGENTS WORK!")
    
    def _setup_database(self):
        """Setup REAL database for AI proof"""
        conn = sqlite3.connect('ai_proof_results.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_test_results (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                model_name TEXT,
                test_type TEXT,
                query TEXT,
                response TEXT,
                response_time REAL,
                success BOOLEAN,
                response_length INTEGER,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_decisions (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                symbol TEXT,
                decision TEXT,
                confidence REAL,
                reasoning TEXT,
                price_target REAL,
                risk_level TEXT,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ AI proof database initialized")
    
    def test_single_agent(self, agent_name: str, model: str, query: str, test_type: str = 'trading') -> Dict[str, Any]:
        """Test SINGLE AI agent with REAL query"""
        
        print(f"\n🤖 TESTING: {agent_name}")
        print(f"   Model: {model}")
        print(f"   Query: {query[:100]}...")
        
        start_time = time.time()
        
        try:
            # REAL Ollama query
            result = subprocess.run([
                'ollama', 'run', model, query
            ], capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Store result
                test_result = {
                    'agent_name': agent_name,
                    'model': model,
                    'success': True,
                    'response': response,
                    'response_time': response_time,
                    'response_length': len(response),
                    'timestamp': datetime.now()
                }
                
                self._store_test_result(test_result, query, test_type)
                
                print(f"   ✅ SUCCESS: {response_time:.1f}s ({len(response)} chars)")
                print(f"   📝 Response: {response[:200]}...")
                
                return test_result
            else:
                error_msg = result.stderr.strip() or 'Unknown error'
                print(f"   ❌ FAILED: {error_msg}")
                
                return {
                    'agent_name': agent_name,
                    'model': model,
                    'success': False,
                    'error': error_msg,
                    'response_time': response_time
                }
                
        except subprocess.TimeoutExpired:
            print(f"   ⏱️ TIMEOUT: 60 seconds")
            return {
                'agent_name': agent_name,
                'model': model,
                'success': False,
                'error': 'Timeout',
                'response_time': 60.0
            }
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            return {
                'agent_name': agent_name,
                'model': model,
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }
    
    def test_all_agents_trading_decision(self, symbol: str = 'BTC-USD', current_price: float = 101000) -> Dict[str, Any]:
        """Test ALL agents with REAL trading decision"""
        
        trading_query = f"""URGENT TRADING DECISION NEEDED:

Symbol: {symbol}
Current Price: ${current_price:,.2f}
Market Status: LIVE TRADING

REQUIRED RESPONSE FORMAT:
DECISION: [BUY/SELL/HOLD]
CONFIDENCE: [1-10]
TARGET_PRICE: $[price]
RISK_LEVEL: [LOW/MEDIUM/HIGH]
REASONING: [brief explanation]

Based on current market conditions, technical analysis, and risk factors, provide your immediate trading recommendation for {symbol}. Be specific and actionable.

RESPOND NOW:"""

        print(f"\n🎯 TESTING ALL AGENTS - TRADING DECISION")
        print(f"   Symbol: {symbol}")
        print(f"   Current Price: ${current_price:,.2f}")
        print(f"   Testing {len(self.ai_agents)} agents...")
        
        results = {}
        successful_agents = 0
        total_response_time = 0
        
        for agent_name, model in self.ai_agents.items():
            result = self.test_single_agent(agent_name, model, trading_query, 'trading_decision')
            results[agent_name] = result
            
            if result['success']:
                successful_agents += 1
                total_response_time += result['response_time']
                
                # Parse trading decision
                decision = self._parse_trading_decision(result['response'])
                if decision:
                    self._store_trading_decision(agent_name, symbol, decision)
        
        summary = {
            'total_agents': len(self.ai_agents),
            'successful_agents': successful_agents,
            'success_rate': (successful_agents / len(self.ai_agents)) * 100,
            'avg_response_time': total_response_time / successful_agents if successful_agents > 0 else 0,
            'results': results
        }
        
        print(f"\n📊 TRADING DECISION TEST RESULTS:")
        print(f"   Successful agents: {successful_agents}/{len(self.ai_agents)}")
        print(f"   Success rate: {summary['success_rate']:.1f}%")
        print(f"   Average response time: {summary['avg_response_time']:.1f}s")
        
        return summary
    
    def test_parallel_agents(self, query: str, agent_count: int = 5) -> Dict[str, Any]:
        """Test multiple agents in PARALLEL"""
        
        print(f"\n⚡ PARALLEL TESTING - {agent_count} AGENTS")
        
        # Select top agents
        selected_agents = list(self.ai_agents.items())[:agent_count]
        
        results = {}
        threads = []
        
        def test_agent_thread(agent_name, model):
            result = self.test_single_agent(agent_name, model, query, 'parallel')
            results[agent_name] = result
        
        # Start all threads
        start_time = time.time()
        
        for agent_name, model in selected_agents:
            thread = threading.Thread(target=test_agent_thread, args=(agent_name, model))
            thread.start()
            threads.append(thread)
        
        # Wait for all to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        successful = len([r for r in results.values() if r['success']])
        
        print(f"\n⚡ PARALLEL TEST COMPLETE:")
        print(f"   Total time: {total_time:.1f}s")
        print(f"   Successful: {successful}/{agent_count}")
        print(f"   Parallel efficiency: {(sum(r.get('response_time', 0) for r in results.values()) / total_time):.1f}x")
        
        return {
            'total_time': total_time,
            'successful_agents': successful,
            'results': results
        }
    
    def _parse_trading_decision(self, response: str) -> Dict[str, Any]:
        """Parse REAL trading decision from AI response"""
        
        decision_data = {}
        
        lines = response.upper().split('\n')
        
        for line in lines:
            if 'DECISION:' in line:
                if 'BUY' in line:
                    decision_data['decision'] = 'BUY'
                elif 'SELL' in line:
                    decision_data['decision'] = 'SELL'
                elif 'HOLD' in line:
                    decision_data['decision'] = 'HOLD'
            
            elif 'CONFIDENCE:' in line:
                try:
                    conf = float([x for x in line.split() if x.replace('.', '').isdigit()][0])
                    decision_data['confidence'] = min(10, max(1, conf))
                except:
                    decision_data['confidence'] = 5
            
            elif 'TARGET_PRICE:' in line or 'TARGET:' in line:
                try:
                    price = float([x.replace('$', '').replace(',', '') for x in line.split() if '$' in x or x.replace('.', '').isdigit()][0])
                    decision_data['target_price'] = price
                except:
                    decision_data['target_price'] = 0
            
            elif 'RISK_LEVEL:' in line or 'RISK:' in line:
                if 'LOW' in line:
                    decision_data['risk_level'] = 'LOW'
                elif 'MEDIUM' in line:
                    decision_data['risk_level'] = 'MEDIUM'
                elif 'HIGH' in line:
                    decision_data['risk_level'] = 'HIGH'
        
        # Extract reasoning
        reasoning_start = response.find('REASONING:')
        if reasoning_start != -1:
            decision_data['reasoning'] = response[reasoning_start+10:reasoning_start+200].strip()
        
        return decision_data if 'decision' in decision_data else None
    
    def _store_test_result(self, result: Dict[str, Any], query: str, test_type: str):
        """Store REAL test result"""
        conn = sqlite3.connect('ai_proof_results.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO ai_test_results 
            (agent_name, model_name, test_type, query, response, response_time, 
             success, response_length, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (result['agent_name'], result['model'], test_type, query,
              result.get('response', ''), result['response_time'], result['success'],
              result.get('response_length', 0), result['timestamp'].isoformat()))
        
        conn.commit()
        conn.close()
    
    def _store_trading_decision(self, agent_name: str, symbol: str, decision: Dict[str, Any]):
        """Store REAL trading decision"""
        conn = sqlite3.connect('ai_proof_results.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO trading_decisions 
            (agent_name, symbol, decision, confidence, reasoning, price_target, risk_level, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (agent_name, symbol, decision.get('decision', ''), decision.get('confidence', 0),
              decision.get('reasoning', ''), decision.get('target_price', 0),
              decision.get('risk_level', ''), datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def get_agent_performance_summary(self) -> Dict[str, Any]:
        """Get REAL agent performance summary"""
        
        conn = sqlite3.connect('ai_proof_results.db')
        cursor = conn.cursor()
        
        # Get test results
        cursor.execute('''
            SELECT agent_name, COUNT(*) as total_tests, 
                   SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_tests,
                   AVG(response_time) as avg_response_time,
                   AVG(response_length) as avg_response_length
            FROM ai_test_results 
            GROUP BY agent_name
        ''')
        
        performance = {}
        for row in cursor.fetchall():
            agent_name, total, successful, avg_time, avg_length = row
            performance[agent_name] = {
                'total_tests': total,
                'successful_tests': successful,
                'success_rate': (successful / total) * 100 if total > 0 else 0,
                'avg_response_time': avg_time or 0,
                'avg_response_length': avg_length or 0
            }
        
        # Get trading decisions
        cursor.execute('''
            SELECT agent_name, decision, COUNT(*) as count
            FROM trading_decisions 
            GROUP BY agent_name, decision
        ''')
        
        trading_stats = {}
        for row in cursor.fetchall():
            agent_name, decision, count = row
            if agent_name not in trading_stats:
                trading_stats[agent_name] = {}
            trading_stats[agent_name][decision] = count
        
        conn.close()
        
        return {
            'performance': performance,
            'trading_decisions': trading_stats,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_ultimate_proof(self) -> Dict[str, Any]:
        """Run ULTIMATE PROOF of all AI agents"""
        
        print("🚀 STARTING ULTIMATE AI PROOF")
        print("=" * 60)
        
        # Test 1: Trading Decision
        trading_results = self.test_all_agents_trading_decision('BTC-USD', 101000)
        
        # Test 2: Market Analysis
        analysis_query = "Analyze current Bitcoin market conditions and provide 3 key insights in 100 words."
        analysis_results = self.test_parallel_agents(analysis_query, 5)
        
        # Test 3: Risk Assessment
        risk_query = "Assess the risk of investing $10,000 in Bitcoin right now. Rate risk 1-10 and explain."
        
        print(f"\n🛡️ TESTING RISK ASSESSMENT...")
        risk_results = {}
        for agent_name, model in list(self.ai_agents.items())[:3]:  # Test top 3
            result = self.test_single_agent(agent_name, model, risk_query, 'risk_assessment')
            risk_results[agent_name] = result
        
        # Get final summary
        summary = self.get_agent_performance_summary()
        
        final_results = {
            'trading_test': trading_results,
            'analysis_test': analysis_results,
            'risk_test': risk_results,
            'performance_summary': summary,
            'total_agents_tested': len(self.ai_agents),
            'total_successful': len([a for a in summary['performance'].values() if a['success_rate'] > 0]),
            'proof_complete': True
        }
        
        print(f"\n🎉 ULTIMATE PROOF COMPLETE!")
        print(f"   Total agents: {final_results['total_agents_tested']}")
        print(f"   Working agents: {final_results['total_successful']}")
        print(f"   Success rate: {(final_results['total_successful']/final_results['total_agents_tested'])*100:.1f}%")
        
        return final_results

def main():
    """Run ULTIMATE AI PROOF"""
    print("🚀 ULTIMATE AI PROOF SYSTEM - NO BULLSHIT")
    print("=" * 60)
    
    # Initialize system
    proof_system = UltimateAIProofSystem()
    
    # Run ultimate proof
    results = proof_system.run_ultimate_proof()
    
    # Show detailed results
    print(f"\n📊 DETAILED RESULTS:")
    
    for agent_name, perf in results['performance_summary']['performance'].items():
        print(f"   {agent_name}:")
        print(f"      Success rate: {perf['success_rate']:.1f}%")
        print(f"      Avg response time: {perf['avg_response_time']:.1f}s")
        print(f"      Avg response length: {perf['avg_response_length']:.0f} chars")
    
    # Show trading decisions
    print(f"\n💼 TRADING DECISIONS:")
    for agent_name, decisions in results['performance_summary']['trading_decisions'].items():
        decision_str = ", ".join([f"{d}: {c}" for d, c in decisions.items()])
        print(f"   {agent_name}: {decision_str}")
    
    print(f"\n✅ PROOF COMPLETE - ALL DATA STORED IN 'ai_proof_results.db'")

if __name__ == "__main__":
    main()
