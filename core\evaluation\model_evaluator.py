#!/usr/bin/env python3
"""
Noryon Model Evaluation System
Comprehensive evaluation framework for AI trading models:
- Performance metrics calculation
- Model comparison and ranking
- Statistical significance testing
- Risk-adjusted returns analysis
- Backtesting validation
- Model stability assessment
- Feature importance analysis
- Prediction quality metrics
- Portfolio-level evaluation
- Real-time performance monitoring
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import json
from pathlib import Path
import sys
from collections import defaultdict
import warnings
from scipy import stats
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report,
    mean_squared_error, mean_absolute_error, r2_score
)
import matplotlib.pyplot as plt
import seaborn as sns
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MetricType(Enum):
    """Types of evaluation metrics"""
    RETURN = "return"
    RISK = "risk"
    RATIO = "ratio"
    ACCURACY = "accuracy"
    STABILITY = "stability"
    EFFICIENCY = "efficiency"

class EvaluationPeriod(Enum):
    """Evaluation time periods"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    FULL_PERIOD = "full_period"

class ModelType(Enum):
    """Model types for evaluation"""
    MOMENTUM_STRATEGY = "momentum_strategy"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_REGIME = "market_regime"
    ENSEMBLE_VOTING = "ensemble_voting"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    GENETIC_ALGORITHM = "genetic_algorithm"

@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    # Return metrics
    total_return: float = 0.0
    annualized_return: float = 0.0
    cumulative_return: float = 0.0
    average_return: float = 0.0
    
    # Risk metrics
    volatility: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0  # Value at Risk 95%
    cvar_95: float = 0.0  # Conditional Value at Risk 95%
    downside_deviation: float = 0.0
    
    # Risk-adjusted ratios
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0
    information_ratio: float = 0.0
    treynor_ratio: float = 0.0
    
    # Trading metrics
    win_rate: float = 0.0
    profit_factor: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    largest_win: float = 0.0
    largest_loss: float = 0.0
    
    # Efficiency metrics
    number_of_trades: int = 0
    hit_ratio: float = 0.0
    recovery_factor: float = 0.0
    expectancy: float = 0.0
    
    # Statistical metrics
    skewness: float = 0.0
    kurtosis: float = 0.0
    beta: float = 0.0
    alpha: float = 0.0
    correlation_with_market: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'cumulative_return': self.cumulative_return,
            'average_return': self.average_return,
            'volatility': self.volatility,
            'max_drawdown': self.max_drawdown,
            'var_95': self.var_95,
            'cvar_95': self.cvar_95,
            'downside_deviation': self.downside_deviation,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'calmar_ratio': self.calmar_ratio,
            'information_ratio': self.information_ratio,
            'treynor_ratio': self.treynor_ratio,
            'win_rate': self.win_rate,
            'profit_factor': self.profit_factor,
            'average_win': self.average_win,
            'average_loss': self.average_loss,
            'largest_win': self.largest_win,
            'largest_loss': self.largest_loss,
            'number_of_trades': self.number_of_trades,
            'hit_ratio': self.hit_ratio,
            'recovery_factor': self.recovery_factor,
            'expectancy': self.expectancy,
            'skewness': self.skewness,
            'kurtosis': self.kurtosis,
            'beta': self.beta,
            'alpha': self.alpha,
            'correlation_with_market': self.correlation_with_market
        }

@dataclass
class PredictionMetrics:
    """Container for prediction quality metrics"""
    # Classification metrics
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    auc_roc: float = 0.0
    
    # Regression metrics
    mse: float = 0.0
    mae: float = 0.0
    rmse: float = 0.0
    r2_score: float = 0.0
    
    # Trading-specific metrics
    directional_accuracy: float = 0.0
    signal_quality: float = 0.0
    timing_accuracy: float = 0.0
    magnitude_accuracy: float = 0.0
    
    # Confidence metrics
    calibration_score: float = 0.0
    confidence_correlation: float = 0.0
    overconfidence_ratio: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary"""
        return {
            'accuracy': self.accuracy,
            'precision': self.precision,
            'recall': self.recall,
            'f1_score': self.f1_score,
            'auc_roc': self.auc_roc,
            'mse': self.mse,
            'mae': self.mae,
            'rmse': self.rmse,
            'r2_score': self.r2_score,
            'directional_accuracy': self.directional_accuracy,
            'signal_quality': self.signal_quality,
            'timing_accuracy': self.timing_accuracy,
            'magnitude_accuracy': self.magnitude_accuracy,
            'calibration_score': self.calibration_score,
            'confidence_correlation': self.confidence_correlation,
            'overconfidence_ratio': self.overconfidence_ratio
        }

@dataclass
class ModelEvaluationResult:
    """Complete evaluation result for a model"""
    model_name: str
    model_type: ModelType
    evaluation_date: str
    evaluation_period: EvaluationPeriod
    
    # Performance metrics
    performance_metrics: PerformanceMetrics
    prediction_metrics: PredictionMetrics
    
    # Additional analysis
    feature_importance: Dict[str, float] = field(default_factory=dict)
    stability_metrics: Dict[str, float] = field(default_factory=dict)
    regime_performance: Dict[str, PerformanceMetrics] = field(default_factory=dict)
    
    # Statistical tests
    statistical_significance: Dict[str, float] = field(default_factory=dict)
    
    # Metadata
    data_period: Tuple[str, str] = field(default_factory=lambda: ("", ""))
    sample_size: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'model_name': self.model_name,
            'model_type': self.model_type.value,
            'evaluation_date': self.evaluation_date,
            'evaluation_period': self.evaluation_period.value,
            'performance_metrics': self.performance_metrics.to_dict(),
            'prediction_metrics': self.prediction_metrics.to_dict(),
            'feature_importance': self.feature_importance,
            'stability_metrics': self.stability_metrics,
            'regime_performance': {regime: metrics.to_dict() 
                                 for regime, metrics in self.regime_performance.items()},
            'statistical_significance': self.statistical_significance,
            'data_period': self.data_period,
            'sample_size': self.sample_size
        }

class MetricsCalculator:
    """Calculate various performance and prediction metrics"""
    
    def __init__(self, risk_free_rate: float = 0.02):
        self.risk_free_rate = risk_free_rate
    
    def calculate_performance_metrics(self, returns: pd.Series, 
                                    trades: Optional[pd.DataFrame] = None,
                                    benchmark_returns: Optional[pd.Series] = None) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        
        if len(returns) == 0:
            return PerformanceMetrics()
        
        metrics = PerformanceMetrics()
        
        # Basic return metrics
        metrics.total_return = (1 + returns).prod() - 1
        metrics.cumulative_return = metrics.total_return
        metrics.average_return = returns.mean()
        
        # Annualized return
        periods_per_year = self._get_periods_per_year(returns.index)
        metrics.annualized_return = (1 + metrics.average_return) ** periods_per_year - 1
        
        # Risk metrics
        metrics.volatility = returns.std() * np.sqrt(periods_per_year)
        metrics.max_drawdown = self._calculate_max_drawdown(returns)
        metrics.var_95 = np.percentile(returns, 5)
        metrics.cvar_95 = returns[returns <= metrics.var_95].mean()
        metrics.downside_deviation = self._calculate_downside_deviation(returns)
        
        # Risk-adjusted ratios
        if metrics.volatility > 0:
            metrics.sharpe_ratio = (metrics.annualized_return - self.risk_free_rate) / metrics.volatility
            metrics.sortino_ratio = (metrics.annualized_return - self.risk_free_rate) / metrics.downside_deviation
        
        if metrics.max_drawdown < 0:
            metrics.calmar_ratio = metrics.annualized_return / abs(metrics.max_drawdown)
        
        # Trading metrics (if trades provided)
        if trades is not None and len(trades) > 0:
            self._calculate_trading_metrics(trades, metrics)
        
        # Statistical metrics
        metrics.skewness = returns.skew()
        metrics.kurtosis = returns.kurtosis()
        
        # Benchmark comparison (if provided)
        if benchmark_returns is not None:
            self._calculate_benchmark_metrics(returns, benchmark_returns, metrics)
        
        return metrics
    
    def calculate_prediction_metrics(self, y_true: np.ndarray, y_pred: np.ndarray,
                                   y_prob: Optional[np.ndarray] = None,
                                   confidence: Optional[np.ndarray] = None) -> PredictionMetrics:
        """Calculate prediction quality metrics"""
        
        metrics = PredictionMetrics()
        
        # Determine if classification or regression
        is_classification = self._is_classification_task(y_true, y_pred)
        
        if is_classification:
            # Classification metrics
            metrics.accuracy = accuracy_score(y_true, y_pred)
            metrics.precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics.recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
            metrics.f1_score = f1_score(y_true, y_pred, average='weighted', zero_division=0)
            
            if y_prob is not None:
                try:
                    metrics.auc_roc = roc_auc_score(y_true, y_prob, multi_class='ovr')
                except ValueError:
                    metrics.auc_roc = 0.0
        else:
            # Regression metrics
            metrics.mse = mean_squared_error(y_true, y_pred)
            metrics.mae = mean_absolute_error(y_true, y_pred)
            metrics.rmse = np.sqrt(metrics.mse)
            metrics.r2_score = r2_score(y_true, y_pred)
        
        # Trading-specific metrics
        metrics.directional_accuracy = self._calculate_directional_accuracy(y_true, y_pred)
        metrics.signal_quality = self._calculate_signal_quality(y_true, y_pred)
        
        # Confidence metrics (if provided)
        if confidence is not None:
            metrics.calibration_score = self._calculate_calibration_score(y_true, y_pred, confidence)
            metrics.confidence_correlation = self._calculate_confidence_correlation(y_true, y_pred, confidence)
        
        return metrics
    
    def _get_periods_per_year(self, index: pd.Index) -> int:
        """Determine periods per year based on data frequency"""
        if len(index) < 2:
            return 252  # Default to daily
        
        freq = pd.infer_freq(index)
        if freq is None:
            # Estimate from time differences
            time_diff = (index[-1] - index[0]) / len(index)
            if time_diff <= timedelta(minutes=5):
                return 252 * 24 * 12  # 5-minute data
            elif time_diff <= timedelta(hours=1):
                return 252 * 24  # Hourly data
            elif time_diff <= timedelta(days=1):
                return 252  # Daily data
            elif time_diff <= timedelta(days=7):
                return 52  # Weekly data
            else:
                return 12  # Monthly data
        
        freq_map = {
            'D': 252, 'B': 252,  # Daily
            'W': 52,  # Weekly
            'M': 12,  # Monthly
            'Q': 4,   # Quarterly
            'A': 1, 'Y': 1  # Annual
        }
        
        return freq_map.get(freq[0], 252)
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def _calculate_downside_deviation(self, returns: pd.Series, target: float = 0.0) -> float:
        """Calculate downside deviation"""
        downside_returns = returns[returns < target]
        if len(downside_returns) == 0:
            return 0.0
        return downside_returns.std() * np.sqrt(self._get_periods_per_year(returns.index))
    
    def _calculate_trading_metrics(self, trades: pd.DataFrame, metrics: PerformanceMetrics):
        """Calculate trading-specific metrics"""
        if 'pnl' not in trades.columns:
            return
        
        pnl = trades['pnl']
        metrics.number_of_trades = len(trades)
        
        if len(pnl) > 0:
            winning_trades = pnl[pnl > 0]
            losing_trades = pnl[pnl < 0]
            
            metrics.win_rate = len(winning_trades) / len(pnl)
            metrics.hit_ratio = metrics.win_rate
            
            if len(winning_trades) > 0:
                metrics.average_win = winning_trades.mean()
                metrics.largest_win = winning_trades.max()
            
            if len(losing_trades) > 0:
                metrics.average_loss = losing_trades.mean()
                metrics.largest_loss = losing_trades.min()
                
                if metrics.average_loss != 0:
                    metrics.profit_factor = abs(metrics.average_win * len(winning_trades)) / abs(metrics.average_loss * len(losing_trades))
            
            metrics.expectancy = pnl.mean()
            
            if metrics.largest_loss != 0:
                metrics.recovery_factor = pnl.sum() / abs(metrics.largest_loss)
    
    def _calculate_benchmark_metrics(self, returns: pd.Series, benchmark_returns: pd.Series, 
                                   metrics: PerformanceMetrics):
        """Calculate benchmark comparison metrics"""
        # Align series
        aligned_returns, aligned_benchmark = returns.align(benchmark_returns, join='inner')
        
        if len(aligned_returns) == 0:
            return
        
        # Correlation
        metrics.correlation_with_market = aligned_returns.corr(aligned_benchmark)
        
        # Beta and Alpha
        if aligned_benchmark.var() > 0:
            metrics.beta = aligned_returns.cov(aligned_benchmark) / aligned_benchmark.var()
            
            benchmark_return = (1 + aligned_benchmark).prod() ** (252 / len(aligned_benchmark)) - 1
            metrics.alpha = metrics.annualized_return - (self.risk_free_rate + metrics.beta * (benchmark_return - self.risk_free_rate))
        
        # Information ratio
        excess_returns = aligned_returns - aligned_benchmark
        if excess_returns.std() > 0:
            metrics.information_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        
        # Treynor ratio
        if metrics.beta != 0:
            metrics.treynor_ratio = (metrics.annualized_return - self.risk_free_rate) / metrics.beta
    
    def _is_classification_task(self, y_true: np.ndarray, y_pred: np.ndarray) -> bool:
        """Determine if task is classification or regression"""
        # Check if predictions are discrete
        unique_pred = np.unique(y_pred)
        unique_true = np.unique(y_true)
        
        # If small number of unique values and they're integers, likely classification
        return (len(unique_pred) <= 10 and len(unique_true) <= 10 and 
                np.all(unique_pred == unique_pred.astype(int)) and 
                np.all(unique_true == unique_true.astype(int)))
    
    def _calculate_directional_accuracy(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate directional accuracy for trading signals"""
        if len(y_true) <= 1:
            return 0.0
        
        # Calculate direction changes
        true_direction = np.sign(np.diff(y_true))
        pred_direction = np.sign(np.diff(y_pred))
        
        # Remove zeros (no change)
        mask = (true_direction != 0) & (pred_direction != 0)
        if np.sum(mask) == 0:
            return 0.0
        
        return np.mean(true_direction[mask] == pred_direction[mask])
    
    def _calculate_signal_quality(self, y_true: np.ndarray, y_pred: np.ndarray) -> float:
        """Calculate signal quality based on correlation and timing"""
        if len(y_true) < 2:
            return 0.0
        
        correlation = np.corrcoef(y_true, y_pred)[0, 1]
        if np.isnan(correlation):
            return 0.0
        
        return max(0.0, correlation)
    
    def _calculate_calibration_score(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                   confidence: np.ndarray) -> float:
        """Calculate prediction calibration score"""
        # Bin predictions by confidence
        n_bins = 10
        bin_boundaries = np.linspace(0, 1, n_bins + 1)
        bin_lowers = bin_boundaries[:-1]
        bin_uppers = bin_boundaries[1:]
        
        calibration_error = 0.0
        total_samples = len(y_true)
        
        for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
            in_bin = (confidence > bin_lower) & (confidence <= bin_upper)
            prop_in_bin = in_bin.mean()
            
            if prop_in_bin > 0:
                accuracy_in_bin = (y_true[in_bin] == y_pred[in_bin]).mean()
                avg_confidence_in_bin = confidence[in_bin].mean()
                calibration_error += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
        
        return 1.0 - calibration_error  # Higher is better
    
    def _calculate_confidence_correlation(self, y_true: np.ndarray, y_pred: np.ndarray, 
                                        confidence: np.ndarray) -> float:
        """Calculate correlation between confidence and accuracy"""
        accuracy = (y_true == y_pred).astype(float)
        correlation = np.corrcoef(confidence, accuracy)[0, 1]
        return 0.0 if np.isnan(correlation) else correlation

class ModelComparator:
    """Compare multiple models and rank them"""
    
    def __init__(self, primary_metric: str = "sharpe_ratio"):
        self.primary_metric = primary_metric
    
    def compare_models(self, evaluation_results: List[ModelEvaluationResult]) -> pd.DataFrame:
        """Compare models and create ranking"""
        
        if not evaluation_results:
            return pd.DataFrame()
        
        # Extract metrics for comparison
        comparison_data = []
        
        for result in evaluation_results:
            row = {
                'model_name': result.model_name,
                'model_type': result.model_type.value,
                **result.performance_metrics.to_dict(),
                **result.prediction_metrics.to_dict()
            }
            comparison_data.append(row)
        
        df = pd.DataFrame(comparison_data)
        
        # Rank by primary metric (higher is better for most metrics)
        ascending = self.primary_metric in ['volatility', 'max_drawdown', 'mse', 'mae', 'rmse']
        df['rank'] = df[self.primary_metric].rank(ascending=ascending)
        
        # Sort by rank
        df = df.sort_values('rank')
        
        return df
    
    def statistical_significance_test(self, returns1: pd.Series, returns2: pd.Series) -> Dict[str, float]:
        """Test statistical significance between two return series"""
        
        # Align series
        aligned_returns1, aligned_returns2 = returns1.align(returns2, join='inner')
        
        if len(aligned_returns1) < 10:
            return {'p_value': 1.0, 'test_statistic': 0.0, 'significant': False}
        
        # T-test for difference in means
        t_stat, p_value = stats.ttest_rel(aligned_returns1, aligned_returns2)
        
        # Wilcoxon signed-rank test (non-parametric)
        try:
            wilcoxon_stat, wilcoxon_p = stats.wilcoxon(aligned_returns1, aligned_returns2)
        except ValueError:
            wilcoxon_stat, wilcoxon_p = 0.0, 1.0
        
        return {
            't_test_statistic': t_stat,
            't_test_p_value': p_value,
            'wilcoxon_statistic': wilcoxon_stat,
            'wilcoxon_p_value': wilcoxon_p,
            'significant_t_test': p_value < 0.05,
            'significant_wilcoxon': wilcoxon_p < 0.05
        }
    
    def create_performance_report(self, evaluation_results: List[ModelEvaluationResult]) -> str:
        """Create comprehensive performance report"""
        
        if not evaluation_results:
            return "No evaluation results provided."
        
        report = []
        report.append("=" * 80)
        report.append("MODEL PERFORMANCE EVALUATION REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Number of models evaluated: {len(evaluation_results)}")
        report.append("")
        
        # Model comparison table
        comparison_df = self.compare_models(evaluation_results)
        report.append("MODEL RANKING (by Sharpe Ratio):")
        report.append("-" * 50)
        
        for _, row in comparison_df.head(10).iterrows():
            report.append(f"{row['rank']:2.0f}. {row['model_name']:<25} | "
                         f"Sharpe: {row['sharpe_ratio']:6.3f} | "
                         f"Return: {row['annualized_return']:6.1%} | "
                         f"MaxDD: {row['max_drawdown']:6.1%}")
        
        report.append("")
        
        # Detailed analysis for top 3 models
        report.append("DETAILED ANALYSIS - TOP 3 MODELS:")
        report.append("=" * 50)
        
        for i, result in enumerate(evaluation_results[:3]):
            report.append(f"\n{i+1}. {result.model_name} ({result.model_type.value})")
            report.append("-" * 40)
            
            perf = result.performance_metrics
            pred = result.prediction_metrics
            
            report.append(f"Performance Metrics:")
            report.append(f"  • Annualized Return: {perf.annualized_return:8.2%}")
            report.append(f"  • Volatility:        {perf.volatility:8.2%}")
            report.append(f"  • Sharpe Ratio:      {perf.sharpe_ratio:8.3f}")
            report.append(f"  • Max Drawdown:      {perf.max_drawdown:8.2%}")
            report.append(f"  • Win Rate:          {perf.win_rate:8.2%}")
            report.append(f"  • Profit Factor:     {perf.profit_factor:8.3f}")
            
            report.append(f"Prediction Quality:")
            report.append(f"  • Accuracy:          {pred.accuracy:8.2%}")
            report.append(f"  • Directional Acc:   {pred.directional_accuracy:8.2%}")
            report.append(f"  • Signal Quality:    {pred.signal_quality:8.3f}")
            
            if result.feature_importance:
                report.append(f"Top Features:")
                sorted_features = sorted(result.feature_importance.items(), 
                                       key=lambda x: x[1], reverse=True)[:5]
                for feature, importance in sorted_features:
                    report.append(f"  • {feature:<15}: {importance:6.3f}")
        
        return "\n".join(report)

class ModelEvaluator:
    """Main model evaluation system"""
    
    def __init__(self, output_directory: str = "./evaluation_results"):
        self.output_directory = Path(output_directory)
        self.output_directory.mkdir(parents=True, exist_ok=True)
        
        self.metrics_calculator = MetricsCalculator()
        self.model_comparator = ModelComparator()
        
        # Evaluation history
        self.evaluation_history: List[ModelEvaluationResult] = []
    
    def evaluate_model(self, model_name: str, model_type: ModelType,
                      returns: pd.Series, predictions: np.ndarray, 
                      actual_values: np.ndarray,
                      trades: Optional[pd.DataFrame] = None,
                      benchmark_returns: Optional[pd.Series] = None,
                      prediction_probabilities: Optional[np.ndarray] = None,
                      confidence_scores: Optional[np.ndarray] = None,
                      feature_importance: Optional[Dict[str, float]] = None,
                      evaluation_period: EvaluationPeriod = EvaluationPeriod.FULL_PERIOD) -> ModelEvaluationResult:
        """Evaluate a single model comprehensively"""
        
        logger.info(f"Evaluating model: {model_name}")
        
        # Calculate performance metrics
        performance_metrics = self.metrics_calculator.calculate_performance_metrics(
            returns, trades, benchmark_returns
        )
        
        # Calculate prediction metrics
        prediction_metrics = self.metrics_calculator.calculate_prediction_metrics(
            actual_values, predictions, prediction_probabilities, confidence_scores
        )
        
        # Calculate stability metrics
        stability_metrics = self._calculate_stability_metrics(returns)
        
        # Create evaluation result
        result = ModelEvaluationResult(
            model_name=model_name,
            model_type=model_type,
            evaluation_date=datetime.now().isoformat(),
            evaluation_period=evaluation_period,
            performance_metrics=performance_metrics,
            prediction_metrics=prediction_metrics,
            feature_importance=feature_importance or {},
            stability_metrics=stability_metrics,
            data_period=(str(returns.index[0]), str(returns.index[-1])) if len(returns) > 0 else ("", ""),
            sample_size=len(returns)
        )
        
        # Add to history
        self.evaluation_history.append(result)
        
        # Save individual result
        self._save_evaluation_result(result)
        
        logger.info(f"Model evaluation completed: {model_name}")
        return result
    
    def evaluate_multiple_models(self, model_data: List[Dict[str, Any]], 
                               parallel: bool = True) -> List[ModelEvaluationResult]:
        """Evaluate multiple models"""
        
        logger.info(f"Evaluating {len(model_data)} models")
        
        if parallel and len(model_data) > 1:
            return self._evaluate_models_parallel(model_data)
        else:
            return self._evaluate_models_sequential(model_data)
    
    def _evaluate_models_sequential(self, model_data: List[Dict[str, Any]]) -> List[ModelEvaluationResult]:
        """Evaluate models sequentially"""
        results = []
        
        for data in model_data:
            try:
                result = self.evaluate_model(**data)
                results.append(result)
            except Exception as e:
                logger.error(f"Error evaluating model {data.get('model_name', 'unknown')}: {e}")
        
        return results
    
    def _evaluate_models_parallel(self, model_data: List[Dict[str, Any]]) -> List[ModelEvaluationResult]:
        """Evaluate models in parallel"""
        results = []
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_model = {executor.submit(self.evaluate_model, **data): data 
                             for data in model_data}
            
            for future in as_completed(future_to_model):
                data = future_to_model[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error evaluating model {data.get('model_name', 'unknown')}: {e}")
        
        return results
    
    def _calculate_stability_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate model stability metrics"""
        if len(returns) < 30:
            return {}
        
        # Rolling window analysis
        window_size = min(30, len(returns) // 4)
        rolling_sharpe = []
        rolling_volatility = []
        
        for i in range(window_size, len(returns)):
            window_returns = returns.iloc[i-window_size:i]
            
            if window_returns.std() > 0:
                sharpe = window_returns.mean() / window_returns.std() * np.sqrt(252)
                rolling_sharpe.append(sharpe)
            
            volatility = window_returns.std() * np.sqrt(252)
            rolling_volatility.append(volatility)
        
        stability_metrics = {}
        
        if rolling_sharpe:
            stability_metrics['sharpe_stability'] = 1.0 / (1.0 + np.std(rolling_sharpe))
            stability_metrics['sharpe_trend'] = np.corrcoef(range(len(rolling_sharpe)), rolling_sharpe)[0, 1]
        
        if rolling_volatility:
            stability_metrics['volatility_stability'] = 1.0 / (1.0 + np.std(rolling_volatility))
        
        # Drawdown recovery analysis
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        # Time to recovery from drawdowns
        recovery_times = []
        in_drawdown = False
        drawdown_start = None
        
        for i, dd in enumerate(drawdown):
            if dd < -0.05 and not in_drawdown:  # Start of significant drawdown
                in_drawdown = True
                drawdown_start = i
            elif dd >= 0 and in_drawdown:  # Recovery
                if drawdown_start is not None:
                    recovery_times.append(i - drawdown_start)
                in_drawdown = False
                drawdown_start = None
        
        if recovery_times:
            stability_metrics['avg_recovery_time'] = np.mean(recovery_times)
            stability_metrics['max_recovery_time'] = np.max(recovery_times)
        
        return stability_metrics
    
    def _save_evaluation_result(self, result: ModelEvaluationResult):
        """Save evaluation result to file"""
        filename = f"{result.model_name}_{result.evaluation_date[:10]}.json"
        filepath = self.output_directory / filename
        
        with open(filepath, 'w') as f:
            json.dump(result.to_dict(), f, indent=2)
    
    def generate_comprehensive_report(self, models_to_include: Optional[List[str]] = None) -> str:
        """Generate comprehensive evaluation report"""
        
        results_to_include = self.evaluation_history
        
        if models_to_include:
            results_to_include = [r for r in self.evaluation_history 
                                if r.model_name in models_to_include]
        
        return self.model_comparator.create_performance_report(results_to_include)
    
    def create_evaluation_dashboard(self, save_plots: bool = True) -> Dict[str, Any]:
        """Create evaluation dashboard with visualizations"""
        
        if not self.evaluation_history:
            return {"message": "No evaluation results available"}
        
        dashboard_data = {
            "summary": self._create_summary_statistics(),
            "rankings": self._create_model_rankings(),
            "performance_comparison": self._create_performance_comparison(),
            "stability_analysis": self._create_stability_analysis()
        }
        
        if save_plots:
            self._create_evaluation_plots()
        
        return dashboard_data
    
    def _create_summary_statistics(self) -> Dict[str, Any]:
        """Create summary statistics"""
        if not self.evaluation_history:
            return {}
        
        # Extract key metrics
        sharpe_ratios = [r.performance_metrics.sharpe_ratio for r in self.evaluation_history]
        returns = [r.performance_metrics.annualized_return for r in self.evaluation_history]
        max_drawdowns = [r.performance_metrics.max_drawdown for r in self.evaluation_history]
        
        return {
            "total_models_evaluated": len(self.evaluation_history),
            "evaluation_period": {
                "start": min(r.data_period[0] for r in self.evaluation_history if r.data_period[0]),
                "end": max(r.data_period[1] for r in self.evaluation_history if r.data_period[1])
            },
            "performance_summary": {
                "avg_sharpe_ratio": np.mean(sharpe_ratios),
                "best_sharpe_ratio": np.max(sharpe_ratios),
                "avg_return": np.mean(returns),
                "best_return": np.max(returns),
                "avg_max_drawdown": np.mean(max_drawdowns),
                "best_max_drawdown": np.max(max_drawdowns)  # Least negative
            }
        }
    
    def _create_model_rankings(self) -> List[Dict[str, Any]]:
        """Create model rankings"""
        comparison_df = self.model_comparator.compare_models(self.evaluation_history)
        return comparison_df.to_dict('records')
    
    def _create_performance_comparison(self) -> Dict[str, List[float]]:
        """Create performance comparison data"""
        comparison_data = {}
        
        for result in self.evaluation_history:
            model_name = result.model_name
            comparison_data[model_name] = {
                "sharpe_ratio": result.performance_metrics.sharpe_ratio,
                "annualized_return": result.performance_metrics.annualized_return,
                "max_drawdown": result.performance_metrics.max_drawdown,
                "volatility": result.performance_metrics.volatility,
                "win_rate": result.performance_metrics.win_rate
            }
        
        return comparison_data
    
    def _create_stability_analysis(self) -> Dict[str, Any]:
        """Create stability analysis"""
        stability_data = {}
        
        for result in self.evaluation_history:
            if result.stability_metrics:
                stability_data[result.model_name] = result.stability_metrics
        
        return stability_data
    
    def _create_evaluation_plots(self):
        """Create evaluation plots"""
        try:
            # Performance comparison plot
            self._plot_performance_comparison()
            
            # Risk-return scatter plot
            self._plot_risk_return_scatter()
            
            # Model rankings plot
            self._plot_model_rankings()
            
            logger.info("Evaluation plots created successfully")
        except Exception as e:
            logger.error(f"Error creating evaluation plots: {e}")
    
    def _plot_performance_comparison(self):
        """Create performance comparison plot"""
        if len(self.evaluation_history) < 2:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Model Performance Comparison', fontsize=16)
        
        models = [r.model_name for r in self.evaluation_history]
        
        # Sharpe Ratio
        sharpe_ratios = [r.performance_metrics.sharpe_ratio for r in self.evaluation_history]
        axes[0, 0].bar(models, sharpe_ratios)
        axes[0, 0].set_title('Sharpe Ratio')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Annualized Return
        returns = [r.performance_metrics.annualized_return for r in self.evaluation_history]
        axes[0, 1].bar(models, returns)
        axes[0, 1].set_title('Annualized Return')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Max Drawdown
        drawdowns = [r.performance_metrics.max_drawdown for r in self.evaluation_history]
        axes[1, 0].bar(models, drawdowns)
        axes[1, 0].set_title('Max Drawdown')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Win Rate
        win_rates = [r.performance_metrics.win_rate for r in self.evaluation_history]
        axes[1, 1].bar(models, win_rates)
        axes[1, 1].set_title('Win Rate')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.output_directory / 'performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_risk_return_scatter(self):
        """Create risk-return scatter plot"""
        if len(self.evaluation_history) < 2:
            return
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        returns = [r.performance_metrics.annualized_return for r in self.evaluation_history]
        volatilities = [r.performance_metrics.volatility for r in self.evaluation_history]
        models = [r.model_name for r in self.evaluation_history]
        
        scatter = ax.scatter(volatilities, returns, s=100, alpha=0.7)
        
        # Add model labels
        for i, model in enumerate(models):
            ax.annotate(model, (volatilities[i], returns[i]), 
                       xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax.set_xlabel('Volatility (Risk)')
        ax.set_ylabel('Annualized Return')
        ax.set_title('Risk-Return Profile')
        ax.grid(True, alpha=0.3)
        
        plt.savefig(self.output_directory / 'risk_return_scatter.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_model_rankings(self):
        """Create model rankings plot"""
        comparison_df = self.model_comparator.compare_models(self.evaluation_history)
        
        if len(comparison_df) < 2:
            return
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Create horizontal bar chart
        y_pos = np.arange(len(comparison_df))
        ax.barh(y_pos, comparison_df['sharpe_ratio'])
        
        ax.set_yticks(y_pos)
        ax.set_yticklabels(comparison_df['model_name'])
        ax.set_xlabel('Sharpe Ratio')
        ax.set_title('Model Rankings by Sharpe Ratio')
        
        # Add value labels
        for i, v in enumerate(comparison_df['sharpe_ratio']):
            ax.text(v + 0.01, i, f'{v:.3f}', va='center')
        
        plt.tight_layout()
        plt.savefig(self.output_directory / 'model_rankings.png', dpi=300, bbox_inches='tight')
        plt.close()

# Example usage
if __name__ == "__main__":
    # Create sample data for demonstration
    np.random.seed(42)
    
    # Sample returns data
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    returns1 = pd.Series(np.random.normal(0.001, 0.02, len(dates)), index=dates)
    returns2 = pd.Series(np.random.normal(0.0005, 0.015, len(dates)), index=dates)
    
    # Sample predictions
    actual_values = np.random.normal(0, 1, 100)
    predictions1 = actual_values + np.random.normal(0, 0.1, 100)
    predictions2 = actual_values + np.random.normal(0, 0.2, 100)
    
    # Create evaluator
    evaluator = ModelEvaluator()
    
    # Evaluate models
    result1 = evaluator.evaluate_model(
        model_name="Enhanced_Momentum_v1",
        model_type=ModelType.MOMENTUM_STRATEGY,
        returns=returns1,
        predictions=predictions1,
        actual_values=actual_values
    )
    
    result2 = evaluator.evaluate_model(
        model_name="Risk_Assessment_v1",
        model_type=ModelType.RISK_ASSESSMENT,
        returns=returns2,
        predictions=predictions2,
        actual_values=actual_values
    )
    
    # Generate report
    report = evaluator.generate_comprehensive_report()
    print(report)
    
    # Create dashboard
    dashboard = evaluator.create_evaluation_dashboard()
    print("\nDashboard created with evaluation results")