#!/usr/bin/env python3
"""
High-Performance Trading Engine
Optimized for speed, parallel processing, and efficiency
"""

import asyncio
import concurrent.futures
import multiprocessing as mp
import threading
import time
import queue
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
import numpy as np
from collections import deque, defaultdict
import psutil
import gc

# Import components
from optimized_model_caller import OptimizedModelCaller
from memory_manager import MemoryManager
from advanced_ensemble_voting import AdvancedEnsembleVoting, EnsembleDecision

@dataclass
class PerformanceMetrics:
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    active_threads: int
    queue_size: int
    processing_rate: float  # operations per second
    latency_ms: float
    throughput_ops_sec: float

@dataclass
class ProcessingTask:
    task_id: str
    task_type: str  # 'signal_generation', 'risk_check', 'execution'
    symbol: str
    priority: int  # 1=highest, 5=lowest
    data: Dict[str, Any]
    created_at: datetime
    timeout: float = 30.0

@dataclass
class TaskResult:
    task_id: str
    success: bool
    result: Any
    processing_time: float
    completed_at: datetime
    error_message: Optional[str] = None

class HighPerformanceEngine:
    """High-performance trading engine with parallel processing"""
    
    def __init__(self, max_workers: int = None):
        # Performance configuration
        self.max_workers = max_workers or min(8, mp.cpu_count())
        self.max_queue_size = 1000
        self.batch_size = 10
        self.processing_interval = 0.1  # 100ms
        
        # Core components
        self.model_caller = OptimizedModelCaller()
        self.memory_manager = MemoryManager()
        self.ensemble = AdvancedEnsembleVoting()
        
        # Processing infrastructure
        self.task_queue = queue.PriorityQueue(maxsize=self.max_queue_size)
        self.result_queue = queue.Queue()
        self.worker_pool = concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers)
        
        # Performance tracking
        self.performance_history = deque(maxlen=1000)
        self.task_history = deque(maxlen=10000)
        self.processing_stats = defaultdict(list)
        
        # Control flags
        self.is_running = False
        self.shutdown_event = threading.Event()
        
        # Caching for performance
        self.response_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.cache_cleanup_interval = 60  # 1 minute
        
        # Optimization settings
        self.optimization_config = {
            'enable_caching': True,
            'enable_batching': True,
            'enable_parallel_processing': True,
            'enable_memory_optimization': True,
            'enable_gpu_acceleration': False,  # Would need GPU setup
            'max_concurrent_models': 3,
            'response_timeout': 30,
            'cache_hit_threshold': 0.8  # 80% similarity for cache hit
        }
        
        print("⚡ High-Performance Trading Engine initialized")
        print(f"   🔧 Workers: {self.max_workers}")
        print(f"   📊 Queue size: {self.max_queue_size}")
        print(f"   🚀 Optimizations: {sum(self.optimization_config.values())} enabled")
        print(f"   💾 Caching: {'Enabled' if self.optimization_config['enable_caching'] else 'Disabled'}")
    
    def start_engine(self):
        """Start the high-performance engine"""
        if self.is_running:
            print("Engine already running")
            return
        
        self.is_running = True
        self.shutdown_event.clear()
        
        # Start background threads
        self.processing_thread = threading.Thread(target=self._processing_loop, daemon=True)
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.cache_cleanup_thread = threading.Thread(target=self._cache_cleanup_loop, daemon=True)
        
        self.processing_thread.start()
        self.monitoring_thread.start()
        self.cache_cleanup_thread.start()
        
        print("🚀 High-Performance Engine started")
        print(f"   Processing thread: Active")
        print(f"   Monitoring thread: Active")
        print(f"   Cache cleanup: Active")
    
    def stop_engine(self):
        """Stop the high-performance engine"""
        if not self.is_running:
            return
        
        print("🛑 Stopping High-Performance Engine...")
        
        self.is_running = False
        self.shutdown_event.set()
        
        # Wait for threads to finish
        if hasattr(self, 'processing_thread'):
            self.processing_thread.join(timeout=5)
        if hasattr(self, 'monitoring_thread'):
            self.monitoring_thread.join(timeout=5)
        if hasattr(self, 'cache_cleanup_thread'):
            self.cache_cleanup_thread.join(timeout=5)
        
        # Shutdown worker pool
        self.worker_pool.shutdown(wait=True)
        
        print("✅ High-Performance Engine stopped")
    
    async def submit_task(self, task: ProcessingTask) -> str:
        """Submit a task for processing"""
        try:
            # Check cache first if enabled
            if self.optimization_config['enable_caching']:
                cache_key = self._generate_cache_key(task)
                cached_result = self._get_cached_result(cache_key)
                if cached_result:
                    print(f"💾 Cache hit for {task.symbol} {task.task_type}")
                    return cached_result
            
            # Add to queue with priority
            priority_item = (task.priority, time.time(), task)
            self.task_queue.put(priority_item, timeout=1.0)
            
            print(f"📥 Task queued: {task.task_id} ({task.task_type}) for {task.symbol}")
            return task.task_id
            
        except queue.Full:
            print(f"❌ Task queue full, rejecting task {task.task_id}")
            return None
        except Exception as e:
            print(f"❌ Error submitting task {task.task_id}: {e}")
            return None
    
    async def get_result(self, task_id: str, timeout: float = 30.0) -> Optional[TaskResult]:
        """Get result for a specific task"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check result queue
                result = self.result_queue.get(timeout=0.1)
                if result.task_id == task_id:
                    return result
                else:
                    # Put back if not our result
                    self.result_queue.put(result)
            except queue.Empty:
                await asyncio.sleep(0.1)
        
        print(f"⏰ Timeout waiting for result {task_id}")
        return None
    
    async def process_trading_signal(self, symbol: str, market_context: Dict[str, Any] = None, 
                                   priority: int = 2) -> Optional[EnsembleDecision]:
        """High-performance trading signal generation"""
        task = ProcessingTask(
            task_id=f"signal_{symbol}_{int(time.time())}",
            task_type="signal_generation",
            symbol=symbol,
            priority=priority,
            data={"market_context": market_context or {}},
            created_at=datetime.now()
        )
        
        task_id = await self.submit_task(task)
        if not task_id:
            return None
        
        result = await self.get_result(task_id, timeout=45.0)
        if result and result.success:
            return result.result
        else:
            print(f"❌ Failed to get trading signal for {symbol}")
            return None
    
    def _processing_loop(self):
        """Main processing loop"""
        print("🔄 Processing loop started")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Process tasks in batches for efficiency
                batch = []
                batch_start = time.time()
                
                # Collect batch
                while len(batch) < self.batch_size and time.time() - batch_start < self.processing_interval:
                    try:
                        priority_item = self.task_queue.get(timeout=0.01)
                        batch.append(priority_item[2])  # Extract task from priority tuple
                    except queue.Empty:
                        break
                
                if batch:
                    self._process_batch(batch)
                else:
                    time.sleep(0.01)  # Small sleep if no tasks
                    
            except Exception as e:
                print(f"❌ Error in processing loop: {e}")
                time.sleep(0.1)
        
        print("🔄 Processing loop stopped")
    
    def _process_batch(self, batch: List[ProcessingTask]):
        """Process a batch of tasks"""
        if not batch:
            return
        
        print(f"⚡ Processing batch of {len(batch)} tasks")
        
        # Group tasks by type for optimization
        task_groups = defaultdict(list)
        for task in batch:
            task_groups[task.task_type].append(task)
        
        # Process each group
        for task_type, tasks in task_groups.items():
            if task_type == "signal_generation":
                self._process_signal_generation_batch(tasks)
            else:
                # Process other task types individually
                for task in tasks:
                    self._process_single_task(task)
    
    def _process_signal_generation_batch(self, tasks: List[ProcessingTask]):
        """Process signal generation tasks in parallel"""
        
        async def process_signals():
            # Create coroutines for parallel processing
            coroutines = []
            for task in tasks:
                coroutine = self._generate_signal_async(task)
                coroutines.append(coroutine)
            
            # Process all signals in parallel
            results = await asyncio.gather(*coroutines, return_exceptions=True)
            
            # Store results
            for task, result in zip(tasks, results):
                if isinstance(result, Exception):
                    task_result = TaskResult(
                        task_id=task.task_id,
                        success=False,
                        result=None,
                        processing_time=0.0,
                        completed_at=datetime.now(),
                        error_message=str(result)
                    )
                else:
                    task_result = result
                
                self.result_queue.put(task_result)
                self.task_history.append(task_result)
        
        # Run the async batch processing
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(process_signals())
            loop.close()
        except Exception as e:
            print(f"❌ Error in batch signal processing: {e}")
    
    async def _generate_signal_async(self, task: ProcessingTask) -> TaskResult:
        """Generate trading signal asynchronously"""
        start_time = time.time()
        
        try:
            # Get ensemble decision
            decision = await self.ensemble.get_ensemble_prediction(
                task.symbol, 
                task.data.get("market_context", {}),
                voting_method=self.ensemble.VotingMethod.ADAPTIVE
            )
            
            processing_time = time.time() - start_time
            
            # Cache result if enabled
            if self.optimization_config['enable_caching']:
                cache_key = self._generate_cache_key(task)
                self._cache_result(cache_key, decision)
            
            return TaskResult(
                task_id=task.task_id,
                success=True,
                result=decision,
                processing_time=processing_time,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return TaskResult(
                task_id=task.task_id,
                success=False,
                result=None,
                processing_time=processing_time,
                completed_at=datetime.now(),
                error_message=str(e)
            )
    
    def _process_single_task(self, task: ProcessingTask):
        """Process a single task"""
        start_time = time.time()
        
        try:
            # Placeholder for other task types
            result = f"Processed {task.task_type} for {task.symbol}"
            
            task_result = TaskResult(
                task_id=task.task_id,
                success=True,
                result=result,
                processing_time=time.time() - start_time,
                completed_at=datetime.now()
            )
            
        except Exception as e:
            task_result = TaskResult(
                task_id=task.task_id,
                success=False,
                result=None,
                processing_time=time.time() - start_time,
                completed_at=datetime.now(),
                error_message=str(e)
            )
        
        self.result_queue.put(task_result)
        self.task_history.append(task_result)
    
    def _monitoring_loop(self):
        """Performance monitoring loop"""
        print("📊 Monitoring loop started")
        
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Collect performance metrics
                metrics = self._collect_performance_metrics()
                self.performance_history.append(metrics)
                
                # Memory cleanup if needed
                if self.optimization_config['enable_memory_optimization']:
                    self.memory_manager.cleanup_memory()
                
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                print(f"❌ Error in monitoring loop: {e}")
                time.sleep(1)
        
        print("📊 Monitoring loop stopped")
    
    def _cache_cleanup_loop(self):
        """Cache cleanup loop"""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                current_time = time.time()
                expired_keys = []
                
                for key, (result, timestamp) in self.response_cache.items():
                    if current_time - timestamp > self.cache_ttl:
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.response_cache[key]
                
                if expired_keys:
                    print(f"🧹 Cleaned {len(expired_keys)} expired cache entries")
                
                time.sleep(self.cache_cleanup_interval)
                
            except Exception as e:
                print(f"❌ Error in cache cleanup: {e}")
                time.sleep(10)
    
    def _collect_performance_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics"""
        # System metrics
        cpu_usage = psutil.cpu_percent()
        memory_info = psutil.virtual_memory()
        memory_usage = memory_info.percent
        
        # Engine metrics
        active_threads = threading.active_count()
        queue_size = self.task_queue.qsize()
        
        # Calculate processing rate
        recent_tasks = [t for t in self.task_history if 
                       (datetime.now() - t.completed_at).total_seconds() < 60]
        processing_rate = len(recent_tasks) / 60.0 if recent_tasks else 0
        
        # Calculate average latency
        if recent_tasks:
            avg_latency = np.mean([t.processing_time for t in recent_tasks]) * 1000  # ms
        else:
            avg_latency = 0
        
        return PerformanceMetrics(
            timestamp=datetime.now(),
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            active_threads=active_threads,
            queue_size=queue_size,
            processing_rate=processing_rate,
            latency_ms=avg_latency,
            throughput_ops_sec=processing_rate
        )
    
    def _generate_cache_key(self, task: ProcessingTask) -> str:
        """Generate cache key for task"""
        return f"{task.task_type}_{task.symbol}_{hash(str(task.data))}"
    
    def _get_cached_result(self, cache_key: str) -> Any:
        """Get cached result if available and not expired"""
        if cache_key in self.response_cache:
            result, timestamp = self.response_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return result
            else:
                del self.response_cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: Any):
        """Cache a result"""
        self.response_cache[cache_key] = (result, time.time())
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        if not self.performance_history:
            return {"error": "No performance data available"}
        
        recent_metrics = list(self.performance_history)[-10:]  # Last 10 measurements
        
        return {
            'timestamp': datetime.now().isoformat(),
            'engine_status': 'running' if self.is_running else 'stopped',
            'current_metrics': asdict(recent_metrics[-1]) if recent_metrics else None,
            'averages': {
                'cpu_usage': np.mean([m.cpu_usage for m in recent_metrics]),
                'memory_usage': np.mean([m.memory_usage for m in recent_metrics]),
                'processing_rate': np.mean([m.processing_rate for m in recent_metrics]),
                'latency_ms': np.mean([m.latency_ms for m in recent_metrics]),
                'throughput_ops_sec': np.mean([m.throughput_ops_sec for m in recent_metrics])
            },
            'task_statistics': {
                'total_processed': len(self.task_history),
                'success_rate': len([t for t in self.task_history if t.success]) / len(self.task_history) if self.task_history else 0,
                'average_processing_time': np.mean([t.processing_time for t in self.task_history]) if self.task_history else 0
            },
            'cache_statistics': {
                'cache_size': len(self.response_cache),
                'cache_enabled': self.optimization_config['enable_caching']
            },
            'configuration': self.optimization_config
        }

async def main():
    """Test the high-performance engine"""
    print("HIGH-PERFORMANCE TRADING ENGINE - PHASE 3B")
    print("=" * 60)
    
    # Initialize engine
    engine = HighPerformanceEngine(max_workers=4)
    engine.start_engine()
    
    try:
        # Test high-performance signal generation
        symbols = ["BTC", "ETH", "TSLA", "AAPL"]
        
        print(f"\n⚡ Testing high-performance signal generation...")
        start_time = time.time()
        
        # Submit multiple tasks in parallel
        tasks = []
        for symbol in symbols:
            market_context = {
                "trend": "bullish",
                "volatility": "medium",
                "volume": "high"
            }
            
            task_coroutine = engine.process_trading_signal(symbol, market_context, priority=1)
            tasks.append(task_coroutine)
        
        # Process all signals in parallel
        results = await asyncio.gather(*tasks)
        
        total_time = time.time() - start_time
        
        print(f"\n📊 PERFORMANCE RESULTS:")
        print(f"   Symbols processed: {len(symbols)}")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Average per symbol: {total_time/len(symbols):.2f}s")
        print(f"   Throughput: {len(symbols)/total_time:.1f} signals/sec")
        
        # Show results
        for symbol, result in zip(symbols, results):
            if result:
                print(f"   {symbol}: {result.final_action} (conf: {result.ensemble_confidence:.2f})")
            else:
                print(f"   {symbol}: Failed")
        
        # Performance report
        await asyncio.sleep(2)  # Let monitoring collect data
        report = engine.get_performance_report()
        
        print(f"\n🔧 ENGINE PERFORMANCE:")
        if 'current_metrics' in report and report['current_metrics']:
            metrics = report['current_metrics']
            print(f"   CPU Usage: {metrics['cpu_usage']:.1f}%")
            print(f"   Memory Usage: {metrics['memory_usage']:.1f}%")
            print(f"   Processing Rate: {metrics['processing_rate']:.1f} ops/sec")
            print(f"   Latency: {metrics['latency_ms']:.1f}ms")
        
        if 'task_statistics' in report:
            stats = report['task_statistics']
            print(f"   Total Processed: {stats['total_processed']}")
            print(f"   Success Rate: {stats['success_rate']:.1%}")
            print(f"   Avg Processing Time: {stats['average_processing_time']:.2f}s")
        
    finally:
        engine.stop_engine()
    
    print(f"\n✅ High-Performance Engine test complete!")

if __name__ == "__main__":
    asyncio.run(main())
