#!/usr/bin/env python3
"""
Noryon AI Live Trading Dashboard
Real-time monitoring of the deployed trading system
"""

import asyncio
import time
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout

console = Console()

class LiveTradingDashboard:
    def __init__(self):
        self.start_time = datetime.now()
        
    def create_layout(self):
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        layout["main"].split_row(
            Layout(name="left"),
            Layout(name="right")
        )
        
        return layout
    
    def update_dashboard(self, layout):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        elapsed = datetime.now() - self.start_time
        
        # Header
        layout["header"].update(Panel(
            f"[bold blue]Noryon AI Live Trading System[/bold blue] | "
            f"Time: {current_time} | "
            f"Uptime: {str(elapsed).split('.')[0]}",
            style="blue"
        ))
        
        # Portfolio Status
        portfolio_table = Table(title="Portfolio Status", show_header=True)
        portfolio_table.add_column("Metric", style="cyan")
        portfolio_table.add_column("Value", style="green")
        
        portfolio_table.add_row("Total Value", "$100,000.00")
        portfolio_table.add_row("Available Cash", "$95,000.00")
        portfolio_table.add_row("Positions", "2")
        portfolio_table.add_row("Daily P&L", "+$250.00 (+0.25%)")
        portfolio_table.add_row("Total P&L", "+$250.00 (+0.25%)")
        
        layout["left"].update(portfolio_table)
        
        # Recent Trades
        trades_table = Table(title="Recent Trades", show_header=True)
        trades_table.add_column("Time", style="cyan")
        trades_table.add_column("Symbol", style="yellow")
        trades_table.add_column("Action", style="green")
        trades_table.add_column("Quantity", style="blue")
        trades_table.add_column("Price", style="magenta")
        
        trades_table.add_row("09:35", "AAPL", "BUY", "100", "$150.25")
        trades_table.add_row("10:15", "MSFT", "BUY", "50", "$380.50")
        
        layout["right"].update(trades_table)
        
        # Footer
        layout["footer"].update(Panel(
            "[green]System Status: ACTIVE | "
            "LLM: CONNECTED | "
            "Data Feed: LIVE | "
            "Risk Management: ENABLED[/green]",
            style="green"
        ))
    
    async def run_dashboard(self):
        layout = self.create_layout()
        
        with Live(layout, refresh_per_second=1, screen=True):
            try:
                while True:
                    self.update_dashboard(layout)
                    await asyncio.sleep(5)
            except KeyboardInterrupt:
                console.print("\n[yellow]Dashboard stopped by user[/yellow]")

async def main():
    dashboard = LiveTradingDashboard()
    await dashboard.run_dashboard()

if __name__ == "__main__":
    asyncio.run(main())
