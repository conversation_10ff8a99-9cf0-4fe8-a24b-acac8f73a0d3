#!/usr/bin/env python3
"""
AI Agent Orchestration Service
Microservice for managing all 16 AI agents and Fathom R1 direct interface
"""

import asyncio
import json
import time
import logging
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    pika = None

from circuit_breaker import CircuitBreaker
from fathom_r1_direct_interface import FathomR1DirectInterface
from expanded_ai_team_system import ExpandedAITeamSystem

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_orchestration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class AgentQuery:
    """Data class for agent queries"""
    query_id: str
    agent_name: str
    query_text: str
    symbol: str
    priority: int = 1
    timeout: int = 30
    timestamp: datetime = None

@dataclass
class AgentResponse:
    """Data class for agent responses"""
    query_id: str
    agent_name: str
    success: bool
    response_text: str = ""
    response_time: float = 0.0
    confidence: float = 0.0
    decision: str = ""
    error: str = ""
    timestamp: datetime = None

class AIAgentOrchestrationService:
    """REAL microservice for AI agent orchestration"""
    
    def __init__(self, rabbitmq_url: str = "amqp://localhost"):
        self.service_name = "ai-agent-orchestration"
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        
        # Initialize AI systems
        self.fathom_interface = FathomR1DirectInterface()
        self.expanded_team = ExpandedAITeamSystem()
        
        # Circuit breakers for each agent
        self.circuit_breakers = {}
        for agent_name in self.expanded_team.ai_team.keys():
            self.circuit_breakers[agent_name] = CircuitBreaker(
                failure_threshold=3,
                recovery_timeout=30,
                expected_exception=Exception
            )
        
        # Performance tracking
        self.agent_performance = {}
        self._initialize_performance_tracking()
        
        # Setup database
        self._setup_database()
        
        logger.info(f"AI Agent Orchestration Service initialized", extra={
            "service": self.service_name,
            "total_agents": len(self.expanded_team.ai_team),
            "fathom_r1_active": True,
            "circuit_breakers": len(self.circuit_breakers)
        })
    
    def _setup_database(self):
        """Setup microservice database"""
        conn = sqlite3.connect('ai_orchestration_service.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_queries (
                id INTEGER PRIMARY KEY,
                query_id TEXT,
                agent_name TEXT,
                query_text TEXT,
                symbol TEXT,
                priority INTEGER,
                status TEXT,
                response_time REAL,
                confidence REAL,
                decision TEXT,
                error TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_performance (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                total_queries INTEGER,
                successful_queries INTEGER,
                avg_response_time REAL,
                avg_confidence REAL,
                success_rate REAL,
                last_updated DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS service_metrics (
                id INTEGER PRIMARY KEY,
                metric_name TEXT,
                metric_value REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("AI Orchestration Service database initialized")
    
    def _initialize_performance_tracking(self):
        """Initialize performance tracking for all agents"""
        for agent_name in self.expanded_team.ai_team.keys():
            self.agent_performance[agent_name] = {
                'total_queries': 0,
                'successful_queries': 0,
                'total_response_time': 0.0,
                'total_confidence': 0.0,
                'success_rate': 0.0,
                'avg_response_time': 0.0,
                'avg_confidence': 0.0
            }
    
    async def setup_message_queue(self):
        """Setup RabbitMQ connection and queues"""
        if not RABBITMQ_AVAILABLE:
            logger.info("RabbitMQ not available, using direct processing mode", extra={
                "service": self.service_name,
                "mode": "direct"
            })
            self.connection = None
            self.channel = None
            return

        try:
            # Connect to RabbitMQ
            self.connection = pika.BlockingConnection(pika.URLParameters(self.rabbitmq_url))
            self.channel = self.connection.channel()

            # Declare queues
            self.channel.queue_declare(queue='ai_agent_queries', durable=True)
            self.channel.queue_declare(queue='ai_agent_responses', durable=True)
            self.channel.queue_declare(queue='fathom_r1_queries', durable=True)

            # Setup exchange
            self.channel.exchange_declare(exchange='ai_orchestration', exchange_type='topic')

            logger.info("Message queue setup completed", extra={
                "service": self.service_name,
                "queues": ["ai_agent_queries", "ai_agent_responses", "fathom_r1_queries"]
            })

        except Exception as e:
            logger.error(f"Failed to setup message queue: {e}", extra={
                "service": self.service_name,
                "error": str(e)
            })
            # Fallback to direct processing without queue
            self.connection = None
            self.channel = None
    
    async def query_agent_async(self, query: AgentQuery) -> AgentResponse:
        """Query AI agent asynchronously with circuit breaker"""
        
        start_time = time.time()
        
        try:
            # Check circuit breaker
            circuit_breaker = self.circuit_breakers.get(query.agent_name)
            if circuit_breaker and circuit_breaker.state == 'open':
                logger.warn(f"Circuit breaker open for {query.agent_name}", extra={
                    "service": self.service_name,
                    "agent": query.agent_name,
                    "query_id": query.query_id
                })
                return AgentResponse(
                    query_id=query.query_id,
                    agent_name=query.agent_name,
                    success=False,
                    error="Circuit breaker open",
                    timestamp=datetime.now()
                )
            
            # Execute query with timeout
            if query.agent_name == 'fathom_r1':
                result = await self._query_fathom_r1(query)
            else:
                result = await self._query_standard_agent(query)
            
            response_time = time.time() - start_time
            
            # Update performance tracking
            self._update_agent_performance(query.agent_name, result.success, response_time, result.confidence)
            
            # Store query result
            self._store_query_result(query, result, response_time)
            
            # Log result
            if result.success:
                logger.info(f"Agent query successful", extra={
                    "service": self.service_name,
                    "agent": query.agent_name,
                    "query_id": query.query_id,
                    "response_time": response_time,
                    "confidence": result.confidence
                })
                
                # Reset circuit breaker on success
                if circuit_breaker:
                    circuit_breaker.call(lambda: True)
            else:
                logger.warn(f"Agent query failed", extra={
                    "service": self.service_name,
                    "agent": query.agent_name,
                    "query_id": query.query_id,
                    "error": result.error,
                    "response_time": response_time
                })
                
                # Trigger circuit breaker on failure
                if circuit_breaker:
                    try:
                        circuit_breaker.call(lambda: False if result.error else True)
                    except:
                        pass  # Circuit breaker opened
            
            return result
            
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            logger.error(f"Agent query timeout", extra={
                "service": self.service_name,
                "agent": query.agent_name,
                "query_id": query.query_id,
                "timeout": query.timeout
            })
            
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=False,
                error=f"Timeout after {query.timeout}s",
                response_time=response_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            logger.error(f"Agent query exception", extra={
                "service": self.service_name,
                "agent": query.agent_name,
                "query_id": query.query_id,
                "error": str(e)
            })
            
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=False,
                error=str(e),
                response_time=response_time,
                timestamp=datetime.now()
            )
    
    async def _query_fathom_r1(self, query: AgentQuery) -> AgentResponse:
        """Query Fathom R1 directly"""
        
        result = self.fathom_interface.chat_with_fathom(query.query_text)
        
        if result.get('success'):
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=True,
                response_text=result['fathom_response'],
                response_time=result['response_time'],
                confidence=8.0,  # Default high confidence for Fathom R1
                timestamp=datetime.now()
            )
        else:
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=False,
                error=result.get('error', 'Unknown error'),
                response_time=result.get('response_time', 0),
                timestamp=datetime.now()
            )
    
    async def _query_standard_agent(self, query: AgentQuery) -> AgentResponse:
        """Query standard AI agent"""
        
        result = self.expanded_team.query_specialist_by_domain(
            self.expanded_team.ai_team[query.agent_name]['specialization'],
            query.query_text,
            query.symbol
        )
        
        if result.get('success'):
            decision = result.get('decision', {})
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=True,
                response_text=result['response'],
                response_time=result['response_time'],
                confidence=decision.get('confidence', 5.0),
                decision=decision.get('action', 'HOLD'),
                timestamp=datetime.now()
            )
        else:
            return AgentResponse(
                query_id=query.query_id,
                agent_name=query.agent_name,
                success=False,
                error=result.get('error', 'Unknown error'),
                response_time=result.get('response_time', 0),
                timestamp=datetime.now()
            )
    
    def _update_agent_performance(self, agent_name: str, success: bool, response_time: float, confidence: float):
        """Update agent performance metrics"""
        
        perf = self.agent_performance[agent_name]
        perf['total_queries'] += 1
        
        if success:
            perf['successful_queries'] += 1
            perf['total_response_time'] += response_time
            perf['total_confidence'] += confidence
        
        # Calculate averages
        perf['success_rate'] = perf['successful_queries'] / perf['total_queries']
        if perf['successful_queries'] > 0:
            perf['avg_response_time'] = perf['total_response_time'] / perf['successful_queries']
            perf['avg_confidence'] = perf['total_confidence'] / perf['successful_queries']
    
    def _store_query_result(self, query: AgentQuery, result: AgentResponse, response_time: float):
        """Store query result in database"""
        
        try:
            conn = sqlite3.connect('ai_orchestration_service.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO agent_queries 
                (query_id, agent_name, query_text, symbol, priority, status, 
                 response_time, confidence, decision, error, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                query.query_id, query.agent_name, query.query_text, query.symbol,
                query.priority, 'SUCCESS' if result.success else 'FAILED',
                response_time, result.confidence, result.decision, result.error,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to store query result: {e}", extra={
                "service": self.service_name,
                "query_id": query.query_id
            })
    
    async def process_message_queue(self):
        """Process messages from RabbitMQ queue"""
        
        if not self.channel:
            logger.warn("No message queue connection, skipping queue processing")
            return
        
        def callback(ch, method, properties, body):
            try:
                message = json.loads(body)
                query = AgentQuery(
                    query_id=message['query_id'],
                    agent_name=message['agent_name'],
                    query_text=message['query_text'],
                    symbol=message['symbol'],
                    priority=message.get('priority', 1),
                    timeout=message.get('timeout', 30)
                )
                
                # Process query asynchronously
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.query_agent_async(query))
                
                # Send response
                response_message = {
                    'query_id': result.query_id,
                    'agent_name': result.agent_name,
                    'success': result.success,
                    'response_text': result.response_text,
                    'confidence': result.confidence,
                    'decision': result.decision,
                    'error': result.error,
                    'timestamp': result.timestamp.isoformat()
                }
                
                ch.basic_publish(
                    exchange='ai_orchestration',
                    routing_key='agent.response',
                    body=json.dumps(response_message)
                )
                
                ch.basic_ack(delivery_tag=method.delivery_tag)
                
            except Exception as e:
                logger.error(f"Failed to process message: {e}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
        
        self.channel.basic_consume(queue='ai_agent_queries', on_message_callback=callback)
        self.channel.start_consuming()
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status"""
        
        total_agents = len(self.expanded_team.ai_team)
        healthy_agents = sum(1 for cb in self.circuit_breakers.values() if cb.state != 'open')
        
        return {
            'service': self.service_name,
            'status': 'healthy' if healthy_agents == total_agents else 'degraded',
            'total_agents': total_agents,
            'healthy_agents': healthy_agents,
            'fathom_r1_active': True,
            'message_queue_connected': self.connection is not None,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_agent_performance_summary(self) -> Dict[str, Any]:
        """Get agent performance summary"""
        
        return {
            'service': self.service_name,
            'agent_performance': self.agent_performance,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main service entry point"""
    
    logger.info("Starting AI Agent Orchestration Service")
    
    # Initialize service
    service = AIAgentOrchestrationService()
    
    # Setup message queue
    await service.setup_message_queue()
    
    # Test service with sample query
    test_query = AgentQuery(
        query_id="test_001",
        agent_name="fathom_r1",
        query_text="Test query: Respond with service status",
        symbol="BTC-USD"
    )
    
    result = await service.query_agent_async(test_query)
    
    logger.info(f"Service test completed", extra={
        "success": result.success,
        "response_time": result.response_time
    })
    
    # Get service health
    health = service.get_service_health()
    logger.info(f"Service health: {health}")
    
    # Get performance summary
    performance = service.get_agent_performance_summary()
    logger.info(f"Agent performance summary available")

if __name__ == "__main__":
    asyncio.run(main())
