
# Model timeout fix
import signal
import subprocess

class TimeoutHandler:
    def __init__(self, timeout):
        self.timeout = timeout
    
    def __enter__(self):
        signal.signal(signal.SIGALRM, self._timeout_handler)
        signal.alarm(self.timeout)
        return self
    
    def __exit__(self, type, value, traceback):
        signal.alarm(0)
    
    def _timeout_handler(self, signum, frame):
        raise TimeoutError("Model response timeout")

def run_model_with_timeout(model_name, prompt, timeout=30):
    try:
        with TimeoutHandler(timeout):
            result = subprocess.run(
                ['ollama', 'run', model_name, prompt],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace'
            )
            return result.stdout
    except TimeoutError:
        return "Model response timeout - try again"
    except Exception as e:
        return f"Error: {e}"
