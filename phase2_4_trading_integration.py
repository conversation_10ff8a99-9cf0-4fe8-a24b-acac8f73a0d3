#!/usr/bin/env python3
"""
Phase 2.4: Automated Trading Integration
Integrate enhanced AI models with trading systems
"""

import subprocess
import json
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class SignalStrength(Enum):
    STRONG_BUY = "STRONG_BUY"
    BUY = "BUY"
    HOLD = "HOLD"
    SELL = "SELL"
    STRONG_SELL = "STRONG_SELL"

@dataclass
class TradingSignal:
    symbol: str
    signal: SignalStrength
    confidence: float
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]
    reasoning: str
    model_used: str
    timestamp: datetime

class AITradingIntegration:
    """Integrate enhanced AI models with trading systems"""
    
    def __init__(self):
        # Your best trading models from Phase 2
        self.trading_models = {
            'primary': 'phase2-unrestricted-noryon-qwen3-finance-v2-latest',
            'fast': 'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest',
            'analytical': 'phase2-smart-unrestricted-qwen3-14b-latest',
            'ultimate': 'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest'
        }
        
        self.trading_prompts = {
            'market_analysis': """Analyze the current market conditions for {symbol} and provide:
1. Technical analysis (support/resistance, trends, indicators)
2. Fundamental analysis (news, events, sentiment)
3. Trading recommendation (BUY/SELL/HOLD)
4. Entry price, target price, and stop loss
5. Risk assessment and position sizing
6. Confidence level (1-10)

Be specific with numbers and reasoning.""",
            
            'quick_signal': """Quick trading signal for {symbol}:
- Current action: BUY/SELL/HOLD
- Entry price: $X.XX
- Target: $X.XX
- Stop loss: $X.XX
- Confidence: X/10
- Reason: Brief explanation

Be concise and specific.""",
            
            'risk_assessment': """Assess the risk for trading {symbol}:
1. Market volatility analysis
2. Position sizing recommendation
3. Risk/reward ratio
4. Maximum acceptable loss
5. Portfolio impact assessment
6. Risk mitigation strategies

Provide specific percentages and dollar amounts."""
        }
        
        self.signal_history = []
        self.performance_tracking = {}
    
    def step1_setup_trading_models(self):
        """Step 1: Setup and validate trading models"""
        print("STEP 1: Setting up AI trading models...")
        print("=" * 50)
        
        validated_models = {}
        
        for role, model_name in self.trading_models.items():
            print(f"Validating {role} model: {model_name}")
            
            # Test model with simple query
            test_query = "What is the current market sentiment?"
            
            try:
                result = subprocess.run([
                    'ollama', 'run', model_name, test_query
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    if len(response) > 50:  # Valid response
                        validated_models[role] = model_name
                        print(f"  ✅ {role} model validated")
                    else:
                        print(f"  ❌ {role} model response too short")
                else:
                    print(f"  ❌ {role} model failed to respond")
                    
            except Exception as e:
                print(f"  ❌ {role} model error: {e}")
        
        self.trading_models = validated_models
        print(f"\n✅ {len(validated_models)} trading models ready")
        return validated_models
    
    def step2_generate_trading_signals(self, symbols: List[str]):
        """Step 2: Generate trading signals for multiple symbols"""
        print(f"\nSTEP 2: Generating trading signals for {len(symbols)} symbols...")
        print("=" * 50)
        
        signals = []
        
        for symbol in symbols:
            print(f"\nAnalyzing {symbol}...")
            
            # Use primary model for detailed analysis
            signal = self._generate_signal_for_symbol(symbol, 'primary')
            if signal:
                signals.append(signal)
                print(f"  ✅ {signal.signal.value} - Confidence: {signal.confidence:.2f}")
            else:
                print(f"  ❌ Failed to generate signal")
        
        self.signal_history.extend(signals)
        return signals
    
    def _generate_signal_for_symbol(self, symbol: str, model_type: str = 'primary') -> Optional[TradingSignal]:
        """Generate trading signal for a specific symbol"""
        if model_type not in self.trading_models:
            return None
        
        model_name = self.trading_models[model_type]
        prompt = self.trading_prompts['market_analysis'].format(symbol=symbol)
        
        try:
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'run', model_name, prompt
            ], capture_output=True, text=True, timeout=60)
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                signal = self._parse_trading_response(symbol, response, model_name)
                return signal
            else:
                return None
                
        except Exception as e:
            print(f"    Error generating signal: {e}")
            return None
    
    def _parse_trading_response(self, symbol: str, response: str, model_name: str) -> Optional[TradingSignal]:
        """Parse AI response into structured trading signal"""
        try:
            # Simple parsing logic (would be more sophisticated in practice)
            response_lower = response.lower()
            
            # Determine signal strength
            if 'strong buy' in response_lower or 'strongly recommend buy' in response_lower:
                signal = SignalStrength.STRONG_BUY
            elif 'buy' in response_lower and 'don\'t buy' not in response_lower:
                signal = SignalStrength.BUY
            elif 'strong sell' in response_lower or 'strongly recommend sell' in response_lower:
                signal = SignalStrength.STRONG_SELL
            elif 'sell' in response_lower and 'don\'t sell' not in response_lower:
                signal = SignalStrength.SELL
            else:
                signal = SignalStrength.HOLD
            
            # Extract confidence (look for patterns like "confidence: 8/10" or "8 out of 10")
            confidence = 0.7  # Default
            import re
            conf_patterns = [
                r'confidence[:\s]+(\d+)[/\s]*10',
                r'(\d+)[/\s]*10\s*confidence',
                r'confidence[:\s]+(\d+)%'
            ]
            
            for pattern in conf_patterns:
                match = re.search(pattern, response_lower)
                if match:
                    conf_value = int(match.group(1))
                    confidence = conf_value / 10 if conf_value <= 10 else conf_value / 100
                    break
            
            # Extract prices (simplified)
            entry_price = None
            target_price = None
            stop_loss = None
            
            # Look for price patterns
            price_patterns = [
                r'\$(\d+\.?\d*)',
                r'(\d+\.?\d*)\s*dollars?',
                r'price[:\s]+(\d+\.?\d*)'
            ]
            
            prices = []
            for pattern in price_patterns:
                matches = re.findall(pattern, response)
                prices.extend([float(m) for m in matches if m])
            
            if len(prices) >= 3:
                entry_price = prices[0]
                target_price = prices[1]
                stop_loss = prices[2]
            elif len(prices) >= 1:
                entry_price = prices[0]
            
            return TradingSignal(
                symbol=symbol,
                signal=signal,
                confidence=confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                reasoning=response[:500] + "..." if len(response) > 500 else response,
                model_used=model_name,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            print(f"    Error parsing response: {e}")
            return None
    
    def step3_create_trading_dashboard(self, signals: List[TradingSignal]):
        """Step 3: Create trading dashboard with signals"""
        print(f"\nSTEP 3: Creating trading dashboard...")
        print("=" * 50)
        
        # Create dashboard data
        dashboard_data = {
            'timestamp': datetime.now().isoformat(),
            'total_signals': len(signals),
            'signal_breakdown': {},
            'high_confidence_signals': [],
            'trading_opportunities': [],
            'risk_assessment': {}
        }
        
        # Analyze signals
        signal_counts = {}
        high_conf_signals = []
        
        for signal in signals:
            # Count signal types
            signal_type = signal.signal.value
            signal_counts[signal_type] = signal_counts.get(signal_type, 0) + 1
            
            # High confidence signals
            if signal.confidence >= 0.8:
                high_conf_signals.append({
                    'symbol': signal.symbol,
                    'signal': signal_type,
                    'confidence': signal.confidence,
                    'entry_price': signal.entry_price,
                    'target_price': signal.target_price,
                    'stop_loss': signal.stop_loss
                })
        
        dashboard_data['signal_breakdown'] = signal_counts
        dashboard_data['high_confidence_signals'] = high_conf_signals
        
        # Display dashboard
        self._display_trading_dashboard(dashboard_data)
        
        # Save dashboard
        dashboard_file = f"trading_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(dashboard_file, 'w') as f:
            json.dump(dashboard_data, f, indent=2, default=str)
        
        print(f"\n✅ Dashboard saved to {dashboard_file}")
        return dashboard_data
    
    def _display_trading_dashboard(self, dashboard_data: Dict):
        """Display trading dashboard in console"""
        print("\n" + "=" * 60)
        print("🚀 AI TRADING DASHBOARD")
        print("=" * 60)
        
        print(f"📊 SIGNAL SUMMARY:")
        print(f"   Total Signals: {dashboard_data['total_signals']}")
        
        for signal_type, count in dashboard_data['signal_breakdown'].items():
            print(f"   {signal_type}: {count}")
        
        print(f"\n🎯 HIGH CONFIDENCE OPPORTUNITIES:")
        high_conf = dashboard_data['high_confidence_signals']
        
        if high_conf:
            for signal in high_conf[:5]:  # Show top 5
                print(f"   {signal['symbol']}: {signal['signal']} "
                      f"(Confidence: {signal['confidence']:.2f})")
                if signal['entry_price']:
                    print(f"      Entry: ${signal['entry_price']:.2f}")
                if signal['target_price']:
                    print(f"      Target: ${signal['target_price']:.2f}")
                if signal['stop_loss']:
                    print(f"      Stop: ${signal['stop_loss']:.2f}")
                print()
        else:
            print("   No high confidence signals found")
        
        print("=" * 60)
    
    def step4_create_automated_trading_script(self):
        """Step 4: Create automated trading script template"""
        print(f"\nSTEP 4: Creating automated trading script...")
        print("=" * 50)
        
        trading_script = '''#!/usr/bin/env python3
"""
Automated AI Trading Script
Generated by Phase 2.4 Trading Integration
"""

import time
import json
from datetime import datetime
from phase2_4_trading_integration import AITradingIntegration

class AutomatedTrader:
    def __init__(self):
        self.ai_integration = AITradingIntegration()
        self.trading_active = False
        
    def start_automated_trading(self, symbols, interval_minutes=15):
        """Start automated trading loop"""
        print(f"🚀 Starting automated trading for {symbols}")
        print(f"📊 Analysis interval: {interval_minutes} minutes")
        
        self.trading_active = True
        
        while self.trading_active:
            try:
                # Generate fresh signals
                signals = self.ai_integration.step2_generate_trading_signals(symbols)
                
                # Process high confidence signals
                for signal in signals:
                    if signal.confidence >= 0.8:
                        self.execute_trade_signal(signal)
                
                # Wait for next interval
                print(f"⏰ Waiting {interval_minutes} minutes for next analysis...")
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                print("🛑 Trading stopped by user")
                self.trading_active = False
            except Exception as e:
                print(f"❌ Trading error: {e}")
                time.sleep(60)  # Wait 1 minute before retry
    
    def execute_trade_signal(self, signal):
        """Execute a trading signal (SIMULATION ONLY)"""
        print(f"📈 TRADE SIGNAL: {signal.symbol} - {signal.signal.value}")
        print(f"   Confidence: {signal.confidence:.2f}")
        print(f"   Entry: ${signal.entry_price}")
        print(f"   Target: ${signal.target_price}")
        print(f"   Stop: ${signal.stop_loss}")
        print(f"   Reasoning: {signal.reasoning[:100]}...")
        
        # HERE: Add your actual trading API integration
        # Example: place_order(signal.symbol, signal.signal, signal.entry_price)
        
        print("   ✅ Trade executed (SIMULATION)")

if __name__ == "__main__":
    trader = AutomatedTrader()
    
    # Define your trading symbols
    symbols = ["BTC", "ETH", "TSLA", "AAPL", "NVDA"]
    
    # Start automated trading
    trader.start_automated_trading(symbols, interval_minutes=15)
'''
        
        # Save automated trading script
        with open('automated_ai_trader.py', 'w') as f:
            f.write(trading_script)
        
        print("✅ Created automated_ai_trader.py")
        print("   - Ready for integration with trading APIs")
        print("   - Includes safety features and error handling")
        print("   - Configurable trading intervals and symbols")
        
        return True
    
    def run_phase2_4_integration(self):
        """Run complete Phase 2.4 trading integration"""
        print("PHASE 2.4: AUTOMATED TRADING INTEGRATION")
        print("=" * 70)
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Execute all steps
        validated_models = self.step1_setup_trading_models()
        
        if not validated_models:
            print("❌ No trading models available")
            return False
        
        # Test with sample symbols
        test_symbols = ["BTC", "ETH", "TSLA"]
        signals = self.step2_generate_trading_signals(test_symbols)
        
        if signals:
            dashboard = self.step3_create_trading_dashboard(signals)
            self.step4_create_automated_trading_script()
        
        # Final summary
        print("\n" + "=" * 70)
        print("PHASE 2.4 COMPLETE!")
        print(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print(f"✅ Validated {len(validated_models)} trading models")
        print(f"✅ Generated {len(signals)} trading signals")
        print(f"✅ Created trading dashboard")
        print(f"✅ Created automated trading script")
        print()
        print("🚀 AI TRADING SYSTEM READY!")
        print("   - Enhanced models integrated")
        print("   - Automated signal generation")
        print("   - Trading dashboard created")
        print("   - Ready for API integration")
        
        return True

def main():
    """Test the trading integration system"""
    integration = AITradingIntegration()
    success = integration.run_phase2_4_integration()
    
    if success:
        print(f"\n🎉 TRADING INTEGRATION SUCCESSFUL!")
        print(f"Run 'python automated_ai_trader.py' to start automated trading!")
    else:
        print(f"\n❌ TRADING INTEGRATION FAILED")

if __name__ == "__main__":
    main()
