#!/usr/bin/env python3
import requests
import json
import sys

def chat_with_ai():
    url = "http://localhost:8000/v1/chat/completions"
    
    print("🤖 Interactive Chat with Mock LLM")
    print("📝 Type 'quit' or 'exit' to stop")
    print("🔗 Server: http://localhost:8000")
    print("-" * 50)
    
    conversation_history = []
    
    while True:
        try:
            # Get user input
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not user_input:
                continue
            
            # Add user message to conversation
            conversation_history.append({"role": "user", "content": user_input})
            
            # Prepare API request
            payload = {
                "model": "qwen3-8b",
                "messages": conversation_history[-10:],  # Keep last 10 messages
                "max_tokens": 150,
                "temperature": 0.7
            }
            
            headers = {"Content-Type": "application/json"}
            
            # Make API request
            print("🤔 AI is thinking...")
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if 'choices' in data and len(data['choices']) > 0:
                    ai_message = data['choices'][0]['message']['content']
                    print(f"🤖 AI: {ai_message}")
                    
                    # Add AI response to conversation
                    conversation_history.append({"role": "assistant", "content": ai_message})
                else:
                    print("❌ No response from AI")
            else:
                print(f"❌ Error: {response.status_code} - {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("\n❌ Cannot connect to the AI server!")
            print("🔧 Make sure the mock server is running:")
            print("   python mock_llm_server.py --host localhost --port 8000")
            break
        except KeyboardInterrupt:
            print("\n👋 Chat interrupted. Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def check_server():
    """Check if the server is running"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Mock LLM Server is running!")
            return True
    except:
        pass
    
    print("❌ Mock LLM Server is not running!")
    print("🔧 Start it with: python mock_llm_server.py --host localhost --port 8000")
    return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        check_server()
    else:
        if check_server():
            chat_with_ai()