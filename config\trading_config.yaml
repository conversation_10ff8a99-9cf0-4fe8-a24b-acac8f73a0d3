# Noryon AI Trading Configuration
# Main trading system configuration

trading:
  mode: "paper"  # paper, live, simulation
  
  # Risk Management
  risk_management:
    max_position_size: 0.05  # 5% of portfolio
    max_daily_loss: 0.02     # 2% daily loss limit
    max_portfolio_risk: 0.15 # 15% total portfolio risk
    stop_loss_percentage: 0.03  # 3% stop loss
    take_profit_percentage: 0.06  # 6% take profit
    
  # Position Sizing
  position_sizing:
    method: "kelly"  # kelly, fixed, volatility_adjusted
    base_size: 0.02  # 2% base position size
    max_leverage: 1.0  # No leverage for now
    
  # Trading Hours
  trading_hours:
    market_open: "09:30"
    market_close: "16:00"
    timezone: "US/Eastern"
    pre_market: false
    after_hours: false
    
  # Symbols
  symbols:
    watchlist:
      - "AAPL"
      - "MSFT" 
      - "GOOGL"
      - "TSLA"
      - "NVDA"
      - "AMZN"
      - "META"
      - "NFLX"
    
    blacklist:
      - "PENNY_STOCKS"  # Avoid penny stocks
      
  # Order Management
  orders:
    default_type: "market"  # market, limit, stop
    timeout: 300  # 5 minutes
    retry_attempts: 3
    slippage_tolerance: 0.001  # 0.1%
    
  # Strategy Configuration
  strategies:
    enabled:
      - "momentum"
      - "mean_reversion"
      - "trend_following"
    
    momentum:
      lookback_period: 20
      threshold: 0.02
      
    mean_reversion:
      lookback_period: 50
      deviation_threshold: 2.0
      
    trend_following:
      short_ma: 10
      long_ma: 50
      
  # Data Requirements
  data:
    required_history: 252  # 1 year of trading days
    update_frequency: "1min"
    sources:
      - "yahoo_finance"
      - "alpha_vantage"
      
  # Notifications
  notifications:
    enabled: true
    channels:
      - "console"
      - "log"
    events:
      - "trade_executed"
      - "risk_limit_hit"
      - "system_error"
      
# Paper Trading Specific
paper_trading:
  initial_balance: 100000  # $100k
  commission: 0.001        # 0.1%
  slippage: 0.0005        # 0.05%
  realistic_fills: true
  
# Live Trading (when ready)
live_trading:
  enabled: false
  confirmation_required: true
  max_daily_trades: 50
  
# Logging
logging:
  level: "INFO"
  file: "logs/trading.log"
  max_size: "10MB"
  backup_count: 5
