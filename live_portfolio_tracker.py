#!/usr/bin/env python3
"""
Live Portfolio & Position Tracking System
Real-time portfolio monitoring, P&L calculation, and position management
"""

import sqlite3
import time
import json
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

@dataclass
class Position:
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    side: str  # 'long' or 'short'
    entry_time: datetime
    last_update: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

@dataclass
class PortfolioMetrics:
    total_value: float
    cash_balance: float
    invested_value: float
    total_pnl: float
    total_pnl_percent: float
    daily_pnl: float
    daily_pnl_percent: float
    positions_count: int
    largest_position_percent: float
    risk_exposure: float
    margin_used: float
    buying_power: float

class LivePortfolioTracker:
    """Real-time portfolio and position tracking"""
    
    def __init__(self, initial_capital: float = 100000.0):
        self.initial_capital = initial_capital
        self.current_cash = initial_capital
        self.positions = {}
        self.closed_positions = []
        self.portfolio_history = []
        
        # Real-time tracking
        self.tracking_active = False
        self.update_interval = 5  # seconds
        self.price_callbacks = {}
        
        # Setup database
        self._setup_database()
        
        # Load existing positions
        self._load_positions()
        
        print("📊 LIVE PORTFOLIO TRACKER INITIALIZED")
        print(f"   💰 Initial Capital: ${initial_capital:,.2f}")
        print(f"   📈 Positions: {len(self.positions)}")
        print(f"   💵 Cash: ${self.current_cash:,.2f}")
    
    def _setup_database(self):
        """Setup portfolio tracking database"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        # Positions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                quantity REAL,
                entry_price REAL,
                current_price REAL,
                side TEXT,
                entry_time DATETIME,
                stop_loss REAL,
                take_profit REAL,
                status TEXT DEFAULT 'open'
            )
        ''')
        
        # Portfolio history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS portfolio_history (
                id INTEGER PRIMARY KEY,
                timestamp DATETIME,
                total_value REAL,
                cash_balance REAL,
                total_pnl REAL,
                daily_pnl REAL,
                positions_count INTEGER
            )
        ''')
        
        # Trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                side TEXT,
                quantity REAL,
                price REAL,
                timestamp DATETIME,
                trade_type TEXT,
                pnl REAL DEFAULT 0
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_positions(self):
        """Load existing positions from database"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT symbol, quantity, entry_price, current_price, side, 
                   entry_time, stop_loss, take_profit
            FROM positions 
            WHERE status = 'open'
        ''')
        
        for row in cursor.fetchall():
            symbol, quantity, entry_price, current_price, side, entry_time, stop_loss, take_profit = row
            
            position = Position(
                symbol=symbol,
                quantity=quantity,
                entry_price=entry_price,
                current_price=current_price or entry_price,
                market_value=quantity * (current_price or entry_price),
                unrealized_pnl=0,
                unrealized_pnl_percent=0,
                side=side,
                entry_time=datetime.fromisoformat(entry_time),
                last_update=datetime.now(),
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            self.positions[symbol] = position
        
        conn.close()
        print(f"   📂 Loaded {len(self.positions)} existing positions")
    
    def add_position(self, symbol: str, quantity: float, entry_price: float, 
                    side: str = 'long', stop_loss: Optional[float] = None,
                    take_profit: Optional[float] = None) -> bool:
        """Add new position to portfolio"""
        
        # Calculate position value
        position_value = quantity * entry_price
        
        # Check if enough cash
        if position_value > self.current_cash:
            print(f"❌ Insufficient cash: Need ${position_value:,.2f}, have ${self.current_cash:,.2f}")
            return False
        
        # Create position
        position = Position(
            symbol=symbol,
            quantity=quantity,
            entry_price=entry_price,
            current_price=entry_price,
            market_value=position_value,
            unrealized_pnl=0,
            unrealized_pnl_percent=0,
            side=side,
            entry_time=datetime.now(),
            last_update=datetime.now(),
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Update portfolio
        if symbol in self.positions:
            # Average down/up existing position
            existing = self.positions[symbol]
            total_quantity = existing.quantity + quantity
            total_cost = (existing.quantity * existing.entry_price) + position_value
            new_avg_price = total_cost / total_quantity
            
            existing.quantity = total_quantity
            existing.entry_price = new_avg_price
            existing.market_value = total_quantity * existing.current_price
            existing.last_update = datetime.now()
        else:
            self.positions[symbol] = position
        
        # Update cash
        self.current_cash -= position_value
        
        # Save to database
        self._save_position(position)
        self._record_trade(symbol, side, quantity, entry_price, 'open')
        
        print(f"✅ Added position: {symbol} {quantity} @ ${entry_price:.2f}")
        print(f"   💵 Remaining cash: ${self.current_cash:,.2f}")
        
        return True
    
    def close_position(self, symbol: str, exit_price: Optional[float] = None) -> Optional[float]:
        """Close position and calculate P&L"""
        
        if symbol not in self.positions:
            print(f"❌ No position found for {symbol}")
            return None
        
        position = self.positions[symbol]
        
        # Use current price if no exit price provided
        if exit_price is None:
            exit_price = position.current_price
        
        # Calculate P&L
        if position.side == 'long':
            pnl = (exit_price - position.entry_price) * position.quantity
        else:  # short
            pnl = (position.entry_price - exit_price) * position.quantity
        
        pnl_percent = (pnl / (position.entry_price * position.quantity)) * 100
        
        # Update cash
        position_value = position.quantity * exit_price
        self.current_cash += position_value
        
        # Record closed position
        closed_position = {
            'symbol': symbol,
            'quantity': position.quantity,
            'entry_price': position.entry_price,
            'exit_price': exit_price,
            'pnl': pnl,
            'pnl_percent': pnl_percent,
            'side': position.side,
            'entry_time': position.entry_time,
            'exit_time': datetime.now(),
            'hold_duration': datetime.now() - position.entry_time
        }
        
        self.closed_positions.append(closed_position)
        
        # Remove from active positions
        del self.positions[symbol]
        
        # Update database
        self._close_position_in_db(symbol)
        self._record_trade(symbol, 'sell' if position.side == 'long' else 'cover', 
                          position.quantity, exit_price, 'close', pnl)
        
        print(f"✅ Closed position: {symbol}")
        print(f"   💰 P&L: ${pnl:,.2f} ({pnl_percent:+.2f}%)")
        print(f"   💵 New cash balance: ${self.current_cash:,.2f}")
        
        return pnl
    
    def update_position_price(self, symbol: str, new_price: float):
        """Update position with new market price"""
        
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        position.current_price = new_price
        position.market_value = position.quantity * new_price
        position.last_update = datetime.now()
        
        # Calculate unrealized P&L
        if position.side == 'long':
            position.unrealized_pnl = (new_price - position.entry_price) * position.quantity
        else:  # short
            position.unrealized_pnl = (position.entry_price - new_price) * position.quantity
        
        position.unrealized_pnl_percent = (position.unrealized_pnl / (position.entry_price * position.quantity)) * 100
        
        # Check stop loss and take profit
        self._check_stop_loss_take_profit(symbol, new_price)
    
    def _check_stop_loss_take_profit(self, symbol: str, current_price: float):
        """Check if stop loss or take profit should trigger"""
        
        position = self.positions[symbol]
        
        if position.side == 'long':
            # Long position: stop loss below, take profit above
            if position.stop_loss and current_price <= position.stop_loss:
                print(f"🛑 STOP LOSS TRIGGERED: {symbol} at ${current_price:.2f}")
                self.close_position(symbol, current_price)
                return
            
            if position.take_profit and current_price >= position.take_profit:
                print(f"🎯 TAKE PROFIT TRIGGERED: {symbol} at ${current_price:.2f}")
                self.close_position(symbol, current_price)
                return
        
        else:  # short position
            # Short position: stop loss above, take profit below
            if position.stop_loss and current_price >= position.stop_loss:
                print(f"🛑 STOP LOSS TRIGGERED: {symbol} at ${current_price:.2f}")
                self.close_position(symbol, current_price)
                return
            
            if position.take_profit and current_price <= position.take_profit:
                print(f"🎯 TAKE PROFIT TRIGGERED: {symbol} at ${current_price:.2f}")
                self.close_position(symbol, current_price)
                return
    
    def get_portfolio_metrics(self) -> PortfolioMetrics:
        """Calculate current portfolio metrics"""
        
        # Calculate total invested value
        invested_value = sum(pos.market_value for pos in self.positions.values())
        total_value = self.current_cash + invested_value
        
        # Calculate total P&L
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        total_realized_pnl = sum(trade['pnl'] for trade in self.closed_positions)
        total_pnl = total_unrealized_pnl + total_realized_pnl
        total_pnl_percent = (total_pnl / self.initial_capital) * 100
        
        # Calculate daily P&L (simplified - would need historical data for accuracy)
        daily_pnl = total_unrealized_pnl  # Approximation
        daily_pnl_percent = (daily_pnl / self.initial_capital) * 100
        
        # Calculate largest position
        largest_position_percent = 0
        if self.positions:
            largest_position_value = max(pos.market_value for pos in self.positions.values())
            largest_position_percent = (largest_position_value / total_value) * 100
        
        # Risk exposure (total invested / total value)
        risk_exposure = (invested_value / total_value) * 100 if total_value > 0 else 0
        
        return PortfolioMetrics(
            total_value=total_value,
            cash_balance=self.current_cash,
            invested_value=invested_value,
            total_pnl=total_pnl,
            total_pnl_percent=total_pnl_percent,
            daily_pnl=daily_pnl,
            daily_pnl_percent=daily_pnl_percent,
            positions_count=len(self.positions),
            largest_position_percent=largest_position_percent,
            risk_exposure=risk_exposure,
            margin_used=0,  # Not implemented
            buying_power=self.current_cash
        )
    
    def start_live_tracking(self, price_update_callback: callable = None):
        """Start live portfolio tracking"""
        
        self.tracking_active = True
        
        def tracking_loop():
            while self.tracking_active:
                try:
                    # Update portfolio metrics
                    metrics = self.get_portfolio_metrics()
                    
                    # Save to history
                    self._save_portfolio_snapshot(metrics)
                    
                    # Call callback if provided
                    if price_update_callback:
                        price_update_callback(metrics)
                    
                    time.sleep(self.update_interval)
                    
                except Exception as e:
                    print(f"❌ Tracking error: {e}")
                    time.sleep(self.update_interval)
        
        # Start tracking thread
        tracking_thread = threading.Thread(target=tracking_loop, daemon=True)
        tracking_thread.start()
        
        print("🔄 Live portfolio tracking started")
    
    def stop_live_tracking(self):
        """Stop live portfolio tracking"""
        self.tracking_active = False
        print("🛑 Live portfolio tracking stopped")
    
    def _save_position(self, position: Position):
        """Save position to database"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO positions 
            (symbol, quantity, entry_price, current_price, side, entry_time, stop_loss, take_profit, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'open')
        ''', (position.symbol, position.quantity, position.entry_price, position.current_price,
              position.side, position.entry_time.isoformat(), position.stop_loss, position.take_profit))
        
        conn.commit()
        conn.close()
    
    def _close_position_in_db(self, symbol: str):
        """Mark position as closed in database"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        cursor.execute('UPDATE positions SET status = "closed" WHERE symbol = ?', (symbol,))
        
        conn.commit()
        conn.close()
    
    def _record_trade(self, symbol: str, side: str, quantity: float, price: float, trade_type: str, pnl: float = 0):
        """Record trade in database"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO trades (symbol, side, quantity, price, timestamp, trade_type, pnl)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (symbol, side, quantity, price, datetime.now().isoformat(), trade_type, pnl))
        
        conn.commit()
        conn.close()
    
    def _save_portfolio_snapshot(self, metrics: PortfolioMetrics):
        """Save portfolio snapshot to history"""
        conn = sqlite3.connect('portfolio_tracker.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO portfolio_history 
            (timestamp, total_value, cash_balance, total_pnl, daily_pnl, positions_count)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (datetime.now().isoformat(), metrics.total_value, metrics.cash_balance,
              metrics.total_pnl, metrics.daily_pnl, metrics.positions_count))
        
        conn.commit()
        conn.close()
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get complete portfolio summary"""
        metrics = self.get_portfolio_metrics()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'portfolio_metrics': asdict(metrics),
            'positions': {
                symbol: {
                    'quantity': pos.quantity,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_percent': pos.unrealized_pnl_percent,
                    'side': pos.side,
                    'stop_loss': pos.stop_loss,
                    'take_profit': pos.take_profit
                }
                for symbol, pos in self.positions.items()
            },
            'recent_trades': self.closed_positions[-10:],  # Last 10 trades
            'tracking_active': self.tracking_active
        }

def main():
    """Test live portfolio tracker"""
    print("📊 LIVE PORTFOLIO TRACKER - TESTING")
    print("=" * 60)
    
    # Initialize tracker
    tracker = LivePortfolioTracker(100000.0)
    
    # Add some test positions
    print("\n📈 Adding test positions...")
    tracker.add_position('BTC-USD', 0.5, 50000, 'long', stop_loss=45000, take_profit=60000)
    tracker.add_position('AAPL', 100, 150, 'long', stop_loss=140, take_profit=170)
    tracker.add_position('TSLA', 50, 200, 'long', stop_loss=180, take_profit=250)
    
    # Update prices
    print("\n💰 Updating prices...")
    tracker.update_position_price('BTC-USD', 52000)  # +4% gain
    tracker.update_position_price('AAPL', 145)       # -3.3% loss
    tracker.update_position_price('TSLA', 220)       # +10% gain
    
    # Get portfolio summary
    summary = tracker.get_portfolio_summary()
    metrics = summary['portfolio_metrics']
    
    print(f"\n📊 PORTFOLIO SUMMARY:")
    print(f"   💰 Total Value: ${metrics['total_value']:,.2f}")
    print(f"   💵 Cash Balance: ${metrics['cash_balance']:,.2f}")
    print(f"   📈 Total P&L: ${metrics['total_pnl']:,.2f} ({metrics['total_pnl_percent']:+.2f}%)")
    print(f"   📊 Positions: {metrics['positions_count']}")
    print(f"   🎯 Risk Exposure: {metrics['risk_exposure']:.1f}%")
    
    print(f"\n📈 POSITIONS:")
    for symbol, pos in summary['positions'].items():
        print(f"   {symbol}: {pos['quantity']} @ ${pos['current_price']:.2f}")
        print(f"      P&L: ${pos['unrealized_pnl']:,.2f} ({pos['unrealized_pnl_percent']:+.2f}%)")
    
    # Test closing a position
    print(f"\n🔄 Closing AAPL position...")
    pnl = tracker.close_position('AAPL', 148)
    
    # Final summary
    final_summary = tracker.get_portfolio_summary()
    final_metrics = final_summary['portfolio_metrics']
    
    print(f"\n📊 FINAL PORTFOLIO:")
    print(f"   💰 Total Value: ${final_metrics['total_value']:,.2f}")
    print(f"   💵 Cash Balance: ${final_metrics['cash_balance']:,.2f}")
    print(f"   📈 Total P&L: ${final_metrics['total_pnl']:,.2f} ({final_metrics['total_pnl_percent']:+.2f}%)")
    print(f"   📊 Active Positions: {final_metrics['positions_count']}")
    
    print(f"\n✅ LIVE PORTFOLIO TRACKER TEST COMPLETE")

if __name__ == "__main__":
    main()
