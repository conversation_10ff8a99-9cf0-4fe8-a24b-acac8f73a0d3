#!/usr/bin/env python3
"""
Production Ready Checklist
What's needed to make the system actually ready for real-world trading
"""

import os
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any

class ProductionReadinessChecker:
    """Check if system is ready for production trading"""
    
    def __init__(self):
        self.checklist = {
            'critical': [],
            'important': [],
            'recommended': [],
            'completed': []
        }
        
        print("🔍 PRODUCTION READINESS CHECKER")
        print("=" * 50)
    
    def check_api_connections(self) -> Dict[str, Any]:
        """Check if trading APIs are properly configured"""
        print("\n🔑 CHECKING API CONNECTIONS")
        
        api_status = {}
        
        # Check environment variables
        required_vars = [
            'BINANCE_API_KEY',
            'BINANCE_SECRET_KEY',
            'ALPACA_API_KEY',
            'ALPACA_SECRET_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.checklist['critical'].append({
                'item': 'API Keys Missing',
                'description': f'Missing environment variables: {missing_vars}',
                'action': 'Set up API keys in environment variables',
                'priority': 'CRITICAL'
            })
            api_status['api_keys'] = 'MISSING'
        else:
            api_status['api_keys'] = 'CONFIGURED'
            self.checklist['completed'].append('API keys configured')
        
        # Check if API libraries are installed
        try:
            import binance
            api_status['binance_lib'] = 'INSTALLED'
        except ImportError:
            self.checklist['critical'].append({
                'item': 'Binance Library Missing',
                'description': 'python-binance library not installed',
                'action': 'pip install python-binance',
                'priority': 'CRITICAL'
            })
            api_status['binance_lib'] = 'MISSING'
        
        try:
            import alpaca_trade_api
            api_status['alpaca_lib'] = 'INSTALLED'
        except ImportError:
            self.checklist['critical'].append({
                'item': 'Alpaca Library Missing',
                'description': 'alpaca-trade-api library not installed',
                'action': 'pip install alpaca-trade-api',
                'priority': 'CRITICAL'
            })
            api_status['alpaca_lib'] = 'MISSING'
        
        return api_status
    
    def check_data_feeds(self) -> Dict[str, Any]:
        """Check data feed availability"""
        print("\n📊 CHECKING DATA FEEDS")
        
        data_status = {}
        
        # Check Yahoo Finance (free)
        try:
            import requests
            response = requests.get('https://query1.finance.yahoo.com/v8/finance/chart/AAPL', timeout=5)
            if response.status_code == 200:
                data_status['yahoo_finance'] = 'WORKING'
                self.checklist['completed'].append('Yahoo Finance data feed working')
            else:
                data_status['yahoo_finance'] = 'ERROR'
        except:
            data_status['yahoo_finance'] = 'ERROR'
            self.checklist['important'].append({
                'item': 'Yahoo Finance Data Feed',
                'description': 'Cannot connect to Yahoo Finance API',
                'action': 'Check internet connection and API availability',
                'priority': 'IMPORTANT'
            })
        
        # Check if premium data APIs are configured
        polygon_key = os.getenv('POLYGON_API_KEY')
        if not polygon_key:
            self.checklist['recommended'].append({
                'item': 'Premium Data Feed',
                'description': 'No Polygon.io API key for real-time stock data',
                'action': 'Get Polygon.io API key for professional data',
                'priority': 'RECOMMENDED'
            })
            data_status['polygon'] = 'NOT_CONFIGURED'
        else:
            data_status['polygon'] = 'CONFIGURED'
        
        return data_status
    
    def check_risk_management(self) -> Dict[str, Any]:
        """Check risk management system"""
        print("\n🛡️ CHECKING RISK MANAGEMENT")
        
        risk_status = {}
        
        # Check if risk management file exists
        if os.path.exists('working_risk_management.py'):
            risk_status['risk_system'] = 'AVAILABLE'
            self.checklist['completed'].append('Risk management system available')
        else:
            self.checklist['critical'].append({
                'item': 'Risk Management System',
                'description': 'Risk management system not found',
                'action': 'Implement comprehensive risk management',
                'priority': 'CRITICAL'
            })
            risk_status['risk_system'] = 'MISSING'
        
        # Check risk configuration
        risk_config_items = [
            'Position size limits',
            'Stop-loss mechanisms',
            'Daily loss limits',
            'Portfolio exposure limits',
            'Emergency stop procedures'
        ]
        
        for item in risk_config_items:
            self.checklist['important'].append({
                'item': f'Risk Config: {item}',
                'description': f'Verify {item.lower()} are properly configured',
                'action': f'Review and test {item.lower()}',
                'priority': 'IMPORTANT'
            })
        
        return risk_status
    
    def check_system_infrastructure(self) -> Dict[str, Any]:
        """Check system infrastructure"""
        print("\n🏗️ CHECKING SYSTEM INFRASTRUCTURE")
        
        infra_status = {}
        
        # Check database
        if os.path.exists('real_agents.db') or os.path.exists('ai_agents.db'):
            infra_status['database'] = 'AVAILABLE'
            self.checklist['completed'].append('Database system available')
        else:
            self.checklist['important'].append({
                'item': 'Database System',
                'description': 'No database files found',
                'action': 'Initialize database system',
                'priority': 'IMPORTANT'
            })
            infra_status['database'] = 'MISSING'
        
        # Check logging
        log_files = ['trading_execution.log', 'risk_management.log']
        log_exists = any(os.path.exists(f) for f in log_files)
        
        if log_exists:
            infra_status['logging'] = 'CONFIGURED'
            self.checklist['completed'].append('Logging system configured')
        else:
            self.checklist['important'].append({
                'item': 'Logging System',
                'description': 'No log files found',
                'action': 'Set up comprehensive logging',
                'priority': 'IMPORTANT'
            })
            infra_status['logging'] = 'MISSING'
        
        # Check AI models
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                model_count = len(lines) - 1  # Subtract header
                infra_status['ai_models'] = f'{model_count} MODELS'
                self.checklist['completed'].append(f'{model_count} AI models available')
            else:
                infra_status['ai_models'] = 'ERROR'
        except:
            self.checklist['critical'].append({
                'item': 'AI Models',
                'description': 'Cannot access Ollama AI models',
                'action': 'Install and configure Ollama with AI models',
                'priority': 'CRITICAL'
            })
            infra_status['ai_models'] = 'MISSING'
        
        return infra_status
    
    def check_security_measures(self) -> Dict[str, Any]:
        """Check security measures"""
        print("\n🔒 CHECKING SECURITY MEASURES")
        
        security_status = {}
        
        # Check if .env file exists (good practice)
        if os.path.exists('.env'):
            security_status['env_file'] = 'EXISTS'
            self.checklist['completed'].append('.env file for secure configuration')
        else:
            self.checklist['recommended'].append({
                'item': 'Environment File',
                'description': 'No .env file for secure configuration',
                'action': 'Create .env file for API keys and secrets',
                'priority': 'RECOMMENDED'
            })
            security_status['env_file'] = 'MISSING'
        
        # Check if sensitive files are in .gitignore
        if os.path.exists('.gitignore'):
            with open('.gitignore', 'r') as f:
                gitignore_content = f.read()
                if '.env' in gitignore_content:
                    security_status['gitignore'] = 'CONFIGURED'
                    self.checklist['completed'].append('.gitignore properly configured')
                else:
                    self.checklist['important'].append({
                        'item': 'Git Security',
                        'description': '.env not in .gitignore',
                        'action': 'Add .env to .gitignore',
                        'priority': 'IMPORTANT'
                    })
                    security_status['gitignore'] = 'INCOMPLETE'
        else:
            self.checklist['recommended'].append({
                'item': 'Git Ignore File',
                'description': 'No .gitignore file',
                'action': 'Create .gitignore to protect sensitive files',
                'priority': 'RECOMMENDED'
            })
            security_status['gitignore'] = 'MISSING'
        
        return security_status
    
    def check_testing_framework(self) -> Dict[str, Any]:
        """Check testing and validation"""
        print("\n🧪 CHECKING TESTING FRAMEWORK")
        
        testing_status = {}
        
        # Critical testing items
        critical_tests = [
            'Paper trading mode',
            'Risk limit validation',
            'Order execution testing',
            'Data feed reliability',
            'Emergency stop procedures'
        ]
        
        for test in critical_tests:
            self.checklist['critical'].append({
                'item': f'Test: {test}',
                'description': f'{test} must be thoroughly tested',
                'action': f'Implement and run {test.lower()} tests',
                'priority': 'CRITICAL'
            })
        
        testing_status['critical_tests'] = 'REQUIRED'
        
        return testing_status
    
    def generate_readiness_report(self) -> Dict[str, Any]:
        """Generate complete readiness report"""
        
        # Run all checks
        api_status = self.check_api_connections()
        data_status = self.check_data_feeds()
        risk_status = self.check_risk_management()
        infra_status = self.check_system_infrastructure()
        security_status = self.check_security_measures()
        testing_status = self.check_testing_framework()
        
        # Calculate readiness score
        total_items = (len(self.checklist['critical']) + 
                      len(self.checklist['important']) + 
                      len(self.checklist['recommended']) + 
                      len(self.checklist['completed']))
        
        completed_items = len(self.checklist['completed'])
        critical_items = len(self.checklist['critical'])
        
        # Can't be ready with critical items outstanding
        if critical_items > 0:
            readiness_score = 0
            readiness_status = 'NOT READY'
        else:
            readiness_score = (completed_items / total_items) * 100 if total_items > 0 else 0
            if readiness_score >= 80:
                readiness_status = 'READY'
            elif readiness_score >= 60:
                readiness_status = 'MOSTLY READY'
            else:
                readiness_status = 'NEEDS WORK'
        
        return {
            'timestamp': datetime.now().isoformat(),
            'readiness_status': readiness_status,
            'readiness_score': readiness_score,
            'critical_items': len(self.checklist['critical']),
            'important_items': len(self.checklist['important']),
            'recommended_items': len(self.checklist['recommended']),
            'completed_items': len(self.checklist['completed']),
            'component_status': {
                'api_connections': api_status,
                'data_feeds': data_status,
                'risk_management': risk_status,
                'infrastructure': infra_status,
                'security': security_status,
                'testing': testing_status
            },
            'checklist': self.checklist
        }
    
    def print_action_plan(self, report: Dict[str, Any]):
        """Print actionable next steps"""
        print(f"\n📋 PRODUCTION READINESS ACTION PLAN")
        print("=" * 60)
        
        print(f"🎯 OVERALL STATUS: {report['readiness_status']}")
        print(f"📊 READINESS SCORE: {report['readiness_score']:.1f}%")
        
        if report['critical_items'] > 0:
            print(f"\n🚨 CRITICAL ITEMS ({report['critical_items']}):")
            for item in self.checklist['critical']:
                print(f"   ❌ {item['item']}")
                print(f"      Action: {item['action']}")
        
        if report['important_items'] > 0:
            print(f"\n⚠️ IMPORTANT ITEMS ({report['important_items']}):")
            for item in self.checklist['important'][:5]:  # Show first 5
                print(f"   🔶 {item['item']}")
                print(f"      Action: {item['action']}")
        
        if report['recommended_items'] > 0:
            print(f"\n💡 RECOMMENDED ITEMS ({report['recommended_items']}):")
            for item in self.checklist['recommended'][:3]:  # Show first 3
                print(f"   🔹 {item['item']}")
                print(f"      Action: {item['action']}")
        
        print(f"\n✅ COMPLETED ITEMS ({report['completed_items']}):")
        for item in self.checklist['completed']:
            print(f"   ✓ {item}")
        
        # Next steps
        print(f"\n🚀 IMMEDIATE NEXT STEPS:")
        if report['critical_items'] > 0:
            print("   1. Fix all CRITICAL items before proceeding")
            print("   2. Test each fix thoroughly")
            print("   3. Re-run readiness check")
        else:
            print("   1. Address IMPORTANT items")
            print("   2. Set up paper trading mode")
            print("   3. Run comprehensive tests")
            print("   4. Start with small live trades")

def main():
    """Run production readiness check"""
    print("🔍 PRODUCTION READINESS CHECK")
    print("=" * 60)
    
    checker = ProductionReadinessChecker()
    report = checker.generate_readiness_report()
    
    # Save report
    with open('production_readiness_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print action plan
    checker.print_action_plan(report)
    
    print(f"\n📄 Full report saved to: production_readiness_report.json")

if __name__ == "__main__":
    main()
