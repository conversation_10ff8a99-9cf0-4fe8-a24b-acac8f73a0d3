#!/usr/bin/env python3
"""
Data Management Service
Microservice for coordinating all 23 databases and real-time market feeds
"""

import asyncio
import json
import time
import logging
import sqlite3
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    pika = None

from circuit_breaker import CircuitBreaker

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_management.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DataRequest:
    """Data class for data requests"""
    request_id: str
    operation: str  # read, write, update, delete
    database: str
    table: str
    data: Dict[str, Any] = None
    query_params: Dict[str, Any] = None
    priority: int = 1
    timestamp: datetime = None

@dataclass
class DataResponse:
    """Data class for data responses"""
    request_id: str
    operation: str
    database: str
    success: bool
    data: Any = None
    rows_affected: int = 0
    execution_time: float = 0.0
    error: str = ""
    timestamp: datetime = None

class DataManagementService:
    """REAL microservice for data management"""
    
    def __init__(self, rabbitmq_url: str = "amqp://localhost"):
        self.service_name = "data-management"
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        
        # Database connection pool
        self.db_connections = {}
        self.db_locks = {}
        
        # Circuit breakers for database operations
        self.circuit_breakers = {}
        
        # Data management metrics
        self.data_metrics = {
            'total_operations': 0,
            'successful_operations': 0,
            'read_operations': 0,
            'write_operations': 0,
            'avg_execution_time': 0.0,
            'total_execution_time': 0.0,
            'database_health': {}
        }
        
        # Initialize database inventory
        self.database_inventory = self._discover_databases()
        
        # Setup circuit breakers for each database
        for db_name in self.database_inventory:
            self.circuit_breakers[db_name] = CircuitBreaker(
                failure_threshold=3,
                recovery_timeout=60,
                expected_exception=Exception
            )
            self.db_locks[db_name] = asyncio.Lock()
        
        # Setup service database
        self._setup_service_database()
        
        logger.info(f"Data Management Service initialized", extra={
            "service": self.service_name,
            "databases_discovered": len(self.database_inventory),
            "circuit_breakers": len(self.circuit_breakers)
        })
    
    def _discover_databases(self) -> List[str]:
        """Discover all database files in the current directory"""
        
        db_files = [f for f in os.listdir('.') if f.endswith('.db')]
        
        logger.info(f"Discovered {len(db_files)} databases", extra={
            "databases": db_files
        })
        
        return db_files
    
    def _setup_service_database(self):
        """Setup data management service database"""
        conn = sqlite3.connect('data_management_service.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_operations (
                id INTEGER PRIMARY KEY,
                request_id TEXT,
                operation TEXT,
                database_name TEXT,
                table_name TEXT,
                status TEXT,
                execution_time REAL,
                rows_affected INTEGER,
                error TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS database_health (
                id INTEGER PRIMARY KEY,
                database_name TEXT,
                status TEXT,
                last_check DATETIME,
                file_size INTEGER,
                table_count INTEGER,
                record_count INTEGER,
                error TEXT
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS data_metrics (
                id INTEGER PRIMARY KEY,
                metric_name TEXT,
                metric_value REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("Data Management Service database initialized")
    
    async def setup_message_queue(self):
        """Setup RabbitMQ connection and queues"""
        if not RABBITMQ_AVAILABLE:
            logger.info("RabbitMQ not available, using direct processing mode", extra={
                "service": self.service_name,
                "mode": "direct"
            })
            self.connection = None
            self.channel = None
            return

        try:
            # Connect to RabbitMQ
            self.connection = pika.BlockingConnection(pika.URLParameters(self.rabbitmq_url))
            self.channel = self.connection.channel()

            # Declare queues
            self.channel.queue_declare(queue='data_requests', durable=True)
            self.channel.queue_declare(queue='data_responses', durable=True)
            self.channel.queue_declare(queue='database_health_checks', durable=True)

            # Setup exchange
            self.channel.exchange_declare(exchange='data_management', exchange_type='topic')

            logger.info("Message queue setup completed", extra={
                "service": self.service_name,
                "queues": ["data_requests", "data_responses", "database_health_checks"]
            })

        except Exception as e:
            logger.error(f"Failed to setup message queue: {e}", extra={
                "service": self.service_name,
                "error": str(e)
            })
            self.connection = None
            self.channel = None
    
    async def process_data_request(self, request: DataRequest) -> DataResponse:
        """Process data request with circuit breaker protection"""
        
        start_time = time.time()
        
        try:
            # Validate database exists
            if request.database not in self.database_inventory:
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error=f"Database {request.database} not found",
                    execution_time=time.time() - start_time,
                    timestamp=datetime.now()
                )
            
            # Check circuit breaker
            circuit_breaker = self.circuit_breakers[request.database]
            if circuit_breaker.is_open:
                logger.warn(f"Database circuit breaker open", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "database": request.database
                })
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error="Database circuit breaker open",
                    execution_time=time.time() - start_time,
                    timestamp=datetime.now()
                )
            
            # Execute operation with circuit breaker protection
            result = await self._execute_database_operation(request, circuit_breaker)
            
            execution_time = time.time() - start_time
            
            # Update metrics
            self._update_data_metrics(request.operation, result.success, execution_time)
            
            # Store operation
            self._store_data_operation(request, result, execution_time)
            
            if result.success:
                logger.info(f"Data operation completed", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "operation": request.operation,
                    "database": request.database,
                    "execution_time": execution_time,
                    "rows_affected": result.rows_affected
                })
            else:
                logger.warn(f"Data operation failed", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "operation": request.operation,
                    "database": request.database,
                    "error": result.error
                })
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            logger.error(f"Data operation exception", extra={
                "service": self.service_name,
                "request_id": request.request_id,
                "operation": request.operation,
                "database": request.database,
                "error": str(e)
            })
            
            # Update metrics
            self._update_data_metrics(request.operation, False, execution_time)
            
            return DataResponse(
                request_id=request.request_id,
                operation=request.operation,
                database=request.database,
                success=False,
                error=str(e),
                execution_time=execution_time,
                timestamp=datetime.now()
            )
    
    async def _execute_database_operation(self, request: DataRequest, circuit_breaker: CircuitBreaker) -> DataResponse:
        """Execute database operation with proper locking"""
        
        # Use database-specific lock to prevent concurrent access issues
        async with self.db_locks[request.database]:
            try:
                if request.operation == 'read':
                    result = circuit_breaker.call(self._read_operation, request)
                elif request.operation == 'write':
                    result = circuit_breaker.call(self._write_operation, request)
                elif request.operation == 'update':
                    result = circuit_breaker.call(self._update_operation, request)
                elif request.operation == 'delete':
                    result = circuit_breaker.call(self._delete_operation, request)
                else:
                    return DataResponse(
                        request_id=request.request_id,
                        operation=request.operation,
                        database=request.database,
                        success=False,
                        error=f"Unknown operation: {request.operation}",
                        timestamp=datetime.now()
                    )
                
                return result
                
            except Exception as e:
                logger.error(f"Database operation failed", extra={
                    "database": request.database,
                    "operation": request.operation,
                    "error": str(e)
                })
                
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error=str(e),
                    timestamp=datetime.now()
                )
    
    def _read_operation(self, request: DataRequest) -> DataResponse:
        """Execute read operation"""
        
        conn = sqlite3.connect(request.database)
        cursor = conn.cursor()
        
        try:
            if request.query_params:
                # Custom query
                query = request.query_params.get('query', '')
                params = request.query_params.get('params', [])
                cursor.execute(query, params)
            else:
                # Simple table read
                cursor.execute(f"SELECT * FROM {request.table}")
            
            data = cursor.fetchall()
            
            # Get column names
            column_names = [description[0] for description in cursor.description]
            
            # Convert to list of dictionaries
            result_data = [dict(zip(column_names, row)) for row in data]
            
            conn.close()
            
            return DataResponse(
                request_id=request.request_id,
                operation=request.operation,
                database=request.database,
                success=True,
                data=result_data,
                rows_affected=len(result_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            conn.close()
            raise e
    
    def _write_operation(self, request: DataRequest) -> DataResponse:
        """Execute write operation"""
        
        conn = sqlite3.connect(request.database)
        cursor = conn.cursor()
        
        try:
            if request.data:
                # Insert data
                columns = list(request.data.keys())
                values = list(request.data.values())
                placeholders = ', '.join(['?' for _ in values])
                
                query = f"INSERT INTO {request.table} ({', '.join(columns)}) VALUES ({placeholders})"
                cursor.execute(query, values)
                
                rows_affected = cursor.rowcount
                conn.commit()
                conn.close()
                
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=True,
                    rows_affected=rows_affected,
                    timestamp=datetime.now()
                )
            else:
                conn.close()
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error="No data provided for write operation",
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            conn.close()
            raise e
    
    def _update_operation(self, request: DataRequest) -> DataResponse:
        """Execute update operation"""
        
        conn = sqlite3.connect(request.database)
        cursor = conn.cursor()
        
        try:
            if request.data and request.query_params:
                # Update with conditions
                set_clause = ', '.join([f"{k} = ?" for k in request.data.keys()])
                where_clause = request.query_params.get('where', '1=1')
                where_params = request.query_params.get('where_params', [])
                
                query = f"UPDATE {request.table} SET {set_clause} WHERE {where_clause}"
                params = list(request.data.values()) + where_params
                
                cursor.execute(query, params)
                rows_affected = cursor.rowcount
                conn.commit()
                conn.close()
                
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=True,
                    rows_affected=rows_affected,
                    timestamp=datetime.now()
                )
            else:
                conn.close()
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error="Insufficient data for update operation",
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            conn.close()
            raise e
    
    def _delete_operation(self, request: DataRequest) -> DataResponse:
        """Execute delete operation"""
        
        conn = sqlite3.connect(request.database)
        cursor = conn.cursor()
        
        try:
            if request.query_params:
                where_clause = request.query_params.get('where', '1=1')
                where_params = request.query_params.get('where_params', [])
                
                query = f"DELETE FROM {request.table} WHERE {where_clause}"
                cursor.execute(query, where_params)
                
                rows_affected = cursor.rowcount
                conn.commit()
                conn.close()
                
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=True,
                    rows_affected=rows_affected,
                    timestamp=datetime.now()
                )
            else:
                conn.close()
                return DataResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    database=request.database,
                    success=False,
                    error="No conditions provided for delete operation",
                    timestamp=datetime.now()
                )
                
        except Exception as e:
            conn.close()
            raise e
    
    def _update_data_metrics(self, operation: str, success: bool, execution_time: float):
        """Update data management metrics"""
        
        self.data_metrics['total_operations'] += 1
        
        if success:
            self.data_metrics['successful_operations'] += 1
            self.data_metrics['total_execution_time'] += execution_time
            
            # Update average execution time
            self.data_metrics['avg_execution_time'] = (
                self.data_metrics['total_execution_time'] / 
                self.data_metrics['successful_operations']
            )
        
        # Count operation types
        if operation == 'read':
            self.data_metrics['read_operations'] += 1
        elif operation in ['write', 'update', 'delete']:
            self.data_metrics['write_operations'] += 1
    
    def _store_data_operation(self, request: DataRequest, result: DataResponse, execution_time: float):
        """Store data operation in service database"""
        
        try:
            conn = sqlite3.connect('data_management_service.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO data_operations 
                (request_id, operation, database_name, table_name, status, 
                 execution_time, rows_affected, error, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                request.request_id, request.operation, request.database, request.table,
                'SUCCESS' if result.success else 'FAILED',
                execution_time, result.rows_affected, result.error,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to store data operation: {e}")
    
    async def check_database_health(self) -> Dict[str, Any]:
        """Check health of all databases"""
        
        health_results = {}
        
        for db_name in self.database_inventory:
            try:
                # Get file size
                file_size = os.path.getsize(db_name)
                
                # Connect and get table info
                conn = sqlite3.connect(db_name)
                cursor = conn.cursor()
                
                # Get table count
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                table_count = cursor.fetchone()[0]
                
                # Get total record count
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                total_records = 0
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    total_records += count
                
                conn.close()
                
                health_results[db_name] = {
                    'status': 'healthy',
                    'file_size': file_size,
                    'table_count': table_count,
                    'record_count': total_records,
                    'circuit_breaker_status': self.circuit_breakers[db_name].get_status()
                }
                
            except Exception as e:
                health_results[db_name] = {
                    'status': 'unhealthy',
                    'error': str(e),
                    'circuit_breaker_status': self.circuit_breakers[db_name].get_status()
                }
        
        # Update database health metrics
        self.data_metrics['database_health'] = health_results
        
        return health_results
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status"""
        
        unhealthy_dbs = [
            db for db, health in self.data_metrics.get('database_health', {}).items()
            if health.get('status') != 'healthy'
        ]
        
        return {
            'service': self.service_name,
            'status': 'healthy' if not unhealthy_dbs else 'degraded',
            'total_databases': len(self.database_inventory),
            'healthy_databases': len(self.database_inventory) - len(unhealthy_dbs),
            'unhealthy_databases': unhealthy_dbs,
            'message_queue_connected': self.connection is not None,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_data_metrics(self) -> Dict[str, Any]:
        """Get data management metrics"""
        
        return {
            'service': self.service_name,
            'metrics': self.data_metrics,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main service entry point"""
    
    logger.info("Starting Data Management Service")
    
    # Initialize service
    service = DataManagementService()
    
    # Setup message queue
    await service.setup_message_queue()
    
    # Check database health
    health_results = await service.check_database_health()
    
    logger.info(f"Database health check completed", extra={
        "total_databases": len(health_results),
        "healthy_databases": len([h for h in health_results.values() if h.get('status') == 'healthy'])
    })
    
    # Test service with sample request
    test_request = DataRequest(
        request_id="test_data_001",
        operation="read",
        database="professional_technical_analysis.db",
        table="technical_analysis",
        query_params={
            "query": "SELECT COUNT(*) as total_records FROM technical_analysis",
            "params": []
        }
    )
    
    result = await service.process_data_request(test_request)
    
    logger.info(f"Service test completed", extra={
        "success": result.success,
        "execution_time": result.execution_time,
        "rows_affected": result.rows_affected
    })
    
    # Get service health
    health = service.get_service_health()
    logger.info(f"Service health: {health['status']}")
    
    # Get data metrics
    metrics = service.get_data_metrics()
    logger.info(f"Data metrics: {metrics['metrics']['total_operations']} operations processed")

if __name__ == "__main__":
    asyncio.run(main())
