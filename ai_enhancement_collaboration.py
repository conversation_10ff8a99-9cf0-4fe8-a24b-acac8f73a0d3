#!/usr/bin/env python3
"""
AI Enhancement: Agent Collaboration System
ACTUAL working system for AI agents to communicate, debate, and collaborate
"""

import sqlite3
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
import subprocess

class AICollaborationSystem:
    """REAL collaboration system for AI agents"""
    
    def __init__(self):
        self.active_discussions = {}
        self.agent_network = {}
        self.collaboration_history = {}
        
        # Setup database
        self._setup_database()
        
        print("🤝 AI COLLABORATION SYSTEM INITIALIZED")
        print("   💬 Inter-agent messaging: READY")
        print("   🗳️ Consensus building: READY")
        print("   🎯 Debate system: READY")
        print("   📊 Collaboration tracking: READY")
    
    def _setup_database(self):
        """Setup REAL database for AI collaboration"""
        conn = sqlite3.connect('ai_collaboration.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_messages (
                id INTEGER PRIMARY KEY,
                from_agent TEXT,
                to_agent TEXT,
                message_type TEXT,
                message_content TEXT,
                timestamp DATETIME,
                discussion_id TEXT,
                response_id INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS discussions (
                id INTEGER PRIMARY KEY,
                discussion_id TEXT,
                topic TEXT,
                participants TEXT,
                status TEXT,
                consensus_reached BOOLEAN,
                final_decision TEXT,
                confidence_score REAL,
                created_time DATETIME,
                completed_time DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS collaboration_metrics (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                collaboration_count INTEGER,
                consensus_participation INTEGER,
                debate_wins INTEGER,
                average_response_time REAL,
                influence_score REAL,
                last_updated DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ Collaboration database initialized")
    
    def send_agent_message(self, from_agent: str, to_agent: str, 
                          message: str, message_type: str = 'query',
                          discussion_id: Optional[str] = None) -> str:
        """Send REAL message between AI agents"""
        
        try:
            conn = sqlite3.connect('ai_collaboration.db')
            cursor = conn.cursor()
            
            message_id = f"msg_{int(time.time())}_{from_agent}_{to_agent}"
            
            cursor.execute('''
                INSERT INTO agent_messages 
                (from_agent, to_agent, message_type, message_content, 
                 timestamp, discussion_id, response_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (from_agent, to_agent, message_type, message,
                  datetime.now().isoformat(), discussion_id, None))
            
            conn.commit()
            conn.close()
            
            print(f"   💬 Message sent: {from_agent} → {to_agent}")
            return message_id
            
        except Exception as e:
            print(f"   ❌ Message sending error: {e}")
            return ""
    
    def get_agent_messages(self, agent_name: str, unread_only: bool = True) -> List[Dict[str, Any]]:
        """Get REAL messages for agent"""
        
        try:
            conn = sqlite3.connect('ai_collaboration.db')
            cursor = conn.cursor()
            
            if unread_only:
                cursor.execute('''
                    SELECT from_agent, message_type, message_content, timestamp, discussion_id
                    FROM agent_messages 
                    WHERE to_agent = ? AND response_id IS NULL
                    ORDER BY timestamp DESC
                ''', (agent_name,))
            else:
                cursor.execute('''
                    SELECT from_agent, message_type, message_content, timestamp, discussion_id
                    FROM agent_messages 
                    WHERE to_agent = ?
                    ORDER BY timestamp DESC
                    LIMIT 10
                ''', (agent_name,))
            
            messages = []
            for row in cursor.fetchall():
                messages.append({
                    'from': row[0],
                    'type': row[1],
                    'content': row[2],
                    'timestamp': row[3],
                    'discussion_id': row[4]
                })
            
            conn.close()
            return messages
            
        except Exception as e:
            print(f"   ❌ Message retrieval error: {e}")
            return []
    
    def start_discussion(self, topic: str, participants: List[str]) -> str:
        """Start REAL multi-agent discussion"""
        
        discussion_id = f"disc_{int(time.time())}"
        
        try:
            conn = sqlite3.connect('ai_collaboration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO discussions 
                (discussion_id, topic, participants, status, consensus_reached,
                 final_decision, confidence_score, created_time, completed_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (discussion_id, topic, json.dumps(participants), 'ACTIVE', False,
                  None, 0.0, datetime.now().isoformat(), None))
            
            conn.commit()
            conn.close()
            
            # Send initial message to all participants
            for participant in participants:
                self.send_agent_message(
                    'SYSTEM', participant, 
                    f"Discussion started: {topic}. Please provide your analysis and recommendation.",
                    'discussion_start', discussion_id
                )
            
            print(f"   🗣️ Discussion started: {discussion_id}")
            print(f"   👥 Participants: {', '.join(participants)}")
            
            return discussion_id
            
        except Exception as e:
            print(f"   ❌ Discussion start error: {e}")
            return ""
    
    def collect_agent_responses(self, discussion_id: str, query: str, 
                               participants: List[str], timeout: int = 120) -> Dict[str, Any]:
        """Collect REAL responses from multiple agents"""
        
        print(f"\n🤖 Collecting responses for discussion {discussion_id}...")
        
        responses = {}
        
        # Import AI system for actual queries
        try:
            from ultimate_ai_proof_system import UltimateAIProofSystem
            ai_system = UltimateAIProofSystem()
            
            for agent_name in participants:
                if agent_name in ai_system.ai_agents:
                    model = ai_system.ai_agents[agent_name]
                    
                    # Create discussion-specific query
                    discussion_query = f"""MULTI-AGENT DISCUSSION:
Topic: {query}
Discussion ID: {discussion_id}

Provide your analysis and recommendation. Be specific and justify your position.

REQUIRED FORMAT:
POSITION: [Your stance]
CONFIDENCE: [1-10]
REASONING: [Your analysis]
RECOMMENDATION: [Specific action]
"""
                    
                    print(f"   🤖 Querying {agent_name}...")
                    result = ai_system.test_single_agent(agent_name, model, discussion_query)
                    
                    if result['success']:
                        responses[agent_name] = {
                            'response': result['response'],
                            'response_time': result['response_time'],
                            'timestamp': datetime.now(),
                            'success': True
                        }
                        
                        # Store as message
                        self.send_agent_message(
                            agent_name, 'DISCUSSION', result['response'],
                            'discussion_response', discussion_id
                        )
                        
                        print(f"   ✅ {agent_name}: {result['response_time']:.1f}s")
                    else:
                        responses[agent_name] = {
                            'error': result.get('error', 'Unknown error'),
                            'success': False
                        }
                        print(f"   ❌ {agent_name}: Failed")
            
        except ImportError:
            print("   ⚠️ AI system not available, using mock responses")
            # Create mock responses for testing
            for agent_name in participants:
                responses[agent_name] = {
                    'response': f"Mock response from {agent_name} for: {query}",
                    'response_time': 5.0,
                    'timestamp': datetime.now(),
                    'success': True
                }
        
        return responses
    
    def build_consensus(self, discussion_id: str, responses: Dict[str, Any]) -> Dict[str, Any]:
        """Build REAL consensus from agent responses"""
        
        print(f"\n🗳️ Building consensus for discussion {discussion_id}...")
        
        # Parse responses for positions
        positions = {}
        confidence_scores = {}
        
        for agent_name, response_data in responses.items():
            if response_data.get('success', False):
                response = response_data['response'].upper()
                
                # Extract position
                if 'BUY' in response and 'SELL' not in response:
                    positions[agent_name] = 'BUY'
                elif 'SELL' in response and 'BUY' not in response:
                    positions[agent_name] = 'SELL'
                else:
                    positions[agent_name] = 'HOLD'
                
                # Extract confidence (look for numbers 1-10)
                import re
                conf_match = re.search(r'CONFIDENCE:\s*(\d+)', response)
                if conf_match:
                    confidence_scores[agent_name] = int(conf_match.group(1))
                else:
                    confidence_scores[agent_name] = 5  # Default
        
        # Calculate consensus
        position_counts = {}
        weighted_positions = {}
        
        for agent, position in positions.items():
            confidence = confidence_scores.get(agent, 5)
            
            # Count votes
            position_counts[position] = position_counts.get(position, 0) + 1
            
            # Weight by confidence
            weighted_positions[position] = weighted_positions.get(position, 0) + confidence
        
        # Determine consensus
        if position_counts:
            # Most voted position
            majority_position = max(position_counts.items(), key=lambda x: x[1])
            
            # Highest weighted position
            weighted_position = max(weighted_positions.items(), key=lambda x: x[1])
            
            # Calculate consensus strength
            total_agents = len(positions)
            consensus_strength = majority_position[1] / total_agents if total_agents > 0 else 0
            
            # Average confidence
            avg_confidence = sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0
            
            consensus = {
                'discussion_id': discussion_id,
                'majority_position': majority_position[0],
                'majority_count': majority_position[1],
                'weighted_position': weighted_position[0],
                'consensus_strength': round(consensus_strength * 100, 1),
                'average_confidence': round(avg_confidence, 1),
                'total_participants': total_agents,
                'position_breakdown': position_counts,
                'individual_positions': positions,
                'individual_confidence': confidence_scores,
                'consensus_reached': consensus_strength >= 0.6,  # 60% agreement
                'timestamp': datetime.now()
            }
            
            # Store consensus
            self._store_consensus(consensus)
            
            print(f"   🎯 Consensus: {majority_position[0]} ({consensus_strength*100:.1f}% agreement)")
            print(f"   📊 Breakdown: {position_counts}")
            print(f"   🔢 Avg confidence: {avg_confidence:.1f}/10")
            
            return consensus
        
        return {'error': 'No valid positions found'}
    
    def _store_consensus(self, consensus: Dict[str, Any]):
        """Store REAL consensus result"""
        
        try:
            conn = sqlite3.connect('ai_collaboration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE discussions 
                SET status = ?, consensus_reached = ?, final_decision = ?, 
                    confidence_score = ?, completed_time = ?
                WHERE discussion_id = ?
            ''', ('COMPLETED', consensus['consensus_reached'], 
                  consensus['majority_position'], consensus['average_confidence'],
                  datetime.now().isoformat(), consensus['discussion_id']))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Consensus storage error: {e}")
    
    def run_agent_debate(self, topic: str, agent1: str, agent2: str) -> Dict[str, Any]:
        """Run REAL debate between two agents"""
        
        print(f"\n⚔️ Starting debate: {agent1} vs {agent2}")
        print(f"   Topic: {topic}")
        
        debate_id = f"debate_{int(time.time())}"
        
        # Round 1: Initial positions
        print(f"\n🥊 Round 1: Initial positions")
        
        initial_query = f"DEBATE ROUND 1: {topic}\nProvide your initial position and strongest argument."
        
        try:
            from ultimate_ai_proof_system import UltimateAIProofSystem
            ai_system = UltimateAIProofSystem()
            
            # Get initial positions
            pos1 = ai_system.test_single_agent(agent1, ai_system.ai_agents[agent1], initial_query)
            pos2 = ai_system.test_single_agent(agent2, ai_system.ai_agents[agent2], initial_query)
            
            if pos1['success'] and pos2['success']:
                print(f"   {agent1}: {pos1['response'][:100]}...")
                print(f"   {agent2}: {pos2['response'][:100]}...")
                
                # Round 2: Counter-arguments
                print(f"\n🥊 Round 2: Counter-arguments")
                
                counter_query1 = f"DEBATE ROUND 2: {topic}\nYour opponent said: '{pos2['response'][:200]}...'\nProvide your counter-argument."
                counter_query2 = f"DEBATE ROUND 2: {topic}\nYour opponent said: '{pos1['response'][:200]}...'\nProvide your counter-argument."
                
                counter1 = ai_system.test_single_agent(agent1, ai_system.ai_agents[agent1], counter_query1)
                counter2 = ai_system.test_single_agent(agent2, ai_system.ai_agents[agent2], counter_query2)
                
                # Analyze debate
                debate_result = {
                    'debate_id': debate_id,
                    'topic': topic,
                    'participants': [agent1, agent2],
                    'round1': {
                        agent1: pos1['response'] if pos1['success'] else 'Failed',
                        agent2: pos2['response'] if pos2['success'] else 'Failed'
                    },
                    'round2': {
                        agent1: counter1['response'] if counter1['success'] else 'Failed',
                        agent2: counter2['response'] if counter2['success'] else 'Failed'
                    },
                    'response_times': {
                        agent1: pos1.get('response_time', 0) + counter1.get('response_time', 0),
                        agent2: pos2.get('response_time', 0) + counter2.get('response_time', 0)
                    },
                    'winner': 'TIE',  # Would need more sophisticated analysis
                    'timestamp': datetime.now()
                }
                
                # Simple winner determination based on response length and speed
                score1 = len(pos1.get('response', '')) + len(counter1.get('response', '')) - (pos1.get('response_time', 60) + counter1.get('response_time', 60))
                score2 = len(pos2.get('response', '')) + len(counter2.get('response', '')) - (pos2.get('response_time', 60) + counter2.get('response_time', 60))
                
                if score1 > score2:
                    debate_result['winner'] = agent1
                elif score2 > score1:
                    debate_result['winner'] = agent2
                
                print(f"\n🏆 Debate result: {debate_result['winner']} wins")
                
                return debate_result
            
        except ImportError:
            print("   ⚠️ AI system not available for debate")
        
        return {'error': 'Debate failed'}
    
    def get_collaboration_summary(self) -> Dict[str, Any]:
        """Get REAL collaboration system summary"""
        
        try:
            conn = sqlite3.connect('ai_collaboration.db')
            cursor = conn.cursor()
            
            # Get discussion stats
            cursor.execute('''
                SELECT COUNT(*), 
                       SUM(CASE WHEN consensus_reached = 1 THEN 1 ELSE 0 END),
                       AVG(confidence_score)
                FROM discussions
            ''')
            
            disc_stats = cursor.fetchone()
            total_discussions = disc_stats[0] or 0
            consensus_reached = disc_stats[1] or 0
            avg_confidence = disc_stats[2] or 0
            
            # Get message stats
            cursor.execute('''
                SELECT COUNT(*), COUNT(DISTINCT from_agent), COUNT(DISTINCT to_agent)
                FROM agent_messages
            ''')
            
            msg_stats = cursor.fetchone()
            total_messages = msg_stats[0] or 0
            active_senders = msg_stats[1] or 0
            active_receivers = msg_stats[2] or 0
            
            conn.close()
            
            return {
                'total_discussions': total_discussions,
                'consensus_reached': consensus_reached,
                'consensus_rate': (consensus_reached / total_discussions * 100) if total_discussions > 0 else 0,
                'average_confidence': round(avg_confidence, 1),
                'total_messages': total_messages,
                'active_agents': max(active_senders, active_receivers),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"   ❌ Summary error: {e}")
            return {}

def main():
    """Test REAL AI collaboration system"""
    print("🤝 AI COLLABORATION SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize system
    collab_system = AICollaborationSystem()
    
    # Test multi-agent discussion
    participants = ['marco_o1_finance', 'deepseek_r1_finance', 'cogito_finance']
    topic = "Should we buy Bitcoin at current price levels?"
    
    print(f"\n🗣️ Testing multi-agent discussion...")
    discussion_id = collab_system.start_discussion(topic, participants)
    
    if discussion_id:
        # Collect responses
        responses = collab_system.collect_agent_responses(discussion_id, topic, participants)
        
        # Build consensus
        if responses:
            consensus = collab_system.build_consensus(discussion_id, responses)
            
            if 'error' not in consensus:
                print(f"\n✅ Consensus achieved: {consensus['majority_position']}")
                print(f"   Agreement: {consensus['consensus_strength']}%")
                print(f"   Confidence: {consensus['average_confidence']}/10")
    
    # Test agent debate
    print(f"\n⚔️ Testing agent debate...")
    debate_result = collab_system.run_agent_debate(
        "Bitcoin will reach $150,000 by end of 2024",
        'marco_o1_finance',
        'cogito_finance'
    )
    
    if 'error' not in debate_result:
        print(f"   Winner: {debate_result['winner']}")
    
    # Get summary
    summary = collab_system.get_collaboration_summary()
    
    print(f"\n📊 COLLABORATION SUMMARY:")
    print(f"   Total discussions: {summary.get('total_discussions', 0)}")
    print(f"   Consensus rate: {summary.get('consensus_rate', 0):.1f}%")
    print(f"   Total messages: {summary.get('total_messages', 0)}")
    print(f"   Active agents: {summary.get('active_agents', 0)}")
    
    print(f"\n✅ AI COLLABORATION SYSTEM TEST COMPLETE")
    print(f"   🔍 Check 'ai_collaboration.db' for stored data")

if __name__ == "__main__":
    main()
