2025-06-06 00:26:57,144 - __main__ - INFO - Starting AI Agent Orchestration Service
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,147 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,148 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,197 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:27:28,245 - __main__ - INFO - AI Orchestration Service database initialized
2025-06-06 00:27:28,245 - __main__ - INFO - AI Agent Orchestration Service initialized
2025-06-06 00:27:28,246 - __main__ - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:27:28,951 - __main__ - INFO - Agent query successful
2025-06-06 00:27:28,952 - __main__ - INFO - Service test completed
2025-06-06 00:27:28,952 - __main__ - INFO - Service health: {'service': 'ai-agent-orchestration', 'status': 'healthy', 'total_agents': 16, 'healthy_agents': 16, 'fathom_r1_active': True, 'message_queue_connected': False, 'timestamp': '2025-06-06T00:27:28.952459'}
2025-06-06 00:27:28,952 - __main__ - INFO - Agent performance summary available
2025-06-06 00:29:24,181 - __main__ - INFO - Starting Microservices Orchestrator
2025-06-06 00:29:24,182 - __main__ - INFO - Microservices Orchestrator initialized
2025-06-06 00:29:24,182 - __main__ - INFO - Initializing microservices...
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,337 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,338 - ai_agent_orchestration_service - INFO - AI Orchestration Service database initialized
2025-06-06 00:29:40,338 - ai_agent_orchestration_service - INFO - AI Agent Orchestration Service initialized
2025-06-06 00:29:40,338 - ai_agent_orchestration_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:29:40,338 - __main__ - INFO - AI Agent Orchestration Service initialized
2025-06-06 00:29:40,339 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,339 - technical_analysis_service - INFO - Technical Analysis Service database initialized
2025-06-06 00:29:40,339 - technical_analysis_service - INFO - Technical Analysis Service initialized
2025-06-06 00:29:40,340 - technical_analysis_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:29:40,340 - __main__ - INFO - Technical Analysis Service initialized
2025-06-06 00:29:40,340 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,340 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,340 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,340 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,340 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,340 - advanced_features_service - INFO - Advanced Features Service database initialized
2025-06-06 00:29:40,340 - advanced_features_service - INFO - Advanced Features Service initialized
2025-06-06 00:29:40,341 - advanced_features_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:29:40,341 - __main__ - INFO - Advanced Features Service initialized
2025-06-06 00:29:40,341 - data_management_service - INFO - Discovered 27 databases
2025-06-06 00:29:40,341 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,342 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,343 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:29:40,344 - data_management_service - INFO - Data Management Service database initialized
2025-06-06 00:29:40,344 - data_management_service - INFO - Data Management Service initialized
2025-06-06 00:29:40,344 - data_management_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:29:40,344 - __main__ - INFO - Data Management Service initialized
2025-06-06 00:29:40,344 - __main__ - INFO - All microservices initialized successfully
2025-06-06 00:29:40,344 - __main__ - INFO - Processing orchestration request
2025-06-06 00:29:43,042 - technical_analysis_service - INFO - Technical analysis completed
2025-06-06 00:29:43,089 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:29:43,125 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:29:43,156 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:29:43,199 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:29:44,529 - ai_agent_orchestration_service - INFO - Agent query successful
2025-06-06 00:29:44,538 - __main__ - INFO - Orchestration request completed
2025-06-06 00:29:44,539 - __main__ - INFO - Orchestrator test completed
2025-06-06 00:29:44,539 - __main__ - INFO - Orchestrator health: healthy
2025-06-06 00:29:44,539 - __main__ - INFO - Microservices health: 4/4 services healthy
2025-06-06 00:30:54,738 - microservices_orchestrator - INFO - Microservices Orchestrator initialized
2025-06-06 00:30:54,739 - microservices_orchestrator - INFO - Initializing microservices...
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,364 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,365 - ai_agent_orchestration_service - INFO - AI Orchestration Service database initialized
2025-06-06 00:31:48,365 - ai_agent_orchestration_service - INFO - AI Agent Orchestration Service initialized
2025-06-06 00:31:48,366 - ai_agent_orchestration_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:31:48,366 - microservices_orchestrator - INFO - AI Agent Orchestration Service initialized
2025-06-06 00:31:48,366 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,366 - technical_analysis_service - INFO - Technical Analysis Service database initialized
2025-06-06 00:31:48,366 - technical_analysis_service - INFO - Technical Analysis Service initialized
2025-06-06 00:31:48,366 - technical_analysis_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:31:48,366 - microservices_orchestrator - INFO - Technical Analysis Service initialized
2025-06-06 00:31:48,367 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,368 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,368 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,368 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,368 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,368 - advanced_features_service - INFO - Advanced Features Service database initialized
2025-06-06 00:31:48,368 - advanced_features_service - INFO - Advanced Features Service initialized
2025-06-06 00:31:48,368 - advanced_features_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:31:48,368 - microservices_orchestrator - INFO - Advanced Features Service initialized
2025-06-06 00:31:48,369 - data_management_service - INFO - Discovered 28 databases
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,369 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,370 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,371 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,371 - circuit_breaker - INFO - Circuit breaker initialized
2025-06-06 00:31:48,371 - data_management_service - INFO - Data Management Service database initialized
2025-06-06 00:31:48,371 - data_management_service - INFO - Data Management Service initialized
2025-06-06 00:31:48,371 - data_management_service - INFO - RabbitMQ not available, using direct processing mode
2025-06-06 00:31:48,371 - microservices_orchestrator - INFO - Data Management Service initialized
2025-06-06 00:31:48,371 - microservices_orchestrator - INFO - All microservices initialized successfully
2025-06-06 00:31:48,372 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:31:51,161 - technical_analysis_service - INFO - Technical analysis completed
2025-06-06 00:31:51,207 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:51,243 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:51,274 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:51,320 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:52,411 - ai_agent_orchestration_service - INFO - Agent query successful
2025-06-06 00:31:52,423 - data_management_service - INFO - Data operation completed
2025-06-06 00:31:52,423 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:31:52,424 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:31:53,391 - ai_agent_orchestration_service - INFO - Agent query successful
2025-06-06 00:31:53,392 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:31:53,392 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:31:55,605 - technical_analysis_service - INFO - Technical analysis completed
2025-06-06 00:31:55,605 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:31:55,605 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:31:55,633 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:55,633 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:31:55,633 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:31:57,033 - technical_analysis_service - INFO - Technical analysis completed
2025-06-06 00:31:57,065 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:57,089 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:57,110 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:31:57,142 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:32:14,724 - ai_agent_orchestration_service - INFO - Agent query successful
2025-06-06 00:32:14,738 - data_management_service - INFO - Data operation completed
2025-06-06 00:32:14,738 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:32:14,739 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:32:28,139 - ai_agent_orchestration_service - INFO - Agent query successful
2025-06-06 00:32:28,139 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:32:28,140 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:32:30,310 - technical_analysis_service - INFO - Technical analysis completed
2025-06-06 00:32:30,311 - microservices_orchestrator - INFO - Orchestration request completed
2025-06-06 00:32:30,311 - microservices_orchestrator - INFO - Processing orchestration request
2025-06-06 00:32:30,353 - advanced_features_service - INFO - Feature processing completed
2025-06-06 00:32:30,353 - microservices_orchestrator - INFO - Orchestration request completed
