#!/usr/bin/env python3
"""
AI Agent Specialization System
REAL implementation of specialized AI agents with expertise domains and cross-consultation
"""

import subprocess
import time
import json
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from ai_technical_analysis_integration import AITechnicalAnalysisIntegration

class AIAgentSpecializationSystem:
    """REAL AI agent specialization with expertise domains"""
    
    def __init__(self):
        # Initialize technical analysis integration
        self.ta_integration = AITechnicalAnalysisIntegration()
        
        # Define REAL agent specializations
        self.agent_specializations = {
            'marco_o1_finance': {
                'name': 'Marco O1 - Scalping & Momentum Specialist',
                'expertise': 'scalping_momentum',
                'description': 'Expert in short-term trading, momentum analysis, and quick profit opportunities',
                'strengths': ['Scalping strategies', 'Momentum indicators', 'Quick decision making', 'Risk management'],
                'timeframes': ['1m', '5m', '15m'],
                'indicators': ['RSI', 'Stochastic', 'MACD', 'Williams %R'],
                'success_rate': 0.0,
                'total_trades': 0,
                'specialization_score': 0.0
            },
            'deepseek_r1_finance': {
                'name': 'DeepSeek R1 - Fundamental Analysis Expert',
                'expertise': 'fundamental_analysis',
                'description': 'Expert in fundamental analysis, company valuation, and long-term investment strategies',
                'strengths': ['Company analysis', 'Financial statements', 'Market research', 'Long-term trends'],
                'timeframes': ['1d', '1w', '1M'],
                'indicators': ['Volume', 'OBV', 'VWAP', 'Fibonacci'],
                'success_rate': 0.0,
                'total_trades': 0,
                'specialization_score': 0.0
            },
            'cogito_finance': {
                'name': 'Cogito - Risk Management Specialist',
                'expertise': 'risk_management',
                'description': 'Expert in risk assessment, position sizing, and portfolio protection strategies',
                'strengths': ['Risk assessment', 'Position sizing', 'Stop-loss strategies', 'Portfolio protection'],
                'timeframes': ['1h', '4h', '1d'],
                'indicators': ['ATR', 'Bollinger Bands', 'ADX', 'Parabolic SAR'],
                'success_rate': 0.0,
                'total_trades': 0,
                'specialization_score': 0.0
            },
            'phi4_finance': {
                'name': 'Phi4 - Options & Derivatives Expert',
                'expertise': 'options_derivatives',
                'description': 'Expert in options trading, derivatives, and complex financial instruments',
                'strengths': ['Options strategies', 'Volatility trading', 'Greeks analysis', 'Hedging strategies'],
                'timeframes': ['1h', '1d', '1w'],
                'indicators': ['Implied Volatility', 'ATR', 'Bollinger Bands', 'Stochastic'],
                'success_rate': 0.0,
                'total_trades': 0,
                'specialization_score': 0.0
            },
            'qwen3_finance': {
                'name': 'Qwen3 - Macro Economic Analyst',
                'expertise': 'macro_economics',
                'description': 'Expert in macroeconomic analysis, market cycles, and global financial trends',
                'strengths': ['Economic indicators', 'Market cycles', 'Global trends', 'Sector analysis'],
                'timeframes': ['1d', '1w', '1M'],
                'indicators': ['Economic data', 'Sector rotation', 'Currency correlations', 'Bond yields'],
                'success_rate': 0.0,
                'total_trades': 0,
                'specialization_score': 0.0
            }
        }
        
        # Setup database
        self._setup_database()
        
        print("🎯 AI AGENT SPECIALIZATION SYSTEM INITIALIZED")
        print(f"   🤖 Specialized agents: {len(self.agent_specializations)}")
        print(f"   📊 Expertise domains: 5 unique specializations")
        print(f"   🔄 Cross-consultation: READY")
        print(f"   📈 Performance tracking: ACTIVE")
    
    def _setup_database(self):
        """Setup REAL database for specialization tracking"""
        conn = sqlite3.connect('ai_specialization.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_specializations (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                expertise_domain TEXT,
                specialization_data TEXT,
                success_rate REAL,
                total_queries INTEGER,
                specialization_score REAL,
                last_updated DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS specialized_queries (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                expertise_used TEXT,
                query_type TEXT,
                query_text TEXT,
                response_text TEXT,
                response_time REAL,
                confidence_score REAL,
                specialization_relevance REAL,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cross_consultations (
                id INTEGER PRIMARY KEY,
                primary_agent TEXT,
                consulted_agents TEXT,
                consultation_topic TEXT,
                primary_response TEXT,
                consultation_responses TEXT,
                final_decision TEXT,
                consensus_strength REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ Specialization tracking database initialized")
    
    def get_agent_expertise(self, agent_name: str) -> Dict[str, Any]:
        """Get REAL agent expertise information"""
        
        if agent_name not in self.agent_specializations:
            return {'error': f'Agent {agent_name} not found'}
        
        return self.agent_specializations[agent_name]
    
    def create_specialized_prompt(self, agent_name: str, query: str, symbol: str) -> str:
        """Create REAL specialized prompt based on agent expertise"""
        
        expertise = self.get_agent_expertise(agent_name)
        if 'error' in expertise:
            return query
        
        # Get technical analysis for the symbol
        ta_analysis = self.ta_integration.ta_engine.get_complete_analysis(symbol, '1d')
        
        # Create specialized prompt based on expertise
        specialized_section = f"""
🎯 SPECIALIZED ANALYSIS REQUEST - {expertise['name']}

🔬 YOUR EXPERTISE: {expertise['description']}

💪 YOUR STRENGTHS:
{chr(10).join(f"- {strength}" for strength in expertise['strengths'])}

⏰ YOUR PREFERRED TIMEFRAMES: {', '.join(expertise['timeframes'])}

📊 YOUR KEY INDICATORS: {', '.join(expertise['indicators'])}
"""
        
        # Add expertise-specific analysis
        if expertise['expertise'] == 'scalping_momentum':
            specialized_section += f"""
🚀 SCALPING & MOMENTUM FOCUS:
- Current RSI: {ta_analysis.get('rsi', 0):.1f} (Look for oversold/overbought levels)
- Stochastic %K: {ta_analysis.get('stochastic', {}).get('k', 0):.1f} (Momentum confirmation)
- MACD: {ta_analysis.get('macd', {}).get('macd', 0):.4f} (Trend momentum)
- Williams %R: {ta_analysis.get('williams_r', 0):.1f} (Entry timing)

SCALPING STRATEGY FOCUS:
1. Identify quick momentum shifts
2. Set tight stop-losses (1-2% max)
3. Target 0.5-1% profits
4. Use 1-5 minute timeframes
5. High probability setups only
"""
        
        elif expertise['expertise'] == 'fundamental_analysis':
            specialized_section += f"""
📊 FUNDAMENTAL ANALYSIS FOCUS:
- Volume Analysis: Current volume vs average
- On-Balance Volume: {ta_analysis.get('obv', 0):,.0f}
- VWAP: ${ta_analysis.get('vwap', 0):,.2f} vs Current Price ${ta_analysis.get('current_price', 0):,.2f}
- Long-term trend analysis

FUNDAMENTAL STRATEGY FOCUS:
1. Analyze company/asset fundamentals
2. Consider market cap and valuation
3. Evaluate long-term growth prospects
4. Assess competitive advantages
5. Factor in economic environment
"""
        
        elif expertise['expertise'] == 'risk_management':
            specialized_section += f"""
🛡️ RISK MANAGEMENT FOCUS:
- ATR (Volatility): {ta_analysis.get('atr', 0):.4f}
- Bollinger Bands %B: {ta_analysis.get('bollinger_bands', {}).get('percent_b', 0):.3f}
- ADX (Trend Strength): {ta_analysis.get('adx', {}).get('adx', 0):.1f}
- Parabolic SAR: ${ta_analysis.get('parabolic_sar', 0):,.2f}

RISK MANAGEMENT STRATEGY:
1. Calculate position size based on risk tolerance
2. Set appropriate stop-loss levels
3. Determine risk-reward ratios
4. Assess portfolio correlation
5. Monitor maximum drawdown limits
"""
        
        elif expertise['expertise'] == 'options_derivatives':
            specialized_section += f"""
📈 OPTIONS & DERIVATIVES FOCUS:
- Implied Volatility Analysis (use ATR as proxy): {ta_analysis.get('atr', 0):.4f}
- Bollinger Band Width: {ta_analysis.get('bollinger_bands', {}).get('bandwidth', 0):.2f}
- Stochastic for timing: %K {ta_analysis.get('stochastic', {}).get('k', 0):.1f}

OPTIONS STRATEGY FOCUS:
1. Analyze implied vs historical volatility
2. Consider time decay effects
3. Evaluate Greeks (Delta, Gamma, Theta, Vega)
4. Assess volatility expansion/contraction
5. Design appropriate options strategies
"""
        
        elif expertise['expertise'] == 'macro_economics':
            specialized_section += f"""
🌍 MACRO ECONOMIC FOCUS:
- Market Cycle Analysis
- Sector Rotation Indicators
- Economic Environment Assessment
- Global Market Correlations

MACRO STRATEGY FOCUS:
1. Analyze current economic cycle phase
2. Consider Federal Reserve policy impact
3. Evaluate global market conditions
4. Assess sector rotation opportunities
5. Factor in geopolitical events
"""
        
        # Create final specialized prompt
        specialized_prompt = f"""{query}

{specialized_section}

🎯 SPECIALIZED RESPONSE REQUIRED:
As the {expertise['name']}, provide your expert analysis focusing on your specialization. 
Include specific recommendations based on your expertise domain.

FORMAT YOUR RESPONSE:
SPECIALIZATION: {expertise['expertise']}
DECISION: [BUY/SELL/HOLD]
CONFIDENCE: [1-10]
EXPERT_REASONING: [Your specialized analysis]
RISK_ASSESSMENT: [Risk factors specific to your expertise]
RECOMMENDED_ACTION: [Specific action based on your specialization]
"""
        
        return specialized_prompt
    
    def query_specialist_agent(self, agent_name: str, query: str, symbol: str) -> Dict[str, Any]:
        """Query REAL specialist agent with expertise-focused prompt"""
        
        if agent_name not in self.agent_specializations:
            return {'success': False, 'error': f'Agent {agent_name} not found'}
        
        expertise = self.agent_specializations[agent_name]
        
        print(f"\n🎯 SPECIALIST QUERY: {expertise['name']}")
        print(f"   Expertise: {expertise['expertise']}")
        
        # Create specialized prompt
        specialized_prompt = self.create_specialized_prompt(agent_name, query, symbol)
        
        # Execute query
        start_time = time.time()
        
        try:
            model = self.ta_integration.ai_agents[agent_name]
            result = subprocess.run([
                'ollama', 'run', model, specialized_prompt
            ], capture_output=True, text=True, timeout=120, encoding='utf-8', errors='ignore')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Parse specialized decision
                decision = self._parse_specialized_decision(response)
                
                # Calculate specialization relevance
                relevance = self._calculate_specialization_relevance(response, expertise)
                
                # Store specialized query
                self._store_specialized_query(agent_name, expertise['expertise'], query, 
                                            response, response_time, decision, relevance)
                
                # Update agent performance
                self._update_agent_performance(agent_name, decision, relevance)
                
                specialist_result = {
                    'agent_name': agent_name,
                    'specialization': expertise['expertise'],
                    'success': True,
                    'response': response,
                    'response_time': response_time,
                    'response_length': len(response),
                    'decision': decision,
                    'specialization_relevance': relevance,
                    'expertise_used': expertise['strengths'],
                    'timestamp': datetime.now()
                }
                
                print(f"   ✅ Specialist response: {response_time:.1f}s ({len(response)} chars)")
                print(f"   🎯 Specialization relevance: {relevance:.1f}/10")
                if decision:
                    print(f"   📊 Decision: {decision.get('action', 'UNKNOWN')} (confidence: {decision.get('confidence', 0)})")
                
                return specialist_result
            else:
                return {
                    'success': False,
                    'error': result.stderr.strip() or 'Unknown error',
                    'response_time': response_time
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': time.time() - start_time
            }
    
    def cross_consult_specialists(self, primary_agent: str, topic: str, symbol: str,
                                 consultants: Optional[List[str]] = None) -> Dict[str, Any]:
        """REAL cross-consultation between specialist agents"""
        
        if consultants is None:
            # Select 2-3 other specialists
            consultants = [agent for agent in self.agent_specializations.keys() 
                          if agent != primary_agent][:3]
        
        print(f"\n🤝 CROSS-CONSULTATION: {primary_agent} consulting {len(consultants)} specialists")
        
        # Get primary agent's analysis
        primary_result = self.query_specialist_agent(primary_agent, topic, symbol)
        
        # Get consultant responses
        consultant_results = {}
        for consultant in consultants:
            consultant_query = f"CONSULTATION REQUEST: {topic}\n\nProvide your expert opinion from your specialization perspective."
            consultant_result = self.query_specialist_agent(consultant, consultant_query, symbol)
            consultant_results[consultant] = consultant_result
        
        # Synthesize consultation
        synthesis = self._synthesize_consultation(primary_result, consultant_results, topic)
        
        # Store cross-consultation
        self._store_cross_consultation(primary_agent, consultants, topic, 
                                     primary_result, consultant_results, synthesis)
        
        return {
            'primary_agent': primary_agent,
            'consultants': consultants,
            'topic': topic,
            'symbol': symbol,
            'primary_response': primary_result,
            'consultant_responses': consultant_results,
            'synthesis': synthesis,
            'timestamp': datetime.now()
        }
    
    def _parse_specialized_decision(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse REAL specialized decision"""
        
        decision = {}
        response_upper = response.upper()
        
        # Extract decision
        if 'DECISION:' in response_upper:
            if 'BUY' in response_upper and 'SELL' not in response_upper:
                decision['action'] = 'BUY'
            elif 'SELL' in response_upper and 'BUY' not in response_upper:
                decision['action'] = 'SELL'
            else:
                decision['action'] = 'HOLD'
        
        # Extract confidence
        import re
        conf_match = re.search(r'CONFIDENCE:\s*(\d+)', response_upper)
        if conf_match:
            decision['confidence'] = int(conf_match.group(1))
        else:
            decision['confidence'] = 5
        
        # Extract specialization
        spec_match = re.search(r'SPECIALIZATION:\s*([A-Z_]+)', response_upper)
        if spec_match:
            decision['specialization'] = spec_match.group(1)
        
        return decision if 'action' in decision else None
    
    def _calculate_specialization_relevance(self, response: str, expertise: Dict[str, Any]) -> float:
        """Calculate REAL specialization relevance score"""
        
        relevance_score = 0.0
        response_lower = response.lower()
        
        # Check for expertise keywords
        for strength in expertise['strengths']:
            if strength.lower() in response_lower:
                relevance_score += 2.0
        
        # Check for indicator mentions
        for indicator in expertise['indicators']:
            if indicator.lower() in response_lower:
                relevance_score += 1.5
        
        # Check for timeframe mentions
        for timeframe in expertise['timeframes']:
            if timeframe in response_lower:
                relevance_score += 1.0
        
        # Check for specialization-specific terms
        specialization_terms = {
            'scalping_momentum': ['scalping', 'momentum', 'quick', 'short-term', 'fast'],
            'fundamental_analysis': ['fundamental', 'valuation', 'company', 'long-term', 'financial'],
            'risk_management': ['risk', 'stop-loss', 'position size', 'volatility', 'drawdown'],
            'options_derivatives': ['options', 'volatility', 'derivatives', 'greeks', 'hedging'],
            'macro_economics': ['economic', 'macro', 'cycle', 'federal', 'global']
        }
        
        if expertise['expertise'] in specialization_terms:
            for term in specialization_terms[expertise['expertise']]:
                if term in response_lower:
                    relevance_score += 1.0
        
        # Normalize to 1-10 scale
        max_possible = len(expertise['strengths']) * 2 + len(expertise['indicators']) * 1.5 + len(expertise['timeframes']) + 5
        normalized_score = min(10.0, (relevance_score / max_possible) * 10)
        
        return round(normalized_score, 1)
    
    def _synthesize_consultation(self, primary_result: Dict[str, Any], 
                               consultant_results: Dict[str, Dict[str, Any]], 
                               topic: str) -> Dict[str, Any]:
        """Synthesize REAL consultation results"""
        
        # Collect all decisions
        decisions = []
        confidences = []
        
        if primary_result.get('success') and primary_result.get('decision'):
            decisions.append(primary_result['decision']['action'])
            confidences.append(primary_result['decision']['confidence'])
        
        for consultant_result in consultant_results.values():
            if consultant_result.get('success') and consultant_result.get('decision'):
                decisions.append(consultant_result['decision']['action'])
                confidences.append(consultant_result['decision']['confidence'])
        
        # Calculate consensus
        if decisions:
            decision_counts = {}
            for decision in decisions:
                decision_counts[decision] = decision_counts.get(decision, 0) + 1
            
            consensus_decision = max(decision_counts.items(), key=lambda x: x[1])
            consensus_strength = consensus_decision[1] / len(decisions)
            avg_confidence = sum(confidences) / len(confidences)
            
            return {
                'consensus_decision': consensus_decision[0],
                'consensus_strength': round(consensus_strength, 2),
                'average_confidence': round(avg_confidence, 1),
                'total_consultants': len(consultant_results) + 1,
                'decision_breakdown': decision_counts
            }
        
        return {'error': 'No valid decisions to synthesize'}
    
    def _store_specialized_query(self, agent_name: str, expertise: str, query: str,
                               response: str, response_time: float, decision: Optional[Dict[str, Any]],
                               relevance: float):
        """Store REAL specialized query"""
        
        try:
            conn = sqlite3.connect('ai_specialization.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO specialized_queries 
                (agent_name, expertise_used, query_type, query_text, response_text,
                 response_time, confidence_score, specialization_relevance, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (agent_name, expertise, 'specialist_consultation', query, response,
                  response_time, decision.get('confidence', 0) if decision else 0,
                  relevance, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Specialized query storage error: {e}")
    
    def _store_cross_consultation(self, primary_agent: str, consultants: List[str], topic: str,
                                primary_result: Dict[str, Any], consultant_results: Dict[str, Dict[str, Any]],
                                synthesis: Dict[str, Any]):
        """Store REAL cross-consultation"""
        
        try:
            conn = sqlite3.connect('ai_specialization.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO cross_consultations 
                (primary_agent, consulted_agents, consultation_topic, primary_response,
                 consultation_responses, final_decision, consensus_strength, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (primary_agent, json.dumps(consultants), topic,
                  json.dumps(primary_result, default=str),
                  json.dumps(consultant_results, default=str),
                  synthesis.get('consensus_decision', ''),
                  synthesis.get('consensus_strength', 0),
                  datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ Cross-consultation storage error: {e}")
    
    def _update_agent_performance(self, agent_name: str, decision: Optional[Dict[str, Any]], relevance: float):
        """Update REAL agent performance metrics"""
        
        if agent_name in self.agent_specializations:
            agent = self.agent_specializations[agent_name]
            agent['total_trades'] += 1
            
            # Update specialization score based on relevance
            if agent['specialization_score'] == 0:
                agent['specialization_score'] = relevance
            else:
                agent['specialization_score'] = (agent['specialization_score'] * 0.8) + (relevance * 0.2)

def main():
    """Test REAL AI agent specialization system"""
    print("🎯 AI AGENT SPECIALIZATION SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize specialization system
    specialization = AIAgentSpecializationSystem()
    
    # Test specialist queries
    test_query = "Analyze this trading opportunity and provide your expert recommendation."
    symbols = ['BTC-USD', 'AAPL']
    
    for symbol in symbols:
        print(f"\n📊 Testing specialist analysis for {symbol}...")
        
        # Test each specialist
        for agent_name in list(specialization.agent_specializations.keys())[:3]:
            specialist_result = specialization.query_specialist_agent(agent_name, test_query, symbol)

            if specialist_result['success']:
                decision = specialist_result.get('decision', {})
                if decision:
                    print(f"   ✅ {agent_name}: {decision.get('action', 'UNKNOWN')} "
                          f"(confidence: {decision.get('confidence', 0)}/10, "
                          f"relevance: {specialist_result.get('specialization_relevance', 0)}/10)")
                else:
                    print(f"   ⚠️ {agent_name}: No decision parsed "
                          f"(relevance: {specialist_result.get('specialization_relevance', 0)}/10)")
            else:
                print(f"   ❌ {agent_name}: {specialist_result.get('error', 'Unknown error')}")
    
    # Test cross-consultation
    print(f"\n🤝 Testing cross-consultation...")
    consultation = specialization.cross_consult_specialists(
        'marco_o1_finance', 
        'Should we enter a position in Bitcoin right now?',
        'BTC-USD'
    )
    
    if 'synthesis' in consultation and 'error' not in consultation['synthesis']:
        synthesis = consultation['synthesis']
        print(f"   🎯 Consensus: {synthesis['consensus_decision']}")
        print(f"   💪 Strength: {synthesis['consensus_strength']:.1%}")
        print(f"   🔢 Confidence: {synthesis['average_confidence']:.1f}/10")
        print(f"   👥 Consultants: {synthesis['total_consultants']}")
    
    print(f"\n✅ AI AGENT SPECIALIZATION SYSTEM TEST COMPLETE")
    print(f"   🔍 Check 'ai_specialization.db' for specialization data")
    print(f"   🎯 All agents now have specialized expertise domains")
    print(f"   🤝 Cross-consultation system operational")

if __name__ == "__main__":
    main()
