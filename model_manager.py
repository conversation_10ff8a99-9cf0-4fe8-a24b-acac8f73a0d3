#!/usr/bin/env python3
"""
Model Manager - Organize and use your 60+ models effectively
Step-by-step approach to manage your AI models
"""

import subprocess
import json
import os
from datetime import datetime

class ModelManager:
    def __init__(self):
        self.models = []
        self.organized_models = {
            'top_performers': [],
            'finance_models': [],
            'unrestricted_models': [],
            'base_models': [],
            'redundant_models': []
        }
    
    def step1_discover_models(self):
        """Step 1: Get all your models"""
        print("STEP 1: Discovering your models...")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]
                self.models = []
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        model_info = {
                            'name': parts[0],
                            'id': parts[1] if len(parts) > 1 else '',
                            'size': parts[2] if len(parts) > 2 else '',
                            'modified': ' '.join(parts[3:]) if len(parts) > 3 else ''
                        }
                        self.models.append(model_info)
                
                print(f"✅ Found {len(self.models)} models")
                return True
            else:
                print("❌ Failed to get models")
                return False
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def step2_organize_models(self):
        """Step 2: Organize models by type and usefulness"""
        print("\nSTEP 2: Organizing models...")
        
        for model in self.models:
            name = model['name'].lower()
            
            # Finance models
            if 'finance' in name:
                self.organized_models['finance_models'].append(model)
            
            # Unrestricted models
            elif any(x in name for x in ['unrestricted', 'liberated']):
                self.organized_models['unrestricted_models'].append(model)
            
            # Base models (original, clean names)
            elif ':' in name and not any(x in name for x in ['noryon', 'enhanced', 'smart', 'expert']):
                self.organized_models['base_models'].append(model)
            
            # Everything else (likely redundant)
            else:
                self.organized_models['redundant_models'].append(model)
        
        # Identify top performers (recent, good models)
        top_candidates = []
        for model in self.models:
            name = model['name'].lower()
            if any(x in name for x in ['deepseek-r1', 'phi4-reasoning', 'qwen3', 'gemma3']):
                if 'hours ago' in model['modified']:
                    top_candidates.append(model)
        
        self.organized_models['top_performers'] = top_candidates[:5]
        
        print("✅ Models organized:")
        for category, models in self.organized_models.items():
            print(f"  {category}: {len(models)} models")
    
    def step3_show_organization(self):
        """Step 3: Show organized models"""
        print("\nSTEP 3: Your organized models:")
        print("=" * 50)
        
        for category, models in self.organized_models.items():
            if models:
                print(f"\n{category.upper().replace('_', ' ')} ({len(models)}):")
                for i, model in enumerate(models[:5], 1):  # Show first 5
                    print(f"  {i}. {model['name']} ({model['size']})")
                if len(models) > 5:
                    print(f"  ... and {len(models) - 5} more")
    
    def step4_test_top_models(self):
        """Step 4: Test your top models"""
        print("\nSTEP 4: Testing top models...")
        
        test_question = "Explain compound interest in simple terms"
        top_models = self.organized_models['top_performers'][:3]
        
        if not top_models:
            print("No top models to test")
            return
        
        results = {}
        for model in top_models:
            print(f"\nTesting {model['name']}...")
            
            try:
                result = subprocess.run([
                    'ollama', 'run', model['name'], test_question
                ], capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    results[model['name']] = {
                        'success': True,
                        'length': len(response),
                        'preview': response[:100] + "..."
                    }
                    print(f"✅ Success - {len(response)} chars")
                else:
                    results[model['name']] = {'success': False, 'error': 'Failed'}
                    print("❌ Failed")
                    
            except subprocess.TimeoutExpired:
                results[model['name']] = {'success': False, 'error': 'Timeout'}
                print("⏰ Timeout")
            except Exception as e:
                results[model['name']] = {'success': False, 'error': str(e)}
                print(f"❌ Error: {e}")
        
        # Show results
        print("\nTest Results:")
        for model_name, result in results.items():
            if result['success']:
                print(f"✅ {model_name}: {result['length']} chars")
            else:
                print(f"❌ {model_name}: {result['error']}")
        
        return results
    
    def step5_recommend_best_models(self):
        """Step 5: Recommend which models to actually use"""
        print("\nSTEP 5: Recommendations for daily use:")
        print("=" * 50)
        
        recommendations = {
            "For Finance/Trading": [],
            "For General Use": [],
            "For Unrestricted Tasks": [],
            "For Quick Tasks": []
        }
        
        # Finance recommendations
        finance_models = self.organized_models['finance_models']
        if finance_models:
            # Prefer recent, larger models
            best_finance = sorted(finance_models, 
                                key=lambda x: ('hours ago' in x['modified'], x['size']), 
                                reverse=True)[:2]
            recommendations["For Finance/Trading"] = [m['name'] for m in best_finance]
        
        # General use recommendations
        top_models = self.organized_models['top_performers']
        if top_models:
            recommendations["For General Use"] = [m['name'] for m in top_models[:2]]
        
        # Unrestricted recommendations
        unrestricted = self.organized_models['unrestricted_models']
        if unrestricted:
            # Prefer larger, recent models
            best_unrestricted = sorted(unrestricted,
                                     key=lambda x: ('hours ago' in x['modified'], x['size']),
                                     reverse=True)[:2]
            recommendations["For Unrestricted Tasks"] = [m['name'] for m in best_unrestricted]
        
        # Quick task recommendations (smaller models)
        base_models = self.organized_models['base_models']
        small_models = [m for m in base_models if 'GB' in m['size'] and float(m['size'].split()[0]) < 6]
        if small_models:
            recommendations["For Quick Tasks"] = [m['name'] for m in small_models[:2]]
        
        # Display recommendations
        for category, models in recommendations.items():
            if models:
                print(f"\n{category}:")
                for model in models:
                    print(f"  • {model}")
        
        return recommendations
    
    def step6_create_quick_launcher(self, recommendations):
        """Step 6: Create a quick launcher for your best models"""
        print("\nSTEP 6: Creating quick launcher...")
        
        # Flatten all recommended models
        all_recommended = []
        for models in recommendations.values():
            all_recommended.extend(models)
        
        # Remove duplicates while preserving order
        unique_recommended = []
        for model in all_recommended:
            if model not in unique_recommended:
                unique_recommended.append(model)
        
        launcher_script = f'''#!/usr/bin/env python3
"""
Quick Model Launcher - Your best models
Generated on {datetime.now().strftime("%Y-%m-%d %H:%M")}
"""

import subprocess

def chat_with_model(model_name):
    print(f"\\nChatting with {{model_name}}")
    print("Type 'quit' to exit\\n")
    
    while True:
        try:
            question = input("You: ")
            if question.lower() in ['quit', 'q', 'exit']:
                break
            
            print(f"\\n{{model_name}}: ", end="", flush=True)
            subprocess.run(['ollama', 'run', model_name, question])
            print()
            
        except KeyboardInterrupt:
            break

def main():
    print("Quick Model Launcher")
    print("Your best models:")
    
    models = {repr(unique_recommended)}
    
    for i, model in enumerate(models, 1):
        print(f"{{i}}. {{model}}")
    
    while True:
        try:
            choice = input(f"\\nPick model (1-{{len(models)}}) or 'q' to quit: ")
            
            if choice.lower() == 'q':
                break
            
            num = int(choice) - 1
            if 0 <= num < len(models):
                chat_with_model(models[num])
            else:
                print("Invalid choice")
                
        except ValueError:
            print("Enter a number")
        except KeyboardInterrupt:
            break

if __name__ == "__main__":
    main()
'''
        
        with open('quick_launcher.py', 'w') as f:
            f.write(launcher_script)
        
        print("✅ Created quick_launcher.py")
        print("Run with: python quick_launcher.py")
    
    def run_full_analysis(self):
        """Run complete step-by-step analysis"""
        print("MODEL MANAGER - STEP BY STEP ANALYSIS")
        print("=" * 50)
        
        if not self.step1_discover_models():
            return
        
        self.step2_organize_models()
        self.step3_show_organization()
        self.step4_test_top_models()
        recommendations = self.step5_recommend_best_models()
        self.step6_create_quick_launcher(recommendations)
        
        print("\n" + "=" * 50)
        print("ANALYSIS COMPLETE!")
        print("Next steps:")
        print("1. Use quick_launcher.py for daily model access")
        print("2. Focus on recommended models")
        print("3. Consider removing redundant models")

def main():
    manager = ModelManager()
    manager.run_full_analysis()

if __name__ == "__main__":
    main()
