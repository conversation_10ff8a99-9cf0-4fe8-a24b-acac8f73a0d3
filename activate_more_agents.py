#!/usr/bin/env python3
"""
Activate More AI Agents
Test and activate additional powerful reasoning models
"""

import subprocess
import time
from datetime import datetime

def test_additional_models():
    """Test additional powerful models from your collection"""
    
    additional_models = {
        # More reasoning models to test
        'gemma3_enhanced': {
            'model': 'unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest:latest',
            'specialty': 'Enhanced reasoning and pattern recognition',
            'size': '8.1 GB'
        },
        'exaone_deep': {
            'model': 'unrestricted-exaone-deep-7.8b:latest', 
            'specialty': 'Fast deep reasoning',
            'size': '4.8 GB'
        },
        'granite_dense': {
            'model': 'unrestricted-granite3.1-dense-8b:latest',
            'specialty': 'Structured dense reasoning',
            'size': '5.0 GB'
        },
        'falcon3_finance': {
            'model': 'unrestricted-noryon-falcon3-finance-v1-latest:latest',
            'specialty': 'Financial analysis with Falcon architecture',
            'size': '6.3 GB'
        },
        'dolphin3_finance': {
            'model': 'unrestricted-noryon-dolphin3-finance-v2-latest:latest',
            'specialty': 'Financial reasoning with Dolphin architecture',
            'size': '4.9 GB'
        },
        'cogito_finance': {
            'model': 'unrestricted-noryon-cogito-finance-v2-latest:latest',
            'specialty': 'Cognitive financial analysis',
            'size': '9.0 GB'
        }
    }
    
    print("🧪 TESTING ADDITIONAL POWERFUL MODELS")
    print("=" * 60)
    
    working_models = []
    failed_models = []
    
    for model_id, model_info in additional_models.items():
        print(f"\n🤖 Testing {model_id}...")
        print(f"   Model: {model_info['model']}")
        print(f"   Specialty: {model_info['specialty']}")
        
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_info['model'], 
                "Quick test: Analyze Bitcoin at $32000. BUY/SELL/HOLD? Brief reasoning."
            ], capture_output=True, text=True, timeout=45, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                working_models.append({
                    'id': model_id,
                    'model': model_info['model'],
                    'specialty': model_info['specialty'],
                    'size': model_info['size'],
                    'response_time': response_time,
                    'response_length': len(response),
                    'sample_response': response[:200] + "..." if len(response) > 200 else response
                })
                print(f"   ✅ SUCCESS: {response_time:.1f}s, {len(response)} chars")
            else:
                failed_models.append(model_id)
                print(f"   ❌ FAILED: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            failed_models.append(model_id)
            print(f"   ⏰ TIMEOUT: 45s")
        except Exception as e:
            failed_models.append(model_id)
            print(f"   ❌ ERROR: {e}")
    
    return working_models, failed_models

def create_agent_command_center():
    """Create a command center for your working agents"""
    
    print("\n🎯 CREATING AGENT COMMAND CENTER")
    print("=" * 50)
    
    # Your confirmed working agents
    working_agents = {
        'deepseek_r1': {
            'model': 'unrestricted-deepseek-r1-14b:latest',
            'specialty': 'Deep logical reasoning',
            'use_for': 'Complex analysis, step-by-step reasoning, problem solving'
        },
        'marco_o1': {
            'model': 'unrestricted-marco-o1-7b:latest', 
            'specialty': 'Strategic reasoning',
            'use_for': 'Strategic planning, quick insights, decision making'
        },
        'deepseek_finance': {
            'model': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
            'specialty': 'Financial reasoning',
            'use_for': 'Financial analysis, risk assessment, trading decisions'
        }
    }
    
    command_center_code = '''#!/usr/bin/env python3
"""
AI Agent Command Center
Quick access to your best working AI agents
"""

import subprocess
import time

class AgentCommandCenter:
    def __init__(self):
        self.agents = {
            'deepseek_r1': 'unrestricted-deepseek-r1-14b:latest',
            'marco_o1': 'unrestricted-marco-o1-7b:latest',
            'deepseek_finance': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest'
        }
    
    def query_agent(self, agent_name, question):
        """Query a specific agent"""
        if agent_name not in self.agents:
            print(f"❌ Unknown agent: {agent_name}")
            print(f"Available agents: {list(self.agents.keys())}")
            return None
        
        model = self.agents[agent_name]
        print(f"🤖 Querying {agent_name}...")
        
        start_time = time.time()
        try:
            result = subprocess.run([
                'ollama', 'run', model, question
            ], capture_output=True, text=True, timeout=60, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                print(f"✅ Response received in {response_time:.1f}s")
                return response
            else:
                print(f"❌ Error: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Timeout after 60s")
            return None
        except Exception as e:
            print(f"❌ Exception: {e}")
            return None
    
    def multi_agent_analysis(self, question):
        """Get analysis from multiple agents"""
        print(f"\\n🧠 MULTI-AGENT ANALYSIS")
        print(f"Question: {question}")
        print("=" * 60)
        
        responses = {}
        for agent_name in self.agents:
            response = self.query_agent(agent_name, question)
            if response:
                responses[agent_name] = response
                print(f"\\n📝 {agent_name.upper()} RESPONSE:")
                print(response[:500] + "..." if len(response) > 500 else response)
                print("-" * 40)
        
        return responses

# Quick usage examples
if __name__ == "__main__":
    center = AgentCommandCenter()
    
    # Example 1: Single agent query
    print("EXAMPLE 1: Single Agent Query")
    response = center.query_agent('deepseek_r1', 'Should I buy Bitcoin at $32000? Explain your reasoning.')
    
    # Example 2: Multi-agent analysis
    print("\\nEXAMPLE 2: Multi-Agent Analysis")
    responses = center.multi_agent_analysis('Analyze the risk of investing $50000 in crypto right now.')
'''
    
    with open('agent_command_center.py', 'w', encoding='utf-8') as f:
        f.write(command_center_code)
    
    print("✅ Created agent_command_center.py")
    print("\nUSAGE:")
    print("   python agent_command_center.py")
    print("   # Or import and use in your code:")
    print("   from agent_command_center import AgentCommandCenter")
    print("   center = AgentCommandCenter()")
    print("   response = center.query_agent('deepseek_r1', 'Your question')")

def main():
    """Main activation process"""
    print("🚀 ACTIVATE MORE AI AGENTS - PHASE 4B")
    print("=" * 60)
    
    # Test additional models
    working_models, failed_models = test_additional_models()
    
    print(f"\n📊 ADDITIONAL MODEL RESULTS:")
    print(f"   Working models: {len(working_models)}")
    print(f"   Failed models: {len(failed_models)}")
    
    if working_models:
        print(f"\n✅ NEW WORKING MODELS:")
        for model in working_models:
            print(f"   {model['id']}: {model['response_time']:.1f}s, {model['specialty']}")
    
    if failed_models:
        print(f"\n❌ FAILED MODELS:")
        for model_id in failed_models:
            print(f"   {model_id}")
    
    # Create command center
    create_agent_command_center()
    
    # Summary
    total_working = 3 + len(working_models)  # 3 from previous test + new ones
    
    print(f"\n🎉 ACTIVATION COMPLETE!")
    print(f"   Total working agents: {total_working}")
    print(f"   Command center created: agent_command_center.py")
    print(f"   Ready for: Deep reasoning, Strategic planning, Financial analysis")
    
    print(f"\n🎯 NEXT STEPS:")
    print(f"   1. Use agent_command_center.py for quick queries")
    print(f"   2. Integrate with your trading system")
    print(f"   3. Test with real market analysis")
    print(f"   4. Scale up to more agents as needed")

if __name__ == "__main__":
    main()
