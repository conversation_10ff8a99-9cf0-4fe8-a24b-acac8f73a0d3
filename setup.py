#!/usr/bin/env python3
"""
Noryon AI Trading System - Setup Script

This script handles the installation and setup of the Noryon AI Trading System.
It installs dependencies, sets up the environment, and prepares the system for use.
"""

from setuptools import setup, find_packages
import os
import sys
from pathlib import Path

# Read the README file
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8')

# Read requirements
requirements_file = this_directory / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
else:
    requirements = [
        # Core dependencies
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "python-multipart>=0.0.6",
        "websockets>=12.0",
        
        # AI/ML dependencies
        "torch>=2.1.0",
        "transformers>=4.36.0",
        "datasets>=2.15.0",
        "accelerate>=0.25.0",
        "peft>=0.7.0",
        "bitsandbytes>=0.41.0",
        "scikit-learn>=1.3.0",
        "numpy>=1.24.0",
        "pandas>=2.1.0",
        
        # Financial data
        "yfinance>=0.2.0",
        "alpha-vantage>=2.3.0",
        "quandl>=3.7.0",
        "ta-lib>=0.4.0",
        
        # Data processing
        "scipy>=1.11.0",
        "matplotlib>=3.8.0",
        "seaborn>=0.13.0",
        "plotly>=5.17.0",
        "pyarrow>=14.0.0",
        
        # Async and concurrency
        "aiohttp>=3.9.0",
        "aiofiles>=23.2.0",
        "asyncio-mqtt>=0.16.0",
        
        # Configuration and utilities
        "pyyaml>=6.0.1",
        "python-dotenv>=1.0.0",
        "click>=8.1.0",
        "rich>=13.7.0",
        "tqdm>=4.66.0",
        
        # MLflow
        "mlflow>=2.8.0",
        "mlflow-skinny>=2.8.0",
        
        # Database
        "sqlalchemy>=2.0.0",
        "alembic>=1.13.0",
        "psycopg2-binary>=2.9.0",
        "redis>=5.0.0",
        
        # Security
        "cryptography>=41.0.0",
        "python-jose[cryptography]>=3.3.0",
        "passlib[bcrypt]>=1.7.0",
        
        # HTTP and API
        "httpx>=0.25.0",
        "requests>=2.31.0",
        "starlette>=0.27.0",
        
        # Monitoring and logging
        "prometheus-client>=0.19.0",
        "structlog>=23.2.0",
        "sentry-sdk>=1.38.0",
        
        # Testing
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
        "httpx>=0.25.0",
        
        # Development
        "black>=23.11.0",
        "isort>=5.12.0",
        "flake8>=6.1.0",
        "mypy>=1.7.0",
        "pre-commit>=3.6.0",
    ]

# Development dependencies
dev_requirements = [
    "jupyter>=1.0.0",
    "notebook>=7.0.0",
    "ipywidgets>=8.1.0",
    "jupyterlab>=4.0.0",
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
]

# Optional dependencies for specific features
extras_require = {
    "dev": dev_requirements,
    "docs": [
        "sphinx>=7.2.0",
        "sphinx-rtd-theme>=1.3.0",
        "mkdocs>=1.5.0",
        "mkdocs-material>=9.4.0",
    ],
    "gpu": [
        "torch>=2.1.0+cu118",
        "torchvision>=0.16.0+cu118",
        "torchaudio>=2.1.0+cu118",
    ],
    "monitoring": [
        "grafana-api>=1.0.3",
        "prometheus-api-client>=0.5.3",
    ],
    "deployment": [
        "docker>=6.1.0",
        "kubernetes>=28.1.0",
        "helm>=3.13.0",
    ],
    "all": dev_requirements + [
        "grafana-api>=1.0.3",
        "prometheus-api-client>=0.5.3",
        "docker>=6.1.0",
        "kubernetes>=28.1.0",
    ]
}

setup(
    name="noryon-ai-trading",
    version="1.0.0",
    author="Noryon AI Team",
    author_email="<EMAIL>",
    description="Advanced AI-powered trading system with ensemble learning and risk management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/noryon-ai/trading-system",
    project_urls={
        "Bug Tracker": "https://github.com/noryon-ai/trading-system/issues",
        "Documentation": "https://docs.noryon.ai/trading-system",
        "Source Code": "https://github.com/noryon-ai/trading-system",
    },
    packages=find_packages(exclude=["tests", "tests.*", "docs", "docs.*"]),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial :: Investment",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require=extras_require,
    entry_points={
        "console_scripts": [
            "noryon=main:main",
            "noryon-train=training_pipeline:main",
            "noryon-backtest=backtesting_framework:main",
            "noryon-deploy=automated_deployment_pipeline:main",
            "noryon-api=api_server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "noryon": [
            "config/*.yaml",
            "config/*.yml",
            "config/*.json",
            "templates/*.html",
            "static/*",
            "data/sample/*",
        ],
    },
    zip_safe=False,
    keywords=[
        "trading", "ai", "machine-learning", "finance", "algorithmic-trading",
        "risk-management", "portfolio-optimization", "backtesting", "ensemble-learning",
        "quantitative-finance", "fintech", "investment", "automation"
    ],
    platforms=["any"],
    license="MIT",
)


def post_install_setup():
    """
    Perform post-installation setup tasks.
    """
    print("Setting up Noryon AI Trading System...")
    
    # Create necessary directories
    directories = [
        "data", "models", "logs", "reports", "config", "temp",
        "mlruns", "artifacts", "checkpoints", "static", "templates"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Create default configuration if it doesn't exist
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("Creating default configuration file...")
        # The config.yaml should already be created by the previous step
        pass
    
    # Set up logging directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Create initial log file
    (log_dir / "noryon.log").touch()
    (log_dir / "main.log").touch()
    (log_dir / "api.log").touch()
    (log_dir / "training.log").touch()
    
    print("Setup completed successfully!")
    print("")
    print("Next steps:")
    print("1. Review and customize config.yaml")
    print("2. Install additional dependencies if needed:")
    print("   pip install -e .[dev]  # For development")
    print("   pip install -e .[gpu]  # For GPU support")
    print("   pip install -e .[all]  # For all features")
    print("3. Run the system:")
    print("   python main.py --help")
    print("   python main.py --mode full")
    print("")
    print("For more information, see README.md")


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run post-install setup if this script is executed directly
    if len(sys.argv) > 1 and sys.argv[1] == "setup":
        post_install_setup()
    else:
        print("Run 'python setup.py setup' to perform initial setup")
        print("Or use 'pip install -e .' to install the package")