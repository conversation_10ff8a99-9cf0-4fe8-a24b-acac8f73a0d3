# Core Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0
pydantic==2.5.0
pydantic-settings==2.1.0
python-multipart==0.0.6

# AI/ML Dependencies
torch>=2.1.0
transformers>=4.35.0
peft>=0.6.0
accelerators>=0.24.0
datasets>=2.14.0
tokenizers>=0.15.0
scipy>=1.11.0
scikit-learn>=1.3.0
numpy>=1.24.0
pandas>=2.1.0

# MLflow and Experiment Tracking
mlflow>=2.8.0
mlflow-skinny>=2.8.0

# Financial Data and Analysis
yfinance>=0.2.18
ta-lib>=0.4.25
quantlib>=1.32
arch>=6.2.0
statsmodels>=0.14.0

# Data Processing and Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
numba>=0.58.0

# Async and Concurrency
aiohttp>=3.9.0
aiofiles>=23.2.1
asyncio-mqtt>=0.16.1

# Database and Storage
sqlalchemy>=2.0.0
alembic>=1.12.0
redis>=5.0.0
aiosqlite>=0.19.0

# Configuration and Environment
pyyaml>=6.0.1
python-dotenv>=1.0.0
click>=8.1.0
rich>=13.6.0

# Security and Authentication
cryptography>=41.0.0
pyjwt>=2.8.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# HTTP and API
httpx>=0.25.0
requests>=2.31.0
starlette>=0.27.0

# Monitoring and Logging
prometheus-client>=0.19.0
structlog>=23.2.0
loguru>=0.7.2

# Testing Dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.12.0
httpx>=0.25.0  # for testing FastAPI

# Development Dependencies
black>=23.9.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.5.0
isort>=5.12.0

# Deployment and DevOps
docker>=6.1.0
kubernetes>=28.1.0
gunicorn>=21.2.0

# Utilities
tqdm>=4.66.0
joblib>=1.3.0
psutil>=5.9.0
typing-extensions>=4.8.0
python-dateutil>=2.8.2
pytz>=2023.3

# Optional GPU Dependencies (install separately if needed)
# torch-audio>=2.1.0
# torch-vision>=0.16.0
# nvidia-ml-py>=12.535.0

# Optional Financial Data Providers
# alpha-vantage>=2.3.1
# quandl>=3.7.0
# bloomberg-api>=0.1.0

# Optional Advanced Analytics
# cvxpy>=1.4.0
# cvxopt>=1.3.0
# pyfolio>=0.9.2
# empyrical>=0.5.5

# Optional Distributed Computing
# dask[complete]>=2023.10.0
# ray[default]>=2.8.0

# Optional Time Series
# prophet>=1.1.4
# pmdarima>=2.0.4
# sktime>=0.24.0