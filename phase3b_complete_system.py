#!/usr/bin/env python3
"""
Phase 3B Complete System
Advanced AI Features + Performance Optimization
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict

# Import all Phase 3B components
from advanced_ensemble_voting import AdvancedEnsembleVoting, EnsembleDecision, VotingMethod
from high_performance_engine import HighPerformanceEngine, ProcessingTask
from realtime_learning_system import RealTimeLearningSystem, MarketObservation, TradingOutcome
from working_risk_management import WorkingRiskManager

@dataclass
class SuperiorTradingSignal:
    symbol: str
    ensemble_decision: EnsembleDecision
    risk_assessment: Dict[str, Any]
    market_predictions: Dict[str, float]
    model_adjustments: Dict[str, Dict[str, float]]
    performance_metrics: Dict[str, Any]
    execution_time: float
    timestamp: datetime

class Phase3BCompleteSystem:
    """Complete Phase 3B system with all advanced features"""
    
    def __init__(self, initial_capital: float = 100000.0):
        print("🚀 PHASE 3B COMPLETE SYSTEM INITIALIZATION")
        print("=" * 60)
        
        # Initialize all components
        print("🧠 Initializing Advanced Ensemble Voting...")
        self.ensemble = AdvancedEnsembleVoting()
        
        print("⚡ Initializing High-Performance Engine...")
        self.performance_engine = HighPerformanceEngine(max_workers=6)
        
        print("📚 Initializing Real-Time Learning...")
        self.learning_system = RealTimeLearningSystem(learning_rate=0.02)
        
        print("🛡️ Initializing Risk Management...")
        self.risk_manager = WorkingRiskManager(initial_capital)
        
        # System state
        self.is_running = False
        self.trading_session_active = False
        self.superior_signals = []
        self.system_metrics = {
            'total_signals_generated': 0,
            'successful_trades': 0,
            'total_profit_loss': 0.0,
            'system_uptime': 0.0,
            'average_signal_time': 0.0
        }
        
        print("\n✅ Phase 3B Complete System initialized")
        print(f"   💰 Initial capital: ${initial_capital:,.2f}")
        print(f"   🧠 Ensemble models: 5 advanced models")
        print(f"   ⚡ Performance workers: 6 parallel workers")
        print(f"   📚 Learning: Real-time adaptation enabled")
        print(f"   🛡️ Risk management: Professional-grade controls")
    
    async def start_system(self):
        """Start the complete Phase 3B system"""
        if self.is_running:
            print("System already running")
            return
        
        print("\n🚀 STARTING PHASE 3B COMPLETE SYSTEM")
        print("=" * 50)
        
        # Start all components
        self.performance_engine.start_engine()
        self.learning_system.start_learning()
        
        self.is_running = True
        self.system_start_time = datetime.now()
        
        print("✅ All systems operational")
        print("   ⚡ Performance engine: Running")
        print("   📚 Learning system: Active")
        print("   🛡️ Risk management: Monitoring")
        print("   🧠 Ensemble voting: Ready")
    
    async def stop_system(self):
        """Stop the complete system"""
        if not self.is_running:
            return
        
        print("\n🛑 STOPPING PHASE 3B COMPLETE SYSTEM")
        
        self.is_running = False
        self.trading_session_active = False
        
        # Stop all components
        self.performance_engine.stop_engine()
        self.learning_system.stop_learning()
        
        # Calculate uptime
        if hasattr(self, 'system_start_time'):
            uptime = (datetime.now() - self.system_start_time).total_seconds()
            self.system_metrics['system_uptime'] = uptime
        
        print("✅ All systems stopped")
    
    async def generate_superior_trading_signal(self, symbol: str, market_data: Dict[str, Any] = None, 
                                             priority: int = 1) -> Optional[SuperiorTradingSignal]:
        """Generate superior trading signal using all advanced features"""
        if not self.is_running:
            print("❌ System not running")
            return None
        
        start_time = time.time()
        
        print(f"\n🎯 GENERATING SUPERIOR SIGNAL: {symbol}")
        print(f"   Priority: {priority}")
        print(f"   Market data: {'Provided' if market_data else 'Using defaults'}")
        
        try:
            # Step 1: Get ensemble decision with performance optimization
            print("   🧠 Step 1: Advanced ensemble analysis...")
            ensemble_decision = await self.performance_engine.process_trading_signal(
                symbol, market_data, priority
            )
            
            if not ensemble_decision:
                print("   ❌ Failed to get ensemble decision")
                return None
            
            # Step 2: Get learning-based predictions
            print("   📚 Step 2: Learning-based market predictions...")
            market_predictions = self.learning_system.predict_market_conditions(symbol, market_data or {})
            
            # Step 3: Get model adjustments from learning
            print("   🔧 Step 3: Model performance adjustments...")
            model_adjustments = {}
            for prediction in ensemble_decision.individual_predictions:
                adjustments = self.learning_system.get_model_adjustments(prediction.model_name)
                model_adjustments[prediction.model_name] = adjustments
            
            # Step 4: Apply learning adjustments to ensemble decision
            print("   ⚖️ Step 4: Applying learning adjustments...")
            adjusted_decision = self._apply_learning_adjustments(ensemble_decision, model_adjustments)
            
            # Step 5: Risk assessment
            print("   🛡️ Step 5: Risk assessment...")
            risk_assessment = await self._comprehensive_risk_assessment(symbol, adjusted_decision)
            
            # Step 6: Performance metrics
            print("   📊 Step 6: Performance metrics...")
            performance_metrics = self._collect_performance_metrics()
            
            execution_time = time.time() - start_time
            
            # Create superior signal
            superior_signal = SuperiorTradingSignal(
                symbol=symbol,
                ensemble_decision=adjusted_decision,
                risk_assessment=risk_assessment,
                market_predictions=market_predictions,
                model_adjustments=model_adjustments,
                performance_metrics=performance_metrics,
                execution_time=execution_time,
                timestamp=datetime.now()
            )
            
            # Store signal
            self.superior_signals.append(superior_signal)
            self.system_metrics['total_signals_generated'] += 1
            self.system_metrics['average_signal_time'] = (
                self.system_metrics['average_signal_time'] * 0.9 + execution_time * 0.1
            )
            
            print(f"   ✅ Superior signal generated in {execution_time:.2f}s")
            print(f"   📊 Action: {superior_signal.ensemble_decision.final_action}")
            print(f"   📊 Confidence: {superior_signal.ensemble_decision.ensemble_confidence:.2f}")
            print(f"   📊 Risk score: {risk_assessment.get('risk_score', 'N/A')}")
            
            return superior_signal
            
        except Exception as e:
            print(f"   ❌ Error generating superior signal: {e}")
            return None
    
    async def execute_superior_signal(self, signal: SuperiorTradingSignal) -> bool:
        """Execute superior trading signal with full system integration"""
        if not signal:
            return False
        
        print(f"\n🎯 EXECUTING SUPERIOR SIGNAL: {signal.symbol}")
        
        # Check risk assessment
        if signal.risk_assessment.get('risk_score', 0) > 0.8:
            print("   🛡️ Signal blocked by risk assessment")
            return False
        
        # Execute with risk management
        decision = signal.ensemble_decision
        
        if decision.final_action == 'BUY':
            # Calculate position size with learning adjustments
            if decision.individual_predictions:
                best_prediction = max(decision.individual_predictions, key=lambda p: p.confidence)
                
                if best_prediction.entry_price and best_prediction.stop_loss:
                    # Apply learning adjustments to confidence
                    model_name = best_prediction.model_name
                    adjustments = signal.model_adjustments.get(model_name, {})
                    adjusted_confidence = best_prediction.confidence * adjustments.get('confidence_multiplier', 1.0)
                    
                    # Calculate optimal position size
                    position_size = self.risk_manager.calculate_optimal_position_size(
                        signal.symbol, best_prediction.entry_price, best_prediction.stop_loss, adjusted_confidence
                    )
                    
                    # Validate and execute
                    allowed, alerts = self.risk_manager.validate_new_position(
                        signal.symbol, position_size, best_prediction.entry_price, 'long', best_prediction.stop_loss
                    )
                    
                    if allowed:
                        success = self.risk_manager.add_position(
                            signal.symbol, position_size, best_prediction.entry_price,
                            'long', best_prediction.stop_loss, best_prediction.target_price
                        )
                        
                        if success:
                            print(f"   ✅ Position opened: {position_size:.2f} shares")
                            
                            # Add market observation for learning
                            observation = MarketObservation(
                                timestamp=datetime.now(),
                                symbol=signal.symbol,
                                price=best_prediction.entry_price,
                                volume=1000000,  # Would be real data
                                volatility=signal.market_predictions.get('predicted_volatility', 0.2),
                                trend=signal.market_predictions.get('predicted_trend', 'neutral'),
                                sentiment=signal.market_predictions.get('predicted_sentiment', 0.0),
                                technical_indicators={}
                            )
                            self.learning_system.add_market_observation(observation)
                            
                            return True
                        else:
                            print(f"   ❌ Failed to open position")
                    else:
                        print(f"   🛡️ Position blocked by risk management:")
                        for alert in alerts:
                            print(f"      • {alert.message}")
        
        elif decision.final_action == 'SELL':
            # Close existing position
            success = self.risk_manager.close_position(signal.symbol)
            if success:
                print(f"   ✅ Position closed")
                return True
            else:
                print(f"   ❌ No position to close")
        
        return False
    
    async def run_trading_session(self, symbols: List[str], market_data: Dict[str, Dict[str, Any]] = None,
                                session_duration: int = 3600) -> Dict[str, Any]:
        """Run complete trading session with all Phase 3B features"""
        if not self.is_running:
            await self.start_system()
        
        print(f"\n🚀 STARTING SUPERIOR TRADING SESSION")
        print(f"   Symbols: {symbols}")
        print(f"   Duration: {session_duration}s ({session_duration/60:.1f} minutes)")
        print(f"   Market data: {'Custom' if market_data else 'Default'}")
        
        self.trading_session_active = True
        session_start = datetime.now()
        session_results = {
            'session_start': session_start.isoformat(),
            'symbols_processed': 0,
            'signals_generated': 0,
            'positions_opened': 0,
            'positions_closed': 0,
            'total_execution_time': 0.0,
            'performance_metrics': {},
            'learning_updates': 0,
            'risk_alerts': 0
        }
        
        try:
            # Process each symbol
            for symbol in symbols:
                if not self.trading_session_active:
                    break
                
                print(f"\n📊 Processing {symbol}...")
                
                # Get symbol-specific market data
                symbol_market_data = market_data.get(symbol, {}) if market_data else {}
                
                # Generate superior signal
                signal = await self.generate_superior_trading_signal(symbol, symbol_market_data, priority=1)
                
                if signal:
                    session_results['signals_generated'] += 1
                    session_results['total_execution_time'] += signal.execution_time
                    
                    # Execute signal
                    if await self.execute_superior_signal(signal):
                        session_results['positions_opened'] += 1
                
                session_results['symbols_processed'] += 1
                
                # Small delay between symbols
                await asyncio.sleep(1)
            
            # Monitor existing positions
            print(f"\n📊 Monitoring existing positions...")
            portfolio_metrics = self.risk_manager.calculate_portfolio_metrics()
            
            # Simulate some market movement and position monitoring
            if self.risk_manager.positions:
                # Update with simulated prices (would be real market data)
                simulated_prices = {symbol: 50000 + (hash(symbol) % 1000) for symbol in symbols}
                triggered = self.risk_manager.check_stop_losses_and_targets(simulated_prices)
                session_results['positions_closed'] = len(triggered)
            
            # Collect final metrics
            session_results['performance_metrics'] = self._collect_comprehensive_metrics()
            session_results['learning_updates'] = len(self.learning_system.learning_updates)
            session_results['risk_alerts'] = len(self.risk_manager.alerts)
            
            session_duration_actual = (datetime.now() - session_start).total_seconds()
            session_results['actual_duration'] = session_duration_actual
            
            print(f"\n📈 TRADING SESSION COMPLETE")
            print(f"   Duration: {session_duration_actual:.1f}s")
            print(f"   Signals generated: {session_results['signals_generated']}")
            print(f"   Positions opened: {session_results['positions_opened']}")
            print(f"   Positions closed: {session_results['positions_closed']}")
            print(f"   Average signal time: {session_results['total_execution_time']/max(1, session_results['signals_generated']):.2f}s")
            
            return session_results
            
        except Exception as e:
            print(f"❌ Error in trading session: {e}")
            return session_results
        
        finally:
            self.trading_session_active = False
    
    def _apply_learning_adjustments(self, decision: EnsembleDecision, 
                                  adjustments: Dict[str, Dict[str, float]]) -> EnsembleDecision:
        """Apply learning adjustments to ensemble decision"""
        # Adjust confidence based on learning
        total_weight = 0
        weighted_confidence = 0
        
        for prediction in decision.individual_predictions:
            model_adjustments = adjustments.get(prediction.model_name, {})
            confidence_multiplier = model_adjustments.get('confidence_multiplier', 1.0)
            weight_adjustment = model_adjustments.get('weight_adjustment', 0.0)
            
            adjusted_confidence = prediction.confidence * confidence_multiplier
            weight = 1.0 + weight_adjustment
            
            weighted_confidence += adjusted_confidence * weight
            total_weight += weight
        
        if total_weight > 0:
            decision.ensemble_confidence = weighted_confidence / total_weight
        
        return decision
    
    async def _comprehensive_risk_assessment(self, symbol: str, decision: EnsembleDecision) -> Dict[str, Any]:
        """Comprehensive risk assessment"""
        risk_factors = []
        risk_score = 0.0
        
        # Ensemble consensus risk
        if decision.consensus_level < 0.6:
            risk_factors.append("Low ensemble consensus")
            risk_score += 0.2
        
        # Confidence risk
        if decision.ensemble_confidence < 0.7:
            risk_factors.append("Low confidence")
            risk_score += 0.1
        
        # Portfolio risk
        portfolio_metrics = self.risk_manager.calculate_portfolio_metrics()
        if portfolio_metrics.current_drawdown > 10:
            risk_factors.append("High portfolio drawdown")
            risk_score += 0.3
        
        # Position concentration risk
        if portfolio_metrics.largest_position_percent > 15:
            risk_factors.append("High position concentration")
            risk_score += 0.2
        
        return {
            'risk_score': min(risk_score, 1.0),
            'risk_factors': risk_factors,
            'portfolio_metrics': asdict(portfolio_metrics),
            'recommendation': 'PROCEED' if risk_score < 0.5 else 'CAUTION' if risk_score < 0.8 else 'BLOCK'
        }
    
    def _collect_performance_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive performance metrics"""
        # Engine performance
        engine_report = self.performance_engine.get_performance_report()
        
        # Learning performance
        learning_report = self.learning_system.get_learning_report()
        
        # Risk management
        risk_report = self.risk_manager.generate_risk_report()
        
        return {
            'engine_performance': engine_report,
            'learning_performance': learning_report,
            'risk_management': risk_report,
            'system_metrics': self.system_metrics
        }
    
    def _collect_comprehensive_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive system metrics"""
        return {
            'ensemble_performance': self.ensemble.get_performance_report(),
            'engine_performance': self.performance_engine.get_performance_report(),
            'learning_performance': self.learning_system.get_learning_report(),
            'risk_management': self.risk_manager.generate_risk_report(),
            'system_metrics': self.system_metrics,
            'superior_signals_count': len(self.superior_signals)
        }
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get complete system status"""
        return {
            'timestamp': datetime.now().isoformat(),
            'system_running': self.is_running,
            'trading_session_active': self.trading_session_active,
            'components_status': {
                'ensemble_voting': 'operational',
                'performance_engine': 'running' if self.performance_engine.is_running else 'stopped',
                'learning_system': 'active' if self.learning_system.is_learning else 'inactive',
                'risk_management': 'monitoring'
            },
            'system_metrics': self.system_metrics,
            'uptime': (datetime.now() - self.system_start_time).total_seconds() if hasattr(self, 'system_start_time') else 0
        }

async def main():
    """Test the complete Phase 3B system"""
    print("PHASE 3B COMPLETE SYSTEM - ADVANCED AI + PERFORMANCE")
    print("=" * 70)
    
    # Initialize complete system
    system = Phase3BCompleteSystem(100000.0)
    
    try:
        # Start system
        await system.start_system()
        
        # Test symbols
        symbols = ["BTC", "ETH", "TSLA"]
        
        # Market data
        market_data = {
            "BTC": {"trend": "bullish", "volatility": 0.25, "sentiment": 0.3},
            "ETH": {"trend": "neutral", "volatility": 0.20, "sentiment": 0.1},
            "TSLA": {"trend": "bearish", "volatility": 0.35, "sentiment": -0.2}
        }
        
        # Run trading session
        results = await system.run_trading_session(symbols, market_data, session_duration=300)
        
        # Display results
        print(f"\n📊 FINAL RESULTS:")
        print(f"   Symbols processed: {results['symbols_processed']}")
        print(f"   Signals generated: {results['signals_generated']}")
        print(f"   Positions opened: {results['positions_opened']}")
        print(f"   Session duration: {results.get('actual_duration', 0):.1f}s")
        
        # System status
        status = system.get_system_status()
        print(f"\n🔧 SYSTEM STATUS:")
        print(f"   System running: {status['system_running']}")
        print(f"   Total signals: {status['system_metrics']['total_signals_generated']}")
        print(f"   Average signal time: {status['system_metrics']['average_signal_time']:.2f}s")
        print(f"   System uptime: {status['uptime']:.1f}s")
        
    finally:
        await system.stop_system()
    
    print(f"\n🎉 PHASE 3B COMPLETE SYSTEM TEST FINISHED!")
    print(f"   ✅ Advanced AI features: Operational")
    print(f"   ✅ Performance optimization: Active")
    print(f"   ✅ Real-time learning: Functional")
    print(f"   ✅ Risk management: Protecting capital")

if __name__ == "__main__":
    asyncio.run(main())
