#!/usr/bin/env python3
"""
Test script for trained Qwen3 finance model
"""

import os
import sys
import torch
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import json
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class TrainedModelTester:
    """Test the trained Qwen3 finance model"""
    
    def __init__(self, model_path: str = "models/qwen3-finance-v1"):
        self.model_path = Path(model_path)
        self.base_model_name = "Qwen/Qwen2.5-7B-Instruct"
        self.tokenizer = None
        self.model = None
        
    def load_model(self):
        """Load the trained model"""
        print(f"🔄 Loading trained model from: {self.model_path}")
        
        try:
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.base_model_name)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Load base model
            base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_name,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True
            )
            
            # Load LoRA adapter if available
            if (self.model_path / "adapter_config.json").exists():
                print("📦 Loading LoRA adapter...")
                self.model = PeftModel.from_pretrained(base_model, str(self.model_path))
                self.model = self.model.merge_and_unload()  # Merge LoRA weights
            else:
                print("⚠️ No LoRA adapter found, using base model")
                self.model = base_model
            
            print("✅ Model loaded successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return False
    
    def generate_response(self, prompt: str, max_length: int = 512, temperature: float = 0.7) -> str:
        """Generate response from the model"""
        if not self.model or not self.tokenizer:
            return "Error: Model not loaded"
        
        try:
            # Tokenize input
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
            
            # Move to device
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # Generate
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_length=inputs['input_ids'].shape[1] + max_length,
                    temperature=temperature,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the generated part
            generated_text = response[len(prompt):].strip()
            return generated_text
            
        except Exception as e:
            return f"Error generating response: {e}"
    
    def run_finance_tests(self):
        """Run finance-specific tests"""
        print("\n🧪 Running Finance Model Tests")
        print("=" * 50)
        
        test_prompts = [
            {
                "name": "Stock Analysis",
                "prompt": "Analyze AAPL stock performance and provide investment recommendation:",
                "expected_keywords": ["apple", "stock", "price", "recommendation", "analysis"]
            },
            {
                "name": "Market Sentiment",
                "prompt": "What is the current market sentiment for tech stocks?",
                "expected_keywords": ["market", "sentiment", "tech", "stocks", "outlook"]
            },
            {
                "name": "Risk Assessment",
                "prompt": "Assess the risk level of investing in cryptocurrency:",
                "expected_keywords": ["risk", "cryptocurrency", "volatility", "investment"]
            },
            {
                "name": "Portfolio Advice",
                "prompt": "Suggest a balanced portfolio allocation for a 30-year-old investor:",
                "expected_keywords": ["portfolio", "allocation", "diversification", "age", "risk"]
            },
            {
                "name": "Economic Indicators",
                "prompt": "Explain how inflation affects stock market performance:",
                "expected_keywords": ["inflation", "stock market", "performance", "economic", "impact"]
            }
        ]
        
        results = []
        
        for i, test in enumerate(test_prompts, 1):
            print(f"\n📊 Test {i}: {test['name']}")
            print(f"Prompt: {test['prompt']}")
            
            response = self.generate_response(test['prompt'])
            
            print(f"Response: {response[:200]}...")
            
            # Check for expected keywords
            response_lower = response.lower()
            found_keywords = [kw for kw in test['expected_keywords'] if kw in response_lower]
            keyword_score = len(found_keywords) / len(test['expected_keywords'])
            
            result = {
                "test_name": test['name'],
                "prompt": test['prompt'],
                "response": response,
                "keyword_score": keyword_score,
                "found_keywords": found_keywords,
                "expected_keywords": test['expected_keywords'],
                "passed": keyword_score >= 0.3  # At least 30% keywords found
            }
            
            results.append(result)
            
            status = "✅ PASS" if result['passed'] else "❌ FAIL"
            print(f"Status: {status} (Keyword Score: {keyword_score:.2f})")
        
        return results
    
    def run_performance_tests(self):
        """Run performance tests"""
        print("\n⚡ Running Performance Tests")
        print("=" * 50)
        
        test_prompt = "Analyze the financial performance of Tesla stock:"
        
        # Test different generation lengths
        lengths = [128, 256, 512]
        performance_results = []
        
        for length in lengths:
            print(f"\n🔄 Testing generation length: {length} tokens")
            
            start_time = datetime.now()
            response = self.generate_response(test_prompt, max_length=length)
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            tokens_per_second = length / duration if duration > 0 else 0
            
            result = {
                "max_length": length,
                "duration_seconds": duration,
                "tokens_per_second": tokens_per_second,
                "response_length": len(response)
            }
            
            performance_results.append(result)
            
            print(f"Duration: {duration:.2f}s")
            print(f"Tokens/sec: {tokens_per_second:.2f}")
            print(f"Response length: {len(response)} chars")
        
        return performance_results
    
    def save_test_results(self, finance_results, performance_results):
        """Save test results to file"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "model_path": str(self.model_path),
            "base_model": self.base_model_name,
            "finance_tests": finance_results,
            "performance_tests": performance_results,
            "summary": {
                "total_finance_tests": len(finance_results),
                "passed_finance_tests": sum(1 for r in finance_results if r['passed']),
                "average_keyword_score": sum(r['keyword_score'] for r in finance_results) / len(finance_results),
                "average_tokens_per_second": sum(r['tokens_per_second'] for r in performance_results) / len(performance_results)
            }
        }
        
        output_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Test results saved to: {output_file}")
        return results

def main():
    """Main test function"""
    print("🚀 Qwen3 Finance Model Testing")
    print("=" * 50)
    
    # Check if model exists
    model_path = "models/qwen3-finance-v1"
    if not Path(model_path).exists():
        print(f"❌ Model not found at: {model_path}")
        print("Please train the model first using: python train_all_models.py --model qwen3")
        return False
    
    # Initialize tester
    tester = TrainedModelTester(model_path)
    
    # Load model
    if not tester.load_model():
        return False
    
    # Run tests
    finance_results = tester.run_finance_tests()
    performance_results = tester.run_performance_tests()
    
    # Save results
    results = tester.save_test_results(finance_results, performance_results)
    
    # Print summary
    print("\n📋 TEST SUMMARY")
    print("=" * 50)
    print(f"Finance Tests: {results['summary']['passed_finance_tests']}/{results['summary']['total_finance_tests']} passed")
    print(f"Average Keyword Score: {results['summary']['average_keyword_score']:.2f}")
    print(f"Average Generation Speed: {results['summary']['average_tokens_per_second']:.2f} tokens/sec")
    
    success_rate = results['summary']['passed_finance_tests'] / results['summary']['total_finance_tests']
    if success_rate >= 0.8:
        print("🎉 Model testing PASSED! Ready for deployment.")
        return True
    else:
        print("⚠️ Model testing needs improvement. Consider additional training.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
