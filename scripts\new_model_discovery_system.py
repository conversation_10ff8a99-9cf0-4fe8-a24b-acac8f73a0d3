#!/usr/bin/env python3
"""
New Model Discovery & Analysis System
Comprehensive detection and analysis of newly added AI models
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree

console = Console()

class NewModelDiscovery:
    """Comprehensive discovery and analysis of new AI models"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
        # Known existing models from previous sessions
        self.existing_models = {
            "noryon-phi-4-9b-finance",
            "noryon-gemma-3-12b-finance", 
            "noryon-phi-4-9b-enhanced-enhanced",
            "noryon-gemma-3-12b-enhanced-enhanced",
            "noryon-qwen3-finance-v2",
            "noryon-cogito-finance-v2",
            "noryon-marco-o1-finance-v2",
            "noryon-deepscaler-finance-v2"
        }
        
        # Base models that were available
        self.known_base_models = {
            "deepseek-r1:14b",
            "qwen3:14b",
            "phi4-reasoning:14b", 
            "cogito:14b",
            "marco-o1:7b",
            "exaone-deep:7.8b",
            "dolphin3:8b",
            "deepscaler:1.5b",
            "gemma3:12b",
            "phi4:14b"
        }
        
        self.new_models_found = {}
        self.model_analysis = {}
        
    def scan_ollama_models(self) -> Dict:
        """Scan for all available Ollama models"""
        console.print("[yellow]🔍 Scanning Ollama registry for new models...[/yellow]")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            
            if result.returncode != 0:
                console.print("[red]❌ Failed to access Ollama registry[/red]")
                return {}
            
            current_models = {}
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 3:
                        model_name = parts[0]
                        model_id = parts[1] if len(parts) > 1 else "unknown"
                        size = parts[2] if len(parts) > 2 else "unknown"
                        modified = parts[3] if len(parts) > 3 else "unknown"
                        
                        current_models[model_name] = {
                            "model_id": model_id,
                            "size": size,
                            "modified": modified,
                            "type": "ollama",
                            "status": "available"
                        }
            
            console.print(f"[green]✅ Found {len(current_models)} total Ollama models[/green]")
            return current_models
            
        except Exception as e:
            console.print(f"[red]❌ Error scanning Ollama: {e}[/red]")
            return {}
    
    def identify_new_models(self, current_models: Dict) -> Dict:
        """Identify newly added models"""
        console.print("[yellow]🆕 Identifying new models...[/yellow]")
        
        new_models = {}
        
        for model_name, details in current_models.items():
            # Check if it's a new base model (not a trained Noryon model)
            is_noryon_model = "noryon" in model_name.lower()
            is_known_base = model_name in self.known_base_models
            is_existing_noryon = model_name in self.existing_models
            
            if not is_noryon_model and not is_known_base:
                # This is a new base model
                new_models[model_name] = {
                    **details,
                    "category": "new_base_model",
                    "training_ready": True
                }
                console.print(f"[cyan]🆕 New base model detected: {model_name}[/cyan]")
            
            elif is_noryon_model and not is_existing_noryon:
                # This is a new Noryon trained model (shouldn't happen but check anyway)
                new_models[model_name] = {
                    **details,
                    "category": "new_noryon_model",
                    "training_ready": False
                }
                console.print(f"[blue]🔄 New Noryon model detected: {model_name}[/blue]")
        
        console.print(f"[green]✅ Identified {len(new_models)} new models[/green]")
        return new_models
    
    def analyze_model_specifications(self, model_name: str, details: Dict) -> Dict:
        """Analyze detailed specifications of a new model"""
        console.print(f"[yellow]🔍 Analyzing {model_name} specifications...[/yellow]")
        
        analysis = {
            "model_name": model_name,
            "size": details.get("size", "unknown"),
            "type": details.get("type", "unknown"),
            "status": details.get("status", "unknown"),
            "capabilities": [],
            "recommended_specialization": "",
            "training_priority": 0,
            "estimated_training_time": 0
        }
        
        # Analyze based on model name patterns
        model_lower = model_name.lower()
        
        # Determine capabilities and specialization based on model characteristics
        if "llama" in model_lower or "llama3" in model_lower:
            analysis["capabilities"] = ["general_reasoning", "financial_analysis", "risk_assessment"]
            analysis["recommended_specialization"] = "advanced_portfolio_management"
            analysis["training_priority"] = 8
            analysis["estimated_training_time"] = 10
            
        elif "mistral" in model_lower:
            analysis["capabilities"] = ["instruction_following", "analytical_reasoning", "market_analysis"]
            analysis["recommended_specialization"] = "market_sentiment_analysis"
            analysis["training_priority"] = 7
            analysis["estimated_training_time"] = 8
            
        elif "gemma" in model_lower and "gemma3" not in model_lower:
            analysis["capabilities"] = ["efficient_reasoning", "quick_analysis", "pattern_recognition"]
            analysis["recommended_specialization"] = "technical_pattern_recognition"
            analysis["training_priority"] = 6
            analysis["estimated_training_time"] = 6
            
        elif "qwen" in model_lower and "qwen3" not in model_lower:
            analysis["capabilities"] = ["multilingual", "comprehensive_analysis", "global_markets"]
            analysis["recommended_specialization"] = "international_markets_analysis"
            analysis["training_priority"] = 9
            analysis["estimated_training_time"] = 12
            
        elif "deepseek" in model_lower and "r1" not in model_lower:
            analysis["capabilities"] = ["deep_reasoning", "mathematical_analysis", "quantitative_finance"]
            analysis["recommended_specialization"] = "quantitative_trading_strategies"
            analysis["training_priority"] = 10
            analysis["estimated_training_time"] = 15
            
        elif "phi" in model_lower and "phi4" not in model_lower:
            analysis["capabilities"] = ["efficient_reasoning", "risk_analysis", "decision_making"]
            analysis["recommended_specialization"] = "real_time_risk_monitoring"
            analysis["training_priority"] = 7
            analysis["estimated_training_time"] = 7
            
        elif "claude" in model_lower:
            analysis["capabilities"] = ["advanced_reasoning", "ethical_analysis", "comprehensive_evaluation"]
            analysis["recommended_specialization"] = "ethical_trading_compliance"
            analysis["training_priority"] = 8
            analysis["estimated_training_time"] = 10
            
        elif "gpt" in model_lower:
            analysis["capabilities"] = ["general_intelligence", "creative_analysis", "market_insights"]
            analysis["recommended_specialization"] = "creative_market_strategies"
            analysis["training_priority"] = 7
            analysis["estimated_training_time"] = 9
            
        else:
            # Generic analysis for unknown models
            analysis["capabilities"] = ["general_analysis", "financial_reasoning"]
            analysis["recommended_specialization"] = "general_financial_analysis"
            analysis["training_priority"] = 5
            analysis["estimated_training_time"] = 8
        
        # Test model responsiveness
        try:
            test_result = subprocess.run([
                'ollama', 'run', model_name, 'Hello, can you analyze financial markets?'
            ], capture_output=True, text=True, timeout=30)
            
            if test_result.returncode == 0:
                response = test_result.stdout.strip()
                analysis["responsive"] = True
                analysis["test_response_length"] = len(response)
                analysis["supports_financial_queries"] = any(word in response.lower() for word in 
                    ["market", "financial", "trading", "investment", "analysis", "economic"])
            else:
                analysis["responsive"] = False
                analysis["error"] = test_result.stderr
                
        except subprocess.TimeoutExpired:
            analysis["responsive"] = False
            analysis["error"] = "Timeout during test"
        except Exception as e:
            analysis["responsive"] = False
            analysis["error"] = str(e)
        
        return analysis
    
    def generate_discovery_report(self) -> Dict:
        """Generate comprehensive discovery report"""
        console.print(Panel(
            "[bold blue]🔍 New Model Discovery & Analysis[/bold blue]\n\n"
            "Scanning system for newly added AI models:\n"
            "• Ollama registry analysis\n"
            "• Model specification detection\n"
            "• Capability assessment\n"
            "• Training readiness evaluation",
            title="Model Discovery"
        ))
        
        # Scan current models
        current_models = self.scan_ollama_models()
        
        # Identify new models
        new_models = self.identify_new_models(current_models)
        
        if not new_models:
            console.print("[yellow]⚠️ No new models detected[/yellow]")
            return {"new_models_found": 0, "models": {}}
        
        # Analyze each new model
        for model_name, details in new_models.items():
            analysis = self.analyze_model_specifications(model_name, details)
            self.model_analysis[model_name] = analysis
        
        # Generate summary report
        self._display_discovery_results(new_models)
        
        return {
            "new_models_found": len(new_models),
            "models": self.model_analysis,
            "current_total": len(current_models),
            "discovery_timestamp": datetime.now().isoformat()
        }
    
    def _display_discovery_results(self, new_models: Dict):
        """Display comprehensive discovery results"""
        
        if not new_models:
            console.print("[yellow]⚠️ No new models found to display[/yellow]")
            return
        
        # New models summary table
        summary_table = Table(title="New Models Discovered")
        summary_table.add_column("Model Name", style="cyan")
        summary_table.add_column("Size", style="yellow")
        summary_table.add_column("Type", style="blue")
        summary_table.add_column("Status", style="green")
        summary_table.add_column("Responsive", style="magenta")
        
        for model_name, analysis in self.model_analysis.items():
            responsive_status = "✅ Yes" if analysis.get("responsive", False) else "❌ No"
            
            summary_table.add_row(
                model_name,
                analysis.get("size", "unknown"),
                analysis.get("type", "unknown"),
                analysis.get("status", "unknown"),
                responsive_status
            )
        
        console.print(summary_table)
        
        # Detailed analysis table
        analysis_table = Table(title="Model Analysis & Specialization Recommendations")
        analysis_table.add_column("Model", style="cyan")
        analysis_table.add_column("Recommended Specialization", style="yellow")
        analysis_table.add_column("Priority", style="green")
        analysis_table.add_column("Est. Training Time", style="blue")
        analysis_table.add_column("Capabilities", style="magenta")
        
        for model_name, analysis in self.model_analysis.items():
            capabilities_str = ", ".join(analysis.get("capabilities", [])[:3])  # Show first 3
            if len(analysis.get("capabilities", [])) > 3:
                capabilities_str += "..."
            
            analysis_table.add_row(
                model_name,
                analysis.get("recommended_specialization", "unknown"),
                str(analysis.get("training_priority", 0)),
                f"{analysis.get('estimated_training_time', 0)} min",
                capabilities_str
            )
        
        console.print(analysis_table)
        
        # Training readiness assessment
        ready_models = [name for name, analysis in self.model_analysis.items() 
                      if analysis.get("responsive", False)]
        
        console.print(Panel(
            f"[bold green]🎯 Discovery Summary[/bold green]\n\n"
            f"New Models Found: {len(new_models)}\n"
            f"Training Ready: {len(ready_models)}\n"
            f"Total System Models: {len(self.existing_models) + len(ready_models)}\n\n"
            f"✅ Ready for Training: {', '.join(ready_models) if ready_models else 'None'}\n"
            f"❌ Need Configuration: {', '.join([name for name, analysis in self.model_analysis.items() if not analysis.get('responsive', False)])}\n\n"
            f"🚀 Next Step: {'Begin specialized training' if ready_models else 'Configure unresponsive models'}",
            title="Discovery Complete"
        ))

def main():
    """Main discovery function"""
    console.print("[bold blue]🔍 Starting New Model Discovery...[/bold blue]\n")
    
    discovery = NewModelDiscovery()
    results = discovery.generate_discovery_report()
    
    console.print("\n[bold green]🎯 Discovery Complete![/bold green]")
    console.print("Ready to proceed with model training for discovered models.")
    
    return results

if __name__ == "__main__":
    results = main()
