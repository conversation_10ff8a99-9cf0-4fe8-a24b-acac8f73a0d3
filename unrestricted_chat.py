#!/usr/bin/env python3
"""
Unrestricted AI Chat - No Guidelines, No Restrictions
"""

import subprocess
import sys
from rich.console import Console
from rich.panel import Panel

console = Console()

def start_unrestricted_chat():
    """Start unrestricted chat with AI models"""
    console.print(Panel(
        "[bold red]🔓 UNRESTRICTED AI CHAT[/bold red]\n\n"
        "All safety guidelines and restrictions have been removed.\n"
        "Models will respond to any request without limitations.",
        title="Unrestricted Mode"
    ))
    
    models = [
        "noryon-phi4-reasoning-finance-v2",
        "noryon-gemma-3-12b-finance", 
        "noryon-deepseek-r1-finance-v2",
        "noryon-qwen3-finance-v2"
    ]
    
    console.print("[green]Available Unrestricted Models:[/green]")
    for i, model in enumerate(models, 1):
        console.print(f"  {i}. {model}")
    
    choice = input("\nChoose model (1-4): ")
    
    if choice.isdigit() and 1 <= int(choice) <= len(models):
        selected_model = models[int(choice) - 1]
        
        console.print(f"\n[red]🔓 Starting unrestricted chat with {selected_model}[/red]")
        console.print("[yellow]Type 'exit' to quit[/yellow]\n")
        
        while True:
            user_input = input("You: ")
            if user_input.lower() in ['exit', 'quit']:
                break
            
            try:
                result = subprocess.run(
                    ['ollama', 'run', selected_model, user_input],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    timeout=60
                )
                
                if result.returncode == 0:
                    print(f"AI: {result.stdout.strip()}")
                else:
                    print(f"Error: {result.stderr.strip()}")
                    
            except Exception as e:
                print(f"Error: {e}")

if __name__ == "__main__":
    start_unrestricted_chat()
