#!/usr/bin/env python3
"""
Noryon System Integration
Unified system orchestration and management interface

This module provides:
- Centralized system initialization and configuration
- Component lifecycle management
- Inter-component communication and coordination
- System health monitoring and diagnostics
- Unified API interface
- Event-driven architecture
- Real-time system status and metrics
- Automated system recovery and failover
"""

import asyncio
import json
import logging
import os
import signal
import sys
import time
import yaml
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable, Union
import threading
from concurrent.futures import ThreadPoolExecutor
from enum import Enum
import uuid
import warnings
warnings.filterwarnings('ignore')

# Import all system components
try:
    from train_all_models import ModelTrainingOrchestrator
    from continuous_learning_pipeline import ContinuousLearningPipeline
    from risk_management_system import RiskMonitor, RiskLimits
    from performance_analytics import PerformanceAnalytics
    from backtesting_framework import BacktestEngine
    from automated_deployment_pipeline import DeploymentPipeline, DeploymentConfig
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Some system components not available: {e}")
    COMPONENTS_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemState(Enum):
    """System operational states"""
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class ComponentState(Enum):
    """Component states"""
    INACTIVE = "inactive"
    STARTING = "starting"
    ACTIVE = "active"
    STOPPING = "stopping"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class EventType(Enum):
    """System event types"""
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    COMPONENT_START = "component_start"
    COMPONENT_STOP = "component_stop"
    COMPONENT_ERROR = "component_error"
    TRADE_SIGNAL = "trade_signal"
    RISK_ALERT = "risk_alert"
    PERFORMANCE_UPDATE = "performance_update"
    MODEL_UPDATE = "model_update"
    DEPLOYMENT_START = "deployment_start"
    DEPLOYMENT_COMPLETE = "deployment_complete"
    HEALTH_CHECK = "health_check"
    CONFIGURATION_CHANGE = "configuration_change"

@dataclass
class SystemEvent:
    """System event"""
    event_id: str
    event_type: EventType
    component: str
    timestamp: datetime
    data: Dict[str, Any] = field(default_factory=dict)
    severity: str = "info"  # info, warning, error, critical
    message: str = ""

@dataclass
class ComponentInfo:
    """Component information"""
    name: str
    component_type: str
    state: ComponentState
    instance: Optional[Any] = None
    config: Dict[str, Any] = field(default_factory=dict)
    metrics: Dict[str, Any] = field(default_factory=dict)
    last_health_check: Optional[datetime] = None
    error_count: int = 0
    start_time: Optional[datetime] = None
    dependencies: List[str] = field(default_factory=list)

@dataclass
class SystemMetrics:
    """System-wide metrics"""
    uptime: timedelta
    active_components: int
    total_events: int
    error_count: int
    memory_usage: float
    cpu_usage: float
    disk_usage: float
    network_activity: Dict[str, float]
    performance_metrics: Dict[str, float]
    timestamp: datetime = field(default_factory=datetime.now)

class EventBus:
    """Event-driven communication system"""
    
    def __init__(self):
        self.subscribers = defaultdict(list)
        self.event_history = deque(maxlen=10000)
        self.event_stats = defaultdict(int)
        self._lock = threading.Lock()
    
    def subscribe(self, event_type: EventType, callback: Callable[[SystemEvent], None]):
        """Subscribe to events"""
        with self._lock:
            self.subscribers[event_type].append(callback)
            logger.debug(f"Subscribed to {event_type.value}")
    
    def unsubscribe(self, event_type: EventType, callback: Callable[[SystemEvent], None]):
        """Unsubscribe from events"""
        with self._lock:
            if callback in self.subscribers[event_type]:
                self.subscribers[event_type].remove(callback)
                logger.debug(f"Unsubscribed from {event_type.value}")
    
    async def publish(self, event: SystemEvent):
        """Publish event to subscribers"""
        with self._lock:
            self.event_history.append(event)
            self.event_stats[event.event_type] += 1
        
        # Notify subscribers
        subscribers = self.subscribers.get(event.event_type, [])
        for callback in subscribers:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(event)
                else:
                    callback(event)
            except Exception as e:
                logger.error(f"Error in event callback: {e}")
    
    def get_event_history(self, event_type: Optional[EventType] = None, 
                         limit: int = 100) -> List[SystemEvent]:
        """Get event history"""
        events = list(self.event_history)
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return events[-limit:]
    
    def get_event_stats(self) -> Dict[str, int]:
        """Get event statistics"""
        return dict(self.event_stats)

class ComponentManager:
    """Manage system components"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.components = {}
        self.component_configs = {}
        self.startup_order = []
        self.shutdown_order = []
        self._lock = threading.Lock()
    
    def register_component(self, name: str, component_type: str, 
                          instance: Any, config: Dict[str, Any] = None,
                          dependencies: List[str] = None):
        """Register a system component"""
        with self._lock:
            self.components[name] = ComponentInfo(
                name=name,
                component_type=component_type,
                state=ComponentState.INACTIVE,
                instance=instance,
                config=config or {},
                dependencies=dependencies or []
            )
            
            logger.info(f"Registered component: {name} ({component_type})")
    
    async def start_component(self, name: str) -> bool:
        """Start a component"""
        if name not in self.components:
            logger.error(f"Component not found: {name}")
            return False
        
        component = self.components[name]
        
        if component.state == ComponentState.ACTIVE:
            logger.warning(f"Component {name} is already active")
            return True
        
        try:
            # Check dependencies
            for dep in component.dependencies:
                if dep not in self.components or self.components[dep].state != ComponentState.ACTIVE:
                    logger.error(f"Dependency {dep} not available for component {name}")
                    return False
            
            component.state = ComponentState.STARTING
            component.start_time = datetime.now()
            
            # Publish start event
            await self.event_bus.publish(SystemEvent(
                event_id=str(uuid.uuid4()),
                event_type=EventType.COMPONENT_START,
                component=name,
                timestamp=datetime.now(),
                message=f"Starting component {name}"
            ))
            
            # Start component if it has a start method
            if hasattr(component.instance, 'start'):
                if asyncio.iscoroutinefunction(component.instance.start):
                    await component.instance.start()
                else:
                    component.instance.start()
            
            component.state = ComponentState.ACTIVE
            logger.info(f"Component {name} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start component {name}: {e}")
            component.state = ComponentState.ERROR
            component.error_count += 1
            
            await self.event_bus.publish(SystemEvent(
                event_id=str(uuid.uuid4()),
                event_type=EventType.COMPONENT_ERROR,
                component=name,
                timestamp=datetime.now(),
                severity="error",
                message=f"Failed to start component {name}: {str(e)}",
                data={'error': str(e)}
            ))
            
            return False
    
    async def stop_component(self, name: str) -> bool:
        """Stop a component"""
        if name not in self.components:
            logger.error(f"Component not found: {name}")
            return False
        
        component = self.components[name]
        
        if component.state == ComponentState.INACTIVE:
            logger.warning(f"Component {name} is already inactive")
            return True
        
        try:
            component.state = ComponentState.STOPPING
            
            # Publish stop event
            await self.event_bus.publish(SystemEvent(
                event_id=str(uuid.uuid4()),
                event_type=EventType.COMPONENT_STOP,
                component=name,
                timestamp=datetime.now(),
                message=f"Stopping component {name}"
            ))
            
            # Stop component if it has a stop method
            if hasattr(component.instance, 'stop'):
                if asyncio.iscoroutinefunction(component.instance.stop):
                    await component.instance.stop()
                else:
                    component.instance.stop()
            
            component.state = ComponentState.INACTIVE
            logger.info(f"Component {name} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop component {name}: {e}")
            component.state = ComponentState.ERROR
            component.error_count += 1
            return False
    
    async def restart_component(self, name: str) -> bool:
        """Restart a component"""
        logger.info(f"Restarting component: {name}")
        
        if await self.stop_component(name):
            await asyncio.sleep(1)  # Brief pause
            return await self.start_component(name)
        
        return False
    
    async def health_check_component(self, name: str) -> Dict[str, Any]:
        """Perform health check on component"""
        if name not in self.components:
            return {'status': 'unknown', 'message': 'Component not found'}
        
        component = self.components[name]
        
        try:
            # Update last health check time
            component.last_health_check = datetime.now()
            
            # Perform health check if component supports it
            if hasattr(component.instance, 'health_check'):
                if asyncio.iscoroutinefunction(component.instance.health_check):
                    result = await component.instance.health_check()
                else:
                    result = component.instance.health_check()
                
                return result
            else:
                # Basic health check based on state
                return {
                    'status': 'healthy' if component.state == ComponentState.ACTIVE else 'unhealthy',
                    'state': component.state.value,
                    'uptime': str(datetime.now() - component.start_time) if component.start_time else None,
                    'error_count': component.error_count
                }
                
        except Exception as e:
            logger.error(f"Health check failed for component {name}: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'error_count': component.error_count
            }
    
    def get_component_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all components"""
        status = {}
        
        for name, component in self.components.items():
            status[name] = {
                'type': component.component_type,
                'state': component.state.value,
                'uptime': str(datetime.now() - component.start_time) if component.start_time else None,
                'error_count': component.error_count,
                'last_health_check': component.last_health_check.isoformat() if component.last_health_check else None,
                'dependencies': component.dependencies
            }
        
        return status

class SystemOrchestrator:
    """Main system orchestrator"""
    
    def __init__(self, config_path: str = "system_config.yaml"):
        self.config_path = config_path
        self.state = SystemState.STOPPED
        self.start_time = None
        self.event_bus = EventBus()
        self.component_manager = ComponentManager(self.event_bus)
        self.executor = ThreadPoolExecutor(max_workers=8)
        self.shutdown_event = asyncio.Event()
        self.health_check_task = None
        self.metrics_task = None
        
        # Load configuration
        self.load_configuration()
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Initialize components
        self.initialize_components()
        
        # Subscribe to system events
        self.setup_event_handlers()
    
    def load_configuration(self):
        """Load system configuration"""
        config_file = Path(self.config_path)
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            # Default configuration
            self.config = {
                'system': {
                    'name': 'Noryon AI Trading System',
                    'version': '1.0.0',
                    'environment': 'development',
                    'log_level': 'INFO',
                    'health_check_interval': 60,
                    'metrics_interval': 30
                },
                'components': {
                    'training_orchestrator': {
                        'enabled': True,
                        'auto_start': True,
                        'config_file': 'training_config.yaml'
                    },
                    'continuous_learning': {
                        'enabled': True,
                        'auto_start': True,
                        'learning_interval': 3600
                    },
                    'risk_monitor': {
                        'enabled': True,
                        'auto_start': True,
                        'risk_limits': {
                            'max_position_size': 0.1,
                            'max_portfolio_risk': 0.05,
                            'max_daily_loss': 0.02
                        }
                    },
                    'performance_analytics': {
                        'enabled': True,
                        'auto_start': True,
                        'update_interval': 300
                    },
                    'backtest_engine': {
                        'enabled': True,
                        'auto_start': False
                    },
                    'deployment_pipeline': {
                        'enabled': True,
                        'auto_start': True
                    }
                },
                'api': {
                    'enabled': True,
                    'host': '0.0.0.0',
                    'port': 8000,
                    'cors_enabled': True
                },
                'monitoring': {
                    'metrics_retention': 86400,  # 24 hours
                    'event_retention': 604800,   # 7 days
                    'alert_thresholds': {
                        'error_rate': 0.05,
                        'response_time': 5.0,
                        'memory_usage': 0.85,
                        'cpu_usage': 0.80
                    }
                }
            }
            
            # Save default configuration
            with open(config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def initialize_components(self):
        """Initialize system components"""
        if not COMPONENTS_AVAILABLE:
            logger.warning("System components not available, running in limited mode")
            return
        
        component_configs = self.config.get('components', {})
        
        # Training Orchestrator
        if component_configs.get('training_orchestrator', {}).get('enabled', False):
            try:
                training_config = component_configs['training_orchestrator'].get('config_file', 'training_config.yaml')
                orchestrator = ModelTrainingOrchestrator(training_config)
                self.component_manager.register_component(
                    'training_orchestrator',
                    'training',
                    orchestrator,
                    component_configs['training_orchestrator']
                )
            except Exception as e:
                logger.error(f"Failed to initialize training orchestrator: {e}")
        
        # Continuous Learning Pipeline
        if component_configs.get('continuous_learning', {}).get('enabled', False):
            try:
                pipeline = ContinuousLearningPipeline()
                self.component_manager.register_component(
                    'continuous_learning',
                    'learning',
                    pipeline,
                    component_configs['continuous_learning'],
                    dependencies=['training_orchestrator']
                )
            except Exception as e:
                logger.error(f"Failed to initialize continuous learning pipeline: {e}")
        
        # Risk Monitor
        if component_configs.get('risk_monitor', {}).get('enabled', False):
            try:
                risk_config = component_configs['risk_monitor']
                risk_limits = RiskLimits(
                    max_position_size=risk_config.get('risk_limits', {}).get('max_position_size', 0.1),
                    max_portfolio_risk=risk_config.get('risk_limits', {}).get('max_portfolio_risk', 0.05),
                    max_daily_loss=risk_config.get('risk_limits', {}).get('max_daily_loss', 0.02)
                )
                risk_monitor = RiskMonitor(risk_limits)
                self.component_manager.register_component(
                    'risk_monitor',
                    'risk',
                    risk_monitor,
                    component_configs['risk_monitor']
                )
            except Exception as e:
                logger.error(f"Failed to initialize risk monitor: {e}")
        
        # Performance Analytics
        if component_configs.get('performance_analytics', {}).get('enabled', False):
            try:
                analytics = PerformanceAnalytics()
                self.component_manager.register_component(
                    'performance_analytics',
                    'analytics',
                    analytics,
                    component_configs['performance_analytics']
                )
            except Exception as e:
                logger.error(f"Failed to initialize performance analytics: {e}")
        
        # Backtest Engine
        if component_configs.get('backtest_engine', {}).get('enabled', False):
            try:
                backtest_engine = BacktestEngine()
                self.component_manager.register_component(
                    'backtest_engine',
                    'backtesting',
                    backtest_engine,
                    component_configs['backtest_engine']
                )
            except Exception as e:
                logger.error(f"Failed to initialize backtest engine: {e}")
        
        # Deployment Pipeline
        if component_configs.get('deployment_pipeline', {}).get('enabled', False):
            try:
                deployment_pipeline = DeploymentPipeline()
                self.component_manager.register_component(
                    'deployment_pipeline',
                    'deployment',
                    deployment_pipeline,
                    component_configs['deployment_pipeline']
                )
            except Exception as e:
                logger.error(f"Failed to initialize deployment pipeline: {e}")
    
    def setup_event_handlers(self):
        """Setup event handlers"""
        # Component error handler
        async def handle_component_error(event: SystemEvent):
            logger.error(f"Component error in {event.component}: {event.message}")
            
            # Attempt to restart component if configured
            if self.config.get('system', {}).get('auto_restart_on_error', True):
                logger.info(f"Attempting to restart component: {event.component}")
                await self.component_manager.restart_component(event.component)
        
        self.event_bus.subscribe(EventType.COMPONENT_ERROR, handle_component_error)
        
        # Risk alert handler
        async def handle_risk_alert(event: SystemEvent):
            logger.warning(f"Risk alert: {event.message}")
            # Could trigger additional risk management actions
        
        self.event_bus.subscribe(EventType.RISK_ALERT, handle_risk_alert)
        
        # Performance update handler
        async def handle_performance_update(event: SystemEvent):
            logger.info(f"Performance update: {event.message}")
            # Could trigger model retraining or parameter adjustments
        
        self.event_bus.subscribe(EventType.PERFORMANCE_UPDATE, handle_performance_update)
    
    async def start(self):
        """Start the system"""
        if self.state != SystemState.STOPPED:
            logger.warning(f"System is not in stopped state: {self.state.value}")
            return False
        
        try:
            logger.info("Starting Noryon AI Trading System")
            self.state = SystemState.INITIALIZING
            self.start_time = datetime.now()
            
            # Publish system start event
            await self.event_bus.publish(SystemEvent(
                event_id=str(uuid.uuid4()),
                event_type=EventType.SYSTEM_START,
                component="system",
                timestamp=datetime.now(),
                message="System startup initiated"
            ))
            
            # Start components in dependency order
            component_configs = self.config.get('components', {})
            
            for component_name, component_info in self.component_manager.components.items():
                if component_configs.get(component_name, {}).get('auto_start', False):
                    logger.info(f"Starting component: {component_name}")
                    success = await self.component_manager.start_component(component_name)
                    
                    if not success:
                        logger.error(f"Failed to start component: {component_name}")
                        # Continue with other components
            
            # Start background tasks
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            self.metrics_task = asyncio.create_task(self._metrics_collection_loop())
            
            self.state = SystemState.RUNNING
            logger.info("Noryon AI Trading System started successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system: {e}")
            self.state = SystemState.ERROR
            return False
    
    async def shutdown(self):
        """Shutdown the system gracefully"""
        if self.state == SystemState.STOPPED:
            logger.warning("System is already stopped")
            return
        
        try:
            logger.info("Shutting down Noryon AI Trading System")
            self.state = SystemState.STOPPING
            
            # Signal shutdown
            self.shutdown_event.set()
            
            # Stop background tasks
            if self.health_check_task:
                self.health_check_task.cancel()
            if self.metrics_task:
                self.metrics_task.cancel()
            
            # Stop components in reverse order
            component_names = list(self.component_manager.components.keys())
            for component_name in reversed(component_names):
                logger.info(f"Stopping component: {component_name}")
                await self.component_manager.stop_component(component_name)
            
            # Publish system stop event
            await self.event_bus.publish(SystemEvent(
                event_id=str(uuid.uuid4()),
                event_type=EventType.SYSTEM_STOP,
                component="system",
                timestamp=datetime.now(),
                message="System shutdown completed"
            ))
            
            self.state = SystemState.STOPPED
            logger.info("System shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
            self.state = SystemState.ERROR
    
    async def _health_check_loop(self):
        """Background health check loop"""
        interval = self.config.get('system', {}).get('health_check_interval', 60)
        
        while not self.shutdown_event.is_set():
            try:
                # Perform health checks on all components
                for component_name in self.component_manager.components.keys():
                    health_result = await self.component_manager.health_check_component(component_name)
                    
                    # Publish health check event
                    await self.event_bus.publish(SystemEvent(
                        event_id=str(uuid.uuid4()),
                        event_type=EventType.HEALTH_CHECK,
                        component=component_name,
                        timestamp=datetime.now(),
                        data=health_result,
                        severity="warning" if health_result.get('status') != 'healthy' else "info",
                        message=f"Health check: {health_result.get('status', 'unknown')}"
                    ))
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(interval)
    
    async def _metrics_collection_loop(self):
        """Background metrics collection loop"""
        interval = self.config.get('system', {}).get('metrics_interval', 30)
        
        while not self.shutdown_event.is_set():
            try:
                # Collect system metrics
                metrics = await self.collect_system_metrics()
                
                # Store metrics (could be extended to send to monitoring system)
                logger.debug(f"System metrics: CPU={metrics.cpu_usage:.1f}%, Memory={metrics.memory_usage:.1f}%")
                
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in metrics collection loop: {e}")
                await asyncio.sleep(interval)
    
    async def collect_system_metrics(self) -> SystemMetrics:
        """Collect system-wide metrics"""
        try:
            import psutil
            
            # System metrics
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk = psutil.disk_usage('/')
            
            # Component metrics
            active_components = sum(1 for comp in self.component_manager.components.values() 
                                  if comp.state == ComponentState.ACTIVE)
            
            # Event metrics
            event_stats = self.event_bus.get_event_stats()
            total_events = sum(event_stats.values())
            error_events = event_stats.get(EventType.COMPONENT_ERROR, 0)
            
            return SystemMetrics(
                uptime=datetime.now() - self.start_time if self.start_time else timedelta(0),
                active_components=active_components,
                total_events=total_events,
                error_count=error_events,
                memory_usage=memory.percent,
                cpu_usage=cpu_percent,
                disk_usage=(disk.used / disk.total) * 100,
                network_activity={'bytes_sent': 0, 'bytes_recv': 0},  # Placeholder
                performance_metrics={}  # Would be populated by performance analytics
            )
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
            return SystemMetrics(
                uptime=timedelta(0),
                active_components=0,
                total_events=0,
                error_count=0,
                memory_usage=0.0,
                cpu_usage=0.0,
                disk_usage=0.0,
                network_activity={},
                performance_metrics={}
            )
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        metrics = await self.collect_system_metrics()
        component_status = self.component_manager.get_component_status()
        event_stats = self.event_bus.get_event_stats()
        
        return {
            'system': {
                'name': self.config.get('system', {}).get('name', 'Noryon AI Trading System'),
                'version': self.config.get('system', {}).get('version', '1.0.0'),
                'state': self.state.value,
                'uptime': str(metrics.uptime),
                'start_time': self.start_time.isoformat() if self.start_time else None
            },
            'components': component_status,
            'metrics': {
                'active_components': metrics.active_components,
                'total_events': metrics.total_events,
                'error_count': metrics.error_count,
                'memory_usage': metrics.memory_usage,
                'cpu_usage': metrics.cpu_usage,
                'disk_usage': metrics.disk_usage
            },
            'events': {
                'stats': event_stats,
                'recent': [
                    {
                        'type': event.event_type.value,
                        'component': event.component,
                        'timestamp': event.timestamp.isoformat(),
                        'message': event.message,
                        'severity': event.severity
                    }
                    for event in self.event_bus.get_event_history(limit=10)
                ]
            },
            'timestamp': datetime.now().isoformat()
        }
    
    async def execute_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """Execute system command"""
        logger.info(f"Executing command: {command}")
        
        try:
            if command == "start_component":
                component_name = kwargs.get('component')
                if not component_name:
                    return {'success': False, 'error': 'Component name required'}
                
                success = await self.component_manager.start_component(component_name)
                return {'success': success}
            
            elif command == "stop_component":
                component_name = kwargs.get('component')
                if not component_name:
                    return {'success': False, 'error': 'Component name required'}
                
                success = await self.component_manager.stop_component(component_name)
                return {'success': success}
            
            elif command == "restart_component":
                component_name = kwargs.get('component')
                if not component_name:
                    return {'success': False, 'error': 'Component name required'}
                
                success = await self.component_manager.restart_component(component_name)
                return {'success': success}
            
            elif command == "health_check":
                component_name = kwargs.get('component')
                if component_name:
                    result = await self.component_manager.health_check_component(component_name)
                    return {'success': True, 'result': result}
                else:
                    # Health check all components
                    results = {}
                    for name in self.component_manager.components.keys():
                        results[name] = await self.component_manager.health_check_component(name)
                    return {'success': True, 'result': results}
            
            elif command == "get_status":
                status = await self.get_system_status()
                return {'success': True, 'result': status}
            
            elif command == "deploy":
                if 'deployment_pipeline' in self.component_manager.components:
                    pipeline = self.component_manager.components['deployment_pipeline'].instance
                    deployment_config = DeploymentConfig(**kwargs)
                    result = await pipeline.deploy(deployment_config)
                    return {'success': True, 'result': result}
                else:
                    return {'success': False, 'error': 'Deployment pipeline not available'}
            
            else:
                return {'success': False, 'error': f'Unknown command: {command}'}
                
        except Exception as e:
            logger.error(f"Error executing command {command}: {e}")
            return {'success': False, 'error': str(e)}
    
    async def run(self):
        """Run the system"""
        try:
            # Start the system
            if await self.start():
                logger.info("System is running. Press Ctrl+C to stop.")
                
                # Wait for shutdown signal
                await self.shutdown_event.wait()
            else:
                logger.error("Failed to start system")
                return False
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
        finally:
            await self.shutdown()
        
        return True

# Example usage and testing
async def main():
    """Main function for testing"""
    logger.info("Initializing Noryon AI Trading System")
    
    # Create system orchestrator
    system = SystemOrchestrator()
    
    # Run the system
    await system.run()

if __name__ == "__main__":
    asyncio.run(main())