#!/usr/bin/env python3
"""
Noryon AI Trading System - Main Entry Point

This script serves as the main entry point for the Noryon AI Trading System.
It initializes all components and starts the system based on the provided configuration.

Usage:
    python main.py [--config CONFIG_FILE] [--mode MODE] [--component COMPONENT]
    
Examples:
    python main.py                                    # Start full system
    python main.py --mode api                        # Start only API server
    python main.py --mode training                   # Start only training
    python main.py --component risk_monitor          # Start only risk monitor
    python main.py --config custom_config.yaml      # Use custom config
"""

import asyncio
import argparse
import logging
import signal
import sys
import os
from pathlib import Path
from typing import Optional, List

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Import system components
from system_integration import SystemOrchestrator
from api_server import create_app
from training_pipeline import TrainingPipeline
from continuous_learning_pipeline import ContinuousLearningPipeline
from risk_management_system import RiskMonitor
from performance_analytics import PerformanceAnalytics
from backtesting_framework import BacktestEngine
from automated_deployment_pipeline import DeploymentPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/main.log', mode='a')
    ]
)
logger = logging.getLogger(__name__)


class NoryonSystem:
    """
    Main system controller for the Noryon AI Trading System.
    
    This class manages the lifecycle of all system components and provides
    different operational modes for various use cases.
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the Noryon system.
        
        Args:
            config_path: Path to the configuration file
        """
        self.config_path = config_path
        self.orchestrator: Optional[SystemOrchestrator] = None
        self.running = False
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Ensure required directories exist
        self._setup_directories()
    
    def _setup_directories(self):
        """Create required directories if they don't exist."""
        directories = [
            "data", "models", "logs", "reports", "config", "temp",
            "mlruns", "artifacts", "checkpoints"
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
            logger.info(f"Ensured directory exists: {directory}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
        
        if self.orchestrator:
            asyncio.create_task(self.orchestrator.shutdown())
    
    async def start_full_system(self):
        """
        Start the complete Noryon AI Trading System.
        
        This includes all components: training, risk management, analytics,
        API server, web dashboard, and continuous learning.
        """
        logger.info("Starting Noryon AI Trading System (Full Mode)")
        
        try:
            # Initialize system orchestrator
            self.orchestrator = SystemOrchestrator(self.config_path)
            await self.orchestrator.initialize()
            
            # Start all components
            await self.orchestrator.start()
            
            self.running = True
            logger.info("Noryon AI Trading System started successfully")
            
            # Keep the system running
            while self.running:
                await asyncio.sleep(1)
                
                # Check system health
                status = await self.orchestrator.get_status()
                if status.get("status") == "error":
                    logger.error("System health check failed, initiating shutdown")
                    break
                    
        except Exception as e:
            logger.error(f"Error starting full system: {e}")
            raise
        finally:
            await self._shutdown()
    
    async def start_api_only(self):
        """
        Start only the API server component.
        
        Useful for providing API access without running the full trading system.
        """
        logger.info("Starting Noryon AI Trading System (API Only Mode)")
        
        try:
            # Initialize minimal orchestrator for API
            self.orchestrator = SystemOrchestrator(self.config_path)
            await self.orchestrator.initialize()
            
            # Start only essential components for API
            components_to_start = [
                "risk_monitor", "performance_analytics", "api_server"
            ]
            
            for component in components_to_start:
                await self.orchestrator.start_component(component)
            
            self.running = True
            logger.info("API server started successfully")
            
            # Keep the API running
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Error starting API server: {e}")
            raise
        finally:
            await self._shutdown()
    
    async def start_training_only(self):
        """
        Start only the training pipeline.
        
        Useful for running training jobs without the full system.
        """
        logger.info("Starting Noryon AI Trading System (Training Only Mode)")
        
        try:
            # Initialize training pipeline
            training_pipeline = TrainingPipeline(self.config_path)
            
            # Run training
            await training_pipeline.run_training()
            
            logger.info("Training completed successfully")
            
        except Exception as e:
            logger.error(f"Error during training: {e}")
            raise
    
    async def start_backtesting_only(self):
        """
        Start only the backtesting engine.
        
        Useful for running backtests without the full system.
        """
        logger.info("Starting Noryon AI Trading System (Backtesting Only Mode)")
        
        try:
            # Initialize backtesting engine
            backtest_engine = BacktestEngine()
            
            # Run example backtest
            from backtesting_framework import run_example_backtest
            await run_example_backtest()
            
            logger.info("Backtesting completed successfully")
            
        except Exception as e:
            logger.error(f"Error during backtesting: {e}")
            raise
    
    async def start_component(self, component_name: str):
        """
        Start a specific system component.
        
        Args:
            component_name: Name of the component to start
        """
        logger.info(f"Starting Noryon AI Trading System (Component: {component_name})")
        
        try:
            # Initialize orchestrator
            self.orchestrator = SystemOrchestrator(self.config_path)
            await self.orchestrator.initialize()
            
            # Start specific component
            await self.orchestrator.start_component(component_name)
            
            self.running = True
            logger.info(f"Component '{component_name}' started successfully")
            
            # Keep the component running
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Error starting component '{component_name}': {e}")
            raise
        finally:
            await self._shutdown()
    
    async def run_deployment(self):
        """
        Run the deployment pipeline.
        
        Useful for deploying trained models to production.
        """
        logger.info("Starting Noryon AI Trading System (Deployment Mode)")
        
        try:
            # Initialize deployment pipeline
            deployment_pipeline = DeploymentPipeline(self.config_path)
            
            # Run deployment
            result = await deployment_pipeline.deploy()
            
            if result.success:
                logger.info("Deployment completed successfully")
            else:
                logger.error(f"Deployment failed: {result.error_message}")
                
        except Exception as e:
            logger.error(f"Error during deployment: {e}")
            raise
    
    async def _shutdown(self):
        """Shutdown the system gracefully."""
        logger.info("Shutting down Noryon AI Trading System...")
        
        if self.orchestrator:
            await self.orchestrator.shutdown()
        
        logger.info("Noryon AI Trading System shutdown complete")


def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Noryon AI Trading System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    Start full system
  %(prog)s --mode api                        Start only API server
  %(prog)s --mode training                   Start only training
  %(prog)s --mode backtesting                Start only backtesting
  %(prog)s --mode deployment                 Run deployment pipeline
  %(prog)s --component risk_monitor          Start only risk monitor
  %(prog)s --config custom_config.yaml      Use custom config file
        """
    )
    
    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file (default: config.yaml)"
    )
    
    parser.add_argument(
        "--mode",
        type=str,
        choices=["full", "api", "training", "backtesting", "deployment"],
        default="full",
        help="System operation mode (default: full)"
    )
    
    parser.add_argument(
        "--component",
        type=str,
        help="Start specific component only"
    )
    
    parser.add_argument(
        "--log-level",
        type=str,
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="Noryon AI Trading System v1.0.0"
    )
    
    return parser.parse_args()


async def main():
    """
    Main entry point for the Noryon AI Trading System.
    """
    # Parse command line arguments
    args = parse_arguments()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Check if config file exists
    if not Path(args.config).exists():
        logger.error(f"Configuration file not found: {args.config}")
        sys.exit(1)
    
    # Initialize system
    system = NoryonSystem(args.config)
    
    try:
        # Start system based on mode
        if args.component:
            await system.start_component(args.component)
        elif args.mode == "full":
            await system.start_full_system()
        elif args.mode == "api":
            await system.start_api_only()
        elif args.mode == "training":
            await system.start_training_only()
        elif args.mode == "backtesting":
            await system.start_backtesting_only()
        elif args.mode == "deployment":
            await system.run_deployment()
        else:
            logger.error(f"Unknown mode: {args.mode}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run the main function
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown complete.")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)