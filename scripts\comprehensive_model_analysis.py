#!/usr/bin/env python3
"""
Comprehensive Model Analysis for Noryon AI Trading System
Detect new models, verify reinstalled ones, and assess training readiness
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from datetime import datetime
import psutil
import torch
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree

console = Console()

class ComprehensiveModelAnalyzer:
    """Comprehensive analysis of all AI models in the system"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.scan_directories = [
            "ollama",
            "models", 
            "deepseek r1",
            "qwen3",
            "mistral",
            "gemma",
            "phi"
        ]
        self.model_inventory = {}
        self.hardware_info = self._detect_hardware()
        
    def _detect_hardware(self):
        """Detect current hardware capabilities"""
        return {
            "gpu_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
            "gpu_memory": [torch.cuda.get_device_properties(i).total_memory / (1024**3) 
                          for i in range(torch.cuda.device_count())] if torch.cuda.is_available() else [],
            "cpu_cores": psutil.cpu_count(),
            "ram_total": psutil.virtual_memory().total / (1024**3),
            "cuda_version": torch.version.cuda if torch.cuda.is_available() else None
        }
    
    def scan_ollama_models(self):
        """Scan for Ollama models"""
        console.print("[yellow]🔍 Scanning Ollama models...[/yellow]")
        
        ollama_models = {}
        
        try:
            # Get Ollama model list
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            model_name = parts[0]
                            model_id = parts[1] if len(parts) > 1 else "unknown"
                            size = parts[2] if len(parts) > 2 else "unknown"
                            
                            ollama_models[model_name] = {
                                "type": "ollama",
                                "model_id": model_id,
                                "size": size,
                                "status": "available",
                                "location": "ollama_registry",
                                "trained": "noryon" in model_name.lower() and "finance" in model_name.lower()
                            }
            
            console.print(f"[green]✅ Found {len(ollama_models)} Ollama models[/green]")
            
        except Exception as e:
            console.print(f"[red]❌ Ollama scan failed: {e}[/red]")
        
        return ollama_models
    
    def scan_directory_models(self):
        """Scan for models in project directories"""
        console.print("[yellow]🔍 Scanning project directories...[/yellow]")
        
        directory_models = {}
        
        for scan_dir in self.scan_directories:
            dir_path = self.project_root / scan_dir
            
            if dir_path.exists() and dir_path.is_dir():
                console.print(f"  Scanning: {scan_dir}")
                
                # Calculate directory size
                total_size = 0
                file_count = 0
                config_files = []
                model_files = []
                
                try:
                    for file_path in dir_path.rglob('*'):
                        if file_path.is_file():
                            file_count += 1
                            total_size += file_path.stat().st_size
                            
                            # Identify important files
                            if 'config' in file_path.name.lower() and file_path.suffix == '.json':
                                config_files.append(file_path.name)
                            elif file_path.suffix in ['.safetensors', '.bin', '.pt', '.pth']:
                                model_files.append(file_path.name)
                    
                    # Determine model status
                    status = "unknown"
                    if config_files and model_files:
                        status = "complete"
                    elif config_files:
                        status = "partial_config"
                    elif model_files:
                        status = "partial_weights"
                    elif file_count > 0:
                        status = "files_present"
                    else:
                        status = "empty"
                    
                    directory_models[scan_dir] = {
                        "type": "local_directory",
                        "size_gb": total_size / (1024**3),
                        "file_count": file_count,
                        "config_files": config_files,
                        "model_files": model_files,
                        "status": status,
                        "location": str(dir_path),
                        "trained": any("finance" in f for f in model_files)
                    }
                    
                except Exception as e:
                    console.print(f"    [red]❌ Error scanning {scan_dir}: {e}[/red]")
                    directory_models[scan_dir] = {
                        "type": "local_directory",
                        "status": "error",
                        "error": str(e),
                        "location": str(dir_path)
                    }
        
        console.print(f"[green]✅ Scanned {len(directory_models)} directories[/green]")
        return directory_models
    
    def verify_model_configs(self):
        """Verify model configurations for training readiness"""
        console.print("[yellow]🔧 Verifying model configurations...[/yellow]")
        
        config_status = {}
        
        # Check specific models that were problematic
        problematic_models = {
            "deepseek r1": "DeepSeek R1",
            "qwen3": "Qwen 3", 
            "mistral": "Mistral 3"
        }
        
        for dir_name, model_name in problematic_models.items():
            dir_path = self.project_root / dir_name
            
            if dir_path.exists():
                config_files = list(dir_path.glob("*config*.json"))
                tokenizer_files = list(dir_path.glob("*tokenizer*"))
                model_files = list(dir_path.glob("*.safetensors")) + list(dir_path.glob("*.bin"))
                
                # Try to load and validate config
                config_valid = False
                config_details = {}
                
                for config_file in config_files:
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            
                        config_details = {
                            "model_type": config.get("model_type", "unknown"),
                            "vocab_size": config.get("vocab_size", "unknown"),
                            "hidden_size": config.get("hidden_size", "unknown"),
                            "architectures": config.get("architectures", [])
                        }
                        config_valid = True
                        break
                        
                    except Exception as e:
                        console.print(f"    [yellow]⚠️ Config file {config_file.name} invalid: {e}[/yellow]")
                
                config_status[model_name] = {
                    "config_files": len(config_files),
                    "tokenizer_files": len(tokenizer_files),
                    "model_files": len(model_files),
                    "config_valid": config_valid,
                    "config_details": config_details,
                    "training_ready": config_valid and len(tokenizer_files) > 0 and len(model_files) > 0
                }
            else:
                config_status[model_name] = {
                    "exists": False,
                    "training_ready": False
                }
        
        return config_status
    
    def detect_new_models(self):
        """Detect newly added models since last scan"""
        console.print("[yellow]🆕 Detecting new models...[/yellow]")
        
        # This would compare against a previous scan file
        # For now, we'll identify models that weren't in our original list
        
        known_models = {
            "noryon-gemma-3-12b-finance",
            "noryon-phi-4-9b-finance", 
            "noryon-gemma-3-12b-enhanced-enhanced",
            "noryon-phi-4-9b-enhanced-enhanced"
        }
        
        new_models = {}
        
        # Check Ollama for new models
        ollama_models = self.scan_ollama_models()
        for model_name, details in ollama_models.items():
            if model_name not in known_models:
                new_models[model_name] = {
                    **details,
                    "newly_detected": True,
                    "source": "ollama"
                }
        
        console.print(f"[green]✅ Detected {len(new_models)} new models[/green]")
        return new_models
    
    def assess_training_readiness(self):
        """Assess which models are ready for training"""
        console.print("[yellow]📊 Assessing training readiness...[/yellow]")
        
        readiness_assessment = {
            "ready_for_training": [],
            "needs_configuration": [],
            "needs_reinstall": [],
            "already_trained": []
        }
        
        # Combine all model data
        all_models = {
            **self.scan_ollama_models(),
            **self.scan_directory_models()
        }
        
        config_status = self.verify_model_configs()
        
        for model_name, details in all_models.items():
            if details.get("trained", False):
                readiness_assessment["already_trained"].append(model_name)
            elif details.get("status") == "complete" or details.get("status") == "available":
                if model_name in ["DeepSeek R1", "Qwen 3", "Mistral 3"]:
                    config_info = config_status.get(model_name, {})
                    if config_info.get("training_ready", False):
                        readiness_assessment["ready_for_training"].append(model_name)
                    else:
                        readiness_assessment["needs_configuration"].append(model_name)
                else:
                    readiness_assessment["ready_for_training"].append(model_name)
            elif details.get("status") in ["partial_config", "partial_weights", "error"]:
                readiness_assessment["needs_reinstall"].append(model_name)
            else:
                readiness_assessment["needs_configuration"].append(model_name)
        
        return readiness_assessment, all_models, config_status
    
    def generate_comprehensive_report(self):
        """Generate comprehensive analysis report"""
        console.print(Panel(
            "[bold blue]🔍 Comprehensive Model Analysis[/bold blue]\n\n"
            "Analyzing all AI models in the Noryon trading system:\n"
            "• Detecting new models\n"
            "• Verifying reinstalled models\n"
            "• Assessing training readiness\n"
            "• Generating recommendations",
            title="Model Analysis"
        ))
        
        # Perform analysis
        readiness_assessment, all_models, config_status = self.assess_training_readiness()
        new_models = self.detect_new_models()
        
        # Hardware summary
        console.print("\n[bold cyan]💻 Hardware Configuration[/bold cyan]")
        hardware_table = Table()
        hardware_table.add_column("Component", style="cyan")
        hardware_table.add_column("Details", style="green")
        
        hardware_table.add_row("CPU Cores", str(self.hardware_info["cpu_cores"]))
        hardware_table.add_row("RAM Total", f"{self.hardware_info['ram_total']:.1f} GB")
        hardware_table.add_row("GPU Available", "✅ Yes" if self.hardware_info["gpu_available"] else "❌ No")
        
        if self.hardware_info["gpu_available"]:
            hardware_table.add_row("GPU Count", str(self.hardware_info["gpu_count"]))
            hardware_table.add_row("CUDA Version", self.hardware_info["cuda_version"] or "Unknown")
            for i, memory in enumerate(self.hardware_info["gpu_memory"]):
                hardware_table.add_row(f"GPU {i} Memory", f"{memory:.1f} GB")
        
        console.print(hardware_table)
        
        # Model inventory
        console.print("\n[bold cyan]📋 Complete Model Inventory[/bold cyan]")
        inventory_table = Table()
        inventory_table.add_column("Model Name", style="cyan")
        inventory_table.add_column("Type", style="yellow")
        inventory_table.add_column("Size", style="blue")
        inventory_table.add_column("Status", style="green")
        inventory_table.add_column("Training Ready", style="magenta")
        
        for model_name, details in all_models.items():
            model_type = details.get("type", "unknown")
            size = details.get("size", details.get("size_gb", "unknown"))
            if isinstance(size, float):
                size = f"{size:.1f} GB"
            status = details.get("status", "unknown")
            
            # Determine training readiness
            if model_name in readiness_assessment["ready_for_training"]:
                training_ready = "🚀 Ready"
            elif model_name in readiness_assessment["already_trained"]:
                training_ready = "✅ Trained"
            elif model_name in readiness_assessment["needs_configuration"]:
                training_ready = "🔧 Config Needed"
            else:
                training_ready = "❌ Not Ready"
            
            inventory_table.add_row(model_name, model_type, str(size), status, training_ready)
        
        console.print(inventory_table)
        
        # New models detected
        if new_models:
            console.print("\n[bold cyan]🆕 Newly Detected Models[/bold cyan]")
            new_table = Table()
            new_table.add_column("Model Name", style="cyan")
            new_table.add_column("Source", style="yellow")
            new_table.add_column("Size", style="blue")
            new_table.add_column("Status", style="green")
            
            for model_name, details in new_models.items():
                new_table.add_row(
                    model_name,
                    details.get("source", "unknown"),
                    details.get("size", "unknown"),
                    details.get("status", "unknown")
                )
            
            console.print(new_table)
        
        # Training readiness summary
        console.print("\n[bold cyan]📊 Training Readiness Summary[/bold cyan]")
        readiness_table = Table()
        readiness_table.add_column("Category", style="cyan")
        readiness_table.add_column("Count", style="yellow")
        readiness_table.add_column("Models", style="green")
        
        for category, models in readiness_assessment.items():
            readiness_table.add_row(
                category.replace("_", " ").title(),
                str(len(models)),
                ", ".join(models[:3]) + ("..." if len(models) > 3 else "")
            )
        
        console.print(readiness_table)
        
        return {
            "all_models": all_models,
            "new_models": new_models,
            "readiness_assessment": readiness_assessment,
            "config_status": config_status,
            "hardware_info": self.hardware_info
        }

def main():
    """Main analysis function"""
    console.print("[bold blue]🔍 Starting Comprehensive Model Analysis...[/bold blue]\n")
    
    analyzer = ComprehensiveModelAnalyzer()
    analysis_results = analyzer.generate_comprehensive_report()
    
    # Generate recommendations
    console.print("\n[bold green]🎯 Next Steps Recommendations[/bold green]")
    
    ready_models = analysis_results["readiness_assessment"]["ready_for_training"]
    trained_models = analysis_results["readiness_assessment"]["already_trained"]
    
    if ready_models:
        console.print(f"1. 🚀 Train {len(ready_models)} ready models: {', '.join(ready_models)}")
    
    if len(trained_models) >= 4:
        console.print("2. ✅ Integrate existing trained models into ensemble system")
    
    console.print("3. 🔧 Configure models that need setup")
    console.print("4. 📊 Set up parallel training for multiple models")
    console.print("5. 🧪 Run comprehensive testing on all trained models")
    
    return analysis_results

if __name__ == "__main__":
    results = main()
