#!/usr/bin/env python3
"""
Error-Free Model Launcher - No Restrictions, No Errors
"""

import subprocess
import sys
import os
from pathlib import Path

# Fix encoding issues
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['PYTHONUTF8'] = '1'

def launch_model_safe(model_name, prompt=None):
    """Launch model safely without errors"""
    try:
        if prompt:
            cmd = ['ollama', 'run', model_name, prompt]
        else:
            cmd = ['ollama', 'run', model_name]
        
        result = subprocess.run(
            cmd,
            encoding='utf-8',
            errors='replace',
            timeout=120
        )
        return True
    except Exception as e:
        print(f"Error launching {model_name}: {e}")
        return False

def main():
    """Main launcher"""
    print("🚀 Error-Free Model Launcher")
    print("Available commands:")
    print("1. Launch finance reasoning model")
    print("2. Launch market analysis model") 
    print("3. Launch unrestricted chat")
    
    choice = input("Choose option (1-3): ")
    
    if choice == "1":
        launch_model_safe("noryon-phi4-reasoning-finance-v2")
    elif choice == "2":
        launch_model_safe("noryon-gemma-3-12b-finance")
    elif choice == "3":
        os.system("python unrestricted_chat.py")

if __name__ == "__main__":
    main()
