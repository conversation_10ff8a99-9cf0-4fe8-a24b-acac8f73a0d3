#!/usr/bin/env python3
"""
Specialized AI Teams - Create expert teams for different trading tasks
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass
from agent_command_center import AgentCommandCenter

@dataclass
class TeamAnalysis:
    team_name: str
    task_type: str
    agents_used: List[str]
    consensus_level: float
    confidence_avg: float
    response_time: float
    analysis_quality: str
    recommendations: List[str]
    timestamp: datetime

class SpecializedAITeams:
    """Create and manage specialized AI teams for different trading tasks"""
    
    def __init__(self):
        self.command_center = AgentCommandCenter()
        
        # Define specialized teams based on agent strengths
        self.teams = {
            'financial_analysis_team': {
                'agents': ['deepseek_finance', 'qwen_finance', 'deepseek_r1'],
                'specialty': 'Deep financial analysis and market evaluation',
                'best_for': ['market_analysis', 'fundamental_analysis', 'financial_modeling']
            },
            'risk_assessment_team': {
                'agents': ['deepseek_finance', 'cogito_reasoner', 'deepseek_r1'],
                'specialty': 'Comprehensive risk evaluation and mitigation',
                'best_for': ['risk_analysis', 'portfolio_risk', 'position_sizing']
            },
            'strategic_planning_team': {
                'agents': ['marco_o1', 'deepseek_r1', 'cogito_reasoner'],
                'specialty': 'Strategic thinking and long-term planning',
                'best_for': ['strategy_development', 'long_term_planning', 'decision_making']
            },
            'quantitative_analysis_team': {
                'agents': ['phi4_reasoning', 'deepseek_r1', 'granite_structured'],
                'specialty': 'Mathematical and quantitative analysis',
                'best_for': ['technical_analysis', 'mathematical_modeling', 'statistical_analysis']
            },
            'rapid_response_team': {
                'agents': ['exaone_fast', 'marco_o1', 'granite_structured'],
                'specialty': 'Quick insights and fast decision making',
                'best_for': ['quick_decisions', 'market_alerts', 'rapid_analysis']
            },
            'consensus_building_team': {
                'agents': ['deepseek_r1', 'marco_o1', 'cogito_reasoner', 'deepseek_finance'],
                'specialty': 'Building consensus on complex decisions',
                'best_for': ['major_decisions', 'conflicting_signals', 'comprehensive_review']
            },
            'pattern_recognition_team': {
                'agents': ['gemma3_enhanced', 'deepseek_r1', 'cogito_reasoner'],
                'specialty': 'Pattern recognition and trend analysis',
                'best_for': ['trend_analysis', 'pattern_detection', 'market_timing']
            }
        }
        
        print("🎯 Specialized AI Teams initialized")
        print(f"   🤖 Total agents available: {len(self.command_center.agents)}")
        print(f"   👥 Specialized teams: {len(self.teams)}")
        
        # Show teams
        for team_name, team_info in self.teams.items():
            available_agents = [agent for agent in team_info['agents'] if agent in self.command_center.agents]
            print(f"   • {team_name}: {len(available_agents)}/{len(team_info['agents'])} agents ready")
    
    async def deploy_team(self, team_name: str, task: str, context: Dict = None) -> Optional[TeamAnalysis]:
        """Deploy a specialized team for a specific task"""
        if team_name not in self.teams:
            print(f"❌ Unknown team: {team_name}")
            return None
        
        team_info = self.teams[team_name]
        available_agents = [agent for agent in team_info['agents'] if agent in self.command_center.agents]
        
        if not available_agents:
            print(f"❌ No agents available for team: {team_name}")
            return None
        
        print(f"\n👥 DEPLOYING {team_name.upper()}")
        print(f"   Specialty: {team_info['specialty']}")
        print(f"   Agents: {available_agents}")
        print(f"   Task: {task}")
        
        # Create enhanced prompt for team
        team_prompt = f"""You are part of the {team_name.replace('_', ' ').title()} specializing in {team_info['specialty']}.

TASK: {task}

CONTEXT: {context or 'Standard market conditions'}

As a specialist in {team_info['specialty']}, provide:
1. Your expert analysis
2. Specific recommendations
3. Confidence level (1-10)
4. Key risk factors
5. Action items

Focus on your team's specialty: {team_info['specialty']}"""

        start_time = time.time()
        
        # Deploy team (multi-agent analysis)
        summary = self.command_center.multi_agent_analysis(team_prompt, agents=available_agents)
        
        response_time = time.time() - start_time
        
        if summary['successful_responses'] == 0:
            print(f"❌ Team deployment failed - no responses")
            return None
        
        # Analyze team performance
        consensus_level = summary['success_rate']
        
        # Extract recommendations from responses
        recommendations = []
        total_confidence = 0
        
        for agent_name, response_data in summary['responses'].items():
            if response_data and response_data['success']:
                response_text = response_data['response'].lower()
                
                # Extract confidence
                import re
                conf_patterns = [
                    r'confidence[:\s]+(\d+)[/\s]*10',
                    r'(\d+)[/\s]*10\s*confidence'
                ]
                
                confidence = 7  # Default
                for pattern in conf_patterns:
                    match = re.search(pattern, response_text)
                    if match:
                        confidence = int(match.group(1))
                        break
                
                total_confidence += confidence
                
                # Extract key recommendations (simplified)
                if 'recommend' in response_text:
                    lines = response_data['response'].split('\n')
                    for line in lines:
                        if 'recommend' in line.lower() and len(line) < 200:
                            recommendations.append(line.strip())
        
        avg_confidence = total_confidence / summary['successful_responses'] if summary['successful_responses'] > 0 else 0
        
        # Determine analysis quality
        if consensus_level >= 0.8 and avg_confidence >= 8:
            quality = "Excellent"
        elif consensus_level >= 0.6 and avg_confidence >= 6:
            quality = "Good"
        elif consensus_level >= 0.4 and avg_confidence >= 4:
            quality = "Fair"
        else:
            quality = "Poor"
        
        team_analysis = TeamAnalysis(
            team_name=team_name,
            task_type=task,
            agents_used=available_agents,
            consensus_level=consensus_level,
            confidence_avg=avg_confidence / 10.0,  # Normalize to 0-1
            response_time=response_time,
            analysis_quality=quality,
            recommendations=recommendations[:5],  # Top 5 recommendations
            timestamp=datetime.now()
        )
        
        print(f"\n📊 TEAM ANALYSIS RESULTS:")
        print(f"   Consensus: {team_analysis.consensus_level:.1%}")
        print(f"   Confidence: {team_analysis.confidence_avg:.2f}")
        print(f"   Quality: {team_analysis.analysis_quality}")
        print(f"   Response time: {team_analysis.response_time:.1f}s")
        print(f"   Recommendations: {len(team_analysis.recommendations)}")
        
        return team_analysis
    
    async def multi_team_analysis(self, task: str, teams: List[str] = None) -> Dict[str, TeamAnalysis]:
        """Deploy multiple teams for comprehensive analysis"""
        if teams is None:
            teams = ['financial_analysis_team', 'risk_assessment_team', 'strategic_planning_team']
        
        print(f"\n🚀 MULTI-TEAM ANALYSIS")
        print(f"   Task: {task}")
        print(f"   Teams: {teams}")
        print("=" * 70)
        
        results = {}
        
        for team_name in teams:
            if team_name in self.teams:
                print(f"\n{'='*50}")
                result = await self.deploy_team(team_name, task)
                if result:
                    results[team_name] = result
                    print(f"✅ {team_name} analysis complete")
                else:
                    print(f"❌ {team_name} analysis failed")
        
        # Summary
        if results:
            print(f"\n📊 MULTI-TEAM SUMMARY:")
            print(f"   Teams deployed: {len(results)}")
            
            avg_consensus = sum(r.consensus_level for r in results.values()) / len(results)
            avg_confidence = sum(r.confidence_avg for r in results.values()) / len(results)
            total_time = sum(r.response_time for r in results.values())
            
            print(f"   Average consensus: {avg_consensus:.1%}")
            print(f"   Average confidence: {avg_confidence:.2f}")
            print(f"   Total analysis time: {total_time:.1f}s")
            
            # Quality distribution
            quality_counts = {}
            for result in results.values():
                quality_counts[result.analysis_quality] = quality_counts.get(result.analysis_quality, 0) + 1
            
            print(f"   Quality distribution: {quality_counts}")
        
        return results
    
    def get_best_team_for_task(self, task_type: str) -> str:
        """Get the best team for a specific task type"""
        task_mapping = {
            'market_analysis': 'financial_analysis_team',
            'risk_analysis': 'risk_assessment_team',
            'strategy_development': 'strategic_planning_team',
            'technical_analysis': 'quantitative_analysis_team',
            'quick_decision': 'rapid_response_team',
            'major_decision': 'consensus_building_team',
            'trend_analysis': 'pattern_recognition_team'
        }
        
        return task_mapping.get(task_type, 'financial_analysis_team')
    
    def get_team_status(self) -> Dict:
        """Get status of all teams"""
        team_status = {}
        
        for team_name, team_info in self.teams.items():
            available_agents = [agent for agent in team_info['agents'] if agent in self.command_center.agents]
            
            team_status[team_name] = {
                'specialty': team_info['specialty'],
                'total_agents': len(team_info['agents']),
                'available_agents': len(available_agents),
                'readiness': len(available_agents) / len(team_info['agents']),
                'best_for': team_info['best_for'],
                'agents': available_agents
            }
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_teams': len(self.teams),
            'teams': team_status,
            'overall_readiness': sum(t['readiness'] for t in team_status.values()) / len(team_status)
        }

async def main():
    """Test specialized AI teams"""
    print("👥 SPECIALIZED AI TEAMS - PHASE C")
    print("=" * 60)
    
    # Initialize teams
    teams = SpecializedAITeams()
    
    # Test individual teams
    test_tasks = [
        {
            'team': 'financial_analysis_team',
            'task': 'Analyze Bitcoin at $32,000 for potential investment of $50,000',
            'context': {'market_trend': 'bullish', 'volatility': 'medium'}
        },
        {
            'team': 'risk_assessment_team', 
            'task': 'Assess the risk of a $50,000 cryptocurrency investment',
            'context': {'portfolio_size': 100000, 'risk_tolerance': 'moderate'}
        },
        {
            'team': 'rapid_response_team',
            'task': 'Quick decision: Bitcoin just dropped 5% in 10 minutes. Buy, sell, or hold?',
            'context': {'urgency': 'high', 'position_size': 'medium'}
        }
    ]
    
    individual_results = []
    
    for test in test_tasks:
        print(f"\n{'='*60}")
        print(f"TESTING {test['team'].upper()}")
        print(f"{'='*60}")
        
        result = await teams.deploy_team(test['team'], test['task'], test['context'])
        if result:
            individual_results.append(result)
            print(f"✅ Team test successful!")
        else:
            print(f"❌ Team test failed")
    
    # Test multi-team analysis
    print(f"\n{'='*60}")
    print(f"TESTING MULTI-TEAM ANALYSIS")
    print(f"{'='*60}")
    
    multi_task = "Should I invest $100,000 in a diversified cryptocurrency portfolio right now?"
    multi_results = await teams.multi_team_analysis(
        multi_task, 
        ['financial_analysis_team', 'risk_assessment_team', 'strategic_planning_team']
    )
    
    # Final summary
    print(f"\n🎉 SPECIALIZED AI TEAMS TESTING COMPLETE!")
    print(f"   Individual team tests: {len(individual_results)}")
    print(f"   Multi-team analysis: {len(multi_results)} teams")
    
    # Team status
    status = teams.get_team_status()
    print(f"   Overall team readiness: {status['overall_readiness']:.1%}")
    print(f"   Total teams available: {status['total_teams']}")
    
    print(f"\n🎯 TEAM CAPABILITIES:")
    for team_name, team_status in status['teams'].items():
        readiness = team_status['readiness']
        status_icon = "✅" if readiness >= 0.8 else "⚠️" if readiness >= 0.5 else "❌"
        print(f"   {status_icon} {team_name}: {readiness:.1%} ready ({team_status['available_agents']}/{team_status['total_agents']} agents)")

if __name__ == "__main__":
    asyncio.run(main())
