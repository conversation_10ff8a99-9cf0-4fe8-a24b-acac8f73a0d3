<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noryon AI Trading System - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
        }

        .header .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-indicator.running {
            background: #27ae60;
        }

        .status-indicator.stopped {
            background: #e74c3c;
        }

        .status-indicator.warning {
            background: #f39c12;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            font-weight: 500;
            color: #34495e;
        }

        .metric-value {
            font-weight: bold;
            color: #2c3e50;
        }

        .metric-value.positive {
            color: #27ae60;
        }

        .metric-value.negative {
            color: #e74c3c;
        }

        .component-list {
            list-style: none;
        }

        .component-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            transition: background 0.3s ease;
        }

        .component-item:hover {
            background: #e9ecef;
        }

        .component-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .component-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            text-transform: uppercase;
        }

        .component-status.active {
            background: #d4edda;
            color: #155724;
        }

        .component-status.inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .component-status.error {
            background: #f5c6cb;
            color: #721c24;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background: #e67e22;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .chart-container {
            height: 300px;
            margin-top: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-style: italic;
        }

        .log-container {
            background: #2c3e50;
            color: #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.2);
        }

        .log-entry.warning {
            background: rgba(243, 156, 18, 0.2);
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.2);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #27ae60;
        }

        .notification.error {
            background: #e74c3c;
        }

        .notification.warning {
            background: #f39c12;
        }

        .notification.info {
            background: #3498db;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
            }
            
            .controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Noryon AI Trading System</h1>
            <p class="subtitle">Advanced AI-Powered Trading Platform Dashboard</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator running" id="systemStatus"></div>
                <span>System Status: <strong id="systemStatusText">Running</strong></span>
            </div>
            <div class="status-item">
                <span>Uptime: <strong id="systemUptime">--</strong></span>
            </div>
            <div class="status-item">
                <span>Active Components: <strong id="activeComponents">--</strong></span>
            </div>
            <div class="status-item">
                <span>Last Update: <strong id="lastUpdate">--</strong></span>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- System Overview Card -->
            <div class="card">
                <h3>📊 System Overview</h3>
                <div class="metric">
                    <span class="metric-label">CPU Usage</span>
                    <span class="metric-value" id="cpuUsage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage</span>
                    <span class="metric-value" id="memoryUsage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Disk Usage</span>
                    <span class="metric-value" id="diskUsage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Events</span>
                    <span class="metric-value" id="totalEvents">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Error Count</span>
                    <span class="metric-value" id="errorCount">--</span>
                </div>
            </div>

            <!-- Components Status Card -->
            <div class="card">
                <h3>🔧 Components Status</h3>
                <ul class="component-list" id="componentsList">
                    <!-- Components will be populated dynamically -->
                </ul>
                <div class="controls">
                    <button class="btn btn-success" onclick="startAllComponents()">Start All</button>
                    <button class="btn btn-warning" onclick="restartAllComponents()">Restart All</button>
                    <button class="btn btn-danger" onclick="stopAllComponents()">Stop All</button>
                </div>
            </div>

            <!-- Performance Metrics Card -->
            <div class="card">
                <h3>📈 Performance Metrics</h3>
                <div class="metric">
                    <span class="metric-label">Portfolio Value</span>
                    <span class="metric-value positive" id="portfolioValue">$1,050,000</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Daily Return</span>
                    <span class="metric-value positive" id="dailyReturn">+1.2%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Return</span>
                    <span class="metric-value positive" id="totalReturn">+5.0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Sharpe Ratio</span>
                    <span class="metric-value" id="sharpeRatio">1.35</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Drawdown</span>
                    <span class="metric-value negative" id="maxDrawdown">-8.0%</span>
                </div>
                <div class="chart-container">
                    Performance Chart (Chart.js integration would go here)
                </div>
            </div>

            <!-- Risk Management Card -->
            <div class="card">
                <h3>⚠️ Risk Management</h3>
                <div class="metric">
                    <span class="metric-label">Portfolio Risk</span>
                    <span class="metric-value" id="portfolioRisk">3.0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">VaR (1 Day)</span>
                    <span class="metric-value negative" id="var1d">-2.0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Position Count</span>
                    <span class="metric-value" id="positionCount">12</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Circuit Breaker</span>
                    <span class="metric-value" id="circuitBreaker">Inactive</span>
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="openRiskLimitsModal()">Update Limits</button>
                    <button class="btn btn-warning" onclick="activateCircuitBreaker()">Emergency Stop</button>
                </div>
            </div>

            <!-- Training Status Card -->
            <div class="card">
                <h3>🧠 Model Training</h3>
                <div class="metric">
                    <span class="metric-label">Training Status</span>
                    <span class="metric-value" id="trainingStatus">Idle</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Current Model</span>
                    <span class="metric-value" id="currentModel">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Progress</span>
                    <span class="metric-value" id="trainingProgress">0%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Training</span>
                    <span class="metric-value" id="lastTraining">--</span>
                </div>
                <div class="controls">
                    <button class="btn btn-success" onclick="openTrainingModal()">Start Training</button>
                    <button class="btn btn-primary" onclick="openBacktestModal()">Run Backtest</button>
                </div>
            </div>

            <!-- System Logs Card -->
            <div class="card">
                <h3>📝 System Logs</h3>
                <div class="log-container" id="systemLogs">
                    <!-- Logs will be populated dynamically -->
                </div>
                <div class="controls">
                    <button class="btn btn-primary" onclick="clearLogs()">Clear Logs</button>
                    <button class="btn btn-primary" onclick="downloadLogs()">Download Logs</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Training Modal -->
    <div id="trainingModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('trainingModal')">&times;</span>
            <h2>🧠 Start Model Training</h2>
            <form id="trainingForm">
                <div class="form-group">
                    <label for="trainingModels">Models to Train:</label>
                    <select id="trainingModels" multiple>
                        <option value="deepseek">DeepSeek</option>
                        <option value="mistral">Mistral</option>
                        <option value="qwen3">Qwen3</option>
                        <option value="llama">LLaMA</option>
                        <option value="gemma">Gemma</option>
                        <option value="phi">Phi</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="parallelTraining">Parallel Training:</label>
                    <input type="checkbox" id="parallelTraining">
                </div>
                <div class="form-group">
                    <label for="gpuCount">GPU Count:</label>
                    <input type="number" id="gpuCount" value="1" min="1" max="8">
                </div>
                <div class="form-group">
                    <label for="experimentName">Experiment Name:</label>
                    <input type="text" id="experimentName" placeholder="Optional experiment name">
                </div>
                <div class="controls">
                    <button type="submit" class="btn btn-success">Start Training</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('trainingModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Backtest Modal -->
    <div id="backtestModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('backtestModal')">&times;</span>
            <h2>📊 Run Backtest</h2>
            <form id="backtestForm">
                <div class="form-group">
                    <label for="backtestStrategy">Strategy:</label>
                    <select id="backtestStrategy">
                        <option value="sma">Simple Moving Average</option>
                        <option value="rsi">RSI Strategy</option>
                        <option value="ensemble">Ensemble Strategy</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="startDate">Start Date:</label>
                    <input type="date" id="startDate" required>
                </div>
                <div class="form-group">
                    <label for="endDate">End Date:</label>
                    <input type="date" id="endDate" required>
                </div>
                <div class="form-group">
                    <label for="initialCapital">Initial Capital:</label>
                    <input type="number" id="initialCapital" value="100000" min="1000">
                </div>
                <div class="form-group">
                    <label for="symbols">Trading Symbols (comma-separated):</label>
                    <input type="text" id="symbols" value="AAPL,GOOGL,MSFT,TSLA,AMZN">
                </div>
                <div class="controls">
                    <button type="submit" class="btn btn-success">Run Backtest</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('backtestModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Risk Limits Modal -->
    <div id="riskLimitsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('riskLimitsModal')">&times;</span>
            <h2>⚠️ Update Risk Limits</h2>
            <form id="riskLimitsForm">
                <div class="form-group">
                    <label for="maxPositionSize">Max Position Size (%):</label>
                    <input type="number" id="maxPositionSize" value="10" min="1" max="100" step="0.1">
                </div>
                <div class="form-group">
                    <label for="maxPortfolioRisk">Max Portfolio Risk (%):</label>
                    <input type="number" id="maxPortfolioRisk" value="5" min="1" max="50" step="0.1">
                </div>
                <div class="form-group">
                    <label for="maxDailyLoss">Max Daily Loss (%):</label>
                    <input type="number" id="maxDailyLoss" value="2" min="0.1" max="20" step="0.1">
                </div>
                <div class="form-group">
                    <label for="maxDrawdown">Max Drawdown (%):</label>
                    <input type="number" id="maxDrawdown" value="15" min="1" max="50" step="0.1">
                </div>
                <div class="controls">
                    <button type="submit" class="btn btn-success">Update Limits</button>
                    <button type="button" class="btn btn-danger" onclick="closeModal('riskLimitsModal')">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // Configuration
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        const WS_URL = 'ws://localhost:8000/ws';
        const API_KEY = 'demo_key_123'; // In production, this should be securely managed
        
        // Global variables
        let websocket = null;
        let systemData = {};
        let updateInterval = null;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            connectWebSocket();
            startPeriodicUpdates();
            setupFormHandlers();
        });
        
        // Initialize dashboard
        async function initializeDashboard() {
            try {
                await updateSystemStatus();
                showNotification('Dashboard initialized successfully', 'success');
            } catch (error) {
                console.error('Failed to initialize dashboard:', error);
                showNotification('Failed to initialize dashboard', 'error');
                // Use mock data for demo
                loadMockData();
            }
        }
        
        // Connect to WebSocket for real-time updates
        function connectWebSocket() {
            try {
                websocket = new WebSocket(`${WS_URL}?api_key=${API_KEY}`);
                
                websocket.onopen = function(event) {
                    console.log('WebSocket connected');
                    showNotification('Real-time connection established', 'success');
                    
                    // Subscribe to system updates
                    websocket.send(JSON.stringify({
                        type: 'subscribe',
                        feed: 'system_updates'
                    }));
                };
                
                websocket.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                websocket.onclose = function(event) {
                    console.log('WebSocket disconnected');
                    showNotification('Real-time connection lost', 'warning');
                    
                    // Attempt to reconnect after 5 seconds
                    setTimeout(connectWebSocket, 5000);
                };
                
                websocket.onerror = function(error) {
                    console.error('WebSocket error:', error);
                    showNotification('WebSocket connection error', 'error');
                };
                
            } catch (error) {
                console.error('Failed to connect WebSocket:', error);
                showNotification('Failed to establish real-time connection', 'warning');
            }
        }
        
        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            if (data.type === 'system_update') {
                systemData = data.data;
                updateDashboard(systemData);
            } else if (data.type === 'subscription_confirmed') {
                console.log('Subscription confirmed for:', data.feed);
            }
        }
        
        // Start periodic updates (fallback if WebSocket fails)
        function startPeriodicUpdates() {
            updateInterval = setInterval(async () => {
                if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                    try {
                        await updateSystemStatus();
                    } catch (error) {
                        console.error('Periodic update failed:', error);
                    }
                }
            }, 30000); // Update every 30 seconds
        }
        
        // Update system status
        async function updateSystemStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/system/status`, {
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                systemData = data;
                updateDashboard(data);
                
            } catch (error) {
                console.error('Failed to fetch system status:', error);
                throw error;
            }
        }
        
        // Update dashboard with system data
        function updateDashboard(data) {
            // Update status bar
            updateElement('systemStatusText', data.system?.state || 'Unknown');
            updateElement('systemUptime', data.system?.uptime || '--');
            updateElement('activeComponents', data.metrics?.active_components || '--');
            updateElement('lastUpdate', new Date().toLocaleTimeString());
            
            // Update system status indicator
            const statusIndicator = document.getElementById('systemStatus');
            const systemState = data.system?.state || 'unknown';
            statusIndicator.className = `status-indicator ${
                systemState === 'running' ? 'running' : 
                systemState === 'stopped' ? 'stopped' : 'warning'
            }`;
            
            // Update system metrics
            updateElement('cpuUsage', `${data.metrics?.cpu_usage?.toFixed(1) || '--'}%`);
            updateElement('memoryUsage', `${data.metrics?.memory_usage?.toFixed(1) || '--'}%`);
            updateElement('diskUsage', `${data.metrics?.disk_usage?.toFixed(1) || '--'}%`);
            updateElement('totalEvents', data.metrics?.total_events || '--');
            updateElement('errorCount', data.metrics?.error_count || '--');
            
            // Update components list
            updateComponentsList(data.components || {});
            
            // Update logs
            updateSystemLogs(data.events?.recent || []);
        }
        
        // Update components list
        function updateComponentsList(components) {
            const componentsList = document.getElementById('componentsList');
            componentsList.innerHTML = '';
            
            Object.entries(components).forEach(([name, info]) => {
                const listItem = document.createElement('li');
                listItem.className = 'component-item';
                
                const statusClass = info.state === 'active' ? 'active' : 
                                  info.state === 'error' ? 'error' : 'inactive';
                
                listItem.innerHTML = `
                    <span class="component-name">${name}</span>
                    <span class="component-status ${statusClass}">${info.state}</span>
                `;
                
                componentsList.appendChild(listItem);
            });
        }
        
        // Update system logs
        function updateSystemLogs(events) {
            const logsContainer = document.getElementById('systemLogs');
            
            // Keep only recent logs to prevent overflow
            const maxLogs = 50;
            const existingLogs = logsContainer.children;
            
            events.forEach(event => {
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${event.severity || 'info'}`;
                logEntry.innerHTML = `
                    <span style="color: #95a5a6;">[${new Date(event.timestamp).toLocaleTimeString()}]</span>
                    <span style="color: #3498db;">[${event.component}]</span>
                    ${event.message}
                `;
                
                logsContainer.appendChild(logEntry);
            });
            
            // Remove old logs
            while (logsContainer.children.length > maxLogs) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
            
            // Scroll to bottom
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        // Load mock data for demo purposes
        function loadMockData() {
            const mockData = {
                system: {
                    name: 'Noryon AI Trading System',
                    state: 'running',
                    uptime: '2d 14h 32m',
                    start_time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
                },
                components: {
                    'training_orchestrator': { state: 'active', type: 'training' },
                    'continuous_learning': { state: 'active', type: 'learning' },
                    'risk_monitor': { state: 'active', type: 'risk' },
                    'performance_analytics': { state: 'active', type: 'analytics' },
                    'backtest_engine': { state: 'inactive', type: 'backtesting' },
                    'deployment_pipeline': { state: 'active', type: 'deployment' }
                },
                metrics: {
                    active_components: 5,
                    total_events: 1247,
                    error_count: 3,
                    memory_usage: 68.5,
                    cpu_usage: 23.7,
                    disk_usage: 45.2
                },
                events: {
                    recent: [
                        {
                            type: 'component_start',
                            component: 'risk_monitor',
                            timestamp: new Date().toISOString(),
                            message: 'Risk monitor started successfully',
                            severity: 'info'
                        },
                        {
                            type: 'performance_update',
                            component: 'performance_analytics',
                            timestamp: new Date(Date.now() - 60000).toISOString(),
                            message: 'Portfolio performance updated',
                            severity: 'info'
                        }
                    ]
                }
            };
            
            updateDashboard(mockData);
        }
        
        // Setup form handlers
        function setupFormHandlers() {
            // Training form
            document.getElementById('trainingForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleTrainingSubmit();
            });
            
            // Backtest form
            document.getElementById('backtestForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleBacktestSubmit();
            });
            
            // Risk limits form
            document.getElementById('riskLimitsForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await handleRiskLimitsSubmit();
            });
        }
        
        // Handle training form submission
        async function handleTrainingSubmit() {
            try {
                const formData = {
                    models: Array.from(document.getElementById('trainingModels').selectedOptions).map(o => o.value),
                    parallel: document.getElementById('parallelTraining').checked,
                    gpu_count: parseInt(document.getElementById('gpuCount').value),
                    experiment_name: document.getElementById('experimentName').value || null
                };
                
                const response = await fetch(`${API_BASE_URL}/training/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showNotification('Training started successfully', 'success');
                    closeModal('trainingModal');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('Training submission failed:', error);
                showNotification('Failed to start training', 'error');
            }
        }
        
        // Handle backtest form submission
        async function handleBacktestSubmit() {
            try {
                const formData = {
                    strategy: document.getElementById('backtestStrategy').value,
                    start_date: document.getElementById('startDate').value,
                    end_date: document.getElementById('endDate').value,
                    initial_capital: parseFloat(document.getElementById('initialCapital').value),
                    symbols: document.getElementById('symbols').value.split(',').map(s => s.trim())
                };
                
                const response = await fetch(`${API_BASE_URL}/backtest/run`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showNotification('Backtest started successfully', 'success');
                    closeModal('backtestModal');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('Backtest submission failed:', error);
                showNotification('Failed to start backtest', 'error');
            }
        }
        
        // Handle risk limits form submission
        async function handleRiskLimitsSubmit() {
            try {
                const formData = {
                    max_position_size: parseFloat(document.getElementById('maxPositionSize').value) / 100,
                    max_portfolio_risk: parseFloat(document.getElementById('maxPortfolioRisk').value) / 100,
                    max_daily_loss: parseFloat(document.getElementById('maxDailyLoss').value) / 100,
                    max_drawdown: parseFloat(document.getElementById('maxDrawdown').value) / 100
                };
                
                const response = await fetch(`${API_BASE_URL}/risk/limits`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${API_KEY}`
                    },
                    body: JSON.stringify(formData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    showNotification('Risk limits updated successfully', 'success');
                    closeModal('riskLimitsModal');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                console.error('Risk limits update failed:', error);
                showNotification('Failed to update risk limits', 'error');
            }
        }
        
        // Component control functions
        async function startAllComponents() {
            showNotification('Starting all components...', 'info');
            // Implementation would call API to start all components
        }
        
        async function restartAllComponents() {
            showNotification('Restarting all components...', 'info');
            // Implementation would call API to restart all components
        }
        
        async function stopAllComponents() {
            if (confirm('Are you sure you want to stop all components?')) {
                showNotification('Stopping all components...', 'warning');
                // Implementation would call API to stop all components
            }
        }
        
        async function activateCircuitBreaker() {
            if (confirm('Are you sure you want to activate the emergency circuit breaker?')) {
                showNotification('Emergency circuit breaker activated', 'warning');
                // Implementation would call API to activate circuit breaker
            }
        }
        
        // Modal functions
        function openTrainingModal() {
            document.getElementById('trainingModal').style.display = 'block';
        }
        
        function openBacktestModal() {
            document.getElementById('backtestModal').style.display = 'block';
            
            // Set default dates
            const endDate = new Date();
            const startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
        }
        
        function openRiskLimitsModal() {
            document.getElementById('riskLimitsModal').style.display = 'block';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // Utility functions
        function updateElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }
        
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 5000);
        }
        
        function clearLogs() {
            document.getElementById('systemLogs').innerHTML = '';
            showNotification('Logs cleared', 'info');
        }
        
        function downloadLogs() {
            const logs = document.getElementById('systemLogs').textContent;
            const blob = new Blob([logs], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `noryon-logs-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showNotification('Logs downloaded', 'success');
        }
        
        // Close modals when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (websocket) {
                websocket.close();
            }
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>