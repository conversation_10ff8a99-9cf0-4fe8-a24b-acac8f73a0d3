#!/usr/bin/env python3
"""
Noryon Adaptive Learning Enhancements - Phase 2 Optimization
Advanced optimizations for the adaptive learning system

This module implements:
- Performance monitoring and metrics collection
- Advanced learning rate adaptation
- Knowledge consolidation and pruning
- Multi-objective optimization
- Real-time performance feedback
- Memory-efficient learning strategies
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
import json
import time
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Enhanced performance metrics for adaptive learning"""
    accuracy: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    f1_score: float = 0.0
    learning_speed: float = 0.0
    memory_efficiency: float = 0.0
    adaptation_rate: float = 0.0
    knowledge_retention: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class OptimizationConfig:
    """Configuration for adaptive learning optimizations"""
    enable_performance_monitoring: bool = True
    enable_adaptive_learning_rate: bool = True
    enable_knowledge_pruning: bool = True
    enable_multi_objective_optimization: bool = True
    performance_window_size: int = 100
    learning_rate_adaptation_factor: float = 0.1
    knowledge_pruning_threshold: float = 0.3
    optimization_interval: int = 50

class AdaptiveLearningOptimizer:
    """Advanced optimizer for adaptive learning systems"""
    
    def __init__(self, config: OptimizationConfig = None):
        self.config = config or OptimizationConfig()
        self.performance_history = deque(maxlen=self.config.performance_window_size)
        self.learning_rates = defaultdict(float)
        self.knowledge_scores = defaultdict(float)
        self.optimization_counter = 0
        
        logger.info("Adaptive Learning Optimizer initialized")
    
    async def optimize_learning_rate(self, strategy_name: str, 
                                   current_performance: float,
                                   current_lr: float) -> float:
        """Dynamically optimize learning rate based on performance"""
        if not self.config.enable_adaptive_learning_rate:
            return current_lr
        
        # Calculate performance trend
        recent_performances = [m.accuracy for m in list(self.performance_history)[-10:]]
        if len(recent_performances) < 2:
            return current_lr
        
        performance_trend = np.mean(np.diff(recent_performances))
        
        # Adapt learning rate based on trend
        if performance_trend > 0.01:  # Improving
            new_lr = current_lr * (1 + self.config.learning_rate_adaptation_factor)
        elif performance_trend < -0.01:  # Declining
            new_lr = current_lr * (1 - self.config.learning_rate_adaptation_factor)
        else:  # Stable
            new_lr = current_lr
        
        # Clamp learning rate
        new_lr = max(0.001, min(0.1, new_lr))
        
        self.learning_rates[strategy_name] = new_lr
        logger.debug(f"Optimized learning rate for {strategy_name}: {current_lr:.4f} -> {new_lr:.4f}")
        
        return new_lr
    
    async def prune_knowledge(self, knowledge_base: Dict[str, Any]) -> Dict[str, Any]:
        """Remove low-value knowledge to improve memory efficiency"""
        if not self.config.enable_knowledge_pruning:
            return knowledge_base
        
        # Calculate knowledge scores based on usage and performance impact
        pruned_knowledge = {}
        
        for key, knowledge_item in knowledge_base.items():
            # Simple scoring based on recency and usage
            score = self._calculate_knowledge_score(knowledge_item)
            
            if score >= self.config.knowledge_pruning_threshold:
                pruned_knowledge[key] = knowledge_item
                self.knowledge_scores[key] = score
        
        pruned_count = len(knowledge_base) - len(pruned_knowledge)
        if pruned_count > 0:
            logger.info(f"Pruned {pruned_count} low-value knowledge items")
        
        return pruned_knowledge
    
    def _calculate_knowledge_score(self, knowledge_item: Any) -> float:
        """Calculate the value score of a knowledge item"""
        # Simple scoring algorithm - can be enhanced
        base_score = 0.5
        
        # Add recency bonus
        if hasattr(knowledge_item, 'timestamp'):
            age_hours = (datetime.now() - knowledge_item.timestamp).total_seconds() / 3600
            recency_score = max(0, 1 - age_hours / 168)  # Decay over a week
            base_score += recency_score * 0.3
        
        # Add usage bonus
        if hasattr(knowledge_item, 'usage_count'):
            usage_score = min(1.0, knowledge_item.usage_count / 10)
            base_score += usage_score * 0.2
        
        return min(1.0, base_score)
    
    async def multi_objective_optimization(self, strategies: Dict[str, Any],
                                         objectives: List[str]) -> Dict[str, float]:
        """Optimize multiple objectives simultaneously"""
        if not self.config.enable_multi_objective_optimization:
            return {name: 1.0 for name in strategies.keys()}
        
        # Simple multi-objective optimization using weighted sum
        strategy_weights = {}
        
        for strategy_name in strategies.keys():
            # Calculate weighted performance across objectives
            total_score = 0.0
            
            for objective in objectives:
                if strategy_name in self.learning_rates:
                    # Use learning rate as proxy for strategy effectiveness
                    score = self.learning_rates[strategy_name]
                else:
                    score = 0.5  # Default score
                
                total_score += score
            
            strategy_weights[strategy_name] = total_score / len(objectives)
        
        # Normalize weights
        total_weight = sum(strategy_weights.values())
        if total_weight > 0:
            strategy_weights = {k: v/total_weight for k, v in strategy_weights.items()}
        
        return strategy_weights
    
    async def update_performance_metrics(self, metrics: PerformanceMetrics):
        """Update performance history and trigger optimizations"""
        self.performance_history.append(metrics)
        self.optimization_counter += 1
        
        # Trigger optimization every N updates
        if self.optimization_counter % self.config.optimization_interval == 0:
            await self._trigger_optimization()
    
    async def _trigger_optimization(self):
        """Trigger comprehensive optimization"""
        logger.info("Triggering adaptive learning optimization cycle")
        
        # Analyze performance trends
        if len(self.performance_history) >= 10:
            recent_accuracy = [m.accuracy for m in list(self.performance_history)[-10:]]
            avg_accuracy = np.mean(recent_accuracy)
            accuracy_trend = np.mean(np.diff(recent_accuracy))
            
            logger.info(f"Performance analysis - Avg accuracy: {avg_accuracy:.3f}, Trend: {accuracy_trend:.4f}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report"""
        if not self.performance_history:
            return {"status": "No performance data available"}
        
        recent_metrics = list(self.performance_history)[-10:]
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_optimizations": self.optimization_counter,
            "performance_summary": {
                "avg_accuracy": np.mean([m.accuracy for m in recent_metrics]),
                "avg_learning_speed": np.mean([m.learning_speed for m in recent_metrics]),
                "avg_memory_efficiency": np.mean([m.memory_efficiency for m in recent_metrics]),
            },
            "active_learning_rates": dict(self.learning_rates),
            "knowledge_scores_count": len(self.knowledge_scores),
            "optimization_config": {
                "performance_monitoring": self.config.enable_performance_monitoring,
                "adaptive_learning_rate": self.config.enable_adaptive_learning_rate,
                "knowledge_pruning": self.config.enable_knowledge_pruning,
                "multi_objective_optimization": self.config.enable_multi_objective_optimization,
            }
        }
        
        return report

class EnhancedAdaptiveLearningSystem:
    """Enhanced adaptive learning system with optimizations"""
    
    def __init__(self, agent_id: str, optimization_config: OptimizationConfig = None):
        self.agent_id = agent_id
        self.optimizer = AdaptiveLearningOptimizer(optimization_config)
        self.learning_session_id = f"session_{int(time.time())}"
        
        # Performance tracking
        self.session_metrics = []
        self.total_learning_steps = 0
        
        logger.info(f"Enhanced Adaptive Learning System initialized for agent {agent_id}")
    
    async def enhanced_learn(self, data: Any, target: Any = None,
                           strategy_name: str = "default") -> PerformanceMetrics:
        """Enhanced learning with real-time optimization"""
        start_time = time.time()
        
        # Simulate learning process (replace with actual learning logic)
        await asyncio.sleep(0.01)  # Simulate computation time
        
        # Calculate performance metrics
        learning_time = time.time() - start_time
        
        # Generate realistic performance metrics
        base_accuracy = 0.7 + np.random.normal(0, 0.1)
        metrics = PerformanceMetrics(
            accuracy=max(0.0, min(1.0, base_accuracy)),
            precision=max(0.0, min(1.0, base_accuracy + np.random.normal(0, 0.05))),
            recall=max(0.0, min(1.0, base_accuracy + np.random.normal(0, 0.05))),
            learning_speed=1.0 / learning_time if learning_time > 0 else 1.0,
            memory_efficiency=np.random.uniform(0.6, 0.9),
            adaptation_rate=np.random.uniform(0.5, 0.8)
        )
        
        # Update optimizer with new metrics
        await self.optimizer.update_performance_metrics(metrics)
        
        # Optimize learning rate for next iteration
        current_lr = self.optimizer.learning_rates.get(strategy_name, 0.01)
        optimized_lr = await self.optimizer.optimize_learning_rate(
            strategy_name, metrics.accuracy, current_lr
        )
        
        self.session_metrics.append(metrics)
        self.total_learning_steps += 1
        
        logger.debug(f"Enhanced learning step completed - Accuracy: {metrics.accuracy:.3f}, LR: {optimized_lr:.4f}")
        
        return metrics
    
    async def optimize_knowledge_base(self, knowledge_base: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize the knowledge base for better performance"""
        return await self.optimizer.prune_knowledge(knowledge_base)
    
    def get_session_report(self) -> Dict[str, Any]:
        """Get comprehensive session report"""
        optimizer_report = self.optimizer.get_session_report()
        
        session_report = {
            "session_id": self.learning_session_id,
            "agent_id": self.agent_id,
            "total_learning_steps": self.total_learning_steps,
            "session_metrics_count": len(self.session_metrics),
            "optimizer_report": optimizer_report
        }
        
        return session_report

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics for enhanced system detection"""
        if not self.session_metrics:
            return {
                "overall_performance": 0.0,
                "accuracy": 0.0,
                "learning_speed": 0.0,
                "memory_efficiency": 0.0,
                "adaptation_rate": 0.0,
                "enhanced_version": True
            }
        
        recent_metrics = self.session_metrics[-10:]  # Last 10 metrics
        
        return {
            "overall_performance": np.mean([m.accuracy for m in recent_metrics]),
            "accuracy": np.mean([m.accuracy for m in recent_metrics]),
            "learning_speed": np.mean([m.learning_speed for m in recent_metrics]),
            "memory_efficiency": np.mean([m.memory_efficiency for m in recent_metrics]),
            "adaptation_rate": np.mean([m.adaptation_rate for m in recent_metrics]),
            "enhanced_version": True,
            "total_optimizations": self.optimizer.optimization_counter,
            "session_id": self.learning_session_id
        }

# Example usage and testing
async def demonstrate_enhanced_learning():
    """Demonstrate the enhanced adaptive learning system"""
    print("\n🚀 Enhanced Adaptive Learning System Demo")
    print("=" * 50)
    
    # Create enhanced system
    config = OptimizationConfig(
        enable_performance_monitoring=True,
        enable_adaptive_learning_rate=True,
        enable_knowledge_pruning=True,
        optimization_interval=10
    )
    
    enhanced_system = EnhancedAdaptiveLearningSystem("enhanced_agent", config)
    
    # Simulate learning sessions
    print("\n📚 Running enhanced learning sessions...")
    
    for i in range(25):
        # Simulate different data patterns
        data = np.random.randn(10, 5)
        target = np.random.randint(0, 2, 10)
        
        metrics = await enhanced_system.enhanced_learn(data, target, "online_strategy")
        
        if i % 5 == 0:
            print(f"Step {i+1}: Accuracy={metrics.accuracy:.3f}, Speed={metrics.learning_speed:.2f}")
    
    # Generate reports
    print("\n📊 Generating optimization reports...")
    session_report = enhanced_system.get_session_report()
    
    print(f"\n✅ Session completed:")
    print(f"   - Total steps: {session_report['total_learning_steps']}")
    print(f"   - Session ID: {session_report['session_id']}")
    print(f"   - Optimizations triggered: {session_report['optimizer_report']['total_optimizations']}")
    
    # Test knowledge base optimization
    print("\n🧠 Testing knowledge base optimization...")
    mock_knowledge = {
        f"knowledge_{i}": {"value": i, "usage_count": np.random.randint(1, 20)}
        for i in range(100)
    }
    
    optimized_knowledge = await enhanced_system.optimize_knowledge_base(mock_knowledge)
    print(f"   - Original knowledge items: {len(mock_knowledge)}")
    print(f"   - Optimized knowledge items: {len(optimized_knowledge)}")
    print(f"   - Reduction: {len(mock_knowledge) - len(optimized_knowledge)} items")
    
    print("\n🎉 Enhanced Adaptive Learning demonstration completed!")
    return session_report

if __name__ == "__main__":
    # Run demonstration
    asyncio.run(demonstrate_enhanced_learning())