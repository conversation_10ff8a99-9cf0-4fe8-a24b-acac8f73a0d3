#!/usr/bin/env python3
"""
Clean Finance Trainer - Train AI models on your financial data
"""

import os
import sys
import json
import subprocess
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

console = Console()

def check_financial_data():
    """Check available financial datasets"""
    data_dir = Path("data")
    datasets = []
    
    # Key financial datasets to check
    key_datasets = [
        "JosephgflowersFinance-Instruct-500k",
        "BAAIIndustryInstruction_Finance-Economics",
        "sp500_news_290k_articles.csv",
        "0xMakatrading-candles-subset-qa-format"
    ]
    
    for dataset in key_datasets:
        path = data_dir / dataset
        if path.exists():
            datasets.append({
                "name": dataset,
                "path": str(path),
                "available": True
            })
    
    return datasets

def create_simple_training_script(model_name, model_path, output_dir):
    """Create a simple training script"""
    return f'''#!/usr/bin/env python3
import torch
from transformers import AutoToken<PERSON>, AutoModelForCausalLM, TrainingArguments, Trainer
from datasets import Dataset
import json
from pathlib import Path

def load_finance_data():
    """Load financial instruction data"""
    all_data = []
    
    # Load main financial dataset
    finance_path = Path("data/JosephgflowersFinance-Instruct-500k")
    if finance_path.exists():
        for file in finance_path.rglob("*.jsonl"):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if line_num >= 1000:  # Limit to 1000 examples per file
                            break
                        if line.strip():
                            data = json.loads(line)
                            all_data.append(data)
                print(f"Loaded {{len(all_data)}} examples from {{file}}")
                if len(all_data) >= 5000:  # Total limit
                    break
            except Exception as e:
                print(f"Error loading {{file}}: {{e}}")
    
    # Load economics dataset
    econ_path = Path("data/BAAIIndustryInstruction_Finance-Economics")
    if econ_path.exists() and len(all_data) < 5000:
        for file in econ_path.rglob("*.jsonl"):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f):
                        if line_num >= 500:  # Smaller limit for second dataset
                            break
                        if line.strip():
                            data = json.loads(line)
                            all_data.append(data)
                print(f"Loaded {{len(all_data)}} total examples")
                if len(all_data) >= 5000:
                    break
            except Exception as e:
                print(f"Error loading {{file}}: {{e}}")
    
    if not all_data:
        print("No data loaded")
        return None
    
    return Dataset.from_list(all_data[:5000])  # Final limit

def main():
    print("Starting {model_name} training...")
    
    # Load model
    tokenizer = AutoTokenizer.from_pretrained("{model_path}", trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        "{model_path}",
        torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
        device_map="auto" if torch.cuda.is_available() else None,
        trust_remote_code=True
    )
    
    # Load data
    dataset = load_finance_data()
    if dataset is None:
        print("No training data available")
        return False
    
    print(f"Training on {{len(dataset)}} examples")
    
    # Format data
    def format_example(example):
        if 'instruction' in example and 'output' in example:
            text = f"Instruction: {{example['instruction']}}\\nResponse: {{example['output']}}"
        elif 'question' in example and 'answer' in example:
            text = f"Question: {{example['question']}}\\nAnswer: {{example['answer']}}"
        else:
            text = str(example)
        return {{"text": text}}
    
    dataset = dataset.map(format_example)
    
    # Tokenize
    def tokenize(examples):
        return tokenizer(
            examples["text"],
            truncation=True,
            padding=True,
            max_length=512,
            return_tensors="pt"
        )
    
    tokenized_dataset = dataset.map(tokenize, batched=True)
    
    # Training settings
    training_args = TrainingArguments(
        output_dir="{output_dir}",
        num_train_epochs=1,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=4,
        learning_rate=5e-5,
        weight_decay=0.01,
        logging_steps=50,
        save_steps=500,
        warmup_steps=100,
        fp16=torch.cuda.is_available(),
        remove_unused_columns=False,
        report_to=None
    )
    
    # Train
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        tokenizer=tokenizer
    )
    
    try:
        print("Starting training...")
        trainer.train()
        trainer.save_model()
        tokenizer.save_pretrained("{output_dir}")
        print("Training completed successfully!")
        return True
    except Exception as e:
        print(f"Training failed: {{e}}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
'''

def train_single_model(model_info):
    """Train a single model"""
    model_name = model_info["name"]
    model_path = model_info["path"]
    output_dir = model_info["output"]
    
    console.print(f"[green]Training {model_name}...[/green]")
    
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Create training script
    script_content = create_simple_training_script(model_name, model_path, output_dir)
    script_file = f"train_{model_name.replace('-', '_')}.py"
    
    try:
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # Run training
        result = subprocess.run([
            sys.executable, script_file
        ], capture_output=True, text=True, timeout=1800)  # 30 minutes
        
        success = result.returncode == 0
        
        if success:
            console.print(f"[green]SUCCESS: {model_name} training completed![/green]")
        else:
            console.print(f"[red]FAILED: {model_name} training failed[/red]")
            if result.stderr:
                console.print(f"Error: {result.stderr[:200]}")
        
        return success
        
    except subprocess.TimeoutExpired:
        console.print(f"[yellow]TIMEOUT: {model_name} training timed out[/yellow]")
        return False
    except Exception as e:
        console.print(f"[red]ERROR: {model_name} training error: {e}[/red]")
        return False
    finally:
        # Clean up script file
        if os.path.exists(script_file):
            os.remove(script_file)

def main():
    """Main training function"""
    console.print(Panel(
        "[bold blue]Finance AI Model Training[/bold blue]\n\n"
        "Training AI models on your financial datasets:\n"
        "- 500k+ financial instructions\n"
        "- Economics data\n"
        "- Market analysis data",
        title="Finance Training"
    ))
    
    # Check available data
    datasets = check_financial_data()
    if not datasets:
        console.print("[red]No financial datasets found![/red]")
        return
    
    console.print(f"[green]Found {len(datasets)} financial datasets[/green]")
    for dataset in datasets:
        console.print(f"  - {dataset['name']}")
    
    # Define models to train
    models = [
        {
            "name": "deepseek-r1",
            "path": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
            "output": "models/deepseek-finance-clean"
        },
        {
            "name": "mistral",
            "path": "mistralai/Mistral-7B-Instruct-v0.2",
            "output": "models/mistral-finance-clean"
        },
        {
            "name": "qwen3",
            "path": "Qwen/Qwen2.5-7B-Instruct",
            "output": "models/qwen3-finance-clean"
        }
    ]
    
    # Train models
    results = {}
    for model in models:
        results[model["name"]] = train_single_model(model)
    
    # Summary
    successful = sum(results.values())
    total = len(results)
    
    console.print(Panel(
        f"[bold green]Training Results[/bold green]\n\n"
        f"Successfully trained: {successful}/{total} models\n"
        f"Success rate: {successful/total*100:.1f}%\n\n"
        "Trained models saved in models/ directory",
        title="Training Complete"
    ))
    
    # Show next steps
    if successful > 0:
        console.print(Panel(
            "[bold yellow]Next Steps[/bold yellow]\n\n"
            "1. Test trained models\n"
            "2. Start paper trading\n"
            "3. Monitor performance\n\n"
            "Commands:\n"
            "- python test_trained_models.py\n"
            "- python start_paper_trading.py",
            title="What's Next"
        ))

if __name__ == "__main__":
    main()
