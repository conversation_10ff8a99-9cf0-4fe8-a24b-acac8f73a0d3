version: '3.8'

services:
  # Main Noryon Trading AI Application
  noryon-ai:
    build: .
    container_name: noryon-trading-ai
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379
      - POSTGRES_URL=*************************************************/noryon
      - LOG_LEVEL=INFO
    volumes:
      - ./config:/app/config
      - ./data:/app/data
      - ./logs:/app/logs
      - noryon_models:/app/models
    depends_on:
      - redis
      - postgres
    networks:
      - noryon-network
    ports:
      - "8000:8000"  # Web interface port
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and real-time data
  redis:
    image: redis:7-alpine
    container_name: noryon-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL for persistent data storage
  postgres:
    image: postgres:15-alpine
    container_name: noryon-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: noryon
      POSTGRES_USER: noryon
      POSTGRES_PASSWORD: noryon_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noryon -d noryon"]
      interval: 30s
      timeout: 10s
      retries: 3

  # TimescaleDB for time-series data (optional, for advanced analytics)
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: noryon-timescaledb
    restart: unless-stopped
    environment:
      POSTGRES_DB: noryon_timeseries
      POSTGRES_USER: noryon
      POSTGRES_PASSWORD: noryon_password
    volumes:
      - timescale_data:/var/lib/postgresql/data
    networks:
      - noryon-network
    profiles:
      - analytics  # Only start with --profile analytics
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noryon -d noryon_timeseries"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for monitoring (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: noryon-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - noryon-network
    ports:
      - "9090:9090"
    profiles:
      - monitoring  # Only start with --profile monitoring

  # Grafana for dashboards (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: noryon-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - noryon-network
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    profiles:
      - monitoring  # Only start with --profile monitoring

  # Nginx reverse proxy (optional, for production)
  nginx:
    image: nginx:alpine
    container_name: noryon-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - noryon-network
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - noryon-ai
    profiles:
      - production  # Only start with --profile production

networks:
  noryon-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  timescale_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  noryon_models:
    driver: local

# Usage Examples:
# 
# Basic setup (core services only):
# docker-compose up -d
# 
# With analytics (includes TimescaleDB):
# docker-compose --profile analytics up -d
# 
# With monitoring (includes Prometheus + Grafana):
# docker-compose --profile monitoring up -d
# 
# Full production setup:
# docker-compose --profile analytics --profile monitoring --profile production up -d
# 
# View logs:
# docker-compose logs -f noryon-ai
# 
# Scale the main application:
# docker-compose up -d --scale noryon-ai=3
# 
# Stop all services:
# docker-compose down
# 
# Stop and remove volumes (WARNING: This will delete all data):
# docker-compose down -v