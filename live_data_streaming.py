#!/usr/bin/env python3
"""
Live Data Streaming - REAL-TIME MARKET DATA
Stream live prices, order book, and market data
"""

import asyncio
import websocket
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Callable, Any
from dataclasses import dataclass
import queue

@dataclass
class MarketTick:
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: float = 0.0
    ask: float = 0.0
    source: str = ""

class LiveDataStreamer:
    """Real-time market data streaming"""
    
    def __init__(self):
        self.subscriptions = {}
        self.callbacks = {}
        self.data_queue = queue.Queue()
        self.running = False
        self.connections = {}
        
        print("📡 LIVE DATA STREAMING INITIALIZED")
        print("   🔗 WebSocket connections ready")
        print("   📊 Real-time data streaming")
    
    def subscribe_binance_stream(self, symbols: List[str], callback: Callable):
        """Subscribe to Binance real-time data"""
        
        def on_message(ws, message):
            try:
                data = json.loads(message)
                
                if 'stream' in data and 'data' in data:
                    stream_data = data['data']
                    symbol = stream_data['s']
                    
                    tick = MarketTick(
                        symbol=symbol,
                        price=float(stream_data['c']),  # Close price
                        volume=float(stream_data['v']),  # Volume
                        bid=float(stream_data['b']),     # Best bid
                        ask=float(stream_data['a']),     # Best ask
                        timestamp=datetime.now(),
                        source='binance'
                    )
                    
                    callback(tick)
                    self.data_queue.put(tick)
                    
            except Exception as e:
                print(f"❌ Binance stream error: {e}")
        
        def on_error(ws, error):
            print(f"❌ Binance WebSocket error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 Binance WebSocket closed")
        
        def on_open(ws):
            print("✅ Binance WebSocket connected")
            
            # Subscribe to ticker streams
            streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": streams,
                "id": 1
            }
            ws.send(json.dumps(subscribe_msg))
        
        # Create WebSocket URL
        streams = [f"{symbol.lower()}@ticker" for symbol in symbols]
        stream_names = "/".join(streams)
        url = f"wss://stream.binance.com:9443/ws/{stream_names}"
        
        # Start WebSocket
        ws = websocket.WebSocketApp(
            url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )
        
        # Run in separate thread
        def run_ws():
            ws.run_forever()
        
        thread = threading.Thread(target=run_ws, daemon=True)
        thread.start()
        
        self.connections['binance'] = ws
        print(f"📡 Subscribed to Binance: {symbols}")
    
    def subscribe_polygon_stream(self, symbols: List[str], callback: Callable, api_key: str):
        """Subscribe to Polygon.io real-time stock data"""
        
        def on_message(ws, message):
            try:
                data = json.loads(message)
                
                if isinstance(data, list):
                    for item in data:
                        if item.get('ev') == 'T':  # Trade event
                            tick = MarketTick(
                                symbol=item['sym'],
                                price=float(item['p']),
                                volume=float(item['s']),
                                timestamp=datetime.fromtimestamp(item['t'] / 1000),
                                source='polygon'
                            )
                            
                            callback(tick)
                            self.data_queue.put(tick)
                            
            except Exception as e:
                print(f"❌ Polygon stream error: {e}")
        
        def on_error(ws, error):
            print(f"❌ Polygon WebSocket error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 Polygon WebSocket closed")
        
        def on_open(ws):
            print("✅ Polygon WebSocket connected")
            
            # Authenticate
            auth_msg = {"action": "auth", "params": api_key}
            ws.send(json.dumps(auth_msg))
            
            # Subscribe to trades
            subscribe_msg = {
                "action": "subscribe",
                "params": f"T.{',T.'.join(symbols)}"
            }
            ws.send(json.dumps(subscribe_msg))
        
        # Create WebSocket connection
        url = "wss://socket.polygon.io/stocks"
        
        ws = websocket.WebSocketApp(
            url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )
        
        # Run in separate thread
        def run_ws():
            ws.run_forever()
        
        thread = threading.Thread(target=run_ws, daemon=True)
        thread.start()
        
        self.connections['polygon'] = ws
        print(f"📡 Subscribed to Polygon: {symbols}")
    
    def subscribe_yahoo_websocket(self, symbols: List[str], callback: Callable):
        """Subscribe to Yahoo Finance WebSocket (free alternative)"""
        
        def fetch_yahoo_data():
            """Fetch data periodically since Yahoo doesn't have public WebSocket"""
            import requests
            
            while self.running:
                try:
                    for symbol in symbols:
                        url = f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}"
                        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
                        
                        response = requests.get(url, headers=headers, timeout=5)
                        
                        if response.status_code == 200:
                            data = response.json()
                            
                            if 'chart' in data and 'result' in data['chart'] and data['chart']['result']:
                                result = data['chart']['result'][0]
                                meta = result.get('meta', {})
                                
                                tick = MarketTick(
                                    symbol=symbol,
                                    price=float(meta.get('regularMarketPrice', 0)),
                                    volume=float(meta.get('regularMarketVolume', 0)),
                                    timestamp=datetime.now(),
                                    source='yahoo'
                                )
                                
                                callback(tick)
                                self.data_queue.put(tick)
                
                except Exception as e:
                    print(f"❌ Yahoo data error: {e}")
                
                time.sleep(5)  # Update every 5 seconds
        
        # Start data fetching thread
        thread = threading.Thread(target=fetch_yahoo_data, daemon=True)
        thread.start()
        
        self.connections['yahoo'] = thread
        print(f"📡 Subscribed to Yahoo Finance: {symbols}")
    
    def start_streaming(self):
        """Start all data streams"""
        self.running = True
        print("🚀 LIVE DATA STREAMING STARTED")
    
    def stop_streaming(self):
        """Stop all data streams"""
        self.running = False
        
        # Close WebSocket connections
        for name, connection in self.connections.items():
            try:
                if hasattr(connection, 'close'):
                    connection.close()
                print(f"🔌 Closed {name} connection")
            except:
                pass
        
        print("🛑 LIVE DATA STREAMING STOPPED")
    
    def get_latest_data(self, symbol: str) -> Optional[MarketTick]:
        """Get latest data for a symbol"""
        latest = None
        temp_queue = queue.Queue()
        
        # Get all items from queue and find latest for symbol
        while not self.data_queue.empty():
            try:
                tick = self.data_queue.get_nowait()
                temp_queue.put(tick)
                
                if tick.symbol == symbol:
                    if not latest or tick.timestamp > latest.timestamp:
                        latest = tick
            except queue.Empty:
                break
        
        # Put items back in queue
        while not temp_queue.empty():
            try:
                self.data_queue.put(temp_queue.get_nowait())
            except queue.Empty:
                break
        
        return latest
    
    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary of streaming data"""
        symbols_seen = set()
        total_ticks = 0
        sources = set()
        
        temp_queue = queue.Queue()
        
        # Analyze queue contents
        while not self.data_queue.empty():
            try:
                tick = self.data_queue.get_nowait()
                temp_queue.put(tick)
                
                symbols_seen.add(tick.symbol)
                sources.add(tick.source)
                total_ticks += 1
            except queue.Empty:
                break
        
        # Put items back
        while not temp_queue.empty():
            try:
                self.data_queue.put(temp_queue.get_nowait())
            except queue.Empty:
                break
        
        return {
            'timestamp': datetime.now().isoformat(),
            'streaming': self.running,
            'connections': len(self.connections),
            'symbols_tracked': len(symbols_seen),
            'total_ticks': total_ticks,
            'data_sources': list(sources),
            'symbols': list(symbols_seen)
        }

class RealTimeDataManager:
    """Manage real-time data for trading system"""
    
    def __init__(self):
        self.streamer = LiveDataStreamer()
        self.latest_prices = {}
        self.price_history = {}
        self.callbacks = []
        
        print("📊 REAL-TIME DATA MANAGER INITIALIZED")
    
    def add_price_callback(self, callback: Callable):
        """Add callback for price updates"""
        self.callbacks.append(callback)
    
    def price_update_handler(self, tick: MarketTick):
        """Handle incoming price updates"""
        # Store latest price
        self.latest_prices[tick.symbol] = tick
        
        # Store in history
        if tick.symbol not in self.price_history:
            self.price_history[tick.symbol] = []
        
        self.price_history[tick.symbol].append(tick)
        
        # Keep only last 1000 ticks per symbol
        if len(self.price_history[tick.symbol]) > 1000:
            self.price_history[tick.symbol] = self.price_history[tick.symbol][-1000:]
        
        # Call registered callbacks
        for callback in self.callbacks:
            try:
                callback(tick)
            except Exception as e:
                print(f"❌ Callback error: {e}")
    
    def start_crypto_stream(self, symbols: List[str]):
        """Start crypto data stream"""
        self.streamer.subscribe_binance_stream(symbols, self.price_update_handler)
        self.streamer.start_streaming()
    
    def start_stock_stream(self, symbols: List[str], polygon_api_key: str = None):
        """Start stock data stream"""
        if polygon_api_key:
            self.streamer.subscribe_polygon_stream(symbols, self.price_update_handler, polygon_api_key)
        else:
            # Fall back to Yahoo Finance
            self.streamer.subscribe_yahoo_websocket(symbols, self.price_update_handler)
        
        self.streamer.start_streaming()
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for symbol"""
        if symbol in self.latest_prices:
            return self.latest_prices[symbol].price
        return None
    
    def get_price_change(self, symbol: str, minutes: int = 5) -> Optional[float]:
        """Get price change over time period"""
        if symbol not in self.price_history:
            return None
        
        history = self.price_history[symbol]
        if len(history) < 2:
            return None
        
        # Find price from X minutes ago
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        old_price = None
        
        for tick in reversed(history):
            if tick.timestamp <= cutoff_time:
                old_price = tick.price
                break
        
        if old_price is None:
            old_price = history[0].price
        
        current_price = history[-1].price
        return ((current_price - old_price) / old_price) * 100
    
    def stop_all_streams(self):
        """Stop all data streams"""
        self.streamer.stop_streaming()

def main():
    """Test live data streaming"""
    print("📡 LIVE DATA STREAMING - TESTING")
    print("=" * 60)
    
    # Initialize data manager
    data_manager = RealTimeDataManager()
    
    # Add callback to print price updates
    def print_price_update(tick: MarketTick):
        print(f"📊 {tick.symbol}: ${tick.price:.2f} from {tick.source}")
    
    data_manager.add_price_callback(print_price_update)
    
    # Start crypto stream
    crypto_symbols = ['BTCUSDT', 'ETHUSDT']
    print(f"🚀 Starting crypto stream: {crypto_symbols}")
    data_manager.start_crypto_stream(crypto_symbols)
    
    # Start stock stream (Yahoo fallback)
    stock_symbols = ['AAPL', 'TSLA', 'SPY']
    print(f"🚀 Starting stock stream: {stock_symbols}")
    data_manager.start_stock_stream(stock_symbols)
    
    # Let it run for a bit
    print("\n⏱️ Streaming for 30 seconds...")
    time.sleep(30)
    
    # Show summary
    summary = data_manager.streamer.get_data_summary()
    print(f"\n📊 STREAMING SUMMARY:")
    print(f"   Symbols tracked: {summary['symbols_tracked']}")
    print(f"   Total ticks: {summary['total_ticks']}")
    print(f"   Data sources: {summary['data_sources']}")
    
    # Show current prices
    print(f"\n💰 CURRENT PRICES:")
    for symbol in crypto_symbols + stock_symbols:
        price = data_manager.get_current_price(symbol)
        if price:
            change = data_manager.get_price_change(symbol, 5)
            change_str = f" ({change:+.2f}%)" if change else ""
            print(f"   {symbol}: ${price:.2f}{change_str}")
    
    # Stop streaming
    data_manager.stop_all_streams()
    print(f"\n✅ LIVE DATA STREAMING TEST COMPLETE")

if __name__ == "__main__":
    main()
