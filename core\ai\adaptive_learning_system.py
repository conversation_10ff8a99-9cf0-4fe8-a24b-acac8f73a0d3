#!/usr/bin/env python3
"""
Noryon Adaptive Learning System - Phase 2 Implementation
Advanced Adaptive Learning for Self-Evolving Trading System

This module implements:
- Continuous learning mechanisms
- Meta-learning and learning-to-learn
- Transfer learning between agents
- Online adaptation to market changes
- Knowledge distillation and sharing
- Curriculum learning
- Catastrophic forgetting prevention
- Performance-based learning rate adaptation
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Callable, Set
import uuid
import pickle
import threading
from concurrent.futures import ThreadPoolExecutor
import queue
import copy
from sklearn.metrics import accuracy_score, precision_score, recall_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LearningType(Enum):
    """Types of learning mechanisms"""
    SUPERVISED = "supervised"
    UNSUPERVISED = "unsupervised"
    REINFORCEMENT = "reinforcement"
    META_LEARNING = "meta_learning"
    TRANSFER_LEARNING = "transfer_learning"
    ONLINE_LEARNING = "online_learning"
    CURRICULUM_LEARNING = "curriculum_learning"
    SELF_SUPERVISED = "self_supervised"

class AdaptationType(Enum):
    """Types of adaptation strategies"""
    PARAMETER_ADAPTATION = "parameter_adaptation"
    ARCHITECTURE_ADAPTATION = "architecture_adaptation"
    STRATEGY_ADAPTATION = "strategy_adaptation"
    LEARNING_RATE_ADAPTATION = "learning_rate_adaptation"
    CURRICULUM_ADAPTATION = "curriculum_adaptation"
    ENSEMBLE_ADAPTATION = "ensemble_adaptation"

class KnowledgeType(Enum):
    """Types of knowledge that can be shared"""
    PARAMETERS = "parameters"
    FEATURES = "features"
    STRATEGIES = "strategies"
    PATTERNS = "patterns"
    EXPERIENCES = "experiences"
    MODELS = "models"
    INSIGHTS = "insights"

@dataclass
class LearningMetrics:
    """Metrics for learning performance"""
    timestamp: datetime = field(default_factory=datetime.now)
    learning_rate: float = 0.001
    loss: float = 0.0
    accuracy: float = 0.0
    convergence_rate: float = 0.0
    adaptation_speed: float = 0.0
    knowledge_retention: float = 0.0
    transfer_efficiency: float = 0.0
    meta_learning_score: float = 0.0
    catastrophic_forgetting: float = 0.0

@dataclass
class KnowledgeItem:
    """Represents a piece of knowledge"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_type: KnowledgeType = KnowledgeType.INSIGHTS
    content: Any = None
    source_agent: str = ""
    confidence: float = 0.0
    relevance_score: float = 0.0
    creation_time: datetime = field(default_factory=datetime.now)
    last_used: datetime = field(default_factory=datetime.now)
    usage_count: int = 0
    effectiveness: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class LearningTask:
    """Represents a learning task"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    task_type: LearningType = LearningType.SUPERVISED
    data: Any = None
    target: Any = None
    difficulty: float = 0.5
    priority: int = 1
    deadline: Optional[datetime] = None
    prerequisites: List[str] = field(default_factory=list)
    learning_objectives: List[str] = field(default_factory=list)
    success_criteria: Dict[str, float] = field(default_factory=dict)

@dataclass
class AdaptationEvent:
    """Represents an adaptation event"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    adaptation_type: AdaptationType = AdaptationType.PARAMETER_ADAPTATION
    trigger: str = ""
    agent_id: str = ""
    before_state: Dict[str, Any] = field(default_factory=dict)
    after_state: Dict[str, Any] = field(default_factory=dict)
    performance_change: float = 0.0
    success: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)

class LearningStrategy(ABC):
    """Base class for learning strategies"""
    
    def __init__(self, name: str, learning_type: LearningType):
        self.name = name
        self.learning_type = learning_type
        self.metrics = LearningMetrics()
        self.is_active = True
        
    @abstractmethod
    async def learn(self, data: Any, target: Any = None) -> LearningMetrics:
        """Execute learning on provided data"""
        pass
    
    @abstractmethod
    def adapt(self, feedback: Dict[str, Any]) -> bool:
        """Adapt strategy based on feedback"""
        pass
    
    @abstractmethod
    def get_knowledge(self) -> List[KnowledgeItem]:
        """Extract knowledge from the strategy"""
        pass
    
    @abstractmethod
    def apply_knowledge(self, knowledge: List[KnowledgeItem]) -> bool:
        """Apply external knowledge to the strategy"""
        pass

class OnlineLearningStrategy(LearningStrategy):
    """Online learning strategy for continuous adaptation"""
    
    def __init__(self, name: str, learning_rate: float = 0.01, 
                 forgetting_factor: float = 0.95):
        super().__init__(name, LearningType.ONLINE_LEARNING)
        self.learning_rate = learning_rate
        self.forgetting_factor = forgetting_factor
        self.model_weights = {}
        self.experience_buffer = deque(maxlen=10000)
        self.performance_history = deque(maxlen=1000)
        
    async def learn(self, data: Any, target: Any = None) -> LearningMetrics:
        """Online learning update"""
        try:
            # Store experience
            experience = {
                'data': data,
                'target': target,
                'timestamp': datetime.now()
            }
            self.experience_buffer.append(experience)
            
            # Update model incrementally
            loss = await self._update_model(data, target)
            
            # Update metrics
            self.metrics.loss = loss
            self.metrics.learning_rate = self.learning_rate
            self.metrics.timestamp = datetime.now()
            
            # Adapt learning rate based on performance
            await self._adapt_learning_rate()
            
            return self.metrics
            
        except Exception as e:
            logger.error(f"Error in online learning: {e}")
            return self.metrics
    
    def adapt(self, feedback: Dict[str, Any]) -> bool:
        """Adapt based on performance feedback"""
        try:
            performance = feedback.get('performance', 0.0)
            self.performance_history.append(performance)
            
            # Adjust forgetting factor based on performance stability
            if len(self.performance_history) > 10:
                recent_std = np.std(list(self.performance_history)[-10:])
                if recent_std > 0.1:  # High volatility
                    self.forgetting_factor = max(0.8, self.forgetting_factor - 0.01)
                else:  # Stable performance
                    self.forgetting_factor = min(0.99, self.forgetting_factor + 0.01)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in adaptation: {e}")
            return False
    
    def get_knowledge(self) -> List[KnowledgeItem]:
        """Extract knowledge from online learning"""
        knowledge_items = []
        
        # Extract model parameters as knowledge
        if self.model_weights:
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.PARAMETERS,
                content=copy.deepcopy(self.model_weights),
                confidence=self._calculate_confidence(),
                effectiveness=self._calculate_effectiveness()
            ))
        
        # Extract patterns from experience buffer
        if len(self.experience_buffer) > 100:
            patterns = self._extract_patterns()
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.PATTERNS,
                content=patterns,
                confidence=0.7,
                effectiveness=0.8
            ))
        
        return knowledge_items
    
    def apply_knowledge(self, knowledge: List[KnowledgeItem]) -> bool:
        """Apply external knowledge"""
        try:
            for item in knowledge:
                if item.knowledge_type == KnowledgeType.PARAMETERS:
                    # Merge parameters with current model
                    self._merge_parameters(item.content, item.confidence)
                
                elif item.knowledge_type == KnowledgeType.PATTERNS:
                    # Use patterns to adjust learning strategy
                    self._apply_patterns(item.content)
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying knowledge: {e}")
            return False
    
    async def _update_model(self, data: Any, target: Any) -> float:
        """Update model with new data"""
        # Simplified model update
        if isinstance(data, dict) and isinstance(target, (int, float)):
            # Extract features
            features = np.array([data.get(key, 0) for key in sorted(data.keys())])
            
            # Initialize weights if needed
            if 'weights' not in self.model_weights:
                self.model_weights['weights'] = np.random.normal(0, 0.1, len(features))
                self.model_weights['bias'] = 0.0
            
            # Predict
            prediction = np.dot(self.model_weights['weights'], features) + self.model_weights['bias']
            
            # Calculate loss
            loss = (prediction - target) ** 2
            
            # Update weights (gradient descent)
            gradient_w = 2 * (prediction - target) * features
            gradient_b = 2 * (prediction - target)
            
            self.model_weights['weights'] -= self.learning_rate * gradient_w
            self.model_weights['bias'] -= self.learning_rate * gradient_b
            
            return loss
        
        return 0.0
    
    async def _adapt_learning_rate(self):
        """Adapt learning rate based on performance"""
        if len(self.performance_history) > 5:
            recent_performance = list(self.performance_history)[-5:]
            if all(p1 <= p2 for p1, p2 in zip(recent_performance, recent_performance[1:])):
                # Performance improving, increase learning rate
                self.learning_rate = min(0.1, self.learning_rate * 1.05)
            elif all(p1 >= p2 for p1, p2 in zip(recent_performance, recent_performance[1:])):
                # Performance declining, decrease learning rate
                self.learning_rate = max(0.001, self.learning_rate * 0.95)
    
    def _calculate_confidence(self) -> float:
        """Calculate confidence in current model"""
        if len(self.performance_history) < 5:
            return 0.5
        
        recent_performance = list(self.performance_history)[-5:]
        return min(1.0, np.mean(recent_performance))
    
    def _calculate_effectiveness(self) -> float:
        """Calculate effectiveness of learning strategy"""
        if len(self.performance_history) < 10:
            return 0.5
        
        # Compare recent vs older performance
        recent = np.mean(list(self.performance_history)[-5:])
        older = np.mean(list(self.performance_history)[-10:-5])
        
        return min(1.0, max(0.0, (recent - older) + 0.5))
    
    def _extract_patterns(self) -> Dict[str, Any]:
        """Extract patterns from experience buffer"""
        patterns = {
            'data_distribution': {},
            'target_correlations': {},
            'temporal_patterns': {}
        }
        
        # Analyze data distribution
        if self.experience_buffer:
            data_samples = [exp['data'] for exp in self.experience_buffer if isinstance(exp['data'], dict)]
            if data_samples:
                for key in data_samples[0].keys():
                    values = [sample.get(key, 0) for sample in data_samples]
                    patterns['data_distribution'][key] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values)
                    }
        
        return patterns
    
    def _merge_parameters(self, external_params: Dict[str, Any], confidence: float):
        """Merge external parameters with current model"""
        if 'weights' in external_params and 'weights' in self.model_weights:
            # Weighted average based on confidence
            self.model_weights['weights'] = (
                (1 - confidence) * self.model_weights['weights'] +
                confidence * external_params['weights']
            )
        
        if 'bias' in external_params and 'bias' in self.model_weights:
            self.model_weights['bias'] = (
                (1 - confidence) * self.model_weights['bias'] +
                confidence * external_params['bias']
            )
    
    def _apply_patterns(self, patterns: Dict[str, Any]):
        """Apply learned patterns to adjust strategy"""
        # Use patterns to adjust learning parameters
        if 'data_distribution' in patterns:
            # Adjust learning rate based on data volatility
            volatilities = []
            for key, stats in patterns['data_distribution'].items():
                if stats['std'] > 0:
                    volatilities.append(stats['std'] / (stats['mean'] + 1e-8))
            
            if volatilities:
                avg_volatility = np.mean(volatilities)
                if avg_volatility > 1.0:  # High volatility
                    self.learning_rate = max(0.001, self.learning_rate * 0.8)
                else:  # Low volatility
                    self.learning_rate = min(0.1, self.learning_rate * 1.2)

class MetaLearningStrategy(LearningStrategy):
    """Meta-learning strategy for learning-to-learn"""
    
    def __init__(self, name: str, adaptation_steps: int = 5):
        super().__init__(name, LearningType.META_LEARNING)
        self.adaptation_steps = adaptation_steps
        self.meta_model = {}
        self.task_history = deque(maxlen=1000)
        self.adaptation_history = deque(maxlen=100)
        
    async def learn(self, data: Any, target: Any = None) -> LearningMetrics:
        """Meta-learning update"""
        try:
            # Store task
            task = {
                'data': data,
                'target': target,
                'timestamp': datetime.now()
            }
            self.task_history.append(task)
            
            # Perform meta-learning if we have enough tasks
            if len(self.task_history) >= 10:
                await self._meta_learn()
            
            # Update metrics
            self.metrics.meta_learning_score = self._calculate_meta_score()
            self.metrics.timestamp = datetime.now()
            
            return self.metrics
            
        except Exception as e:
            logger.error(f"Error in meta-learning: {e}")
            return self.metrics
    
    def adapt(self, feedback: Dict[str, Any]) -> bool:
        """Meta-adapt based on feedback"""
        try:
            adaptation_event = {
                'feedback': feedback,
                'timestamp': datetime.now(),
                'adaptation_success': feedback.get('performance', 0) > 0.5
            }
            self.adaptation_history.append(adaptation_event)
            
            # Learn from adaptation patterns
            if len(self.adaptation_history) >= 5:
                self._learn_adaptation_patterns()
            
            return True
            
        except Exception as e:
            logger.error(f"Error in meta-adaptation: {e}")
            return False
    
    def get_knowledge(self) -> List[KnowledgeItem]:
        """Extract meta-knowledge"""
        knowledge_items = []
        
        # Extract meta-model as knowledge
        if self.meta_model:
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.MODELS,
                content=copy.deepcopy(self.meta_model),
                confidence=self.metrics.meta_learning_score,
                effectiveness=0.9
            ))
        
        # Extract adaptation patterns
        if len(self.adaptation_history) > 10:
            patterns = self._extract_adaptation_patterns()
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.STRATEGIES,
                content=patterns,
                confidence=0.8,
                effectiveness=0.85
            ))
        
        return knowledge_items
    
    def apply_knowledge(self, knowledge: List[KnowledgeItem]) -> bool:
        """Apply meta-knowledge"""
        try:
            for item in knowledge:
                if item.knowledge_type == KnowledgeType.MODELS:
                    # Merge meta-models
                    self._merge_meta_models(item.content, item.confidence)
                
                elif item.knowledge_type == KnowledgeType.STRATEGIES:
                    # Apply adaptation strategies
                    self._apply_adaptation_strategies(item.content)
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying meta-knowledge: {e}")
            return False
    
    async def _meta_learn(self):
        """Perform meta-learning on task history"""
        # Analyze task patterns
        task_features = self._extract_task_features()
        
        # Update meta-model based on task patterns
        if task_features:
            self.meta_model['task_patterns'] = task_features
            self.meta_model['optimal_adaptations'] = self._find_optimal_adaptations()
    
    def _calculate_meta_score(self) -> float:
        """Calculate meta-learning performance score"""
        if len(self.adaptation_history) < 5:
            return 0.5
        
        # Calculate success rate of adaptations
        recent_adaptations = list(self.adaptation_history)[-5:]
        success_rate = sum(1 for a in recent_adaptations if a['adaptation_success']) / len(recent_adaptations)
        
        return success_rate
    
    def _learn_adaptation_patterns(self):
        """Learn patterns from adaptation history"""
        # Analyze which adaptations work best in which contexts
        successful_adaptations = [a for a in self.adaptation_history if a['adaptation_success']]
        
        if successful_adaptations:
            # Extract common patterns from successful adaptations
            patterns = self._extract_adaptation_patterns()
            self.meta_model['successful_patterns'] = patterns
    
    def _extract_task_features(self) -> Dict[str, Any]:
        """Extract features from task history"""
        features = {
            'task_complexity': [],
            'data_characteristics': {},
            'temporal_patterns': {}
        }
        
        for task in self.task_history:
            # Analyze task complexity
            if isinstance(task['data'], dict):
                complexity = len(task['data'])  # Simple complexity measure
                features['task_complexity'].append(complexity)
        
        return features
    
    def _find_optimal_adaptations(self) -> Dict[str, Any]:
        """Find optimal adaptation strategies"""
        optimal = {
            'learning_rate_adjustments': {},
            'architecture_changes': {},
            'strategy_switches': {}
        }
        
        # Analyze successful adaptations
        successful = [a for a in self.adaptation_history if a['adaptation_success']]
        
        if successful:
            # Extract common characteristics of successful adaptations
            for adaptation in successful:
                feedback = adaptation['feedback']
                # Analyze what made this adaptation successful
                # This is a simplified implementation
                optimal['learning_rate_adjustments']['success_rate'] = 0.8
        
        return optimal
    
    def _extract_adaptation_patterns(self) -> Dict[str, Any]:
        """Extract patterns from adaptation history"""
        patterns = {
            'success_factors': [],
            'failure_factors': [],
            'optimal_timing': {},
            'context_dependencies': {}
        }
        
        successful = [a for a in self.adaptation_history if a['adaptation_success']]
        failed = [a for a in self.adaptation_history if not a['adaptation_success']]
        
        # Analyze success factors
        if successful:
            patterns['success_factors'] = ['high_confidence', 'gradual_change', 'context_aware']
        
        # Analyze failure factors
        if failed:
            patterns['failure_factors'] = ['low_confidence', 'abrupt_change', 'context_ignore']
        
        return patterns
    
    def _merge_meta_models(self, external_model: Dict[str, Any], confidence: float):
        """Merge external meta-model with current one"""
        for key, value in external_model.items():
            if key in self.meta_model:
                # Weighted merge based on confidence
                if isinstance(value, dict) and isinstance(self.meta_model[key], dict):
                    for subkey, subvalue in value.items():
                        if subkey in self.meta_model[key]:
                            # Simple weighted average for numeric values
                            if isinstance(subvalue, (int, float)) and isinstance(self.meta_model[key][subkey], (int, float)):
                                self.meta_model[key][subkey] = (
                                    (1 - confidence) * self.meta_model[key][subkey] +
                                    confidence * subvalue
                                )
                        else:
                            self.meta_model[key][subkey] = subvalue
            else:
                self.meta_model[key] = value
    
    def _apply_adaptation_strategies(self, strategies: Dict[str, Any]):
        """Apply learned adaptation strategies"""
        # Use strategies to improve future adaptations
        if 'success_factors' in strategies:
            # Incorporate success factors into adaptation logic
            self.adaptation_steps = max(3, min(10, self.adaptation_steps))
        
        if 'optimal_timing' in strategies:
            # Adjust timing of adaptations
            pass

class TransferLearningStrategy(LearningStrategy):
    """Transfer learning strategy for knowledge sharing between agents"""
    
    def __init__(self, name: str, similarity_threshold: float = 0.7):
        super().__init__(name, LearningType.TRANSFER_LEARNING)
        self.similarity_threshold = similarity_threshold
        self.source_models = {}
        self.transfer_history = deque(maxlen=100)
        self.domain_mappings = {}
        
    async def learn(self, data: Any, target: Any = None) -> LearningMetrics:
        """Transfer learning update"""
        try:
            # Identify similar source domains
            similar_domains = self._find_similar_domains(data)
            
            # Transfer knowledge from similar domains
            if similar_domains:
                transfer_success = await self._transfer_knowledge(similar_domains, data, target)
                self.metrics.transfer_efficiency = transfer_success
            
            # Update metrics
            self.metrics.timestamp = datetime.now()
            
            return self.metrics
            
        except Exception as e:
            logger.error(f"Error in transfer learning: {e}")
            return self.metrics
    
    def adapt(self, feedback: Dict[str, Any]) -> bool:
        """Adapt transfer learning strategy"""
        try:
            transfer_event = {
                'feedback': feedback,
                'timestamp': datetime.now(),
                'transfer_success': feedback.get('performance', 0) > 0.6
            }
            self.transfer_history.append(transfer_event)
            
            # Adjust similarity threshold based on transfer success
            if len(self.transfer_history) >= 10:
                recent_success_rate = sum(1 for t in list(self.transfer_history)[-10:] 
                                        if t['transfer_success']) / 10
                
                if recent_success_rate < 0.5:
                    # Increase threshold to be more selective
                    self.similarity_threshold = min(0.9, self.similarity_threshold + 0.05)
                elif recent_success_rate > 0.8:
                    # Decrease threshold to be more inclusive
                    self.similarity_threshold = max(0.5, self.similarity_threshold - 0.05)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in transfer adaptation: {e}")
            return False
    
    def get_knowledge(self) -> List[KnowledgeItem]:
        """Extract transferable knowledge"""
        knowledge_items = []
        
        # Extract source models as transferable knowledge
        for domain, model in self.source_models.items():
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.MODELS,
                content=copy.deepcopy(model),
                confidence=0.8,
                effectiveness=0.75,
                metadata={'domain': domain, 'transferable': True}
            ))
        
        # Extract domain mappings
        if self.domain_mappings:
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.FEATURES,
                content=copy.deepcopy(self.domain_mappings),
                confidence=0.7,
                effectiveness=0.8,
                metadata={'type': 'domain_mappings'}
            ))
        
        return knowledge_items
    
    def apply_knowledge(self, knowledge: List[KnowledgeItem]) -> bool:
        """Apply transferred knowledge"""
        try:
            for item in knowledge:
                if item.knowledge_type == KnowledgeType.MODELS:
                    # Add as source model for future transfers
                    domain = item.metadata.get('domain', 'unknown')
                    self.source_models[domain] = item.content
                
                elif item.knowledge_type == KnowledgeType.FEATURES:
                    # Update domain mappings
                    if item.metadata.get('type') == 'domain_mappings':
                        self.domain_mappings.update(item.content)
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying transferred knowledge: {e}")
            return False
    
    def _find_similar_domains(self, data: Any) -> List[str]:
        """Find domains similar to current data"""
        similar_domains = []
        
        if isinstance(data, dict):
            current_features = set(data.keys())
            
            for domain, model in self.source_models.items():
                if 'features' in model:
                    source_features = set(model['features'])
                    
                    # Calculate Jaccard similarity
                    intersection = len(current_features & source_features)
                    union = len(current_features | source_features)
                    
                    if union > 0:
                        similarity = intersection / union
                        if similarity >= self.similarity_threshold:
                            similar_domains.append(domain)
        
        return similar_domains
    
    async def _transfer_knowledge(self, similar_domains: List[str], data: Any, target: Any) -> float:
        """Transfer knowledge from similar domains"""
        transfer_success = 0.0
        
        for domain in similar_domains:
            if domain in self.source_models:
                source_model = self.source_models[domain]
                
                # Simple transfer: use source model parameters as initialization
                if 'parameters' in source_model:
                    # This would involve more sophisticated transfer in practice
                    transfer_success += 0.1
        
        return min(1.0, transfer_success)

class CurriculumLearningStrategy(LearningStrategy):
    """Curriculum learning strategy for progressive difficulty"""
    
    def __init__(self, name: str, initial_difficulty: float = 0.1):
        super().__init__(name, LearningType.CURRICULUM_LEARNING)
        self.current_difficulty = initial_difficulty
        self.difficulty_progression = deque(maxlen=100)
        self.performance_threshold = 0.8
        self.curriculum = []
        self.current_stage = 0
        
    async def learn(self, data: Any, target: Any = None) -> LearningMetrics:
        """Curriculum learning update"""
        try:
            # Create learning task with current difficulty
            task = self._create_curriculum_task(data, target)
            
            # Learn on current task
            performance = await self._learn_task(task)
            
            # Update curriculum based on performance
            await self._update_curriculum(performance)
            
            # Update metrics
            self.metrics.timestamp = datetime.now()
            self.metrics.accuracy = performance
            
            return self.metrics
            
        except Exception as e:
            logger.error(f"Error in curriculum learning: {e}")
            return self.metrics
    
    def adapt(self, feedback: Dict[str, Any]) -> bool:
        """Adapt curriculum based on feedback"""
        try:
            performance = feedback.get('performance', 0.0)
            
            # Adjust difficulty based on performance
            if performance > self.performance_threshold:
                # Increase difficulty
                self.current_difficulty = min(1.0, self.current_difficulty + 0.1)
            elif performance < 0.5:
                # Decrease difficulty
                self.current_difficulty = max(0.1, self.current_difficulty - 0.05)
            
            self.difficulty_progression.append({
                'difficulty': self.current_difficulty,
                'performance': performance,
                'timestamp': datetime.now()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"Error in curriculum adaptation: {e}")
            return False
    
    def get_knowledge(self) -> List[KnowledgeItem]:
        """Extract curriculum knowledge"""
        knowledge_items = []
        
        # Extract curriculum structure
        if self.curriculum:
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.STRATEGIES,
                content={
                    'curriculum': copy.deepcopy(self.curriculum),
                    'current_stage': self.current_stage,
                    'difficulty_progression': list(self.difficulty_progression)
                },
                confidence=0.8,
                effectiveness=0.85
            ))
        
        return knowledge_items
    
    def apply_knowledge(self, knowledge: List[KnowledgeItem]) -> bool:
        """Apply curriculum knowledge"""
        try:
            for item in knowledge:
                if item.knowledge_type == KnowledgeType.STRATEGIES:
                    content = item.content
                    if 'curriculum' in content:
                        # Merge curricula
                        self._merge_curriculum(content['curriculum'])
                    
                    if 'difficulty_progression' in content:
                        # Learn from difficulty progression patterns
                        self._learn_from_progression(content['difficulty_progression'])
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying curriculum knowledge: {e}")
            return False
    
    def _create_curriculum_task(self, data: Any, target: Any) -> LearningTask:
        """Create a task with appropriate difficulty"""
        return LearningTask(
            task_type=LearningType.CURRICULUM_LEARNING,
            data=data,
            target=target,
            difficulty=self.current_difficulty
        )
    
    async def _learn_task(self, task: LearningTask) -> float:
        """Learn on a curriculum task"""
        # Simplified learning simulation
        # In practice, this would involve actual model training
        base_performance = 0.5
        difficulty_factor = 1.0 - task.difficulty * 0.5
        
        # Simulate learning performance based on difficulty
        performance = base_performance * difficulty_factor + np.random.normal(0, 0.1)
        return np.clip(performance, 0.0, 1.0)
    
    async def _update_curriculum(self, performance: float):
        """Update curriculum based on performance"""
        if performance > self.performance_threshold:
            # Move to next stage if available
            if self.current_stage < len(self.curriculum) - 1:
                self.current_stage += 1
                self.current_difficulty = self.curriculum[self.current_stage].get('difficulty', self.current_difficulty)
        elif performance < 0.4:
            # Move back a stage if performance is very poor
            if self.current_stage > 0:
                self.current_stage -= 1
                self.current_difficulty = self.curriculum[self.current_stage].get('difficulty', self.current_difficulty)
    
    def _merge_curriculum(self, external_curriculum: List[Dict[str, Any]]):
        """Merge external curriculum with current one"""
        # Simple merge: add new stages that don't exist
        existing_difficulties = {stage.get('difficulty', 0) for stage in self.curriculum}
        
        for stage in external_curriculum:
            if stage.get('difficulty', 0) not in existing_difficulties:
                self.curriculum.append(stage)
        
        # Sort by difficulty
        self.curriculum.sort(key=lambda x: x.get('difficulty', 0))
    
    def _learn_from_progression(self, progression: List[Dict[str, Any]]):
        """Learn from difficulty progression patterns"""
        # Analyze progression patterns to optimize curriculum
        if len(progression) > 10:
            # Find optimal difficulty progression rate
            difficulties = [p['difficulty'] for p in progression]
            performances = [p['performance'] for p in progression]
            
            # Simple analysis: find correlation between difficulty changes and performance
            if len(difficulties) > 1:
                difficulty_changes = np.diff(difficulties)
                performance_changes = np.diff(performances)
                
                # Adjust progression strategy based on correlation
                correlation = np.corrcoef(difficulty_changes, performance_changes)[0, 1]
                if not np.isnan(correlation) and correlation < -0.5:
                    # Strong negative correlation: slow down progression
                    self.performance_threshold = min(0.9, self.performance_threshold + 0.05)

class AdaptiveLearningSystem:
    """Main adaptive learning system coordinating all learning strategies"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.learning_strategies: Dict[str, LearningStrategy] = {}
        self.knowledge_base: Dict[str, KnowledgeItem] = {}
        self.adaptation_events: List[AdaptationEvent] = []
        
        # System metrics
        self.system_metrics = LearningMetrics()
        self.performance_history = deque(maxlen=1000)
        
        # Initialize default strategies
        self._initialize_strategies()
        
        # Learning coordination
        self.active_strategies = set()
        self.strategy_performance = defaultdict(list)
        
        logger.info(f"Adaptive Learning System initialized for agent {agent_id}")
    
    def _initialize_strategies(self):
        """Initialize default learning strategies"""
        # Online learning for continuous adaptation
        self.learning_strategies['online'] = OnlineLearningStrategy(
            "online_learning", learning_rate=0.01
        )
        
        # Meta-learning for learning-to-learn
        self.learning_strategies['meta'] = MetaLearningStrategy(
            "meta_learning", adaptation_steps=5
        )
        
        # Transfer learning for knowledge sharing
        self.learning_strategies['transfer'] = TransferLearningStrategy(
            "transfer_learning", similarity_threshold=0.7
        )
        
        # Curriculum learning for progressive difficulty
        self.learning_strategies['curriculum'] = CurriculumLearningStrategy(
            "curriculum_learning", initial_difficulty=0.2
        )
        
        # Activate all strategies initially
        self.active_strategies = set(self.learning_strategies.keys())
    
    async def learn(self, data: Any, target: Any = None, 
                   strategy_names: Optional[List[str]] = None) -> Dict[str, LearningMetrics]:
        """Execute learning across specified strategies"""
        if strategy_names is None:
            strategy_names = list(self.active_strategies)
        
        results = {}
        
        for strategy_name in strategy_names:
            if strategy_name in self.learning_strategies:
                strategy = self.learning_strategies[strategy_name]
                if strategy.is_active:
                    try:
                        metrics = await strategy.learn(data, target)
                        results[strategy_name] = metrics
                        
                        # Update strategy performance tracking
                        self.strategy_performance[strategy_name].append(metrics.accuracy)
                        
                    except Exception as e:
                        logger.error(f"Error in {strategy_name} learning: {e}")
        
        # Update system metrics
        await self._update_system_metrics(results)
        
        return results
    
    async def adapt(self, feedback: Dict[str, Any], 
                   adaptation_type: AdaptationType = AdaptationType.PARAMETER_ADAPTATION) -> bool:
        """Adapt the learning system based on feedback"""
        try:
            # Record adaptation event
            before_state = self._capture_system_state()
            
            # Apply adaptation to all active strategies
            adaptation_success = True
            for strategy_name in self.active_strategies:
                strategy = self.learning_strategies[strategy_name]
                if not strategy.adapt(feedback):
                    adaptation_success = False
            
            # System-level adaptations
            await self._system_level_adaptation(feedback, adaptation_type)
            
            # Record adaptation event
            after_state = self._capture_system_state()
            
            adaptation_event = AdaptationEvent(
                adaptation_type=adaptation_type,
                trigger=feedback.get('trigger', 'performance_feedback'),
                agent_id=self.agent_id,
                before_state=before_state,
                after_state=after_state,
                performance_change=feedback.get('performance_change', 0.0),
                success=adaptation_success
            )
            
            self.adaptation_events.append(adaptation_event)
            
            return adaptation_success
            
        except Exception as e:
            logger.error(f"Error in system adaptation: {e}")
            return False
    
    def share_knowledge(self, target_agent_id: str) -> List[KnowledgeItem]:
        """Share knowledge with another agent"""
        shared_knowledge = []
        
        # Collect knowledge from all strategies
        for strategy in self.learning_strategies.values():
            if strategy.is_active:
                strategy_knowledge = strategy.get_knowledge()
                shared_knowledge.extend(strategy_knowledge)
        
        # Add system-level knowledge
        system_knowledge = self._extract_system_knowledge()
        shared_knowledge.extend(system_knowledge)
        
        # Update knowledge usage
        for knowledge in shared_knowledge:
            knowledge.usage_count += 1
            knowledge.last_used = datetime.now()
        
        logger.info(f"Shared {len(shared_knowledge)} knowledge items with agent {target_agent_id}")
        
        return shared_knowledge
    
    def receive_knowledge(self, knowledge_items: List[KnowledgeItem], 
                         source_agent_id: str) -> bool:
        """Receive and integrate knowledge from another agent"""
        try:
            integration_success = True
            
            # Integrate knowledge into strategies
            for strategy in self.learning_strategies.values():
                if strategy.is_active:
                    if not strategy.apply_knowledge(knowledge_items):
                        integration_success = False
            
            # Store knowledge in system knowledge base
            for knowledge in knowledge_items:
                knowledge.source_agent = source_agent_id
                self.knowledge_base[knowledge.id] = knowledge
            
            logger.info(f"Received and integrated {len(knowledge_items)} knowledge items from agent {source_agent_id}")
            
            return integration_success
            
        except Exception as e:
            logger.error(f"Error receiving knowledge: {e}")
            return False
    
    async def _update_system_metrics(self, strategy_results: Dict[str, LearningMetrics]):
        """Update system-level learning metrics"""
        if strategy_results:
            # Aggregate metrics from all strategies
            accuracies = [metrics.accuracy for metrics in strategy_results.values()]
            losses = [metrics.loss for metrics in strategy_results.values()]
            
            self.system_metrics.accuracy = np.mean(accuracies)
            self.system_metrics.loss = np.mean(losses)
            self.system_metrics.timestamp = datetime.now()
            
            # Calculate adaptation speed
            if len(self.performance_history) > 1:
                recent_performance = self.performance_history[-1]
                current_performance = self.system_metrics.accuracy
                self.system_metrics.adaptation_speed = abs(current_performance - recent_performance)
            
            # Update performance history
            self.performance_history.append(self.system_metrics.accuracy)
            
            # Calculate knowledge retention
            self.system_metrics.knowledge_retention = self._calculate_knowledge_retention()
    
    async def _system_level_adaptation(self, feedback: Dict[str, Any], 
                                     adaptation_type: AdaptationType):
        """Perform system-level adaptations"""
        performance = feedback.get('performance', 0.0)
        
        if adaptation_type == AdaptationType.STRATEGY_ADAPTATION:
            # Adapt which strategies are active based on performance
            await self._adapt_strategy_selection(performance)
        
        elif adaptation_type == AdaptationType.LEARNING_RATE_ADAPTATION:
            # Adapt learning rates across strategies
            await self._adapt_learning_rates(performance)
        
        elif adaptation_type == AdaptationType.ENSEMBLE_ADAPTATION:
            # Adapt how strategies are combined
            await self._adapt_ensemble_weights(performance)
    
    async def _adapt_strategy_selection(self, performance: float):
        """Adapt which learning strategies are active"""
        # Evaluate strategy performance
        strategy_scores = {}
        
        for strategy_name, performances in self.strategy_performance.items():
            if len(performances) >= 5:
                recent_performance = np.mean(performances[-5:])
                strategy_scores[strategy_name] = recent_performance
        
        # Deactivate poorly performing strategies
        if performance < 0.5:  # Poor overall performance
            worst_strategy = min(strategy_scores.items(), key=lambda x: x[1], default=(None, 0))
            if worst_strategy[0] and worst_strategy[1] < 0.3:
                if worst_strategy[0] in self.active_strategies:
                    self.active_strategies.remove(worst_strategy[0])
                    self.learning_strategies[worst_strategy[0]].is_active = False
                    logger.info(f"Deactivated strategy {worst_strategy[0]} due to poor performance")
        
        # Reactivate strategies if performance is good
        elif performance > 0.8:
            for strategy_name, strategy in self.learning_strategies.items():
                if not strategy.is_active:
                    strategy.is_active = True
                    self.active_strategies.add(strategy_name)
                    logger.info(f"Reactivated strategy {strategy_name} due to good performance")
    
    async def _adapt_learning_rates(self, performance: float):
        """Adapt learning rates across strategies"""
        for strategy in self.learning_strategies.values():
            if hasattr(strategy, 'learning_rate'):
                if performance < 0.4:
                    # Decrease learning rate for stability
                    strategy.learning_rate *= 0.9
                elif performance > 0.8:
                    # Increase learning rate for faster adaptation
                    strategy.learning_rate *= 1.1
                
                # Clip learning rate
                strategy.learning_rate = np.clip(strategy.learning_rate, 0.001, 0.1)
    
    async def _adapt_ensemble_weights(self, performance: float):
        """Adapt how strategies are weighted in ensemble decisions"""
        # This would involve more sophisticated ensemble weighting
        # For now, we adjust based on individual strategy performance
        total_weight = 0
        strategy_weights = {}
        
        for strategy_name, performances in self.strategy_performance.items():
            if len(performances) >= 3:
                weight = np.mean(performances[-3:])
                strategy_weights[strategy_name] = weight
                total_weight += weight
        
        # Normalize weights
        if total_weight > 0:
            for strategy_name in strategy_weights:
                strategy_weights[strategy_name] /= total_weight
    
    def _capture_system_state(self) -> Dict[str, Any]:
        """Capture current system state for adaptation tracking"""
        return {
            'active_strategies': list(self.active_strategies),
            'system_metrics': {
                'accuracy': self.system_metrics.accuracy,
                'loss': self.system_metrics.loss,
                'learning_rate': self.system_metrics.learning_rate
            },
            'knowledge_base_size': len(self.knowledge_base),
            'strategy_performance': {name: list(perf)[-5:] for name, perf in self.strategy_performance.items()}
        }
    
    def _extract_system_knowledge(self) -> List[KnowledgeItem]:
        """Extract system-level knowledge"""
        knowledge_items = []
        
        # Extract adaptation patterns
        if len(self.adaptation_events) > 10:
            successful_adaptations = [e for e in self.adaptation_events if e.success]
            
            if successful_adaptations:
                adaptation_patterns = {
                    'successful_triggers': [e.trigger for e in successful_adaptations],
                    'successful_types': [e.adaptation_type.value for e in successful_adaptations],
                    'performance_improvements': [e.performance_change for e in successful_adaptations]
                }
                
                knowledge_items.append(KnowledgeItem(
                    knowledge_type=KnowledgeType.INSIGHTS,
                    content=adaptation_patterns,
                    confidence=0.8,
                    effectiveness=0.85,
                    metadata={'type': 'adaptation_patterns'}
                ))
        
        # Extract performance patterns
        if len(self.performance_history) > 20:
            performance_analysis = {
                'trend': self._calculate_performance_trend(),
                'volatility': np.std(list(self.performance_history)[-20:]),
                'best_performance': max(self.performance_history),
                'average_performance': np.mean(list(self.performance_history)[-20:])
            }
            
            knowledge_items.append(KnowledgeItem(
                knowledge_type=KnowledgeType.INSIGHTS,
                content=performance_analysis,
                confidence=0.9,
                effectiveness=0.8,
                metadata={'type': 'performance_analysis'}
            ))
        
        return knowledge_items
    
    def _calculate_knowledge_retention(self) -> float:
        """Calculate how well knowledge is retained over time"""
        if not self.knowledge_base:
            return 0.0
        
        current_time = datetime.now()
        total_knowledge = len(self.knowledge_base)
        recent_usage = 0
        
        for knowledge in self.knowledge_base.values():
            time_since_use = (current_time - knowledge.last_used).total_seconds() / 3600  # hours
            if time_since_use < 24:  # Used within last 24 hours
                recent_usage += 1
        
        return recent_usage / total_knowledge if total_knowledge > 0 else 0.0
    
    def _calculate_performance_trend(self) -> float:
        """Calculate performance trend over recent history"""
        if len(self.performance_history) < 10:
            return 0.0
        
        recent_performance = list(self.performance_history)[-10:]
        
        # Simple linear trend calculation
        x = np.arange(len(recent_performance))
        y = np.array(recent_performance)
        
        # Calculate slope
        slope = np.polyfit(x, y, 1)[0]
        
        return slope
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        strategy_status = {}
        for name, strategy in self.learning_strategies.items():
            strategy_status[name] = {
                'is_active': strategy.is_active,
                'learning_type': strategy.learning_type.value,
                'recent_performance': (self.strategy_performance[name][-5:] 
                                     if len(self.strategy_performance[name]) >= 5 else []),
                'metrics': {
                    'accuracy': strategy.metrics.accuracy,
                    'loss': strategy.metrics.loss,
                    'learning_rate': strategy.metrics.learning_rate
                }
            }
        
        return {
            'agent_id': self.agent_id,
            'active_strategies': list(self.active_strategies),
            'system_metrics': {
                'accuracy': self.system_metrics.accuracy,
                'loss': self.system_metrics.loss,
                'adaptation_speed': self.system_metrics.adaptation_speed,
                'knowledge_retention': self.system_metrics.knowledge_retention,
                'meta_learning_score': self.system_metrics.meta_learning_score
            },
            'knowledge_base_size': len(self.knowledge_base),
            'adaptation_events_count': len(self.adaptation_events),
            'performance_trend': self._calculate_performance_trend(),
            'strategies': strategy_status
        }

# Example usage and integration
if __name__ == "__main__":
    async def main():
        # Create adaptive learning system
        learning_system = AdaptiveLearningSystem("test_agent")
        
        # Simulate learning data
        market_data = {
            'price': 100.0,
            'volume': 1000000,
            'volatility': 0.02,
            'trend': 0.01,
            'momentum': 0.005
        }
        
        target_return = 0.05
        
        # Execute learning across all strategies
        print("Starting adaptive learning...")
        
        for i in range(10):
            # Simulate changing market conditions
            market_data['price'] += np.random.normal(0, 2)
            market_data['volatility'] = max(0.01, market_data['volatility'] + np.random.normal(0, 0.005))
            
            # Learn
            results = await learning_system.learn(market_data, target_return)
            
            print(f"Iteration {i+1}:")
            for strategy_name, metrics in results.items():
                print(f"  {strategy_name}: accuracy={metrics.accuracy:.3f}, loss={metrics.loss:.3f}")
            
            # Simulate feedback
            performance = np.random.uniform(0.3, 0.9)
            feedback = {
                'performance': performance,
                'performance_change': performance - 0.5,
                'trigger': 'market_change'
            }
            
            # Adapt
            adaptation_success = await learning_system.adapt(feedback)
            print(f"  Adaptation successful: {adaptation_success}")
            
            # Simulate knowledge sharing
            if i % 3 == 0:
                shared_knowledge = learning_system.share_knowledge("other_agent")
                print(f"  Shared {len(shared_knowledge)} knowledge items")
                
                # Simulate receiving knowledge
                if shared_knowledge:
                    received = learning_system.receive_knowledge(shared_knowledge[:2], "other_agent")
                    print(f"  Received knowledge integration: {received}")
            
            print()
        
        # Get final system status
        status = learning_system.get_system_status()
        print("Final System Status:")
        print(f"Active strategies: {status['active_strategies']}")
        print(f"System accuracy: {status['system_metrics']['accuracy']:.3f}")
        print(f"Knowledge retention: {status['system_metrics']['knowledge_retention']:.3f}")
        print(f"Performance trend: {status['performance_trend']:.3f}")
        print(f"Knowledge base size: {status['knowledge_base_size']}")
        
        print("\nAdaptive Learning System demonstration completed!")
    
    # Run the example
    asyncio.run(main())