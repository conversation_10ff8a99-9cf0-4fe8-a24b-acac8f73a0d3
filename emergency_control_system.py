#!/usr/bin/env python3
"""
Emergency Control System
Panic button, emergency stops, and crisis management for trading system
"""

import time
import json
import threading
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum

class EmergencyLevel(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class EmergencyType(Enum):
    MANUAL_STOP = "MANUAL_STOP"
    LOSS_LIMIT = "LOSS_LIMIT"
    POSITION_LIMIT = "POSITION_LIMIT"
    SYSTEM_ERROR = "SYSTEM_ERROR"
    MARKET_CRASH = "MARKET_CRASH"
    CONNECTION_LOSS = "CONNECTION_LOSS"
    RISK_BREACH = "RISK_BREACH"

@dataclass
class EmergencyEvent:
    event_id: str
    event_type: EmergencyType
    level: EmergencyLevel
    message: str
    triggered_by: str
    timestamp: datetime
    actions_taken: List[str]
    resolved: bool = False
    resolution_time: Optional[datetime] = None

class EmergencyControlSystem:
    """Complete emergency control and panic button system"""
    
    def __init__(self):
        self.emergency_active = False
        self.emergency_events = {}
        self.emergency_callbacks = {}
        self.monitoring_active = False
        
        # Emergency thresholds
        self.emergency_config = {
            'max_daily_loss_percent': 10.0,
            'max_position_loss_percent': 15.0,
            'max_total_loss_amount': 20000.0,
            'max_drawdown_percent': 20.0,
            'connection_timeout_seconds': 30,
            'system_error_threshold': 5,
            'market_crash_threshold': -10.0  # 10% market drop
        }
        
        # Emergency actions
        self.emergency_actions = {
            'stop_all_trading': False,
            'cancel_all_orders': False,
            'close_all_positions': False,
            'disable_new_orders': False,
            'send_alerts': True,
            'log_everything': True
        }
        
        # Setup database
        self._setup_database()
        
        print("🚨 EMERGENCY CONTROL SYSTEM INITIALIZED")
        print(f"   🛑 Panic button: READY")
        print(f"   📊 Monitoring: {len(self.emergency_config)} thresholds")
        print(f"   🔔 Alert system: ACTIVE")
    
    def _setup_database(self):
        """Setup emergency events database"""
        conn = sqlite3.connect('emergency_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS emergency_events (
                event_id TEXT PRIMARY KEY,
                event_type TEXT,
                level TEXT,
                message TEXT,
                triggered_by TEXT,
                timestamp DATETIME,
                actions_taken TEXT,
                resolved BOOLEAN,
                resolution_time DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS emergency_logs (
                id INTEGER PRIMARY KEY,
                timestamp DATETIME,
                log_level TEXT,
                message TEXT,
                system_state TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def PANIC_BUTTON(self, reason: str = "Manual emergency stop") -> bool:
        """🚨 EMERGENCY PANIC BUTTON - STOP EVERYTHING 🚨"""
        
        print("\n" + "="*60)
        print("🚨🚨🚨 EMERGENCY PANIC BUTTON ACTIVATED 🚨🚨🚨")
        print("="*60)
        print(f"Reason: {reason}")
        print(f"Time: {datetime.now()}")
        print("="*60)
        
        # Create emergency event
        event_id = f"PANIC_{int(time.time())}"
        emergency_event = EmergencyEvent(
            event_id=event_id,
            event_type=EmergencyType.MANUAL_STOP,
            level=EmergencyLevel.CRITICAL,
            message=f"PANIC BUTTON: {reason}",
            triggered_by="MANUAL",
            timestamp=datetime.now(),
            actions_taken=[]
        )
        
        # Execute emergency actions
        actions_taken = []
        
        try:
            # 1. Stop all trading immediately
            self.emergency_active = True
            actions_taken.append("Trading halted")
            print("✅ Trading halted")
            
            # 2. Cancel all pending orders
            if self._cancel_all_orders():
                actions_taken.append("All orders cancelled")
                print("✅ All orders cancelled")
            
            # 3. Close all positions (if configured)
            if self.emergency_actions['close_all_positions']:
                if self._close_all_positions():
                    actions_taken.append("All positions closed")
                    print("✅ All positions closed")
            
            # 4. Disable new order creation
            self.emergency_actions['disable_new_orders'] = True
            actions_taken.append("New orders disabled")
            print("✅ New orders disabled")
            
            # 5. Send emergency alerts
            self._send_emergency_alerts(emergency_event)
            actions_taken.append("Emergency alerts sent")
            print("✅ Emergency alerts sent")
            
            # 6. Log everything
            self._log_emergency_event(emergency_event)
            actions_taken.append("Emergency logged")
            print("✅ Emergency logged")
            
            # 7. Save system state
            self._save_system_state()
            actions_taken.append("System state saved")
            print("✅ System state saved")
            
        except Exception as e:
            actions_taken.append(f"ERROR: {str(e)}")
            print(f"❌ Emergency action error: {e}")
        
        emergency_event.actions_taken = actions_taken
        self.emergency_events[event_id] = emergency_event
        
        print("\n🚨 EMERGENCY PROCEDURES COMPLETE")
        print(f"Actions taken: {len(actions_taken)}")
        print("System is now in EMERGENCY MODE")
        print("Call resolve_emergency() to resume operations")
        print("="*60)
        
        return True
    
    def trigger_emergency(self, event_type: EmergencyType, level: EmergencyLevel, 
                         message: str, triggered_by: str) -> str:
        """Trigger emergency based on system conditions"""
        
        event_id = f"EMRG_{event_type.value}_{int(time.time())}"
        
        emergency_event = EmergencyEvent(
            event_id=event_id,
            event_type=event_type,
            level=level,
            message=message,
            triggered_by=triggered_by,
            timestamp=datetime.now(),
            actions_taken=[]
        )
        
        print(f"\n🚨 EMERGENCY TRIGGERED: {level.value}")
        print(f"Type: {event_type.value}")
        print(f"Message: {message}")
        print(f"Triggered by: {triggered_by}")
        
        # Execute appropriate emergency response
        if level == EmergencyLevel.CRITICAL:
            self.PANIC_BUTTON(f"CRITICAL: {message}")
        elif level == EmergencyLevel.HIGH:
            self._execute_high_priority_emergency(emergency_event)
        elif level == EmergencyLevel.MEDIUM:
            self._execute_medium_priority_emergency(emergency_event)
        else:
            self._execute_low_priority_emergency(emergency_event)
        
        self.emergency_events[event_id] = emergency_event
        return event_id
    
    def _execute_high_priority_emergency(self, event: EmergencyEvent):
        """Execute high priority emergency response"""
        actions = []
        
        # Stop new trading
        self.emergency_actions['disable_new_orders'] = True
        actions.append("New orders disabled")
        
        # Cancel risky orders
        self._cancel_risky_orders()
        actions.append("Risky orders cancelled")
        
        # Send alerts
        self._send_emergency_alerts(event)
        actions.append("Alerts sent")
        
        event.actions_taken = actions
        print(f"✅ High priority emergency response: {len(actions)} actions")
    
    def _execute_medium_priority_emergency(self, event: EmergencyEvent):
        """Execute medium priority emergency response"""
        actions = []
        
        # Reduce position sizes
        self._reduce_position_sizes()
        actions.append("Position sizes reduced")
        
        # Tighten stop losses
        self._tighten_stop_losses()
        actions.append("Stop losses tightened")
        
        # Send warnings
        self._send_warnings(event)
        actions.append("Warnings sent")
        
        event.actions_taken = actions
        print(f"⚠️ Medium priority emergency response: {len(actions)} actions")
    
    def _execute_low_priority_emergency(self, event: EmergencyEvent):
        """Execute low priority emergency response"""
        actions = []
        
        # Log warning
        self._log_warning(event)
        actions.append("Warning logged")
        
        # Monitor closely
        self._increase_monitoring()
        actions.append("Monitoring increased")
        
        event.actions_taken = actions
        print(f"📝 Low priority emergency response: {len(actions)} actions")
    
    def check_emergency_conditions(self, portfolio_metrics: Dict[str, Any]) -> List[str]:
        """Check for emergency conditions"""
        triggered_emergencies = []
        
        # Check daily loss limit
        daily_loss_percent = portfolio_metrics.get('daily_pnl_percent', 0)
        if daily_loss_percent <= -self.emergency_config['max_daily_loss_percent']:
            event_id = self.trigger_emergency(
                EmergencyType.LOSS_LIMIT,
                EmergencyLevel.HIGH,
                f"Daily loss limit exceeded: {daily_loss_percent:.2f}%",
                "SYSTEM_MONITOR"
            )
            triggered_emergencies.append(event_id)
        
        # Check total loss amount
        total_pnl = portfolio_metrics.get('total_pnl', 0)
        if total_pnl <= -self.emergency_config['max_total_loss_amount']:
            event_id = self.trigger_emergency(
                EmergencyType.LOSS_LIMIT,
                EmergencyLevel.CRITICAL,
                f"Total loss limit exceeded: ${total_pnl:,.2f}",
                "SYSTEM_MONITOR"
            )
            triggered_emergencies.append(event_id)
        
        # Check position concentration
        largest_position = portfolio_metrics.get('largest_position_percent', 0)
        if largest_position > 50:  # More than 50% in one position
            event_id = self.trigger_emergency(
                EmergencyType.POSITION_LIMIT,
                EmergencyLevel.MEDIUM,
                f"Position concentration too high: {largest_position:.1f}%",
                "SYSTEM_MONITOR"
            )
            triggered_emergencies.append(event_id)
        
        return triggered_emergencies
    
    def _cancel_all_orders(self) -> bool:
        """Cancel all pending orders"""
        try:
            # This would integrate with order management system
            print("🚫 Cancelling all orders...")
            # Placeholder for actual order cancellation
            return True
        except Exception as e:
            print(f"❌ Error cancelling orders: {e}")
            return False
    
    def _close_all_positions(self) -> bool:
        """Close all open positions"""
        try:
            # This would integrate with portfolio tracker
            print("📤 Closing all positions...")
            # Placeholder for actual position closing
            return True
        except Exception as e:
            print(f"❌ Error closing positions: {e}")
            return False
    
    def _cancel_risky_orders(self):
        """Cancel orders that are too risky"""
        print("🚫 Cancelling risky orders...")
        # Implementation would check order risk levels
    
    def _reduce_position_sizes(self):
        """Reduce position sizes to lower risk"""
        print("📉 Reducing position sizes...")
        # Implementation would reduce position sizes
    
    def _tighten_stop_losses(self):
        """Tighten stop loss levels"""
        print("🛡️ Tightening stop losses...")
        # Implementation would update stop loss levels
    
    def _send_emergency_alerts(self, event: EmergencyEvent):
        """Send emergency alerts"""
        print(f"📢 Sending emergency alerts for {event.event_type.value}")
        
        # Call all registered callbacks
        for callback in self.emergency_callbacks.values():
            try:
                callback(event)
            except Exception as e:
                print(f"❌ Alert callback error: {e}")
    
    def _send_warnings(self, event: EmergencyEvent):
        """Send warning notifications"""
        print(f"⚠️ Sending warnings for {event.event_type.value}")
    
    def _log_warning(self, event: EmergencyEvent):
        """Log warning event"""
        self._log_emergency_event(event)
    
    def _increase_monitoring(self):
        """Increase monitoring frequency"""
        print("👁️ Increasing monitoring frequency...")
    
    def _log_emergency_event(self, event: EmergencyEvent):
        """Log emergency event to database"""
        conn = sqlite3.connect('emergency_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO emergency_events 
            (event_id, event_type, level, message, triggered_by, timestamp, actions_taken, resolved, resolution_time)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            event.event_id, event.event_type.value, event.level.value,
            event.message, event.triggered_by, event.timestamp.isoformat(),
            json.dumps(event.actions_taken), event.resolved,
            event.resolution_time.isoformat() if event.resolution_time else None
        ))
        
        conn.commit()
        conn.close()
    
    def _save_system_state(self):
        """Save current system state"""
        system_state = {
            'timestamp': datetime.now().isoformat(),
            'emergency_active': self.emergency_active,
            'emergency_config': self.emergency_config,
            'emergency_actions': self.emergency_actions,
            'active_events': len([e for e in self.emergency_events.values() if not e.resolved])
        }
        
        conn = sqlite3.connect('emergency_system.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO emergency_logs (timestamp, log_level, message, system_state)
            VALUES (?, ?, ?, ?)
        ''', (
            datetime.now().isoformat(), 'EMERGENCY', 'System state saved',
            json.dumps(system_state)
        ))
        
        conn.commit()
        conn.close()
    
    def resolve_emergency(self, event_id: str, resolution_notes: str = "") -> bool:
        """Resolve emergency and resume operations"""
        
        if event_id not in self.emergency_events:
            print(f"❌ Emergency event not found: {event_id}")
            return False
        
        event = self.emergency_events[event_id]
        
        if event.resolved:
            print(f"⚠️ Emergency already resolved: {event_id}")
            return True
        
        print(f"\n🔧 RESOLVING EMERGENCY: {event_id}")
        print(f"Type: {event.event_type.value}")
        print(f"Level: {event.level.value}")
        
        # Mark as resolved
        event.resolved = True
        event.resolution_time = datetime.now()
        
        # Check if this was the last critical emergency
        critical_events = [e for e in self.emergency_events.values() 
                          if e.level == EmergencyLevel.CRITICAL and not e.resolved]
        
        if not critical_events:
            # Resume normal operations
            self.emergency_active = False
            self.emergency_actions['disable_new_orders'] = False
            print("✅ Normal operations resumed")
        
        # Update database
        self._log_emergency_event(event)
        
        print(f"✅ Emergency resolved: {event_id}")
        if resolution_notes:
            print(f"Notes: {resolution_notes}")
        
        return True
    
    def add_emergency_callback(self, callback: Callable) -> str:
        """Add callback for emergency events"""
        callback_id = f"callback_{int(time.time())}"
        self.emergency_callbacks[callback_id] = callback
        return callback_id
    
    def get_emergency_status(self) -> Dict[str, Any]:
        """Get current emergency system status"""
        
        active_events = [e for e in self.emergency_events.values() if not e.resolved]
        resolved_events = [e for e in self.emergency_events.values() if e.resolved]
        
        return {
            'timestamp': datetime.now().isoformat(),
            'emergency_active': self.emergency_active,
            'total_events': len(self.emergency_events),
            'active_events': len(active_events),
            'resolved_events': len(resolved_events),
            'emergency_config': self.emergency_config,
            'emergency_actions': self.emergency_actions,
            'recent_events': [asdict(e) for e in list(self.emergency_events.values())[-5:]]
        }

def main():
    """Test emergency control system"""
    print("🚨 EMERGENCY CONTROL SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize emergency system
    emergency = EmergencyControlSystem()
    
    # Add emergency callback
    def emergency_alert(event: EmergencyEvent):
        print(f"🔔 EMERGENCY ALERT: {event.level.value} - {event.message}")
    
    emergency.add_emergency_callback(emergency_alert)
    
    # Test emergency conditions
    print("\n🧪 Testing emergency conditions...")
    
    # Simulate portfolio with losses
    test_portfolio = {
        'daily_pnl_percent': -12.0,  # Exceeds 10% limit
        'total_pnl': -25000,         # Exceeds $20k limit
        'largest_position_percent': 60  # Exceeds 50% limit
    }
    
    triggered = emergency.check_emergency_conditions(test_portfolio)
    print(f"Triggered emergencies: {len(triggered)}")
    
    # Test manual panic button
    print(f"\n🚨 Testing PANIC BUTTON...")
    emergency.PANIC_BUTTON("Testing emergency procedures")
    
    # Check status
    status = emergency.get_emergency_status()
    print(f"\n📊 EMERGENCY STATUS:")
    print(f"   Emergency active: {status['emergency_active']}")
    print(f"   Total events: {status['total_events']}")
    print(f"   Active events: {status['active_events']}")
    
    # Test resolution
    print(f"\n🔧 Testing emergency resolution...")
    if status['active_events'] > 0:
        # Resolve the first active event
        for event_id, event in emergency.emergency_events.items():
            if not event.resolved:
                emergency.resolve_emergency(event_id, "Test resolution")
                break
    
    print(f"\n✅ EMERGENCY CONTROL SYSTEM TEST COMPLETE")

if __name__ == "__main__":
    main()
