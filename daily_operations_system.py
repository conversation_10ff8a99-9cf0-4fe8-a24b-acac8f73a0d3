#!/usr/bin/env python3
"""
Daily Operations System
How to run your AI trading system day-to-day
"""

import time
import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Import your working systems
from agent_command_center import AgentCommandCenter
from real_working_ai_agents import RealWorkingAIAgents
from working_risk_management import WorkingRiskManager

class DailyOperationsSystem:
    """Complete daily operations workflow"""
    
    def __init__(self, capital: float = 100000.0):
        # Initialize all systems
        self.command_center = AgentCommandCenter()
        self.ai_agents = RealWorkingAIAgents()
        self.risk_manager = WorkingRiskManager(capital)
        
        # Daily operation settings
        self.daily_config = {
            'market_check_interval': 300,  # 5 minutes
            'risk_check_interval': 60,     # 1 minute
            'max_daily_trades': 5,
            'trading_hours': {
                'start': 9,   # 9 AM
                'end': 16     # 4 PM
            },
            'symbols_to_monitor': ['BTC-USD', 'ETH-USD', 'AAPL', 'TSLA', 'SPY']
        }
        
        # Daily tracking
        self.daily_stats = {
            'trades_executed': 0,
            'market_checks': 0,
            'ai_queries': 0,
            'alerts_generated': 0,
            'start_time': datetime.now(),
            'last_market_check': None,
            'last_risk_check': None
        }
        
        print("🏢 DAILY OPERATIONS SYSTEM INITIALIZED")
        print(f"   💰 Capital: ${capital:,.2f}")
        print(f"   🤖 AI Agents: {len(self.ai_agents.agents)}")
        print(f"   📊 Monitoring: {len(self.daily_config['symbols_to_monitor'])} symbols")
    
    def morning_startup_routine(self) -> Dict[str, Any]:
        """Complete morning startup checklist"""
        print("\n🌅 MORNING STARTUP ROUTINE")
        print("=" * 50)
        
        startup_results = {}
        
        # 1. System Health Check
        print("1. 🔧 System Health Check...")
        health_check = self._system_health_check()
        startup_results['health_check'] = health_check
        
        # 2. Market Data Check
        print("2. 📊 Market Data Check...")
        market_check = self._morning_market_check()
        startup_results['market_check'] = market_check
        
        # 3. Portfolio Review
        print("3. 💼 Portfolio Review...")
        portfolio_review = self._portfolio_review()
        startup_results['portfolio_review'] = portfolio_review
        
        # 4. AI Agent Status
        print("4. 🤖 AI Agent Status...")
        agent_status = self._check_ai_agents()
        startup_results['agent_status'] = agent_status
        
        # 5. Risk Management Check
        print("5. 🛡️ Risk Management Check...")
        risk_check = self._risk_management_check()
        startup_results['risk_check'] = risk_check
        
        # 6. Daily Trading Plan
        print("6. 📋 Daily Trading Plan...")
        trading_plan = self._create_daily_trading_plan()
        startup_results['trading_plan'] = trading_plan
        
        print(f"\n✅ MORNING STARTUP COMPLETE")
        print(f"   System Status: {'OPERATIONAL' if all(r.get('success', False) for r in startup_results.values()) else 'ISSUES DETECTED'}")
        
        return startup_results
    
    def _system_health_check(self) -> Dict[str, Any]:
        """Check all system components"""
        health_status = {}
        
        # Check AI models
        try:
            test_response = self.command_center.query_agent('marco_o1', 'System health check - respond with OK')
            health_status['ai_models'] = 'OK' if test_response and test_response['success'] else 'ERROR'
        except:
            health_status['ai_models'] = 'ERROR'
        
        # Check database
        try:
            conn = sqlite3.connect('real_agents.db')
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM market_prices')
            count = cursor.fetchone()[0]
            conn.close()
            health_status['database'] = 'OK'
        except:
            health_status['database'] = 'ERROR'
        
        # Check market data
        try:
            market_data = self.ai_agents.get_real_market_data('market_analyst', 'BTC-USD')
            health_status['market_data'] = 'OK' if market_data['success'] else 'ERROR'
        except:
            health_status['market_data'] = 'ERROR'
        
        # Check risk management
        try:
            metrics = self.risk_manager.calculate_portfolio_metrics()
            health_status['risk_management'] = 'OK'
        except:
            health_status['risk_management'] = 'ERROR'
        
        all_ok = all(status == 'OK' for status in health_status.values())
        
        print(f"   🤖 AI Models: {health_status['ai_models']}")
        print(f"   💾 Database: {health_status['database']}")
        print(f"   📊 Market Data: {health_status['market_data']}")
        print(f"   🛡️ Risk Management: {health_status['risk_management']}")
        
        return {
            'success': all_ok,
            'components': health_status,
            'timestamp': datetime.now()
        }
    
    def _morning_market_check(self) -> Dict[str, Any]:
        """Check market conditions for the day"""
        market_data = {}
        
        for symbol in self.daily_config['symbols_to_monitor']:
            try:
                data = self.ai_agents.get_real_market_data('market_analyst', symbol)
                if data['success']:
                    market_data[symbol] = {
                        'price': data['data']['current_price'],
                        'previous_close': data['data']['previous_close'],
                        'change_percent': ((data['data']['current_price'] - data['data']['previous_close']) / data['data']['previous_close']) * 100
                    }
                    print(f"   {symbol}: ${data['data']['current_price']:,.2f} ({market_data[symbol]['change_percent']:+.2f}%)")
            except:
                print(f"   {symbol}: ERROR getting data")
        
        self.daily_stats['market_checks'] += 1
        self.daily_stats['last_market_check'] = datetime.now()
        
        return {
            'success': len(market_data) > 0,
            'market_data': market_data,
            'symbols_checked': len(market_data),
            'timestamp': datetime.now()
        }
    
    def _portfolio_review(self) -> Dict[str, Any]:
        """Review current portfolio status"""
        try:
            report = self.risk_manager.generate_risk_report()
            metrics = report['portfolio_metrics']
            
            print(f"   💰 Portfolio Value: ${metrics['total_value']:,.2f}")
            print(f"   💵 Cash Balance: ${metrics['cash_balance']:,.2f}")
            print(f"   📈 Daily P&L: ${metrics['daily_pnl']:,.2f} ({metrics['daily_pnl_percent']:+.2f}%)")
            print(f"   📊 Positions: {metrics['positions_count']}")
            print(f"   📉 Drawdown: {metrics['current_drawdown']:.2f}%")
            
            return {
                'success': True,
                'portfolio_value': metrics['total_value'],
                'daily_pnl': metrics['daily_pnl'],
                'positions_count': metrics['positions_count'],
                'cash_balance': metrics['cash_balance'],
                'timestamp': datetime.now()
            }
        except Exception as e:
            print(f"   ❌ Portfolio review error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _check_ai_agents(self) -> Dict[str, Any]:
        """Check AI agent status and performance"""
        agent_status = {}
        
        for agent_id, agent in self.ai_agents.agents.items():
            try:
                # Quick test query
                test_query = "Quick status check - respond with your current status"
                if agent_id == 'market_analyst':
                    result = self.ai_agents.get_real_market_data(agent_id, 'BTC-USD')
                    agent_status[agent_id] = 'OK' if result['success'] else 'ERROR'
                else:
                    # For other agents, check their tools
                    result = self.ai_agents.agent_query_database(agent_id, 'recent_prices')
                    agent_status[agent_id] = 'OK' if result['success'] else 'ERROR'
                
                print(f"   🤖 {agent_id}: {agent_status[agent_id]} (Success rate: {agent.success_rate:.1%})")
            except:
                agent_status[agent_id] = 'ERROR'
                print(f"   🤖 {agent_id}: ERROR")
        
        return {
            'success': all(status == 'OK' for status in agent_status.values()),
            'agent_status': agent_status,
            'total_agents': len(agent_status),
            'working_agents': len([s for s in agent_status.values() if s == 'OK']),
            'timestamp': datetime.now()
        }
    
    def _risk_management_check(self) -> Dict[str, Any]:
        """Check risk management status"""
        try:
            alerts = self.risk_manager.check_risk_limits()
            
            if alerts:
                print(f"   ⚠️ {len(alerts)} risk alerts:")
                for alert in alerts:
                    print(f"      • {alert.message}")
            else:
                print("   ✅ All risk limits within acceptable ranges")
            
            return {
                'success': True,
                'alerts_count': len(alerts),
                'emergency_stop': self.risk_manager.emergency_stop,
                'alerts': [alert.message for alert in alerts],
                'timestamp': datetime.now()
            }
        except Exception as e:
            print(f"   ❌ Risk check error: {e}")
            return {'success': False, 'error': str(e)}
    
    def _create_daily_trading_plan(self) -> Dict[str, Any]:
        """Create trading plan for the day"""
        # Get market analysis from AI
        analysis_prompt = """Analyze current market conditions and provide a daily trading plan:

1. Market sentiment (bullish/bearish/neutral)
2. Key levels to watch
3. Potential trading opportunities
4. Risk factors to monitor
5. Recommended position sizes

Keep it concise and actionable."""

        try:
            analysis = self.command_center.query_agent('deepseek_finance', analysis_prompt)
            
            if analysis and analysis['success']:
                print(f"   📋 Trading plan generated ({analysis['response_length']} chars)")
                print(f"   🕐 Analysis time: {analysis['response_time']:.1f}s")
                
                # Save plan to file
                plan_filename = f"trading_plan_{datetime.now().strftime('%Y%m%d')}.txt"
                with open(plan_filename, 'w') as f:
                    f.write(f"Daily Trading Plan - {datetime.now().strftime('%Y-%m-%d')}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(analysis['response'])
                
                return {
                    'success': True,
                    'plan_generated': True,
                    'plan_file': plan_filename,
                    'analysis_length': analysis['response_length'],
                    'timestamp': datetime.now()
                }
            else:
                print("   ❌ Failed to generate trading plan")
                return {'success': False, 'error': 'AI analysis failed'}
                
        except Exception as e:
            print(f"   ❌ Trading plan error: {e}")
            return {'success': False, 'error': str(e)}
    
    def continuous_monitoring_loop(self, duration_hours: int = 8):
        """Run continuous monitoring during trading hours"""
        print(f"\n🔄 STARTING CONTINUOUS MONITORING ({duration_hours} hours)")
        print("=" * 60)
        
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)
        
        while datetime.now() < end_time:
            current_time = datetime.now()
            
            # Check if within trading hours
            if self._is_trading_hours():
                # Market data check every 5 minutes
                if self._should_check_market():
                    self._periodic_market_check()
                
                # Risk check every minute
                if self._should_check_risk():
                    self._periodic_risk_check()
                
                # Look for trading opportunities
                if self.daily_stats['trades_executed'] < self.daily_config['max_daily_trades']:
                    self._scan_for_opportunities()
            
            # Sleep for 30 seconds
            time.sleep(30)
        
        print(f"\n🏁 MONITORING COMPLETE")
        self._end_of_day_summary()
    
    def _is_trading_hours(self) -> bool:
        """Check if currently in trading hours"""
        current_hour = datetime.now().hour
        return self.daily_config['trading_hours']['start'] <= current_hour <= self.daily_config['trading_hours']['end']
    
    def _should_check_market(self) -> bool:
        """Check if it's time for market data update"""
        if not self.daily_stats['last_market_check']:
            return True
        
        time_since_last = datetime.now() - self.daily_stats['last_market_check']
        return time_since_last.seconds >= self.daily_config['market_check_interval']
    
    def _should_check_risk(self) -> bool:
        """Check if it's time for risk assessment"""
        if not self.daily_stats['last_risk_check']:
            return True
        
        time_since_last = datetime.now() - self.daily_stats['last_risk_check']
        return time_since_last.seconds >= self.daily_config['risk_check_interval']
    
    def _periodic_market_check(self):
        """Periodic market data update"""
        print(f"📊 Market check at {datetime.now().strftime('%H:%M:%S')}")
        
        # Quick market update
        for symbol in ['BTC-USD', 'ETH-USD']:  # Check key symbols
            try:
                data = self.ai_agents.get_real_market_data('market_analyst', symbol)
                if data['success']:
                    price = data['data']['current_price']
                    print(f"   {symbol}: ${price:,.2f}")
            except:
                pass
        
        self.daily_stats['market_checks'] += 1
        self.daily_stats['last_market_check'] = datetime.now()
    
    def _periodic_risk_check(self):
        """Periodic risk assessment"""
        alerts = self.risk_manager.check_risk_limits()
        
        if alerts:
            print(f"⚠️ Risk alerts: {len(alerts)}")
            self.daily_stats['alerts_generated'] += len(alerts)
        
        self.daily_stats['last_risk_check'] = datetime.now()
    
    def _scan_for_opportunities(self):
        """Scan for trading opportunities"""
        # This would contain your trading logic
        # For now, just log that we're scanning
        pass
    
    def _end_of_day_summary(self):
        """Generate end of day summary"""
        print("\n📊 END OF DAY SUMMARY")
        print("=" * 40)
        
        runtime = datetime.now() - self.daily_stats['start_time']
        
        print(f"   ⏱️ Runtime: {runtime}")
        print(f"   📊 Market checks: {self.daily_stats['market_checks']}")
        print(f"   🤖 AI queries: {self.daily_stats['ai_queries']}")
        print(f"   💼 Trades executed: {self.daily_stats['trades_executed']}")
        print(f"   ⚠️ Alerts generated: {self.daily_stats['alerts_generated']}")
        
        # Final portfolio status
        try:
            report = self.risk_manager.generate_risk_report()
            metrics = report['portfolio_metrics']
            print(f"   💰 Final portfolio value: ${metrics['total_value']:,.2f}")
            print(f"   📈 Daily P&L: ${metrics['daily_pnl']:,.2f} ({metrics['daily_pnl_percent']:+.2f}%)")
        except:
            print("   ❌ Could not get final portfolio status")

def main():
    """Run daily operations system"""
    print("🏢 DAILY OPERATIONS SYSTEM")
    print("=" * 50)
    
    # Initialize system
    ops = DailyOperationsSystem(100000.0)
    
    # Run morning startup
    startup_results = ops.morning_startup_routine()
    
    # Check if system is ready
    if all(r.get('success', False) for r in startup_results.values()):
        print("\n🚀 SYSTEM READY FOR TRADING")
        
        # For demo, just show what continuous monitoring would do
        print("\n📋 CONTINUOUS MONITORING WOULD:")
        print("   • Check market data every 5 minutes")
        print("   • Monitor risk every 1 minute")
        print("   • Scan for trading opportunities")
        print("   • Execute trades within risk limits")
        print("   • Generate alerts when needed")
        print("   • Provide end-of-day summary")
        
        # Uncomment to run actual monitoring:
        # ops.continuous_monitoring_loop(8)  # 8 hours
    else:
        print("\n❌ SYSTEM NOT READY - Fix issues before trading")

if __name__ == "__main__":
    main()
