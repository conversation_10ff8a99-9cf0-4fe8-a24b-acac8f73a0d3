#!/usr/bin/env python3
"""
Noryon AI Trading System - Monitoring and Alerting

This module provides comprehensive monitoring, alerting, and observability
for the Noryon AI Trading System.

Features:
- System health monitoring
- Performance metrics collection
- Alert management
- Log aggregation
- Custom dashboards
- Anomaly detection
"""

import asyncio
import logging
import json
import time
import psutil
import aiohttp
import smtplib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from pathlib import Path
import yaml
import sqlite3
from collections import defaultdict, deque
import statistics

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """Represents a single metric data point."""
    timestamp: datetime
    name: str
    value: float
    tags: Dict[str, str]
    unit: str = ""


@dataclass
class Alert:
    """Represents an alert."""
    id: str
    name: str
    severity: str  # critical, warning, info
    message: str
    timestamp: datetime
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    tags: Dict[str, str] = None


@dataclass
class HealthCheck:
    """Represents a health check result."""
    name: str
    status: str  # healthy, unhealthy, unknown
    message: str
    timestamp: datetime
    response_time: float
    details: Dict[str, Any] = None


class MetricsCollector:
    """
    Collects system and application metrics.
    """
    
    def __init__(self):
        self.metrics_buffer = deque(maxlen=10000)
        self.collection_interval = 30  # seconds
        self.running = False
    
    async def start_collection(self):
        """Start metrics collection."""
        self.running = True
        logger.info("Starting metrics collection")
        
        while self.running:
            try:
                await self._collect_system_metrics()
                await self._collect_application_metrics()
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error collecting metrics: {e}")
                await asyncio.sleep(self.collection_interval)
    
    def stop_collection(self):
        """Stop metrics collection."""
        self.running = False
        logger.info("Stopped metrics collection")
    
    async def _collect_system_metrics(self):
        """Collect system-level metrics."""
        timestamp = datetime.now()
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        self._add_metric("system.cpu.usage", cpu_percent, timestamp, {"unit": "percent"})
        
        # Memory metrics
        memory = psutil.virtual_memory()
        self._add_metric("system.memory.usage", memory.percent, timestamp, {"unit": "percent"})
        self._add_metric("system.memory.available", memory.available, timestamp, {"unit": "bytes"})
        self._add_metric("system.memory.total", memory.total, timestamp, {"unit": "bytes"})
        
        # Disk metrics
        disk = psutil.disk_usage('/')
        self._add_metric("system.disk.usage", (disk.used / disk.total) * 100, timestamp, {"unit": "percent"})
        self._add_metric("system.disk.free", disk.free, timestamp, {"unit": "bytes"})
        
        # Network metrics
        network = psutil.net_io_counters()
        self._add_metric("system.network.bytes_sent", network.bytes_sent, timestamp, {"unit": "bytes"})
        self._add_metric("system.network.bytes_recv", network.bytes_recv, timestamp, {"unit": "bytes"})
    
    async def _collect_application_metrics(self):
        """Collect application-specific metrics."""
        timestamp = datetime.now()
        
        try:
            # Check if API is responding
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                async with session.get('http://localhost:8000/health', timeout=5) as response:
                    response_time = (time.time() - start_time) * 1000
                    self._add_metric("app.api.response_time", response_time, timestamp, {"unit": "ms"})
                    self._add_metric("app.api.status", 1 if response.status == 200 else 0, timestamp)
        except Exception as e:
            logger.warning(f"Failed to collect API metrics: {e}")
            self._add_metric("app.api.status", 0, timestamp)
    
    def _add_metric(self, name: str, value: float, timestamp: datetime, tags: Dict[str, str] = None):
        """Add a metric to the buffer."""
        metric = MetricPoint(
            timestamp=timestamp,
            name=name,
            value=value,
            tags=tags or {},
            unit=tags.get("unit", "") if tags else ""
        )
        self.metrics_buffer.append(metric)
    
    def get_metrics(self, name_pattern: str = None, since: datetime = None) -> List[MetricPoint]:
        """Get metrics from buffer."""
        metrics = list(self.metrics_buffer)
        
        if name_pattern:
            metrics = [m for m in metrics if name_pattern in m.name]
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics


class AlertManager:
    """
    Manages alerts and notifications.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)
        self.notification_channels = self._setup_notification_channels()
        self.alert_rules = self._load_alert_rules()
    
    def _setup_notification_channels(self) -> Dict[str, Any]:
        """Setup notification channels."""
        channels = {}
        
        # Email channel
        if self.config.get('email', {}).get('enabled', False):
            channels['email'] = {
                'smtp_server': self.config['email']['smtp_server'],
                'smtp_port': self.config['email']['smtp_port'],
                'username': self.config['email']['username'],
                'password': self.config['email']['password'],
                'from_email': self.config['email']['from_email'],
                'to_emails': self.config['email']['to_emails']
            }
        
        # Slack channel
        if self.config.get('slack', {}).get('enabled', False):
            channels['slack'] = {
                'webhook_url': self.config['slack']['webhook_url'],
                'channel': self.config['slack']['channel']
            }
        
        # Discord channel
        if self.config.get('discord', {}).get('enabled', False):
            channels['discord'] = {
                'webhook_url': self.config['discord']['webhook_url']
            }
        
        return channels
    
    def _load_alert_rules(self) -> List[Dict[str, Any]]:
        """Load alert rules from configuration."""
        return [
            {
                'name': 'high_cpu_usage',
                'condition': lambda metrics: any(m.value > 80 for m in metrics if m.name == 'system.cpu.usage'),
                'severity': 'warning',
                'message': 'High CPU usage detected'
            },
            {
                'name': 'high_memory_usage',
                'condition': lambda metrics: any(m.value > 85 for m in metrics if m.name == 'system.memory.usage'),
                'severity': 'warning',
                'message': 'High memory usage detected'
            },
            {
                'name': 'api_down',
                'condition': lambda metrics: any(m.value == 0 for m in metrics if m.name == 'app.api.status'),
                'severity': 'critical',
                'message': 'API is not responding'
            },
            {
                'name': 'slow_api_response',
                'condition': lambda metrics: any(m.value > 5000 for m in metrics if m.name == 'app.api.response_time'),
                'severity': 'warning',
                'message': 'API response time is slow'
            }
        ]
    
    async def evaluate_alerts(self, metrics: List[MetricPoint]):
        """Evaluate alert rules against metrics."""
        for rule in self.alert_rules:
            try:
                if rule['condition'](metrics):
                    await self._trigger_alert(rule)
                else:
                    await self._resolve_alert(rule['name'])
            except Exception as e:
                logger.error(f"Error evaluating alert rule {rule['name']}: {e}")
    
    async def _trigger_alert(self, rule: Dict[str, Any]):
        """Trigger an alert."""
        alert_id = rule['name']
        
        # Check if alert is already active
        if alert_id in self.active_alerts:
            return
        
        alert = Alert(
            id=alert_id,
            name=rule['name'],
            severity=rule['severity'],
            message=rule['message'],
            timestamp=datetime.now()
        )
        
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        logger.warning(f"Alert triggered: {alert.name} - {alert.message}")
        
        # Send notifications
        await self._send_notifications(alert)
    
    async def _resolve_alert(self, alert_id: str):
        """Resolve an alert."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()
            
            del self.active_alerts[alert_id]
            
            logger.info(f"Alert resolved: {alert.name}")
            
            # Send resolution notification
            await self._send_resolution_notification(alert)
    
    async def _send_notifications(self, alert: Alert):
        """Send alert notifications to configured channels."""
        for channel_name, channel_config in self.notification_channels.items():
            try:
                if channel_name == 'email':
                    await self._send_email_notification(alert, channel_config)
                elif channel_name == 'slack':
                    await self._send_slack_notification(alert, channel_config)
                elif channel_name == 'discord':
                    await self._send_discord_notification(alert, channel_config)
            except Exception as e:
                logger.error(f"Failed to send {channel_name} notification: {e}")
    
    async def _send_email_notification(self, alert: Alert, config: Dict[str, Any]):
        """Send email notification."""
        msg = MimeMultipart()
        msg['From'] = config['from_email']
        msg['To'] = ', '.join(config['to_emails'])
        msg['Subject'] = f"[{alert.severity.upper()}] {alert.name}"
        
        body = f"""
        Alert: {alert.name}
        Severity: {alert.severity}
        Message: {alert.message}
        Timestamp: {alert.timestamp}
        """
        
        msg.attach(MimeText(body, 'plain'))
        
        server = smtplib.SMTP(config['smtp_server'], config['smtp_port'])
        server.starttls()
        server.login(config['username'], config['password'])
        server.send_message(msg)
        server.quit()
    
    async def _send_slack_notification(self, alert: Alert, config: Dict[str, Any]):
        """Send Slack notification."""
        payload = {
            'channel': config['channel'],
            'text': f"🚨 *{alert.severity.upper()}*: {alert.name}",
            'attachments': [{
                'color': 'danger' if alert.severity == 'critical' else 'warning',
                'fields': [
                    {'title': 'Message', 'value': alert.message, 'short': False},
                    {'title': 'Timestamp', 'value': str(alert.timestamp), 'short': True}
                ]
            }]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(config['webhook_url'], json=payload) as response:
                if response.status != 200:
                    logger.error(f"Failed to send Slack notification: {response.status}")
    
    async def _send_discord_notification(self, alert: Alert, config: Dict[str, Any]):
        """Send Discord notification."""
        payload = {
            'embeds': [{
                'title': f"🚨 {alert.severity.upper()}: {alert.name}",
                'description': alert.message,
                'color': 15158332 if alert.severity == 'critical' else 16776960,  # Red or Yellow
                'timestamp': alert.timestamp.isoformat(),
                'fields': [
                    {'name': 'Severity', 'value': alert.severity, 'inline': True},
                    {'name': 'Time', 'value': str(alert.timestamp), 'inline': True}
                ]
            }]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(config['webhook_url'], json=payload) as response:
                if response.status != 204:
                    logger.error(f"Failed to send Discord notification: {response.status}")
    
    async def _send_resolution_notification(self, alert: Alert):
        """Send alert resolution notification."""
        # Simplified - just log for now
        logger.info(f"Alert {alert.name} resolved at {alert.resolved_at}")
    
    def get_active_alerts(self) -> List[Alert]:
        """Get list of active alerts."""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """Get alert history."""
        return list(self.alert_history)[-limit:]


class HealthMonitor:
    """
    Monitors system and application health.
    """
    
    def __init__(self):
        self.health_checks = {}
        self.check_interval = 60  # seconds
        self.running = False
    
    def register_health_check(self, name: str, check_func: Callable, interval: int = None):
        """Register a health check."""
        self.health_checks[name] = {
            'func': check_func,
            'interval': interval or self.check_interval,
            'last_run': None,
            'last_result': None
        }
    
    async def start_monitoring(self):
        """Start health monitoring."""
        self.running = True
        logger.info("Starting health monitoring")
        
        # Register default health checks
        self._register_default_checks()
        
        while self.running:
            try:
                await self._run_health_checks()
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(30)
    
    def stop_monitoring(self):
        """Stop health monitoring."""
        self.running = False
        logger.info("Stopped health monitoring")
    
    def _register_default_checks(self):
        """Register default health checks."""
        self.register_health_check('api_health', self._check_api_health)
        self.register_health_check('database_health', self._check_database_health)
        self.register_health_check('disk_space', self._check_disk_space)
        self.register_health_check('memory_usage', self._check_memory_usage)
    
    async def _run_health_checks(self):
        """Run all health checks."""
        current_time = datetime.now()
        
        for name, check_config in self.health_checks.items():
            last_run = check_config['last_run']
            interval = check_config['interval']
            
            if last_run is None or (current_time - last_run).seconds >= interval:
                try:
                    start_time = time.time()
                    result = await check_config['func']()
                    response_time = (time.time() - start_time) * 1000
                    
                    health_check = HealthCheck(
                        name=name,
                        status=result.get('status', 'unknown'),
                        message=result.get('message', ''),
                        timestamp=current_time,
                        response_time=response_time,
                        details=result.get('details')
                    )
                    
                    check_config['last_run'] = current_time
                    check_config['last_result'] = health_check
                    
                    if health_check.status != 'healthy':
                        logger.warning(f"Health check failed: {name} - {health_check.message}")
                    
                except Exception as e:
                    logger.error(f"Health check {name} failed with exception: {e}")
                    check_config['last_result'] = HealthCheck(
                        name=name,
                        status='unhealthy',
                        message=f"Check failed: {e}",
                        timestamp=current_time,
                        response_time=0
                    )
    
    async def _check_api_health(self) -> Dict[str, Any]:
        """Check API health."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:8000/health', timeout=5) as response:
                    if response.status == 200:
                        return {'status': 'healthy', 'message': 'API is responding'}
                    else:
                        return {'status': 'unhealthy', 'message': f'API returned status {response.status}'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'API check failed: {e}'}
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database health."""
        # Simplified check - in production, you'd test actual DB connection
        try:
            # This is a placeholder - implement actual DB health check
            return {'status': 'healthy', 'message': 'Database is accessible'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Database check failed: {e}'}
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """Check disk space."""
        try:
            disk = psutil.disk_usage('/')
            usage_percent = (disk.used / disk.total) * 100
            
            if usage_percent > 90:
                return {'status': 'unhealthy', 'message': f'Disk usage critical: {usage_percent:.1f}%'}
            elif usage_percent > 80:
                return {'status': 'warning', 'message': f'Disk usage high: {usage_percent:.1f}%'}
            else:
                return {'status': 'healthy', 'message': f'Disk usage normal: {usage_percent:.1f}%'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Disk check failed: {e}'}
    
    async def _check_memory_usage(self) -> Dict[str, Any]:
        """Check memory usage."""
        try:
            memory = psutil.virtual_memory()
            
            if memory.percent > 90:
                return {'status': 'unhealthy', 'message': f'Memory usage critical: {memory.percent:.1f}%'}
            elif memory.percent > 80:
                return {'status': 'warning', 'message': f'Memory usage high: {memory.percent:.1f}%'}
            else:
                return {'status': 'healthy', 'message': f'Memory usage normal: {memory.percent:.1f}%'}
        except Exception as e:
            return {'status': 'unhealthy', 'message': f'Memory check failed: {e}'}
    
    def get_health_status(self) -> Dict[str, HealthCheck]:
        """Get current health status."""
        status = {}
        for name, check_config in self.health_checks.items():
            if check_config['last_result']:
                status[name] = check_config['last_result']
        return status


class MonitoringSystem:
    """
    Main monitoring system that coordinates all monitoring components.
    """
    
    def __init__(self, config_path: str = "monitoring_config.yaml"):
        self.config = self._load_config(config_path)
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager(self.config.get('alerting', {}))
        self.health_monitor = HealthMonitor()
        self.running = False
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load monitoring configuration."""
        config_file = Path(config_path)
        
        if not config_file.exists():
            # Create default config
            default_config = {
                'metrics': {
                    'collection_interval': 30,
                    'retention_days': 7
                },
                'alerting': {
                    'email': {
                        'enabled': False,
                        'smtp_server': 'smtp.gmail.com',
                        'smtp_port': 587,
                        'username': '',
                        'password': '',
                        'from_email': '',
                        'to_emails': []
                    },
                    'slack': {
                        'enabled': False,
                        'webhook_url': '',
                        'channel': '#alerts'
                    },
                    'discord': {
                        'enabled': False,
                        'webhook_url': ''
                    }
                },
                'health_checks': {
                    'interval': 60,
                    'timeout': 30
                }
            }
            
            with open(config_file, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
            
            logger.info(f"Created default monitoring config: {config_file}")
            return default_config
        
        with open(config_file, 'r') as f:
            return yaml.safe_load(f)
    
    async def start(self):
        """Start the monitoring system."""
        logger.info("Starting monitoring system")
        self.running = True
        
        # Start all monitoring components
        tasks = [
            asyncio.create_task(self.metrics_collector.start_collection()),
            asyncio.create_task(self.health_monitor.start_monitoring()),
            asyncio.create_task(self._monitoring_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Monitoring system error: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """Stop the monitoring system."""
        logger.info("Stopping monitoring system")
        self.running = False
        self.metrics_collector.stop_collection()
        self.health_monitor.stop_monitoring()
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.running:
            try:
                # Get recent metrics
                since = datetime.now() - timedelta(minutes=5)
                metrics = self.metrics_collector.get_metrics(since=since)
                
                # Evaluate alerts
                await self.alert_manager.evaluate_alerts(metrics)
                
                # Sleep before next iteration
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status."""
        health_status = self.health_monitor.get_health_status()
        active_alerts = self.alert_manager.get_active_alerts()
        
        # Determine overall status
        overall_status = "healthy"
        if any(alert.severity == "critical" for alert in active_alerts):
            overall_status = "critical"
        elif any(alert.severity == "warning" for alert in active_alerts):
            overall_status = "warning"
        elif any(check.status == "unhealthy" for check in health_status.values()):
            overall_status = "unhealthy"
        
        return {
            'overall_status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'health_checks': {name: asdict(check) for name, check in health_status.items()},
            'active_alerts': [asdict(alert) for alert in active_alerts],
            'metrics_collected': len(self.metrics_collector.metrics_buffer)
        }
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get metrics summary."""
        metrics = self.metrics_collector.get_metrics()
        
        summary = defaultdict(list)
        for metric in metrics:
            summary[metric.name].append(metric.value)
        
        result = {}
        for name, values in summary.items():
            if values:
                result[name] = {
                    'count': len(values),
                    'avg': statistics.mean(values),
                    'min': min(values),
                    'max': max(values),
                    'latest': values[-1]
                }
        
        return result


async def main():
    """
    Main entry point for monitoring system.
    """
    monitoring = MonitoringSystem()
    
    try:
        await monitoring.start()
    except KeyboardInterrupt:
        logger.info("Monitoring interrupted by user")
    except Exception as e:
        logger.error(f"Monitoring system failed: {e}")
    finally:
        await monitoring.stop()


if __name__ == "__main__":
    asyncio.run(main())