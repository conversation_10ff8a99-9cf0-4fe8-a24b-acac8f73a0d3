#!/usr/bin/env python3
"""
Complete Model Architecture Analysis
Detailed breakdown of all AI models and their architectures
"""

import subprocess
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree

console = Console()

def get_model_architectures():
    """Define model architectures and specifications"""
    return {
        # DeepSeek Models
        "deepseek-r1": {
            "base_architecture": "Llama-based Transformer",
            "parameters": "8B (8 Billion)",
            "context_length": "32K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Reasoning chains", "Multi-step thinking", "CoT (Chain of Thought)"],
            "training_method": "Reinforcement Learning from Human Feedback (RLHF)",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "8-12 GB VRAM"
        },
        
        # Gemma Models  
        "gemma3": {
            "base_architecture": "Gemma Transformer",
            "parameters": "9B-12B (9-12 Billion)",
            "context_length": "8K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Instruction tuning", "Safety filtering", "Multi-task learning"],
            "training_method": "Supervised Fine-tuning + Constitutional AI",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "8-10 GB VRAM"
        },
        
        # Phi Models
        "phi4": {
            "base_architecture": "Phi Transformer",
            "parameters": "9B-14B (9-14 Billion)",
            "context_length": "4K-16K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Reasoning focus", "Mathematical capabilities", "Code understanding"],
            "training_method": "High-quality data curation + Instruction tuning",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "9-14 GB VRAM"
        },
        
        # Qwen Models
        "qwen3": {
            "base_architecture": "Qwen Transformer",
            "parameters": "7B-14B (7-14 Billion)",
            "context_length": "32K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Multilingual", "Tool use", "Function calling", "Long context"],
            "training_method": "Supervised Fine-tuning + RLHF",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "7-14 GB VRAM"
        },
        
        # Falcon Models
        "falcon3": {
            "base_architecture": "Falcon Transformer",
            "parameters": "10B (10 Billion)",
            "context_length": "8K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["High performance", "Efficient inference", "Optimized attention"],
            "training_method": "Large-scale pretraining + Instruction tuning",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "6-8 GB VRAM"
        },
        
        # Granite Models
        "granite": {
            "base_architecture": "Granite Vision Transformer",
            "parameters": "2B (2 Billion)",
            "context_length": "4K tokens",
            "architecture_type": "Vision-Language Transformer",
            "special_features": ["Vision understanding", "Chart analysis", "Multimodal", "Image processing"],
            "training_method": "Vision-Language pretraining + Fine-tuning",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "2-4 GB VRAM"
        },
        
        # Dolphin Models
        "dolphin3": {
            "base_architecture": "Llama-based Transformer",
            "parameters": "8B (8 Billion)",
            "context_length": "8K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Uncensored", "Creative", "Adaptive", "Role-playing"],
            "training_method": "Supervised Fine-tuning on diverse datasets",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "5-8 GB VRAM"
        },
        
        # Exaone Models
        "exaone": {
            "base_architecture": "Exaone Transformer",
            "parameters": "7.8B (7.8 Billion)",
            "context_length": "4K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Deep reasoning", "Pattern recognition", "Analytical thinking"],
            "training_method": "Large-scale pretraining + Specialized fine-tuning",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "5-8 GB VRAM"
        },
        
        # Marco-O1 Models
        "marco-o1": {
            "base_architecture": "O1-style Reasoning Transformer",
            "parameters": "7B (7 Billion)",
            "context_length": "8K tokens",
            "architecture_type": "Reasoning-enhanced Transformer",
            "special_features": ["Step-by-step reasoning", "Transparent thinking", "Logical chains"],
            "training_method": "Reasoning-focused training + Chain-of-thought",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "5-7 GB VRAM"
        },
        
        # Cogito Models
        "cogito": {
            "base_architecture": "Cogito Transformer",
            "parameters": "14B (14 Billion)",
            "context_length": "8K tokens",
            "architecture_type": "Decoder-only Transformer",
            "special_features": ["Cognitive analysis", "Behavioral understanding", "Psychology-aware"],
            "training_method": "Cognitive science datasets + Behavioral fine-tuning",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "9-14 GB VRAM"
        },
        
        # DeepScaler Models
        "deepscaler": {
            "base_architecture": "Scaling-optimized Transformer",
            "parameters": "1.5B (1.5 Billion)",
            "context_length": "4K tokens",
            "architecture_type": "Efficient Transformer",
            "special_features": ["Scaling optimization", "Efficiency focus", "Resource management"],
            "training_method": "Efficiency-focused training + Scaling optimization",
            "quantization": "4-bit, 8-bit, FP16",
            "memory_requirement": "2-4 GB VRAM"
        }
    }

def get_ollama_models_detailed():
    """Get detailed Ollama model information"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        model_name = parts[0]
                        model_id = parts[1]
                        size = parts[2]
                        modified = ' '.join(parts[3:])
                        
                        # Extract base model type
                        base_type = extract_base_model_type(model_name)
                        
                        models.append({
                            'name': model_name,
                            'id': model_id,
                            'size': size,
                            'modified': modified,
                            'base_type': base_type,
                            'status': 'Ready'
                        })
            return models
    except Exception as e:
        console.print(f"[red]Error getting Ollama models: {e}[/red]")
    return []

def extract_base_model_type(model_name):
    """Extract base model type from model name"""
    name_lower = model_name.lower()
    
    if 'deepseek' in name_lower:
        return 'deepseek-r1'
    elif 'gemma' in name_lower:
        return 'gemma3'
    elif 'phi' in name_lower:
        return 'phi4'
    elif 'qwen' in name_lower:
        return 'qwen3'
    elif 'falcon' in name_lower:
        return 'falcon3'
    elif 'granite' in name_lower:
        return 'granite'
    elif 'dolphin' in name_lower:
        return 'dolphin3'
    elif 'exaone' in name_lower:
        return 'exaone'
    elif 'marco' in name_lower:
        return 'marco-o1'
    elif 'cogito' in name_lower:
        return 'cogito'
    elif 'deepscaler' in name_lower:
        return 'deepscaler'
    else:
        return 'unknown'

def display_model_count_summary():
    """Display model count summary"""
    ollama_models = get_ollama_models_detailed()
    architectures = get_model_architectures()
    
    # Count by architecture type
    arch_counts = {}
    total_params = 0
    total_memory = 0
    
    for model in ollama_models:
        base_type = model['base_type']
        if base_type in architectures:
            arch_counts[base_type] = arch_counts.get(base_type, 0) + 1
            
            # Extract parameter count for calculation
            params_str = architectures[base_type]['parameters']
            if 'B' in params_str:
                params = float(params_str.split('B')[0].split('-')[-1])
                total_params += params
            
            # Extract memory requirement
            memory_str = architectures[base_type]['memory_requirement']
            if 'GB' in memory_str:
                memory = float(memory_str.split('-')[-1].split(' ')[0])
                total_memory += memory
    
    console.print(Panel(
        f"[bold blue]🤖 COMPLETE MODEL INVENTORY[/bold blue]\n\n"
        f"[green]TOTAL MODELS: {len(ollama_models)}[/green]\n"
        f"[yellow]TOTAL PARAMETERS: ~{total_params:.1f}B[/yellow]\n"
        f"[red]TOTAL MEMORY USAGE: ~{total_memory:.1f} GB[/red]\n\n"
        f"[cyan]ARCHITECTURE BREAKDOWN:[/cyan]\n" +
        "\n".join([f"• {arch.title()}: {count} models" for arch, count in arch_counts.items()]),
        title="Model Summary"
    ))

def display_architecture_details():
    """Display detailed architecture information"""
    architectures = get_model_architectures()
    ollama_models = get_ollama_models_detailed()
    
    console.print("\n[bold yellow]🏗️ DETAILED ARCHITECTURE BREAKDOWN[/bold yellow]")
    
    for arch_name, arch_info in architectures.items():
        # Count models of this architecture
        model_count = len([m for m in ollama_models if m['base_type'] == arch_name])
        
        if model_count > 0:
            # Create architecture table
            arch_table = Table(title=f"{arch_name.title()} Architecture ({model_count} models)")
            arch_table.add_column("Specification", style="cyan", width=20)
            arch_table.add_column("Details", style="green", width=50)
            
            arch_table.add_row("Base Architecture", arch_info['base_architecture'])
            arch_table.add_row("Parameters", arch_info['parameters'])
            arch_table.add_row("Context Length", arch_info['context_length'])
            arch_table.add_row("Architecture Type", arch_info['architecture_type'])
            arch_table.add_row("Special Features", ", ".join(arch_info['special_features']))
            arch_table.add_row("Training Method", arch_info['training_method'])
            arch_table.add_row("Quantization", arch_info['quantization'])
            arch_table.add_row("Memory Requirement", arch_info['memory_requirement'])
            
            console.print(arch_table)
            console.print()

def display_model_tree():
    """Display models in a tree structure by architecture"""
    ollama_models = get_ollama_models_detailed()
    architectures = get_model_architectures()
    
    tree = Tree("[bold blue]🌳 AI Model Architecture Tree[/bold blue]")
    
    # Group models by architecture
    arch_groups = {}
    for model in ollama_models:
        base_type = model['base_type']
        if base_type not in arch_groups:
            arch_groups[base_type] = []
        arch_groups[base_type].append(model)
    
    for arch_name, models in arch_groups.items():
        if arch_name in architectures:
            arch_info = architectures[arch_name]
            arch_branch = tree.add(f"[green]{arch_name.title()}[/green] ({arch_info['parameters']}) - {len(models)} models")
            
            for model in models:
                model_branch = arch_branch.add(f"[cyan]{model['name']}[/cyan] ({model['size']})")
                model_branch.add(f"[yellow]ID: {model['id']}[/yellow]")
                model_branch.add(f"[blue]Modified: {model['modified']}[/blue]")
    
    console.print(tree)

def display_technical_specifications():
    """Display technical specifications summary"""
    architectures = get_model_architectures()
    ollama_models = get_ollama_models_detailed()
    
    # Create technical specs table
    tech_table = Table(title="🔧 Technical Specifications Summary")
    tech_table.add_column("Architecture", style="cyan", width=15)
    tech_table.add_column("Models", style="green", width=8)
    tech_table.add_column("Parameters", style="yellow", width=12)
    tech_table.add_column("Context", style="blue", width=10)
    tech_table.add_column("Memory", style="red", width=12)
    tech_table.add_column("Special Features", style="magenta", width=30)
    
    for arch_name, arch_info in architectures.items():
        model_count = len([m for m in ollama_models if m['base_type'] == arch_name])
        if model_count > 0:
            features = ", ".join(arch_info['special_features'][:2]) + "..."
            tech_table.add_row(
                arch_name.title(),
                str(model_count),
                arch_info['parameters'],
                arch_info['context_length'],
                arch_info['memory_requirement'],
                features
            )
    
    console.print(tech_table)

def main():
    """Main analysis function"""
    console.print(Panel(
        "[bold blue]🏗️ Complete Model Architecture Analysis[/bold blue]\n\n"
        "Comprehensive breakdown of all AI models and their architectures,\n"
        "including technical specifications, parameters, and capabilities.",
        title="Architecture Analysis"
    ))
    
    display_model_count_summary()
    display_technical_specifications()
    display_model_tree()
    display_architecture_details()

if __name__ == "__main__":
    main()
