#!/usr/bin/env python3
"""
Noryon AI Trading System - Master Launch Script

This script orchestrates the complete setup, training, and launch of the
Noryon AI Trading System for paper trading.

Usage:
    python launch_noryon.py --full-setup
    python launch_noryon.py --quick-start
    python launch_noryon.py --paper-trading-only
    python launch_noryon.py --health-check
"""

import os
import sys
import json
import yaml
import argparse
import subprocess
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.columns import Columns
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.layout import Layout
from rich.live import Live

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

console = Console()

def docker_compose_up(compose_file: str):
    """Runs docker-compose up with the specified compose file."""
    console.print(f"[cyan]Running docker-compose -f {compose_file} up -d --build...[/cyan]")
    try:
        subprocess.run(
            ['docker-compose', '-f', compose_file, 'up', '-d', '--build'],
            check=True, capture_output=True, text=True
        )
        console.print(f"[green]✅ docker-compose {compose_file} started successfully.[/green]")
    except subprocess.CalledProcessError as e:
        console.print(f"[red]❌ docker-compose {compose_file} failed: {e.stderr}[/red]")
        raise

def docker_compose_down(compose_file: str):
    """Runs docker-compose down with the specified compose file."""
    console.print(f"[cyan]Running docker-compose -f {compose_file} down...[/cyan]")
    try:
        # First check if Docker is accessible
        docker_check = subprocess.run(
            ['docker', 'info'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if docker_check.returncode != 0:
            console.print(f"[yellow]⚠️ Docker not accessible, skipping {compose_file} down[/yellow]")
            return
        
        subprocess.run(
            ['docker-compose', '-f', compose_file, 'down'],
            check=True, capture_output=True, text=True
        )
        console.print(f"[green]✅ docker-compose {compose_file} stopped successfully.[/green]")
    except subprocess.TimeoutExpired:
        console.print(f"[yellow]⚠️ Docker check timeout, skipping {compose_file} down[/yellow]")
    except subprocess.CalledProcessError as e:
        console.print(f"[yellow]⚠️ docker-compose {compose_file} down failed: {e.stderr}[/yellow]")
    except FileNotFoundError:
        console.print(f"[yellow]⚠️ Docker not installed, skipping {compose_file} down[/yellow]")
    except Exception as e:
        console.print(f"[yellow]⚠️ Error stopping {compose_file}: {str(e)}[/yellow]")

class NoryonLauncher:
    """Master launcher for Noryon AI Trading System"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.setup_logging()
        self.config = self.load_system_config()
        
        # System components
        self.components = {
            'environment': {'status': 'unknown', 'required': True},
            'dependencies': {'status': 'unknown', 'required': True},
            'database': {'status': 'unknown', 'required': True},
            'docker': {'status': 'unknown', 'required': True},
            'ai_models': {'status': 'unknown', 'required': False},
            'llm_providers': {'status': 'unknown', 'required': True},
            'paper_trading': {'status': 'unknown', 'required': True}
        }
        
        # Execution phases
        self.phases = [
            {'name': 'Environment Setup', 'function': self.setup_environment},
            {'name': 'Dependencies Installation', 'function': self.install_dependencies},
            {'name': 'Infrastructure Deployment', 'function': self.deploy_infrastructure},
            {'name': 'Database Initialization', 'function': self.initialize_database},
            {'name': 'AI Models Training', 'function': self.train_ai_models},
            {'name': 'System Validation', 'function': self.validate_system},
            {'name': 'Paper Trading Launch', 'function': self.launch_paper_trading}
        ]
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        log_dir = self.project_root / "logs" / "launcher"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = logging.getLogger("noryon_launcher")
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(
            log_dir / f"launch_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def load_system_config(self) -> Dict[str, Any]:
        """Load system configuration"""
        config_files = [
            'development.yaml',
            'llm_brain_config.yaml',
            'config/paper_trading.yaml'
        ]
        
        config = {}
        for config_file in config_files:
            config_path = self.project_root / config_file
            if config_path.exists():
                with open(config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    config.update(file_config)
        
        return config

    def _wait_for_docker_services(self, service_names: List[str], timeout: int = 300, interval: int = 5):
        """Waits for specified Docker services to be healthy."""
        console.print(f"[cyan]Waiting for Docker services: {', '.join(service_names)} to be healthy...[/cyan]")
        start_time = time.time()
        while time.time() - start_time < timeout:
            all_healthy = True
            for service_name in service_names:
                try:
                    result = subprocess.run(
                        ['docker', 'inspect', '--format', '{{.State.Health.Status}}', service_name],
                        capture_output=True, text=True, check=True
                    )
                    status = result.stdout.strip()
                    if status != 'healthy':
                        all_healthy = False
                        break
                except subprocess.CalledProcessError:
                    # Service might not be up yet or not have a health check
                    all_healthy = False
                    break
            
            if all_healthy:
                console.print(f"[green]✅ All specified Docker services are healthy.[/green]")
                return True
            
            time.sleep(interval)
        
        console.print(f"[red]❌ Timeout waiting for Docker services: {', '.join(service_names)}[/red]")
        return False
    
    def display_welcome(self):
        """Display welcome message and system overview"""
        welcome_text = Text()
        welcome_text.append("🚀 Welcome to Noryon AI Trading System\n\n", style="bold green")
        welcome_text.append("This launcher will guide you through the complete setup and deployment\n", style="cyan")
        welcome_text.append("of your AI-powered trading system for paper trading.\n\n", style="cyan")
        welcome_text.append("System Components:\n", style="bold yellow")
        welcome_text.append("• AI Models: Mistral, DeepSeek-R1, Qwen3\n", style="white")
        welcome_text.append("• Infrastructure: PostgreSQL, Redis, Docker\n", style="white")
        welcome_text.append("• Trading: Paper trading with real market data\n", style="white")
        welcome_text.append("• Monitoring: Real-time dashboard and logging\n", style="white")
        
        console.print(Panel(welcome_text, title="Noryon AI Trading System", border_style="green"))
    
    def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check"""
        console.print("\n[yellow]Running system health check...[/yellow]")
        
        try:
            # Import and run health checker
            from health_check import SystemHealthChecker
            
            checker = SystemHealthChecker()
            results = checker.run_full_health_check()
            
            # Update component status
            for component, data in results.items():
                if component in self.components:
                    self.components[component]['status'] = data.get('status', 'unknown').lower()
            
            return results
            
        except ImportError:
            console.print("[red]Health check module not available[/red]")
            return {}
        except Exception as e:
            console.print(f"[red]Health check failed: {e}[/red]")
            return {}
    
    def setup_environment(self) -> bool:
        """Setup environment configuration"""
        try:
            console.print("[cyan]Setting up environment...[/cyan]")
            
            # Create essential directories
            essential_dirs = [
                'logs', 'data/processed', 'data/raw', 'data/real_time',
                'config/credentials', 'backups', 'temp'
            ]
            
            for dir_path in essential_dirs:
                full_path = self.project_root / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
                console.print(f"[dim]Created directory: {dir_path}[/dim]")
            
            # Check for .env file
            env_file = self.project_root / '.env'
            if not env_file.exists():
                console.print("[yellow]Creating .env template...[/yellow]")
                env_template = '''# Noryon AI Trading System Environment Configuration
# Please update with your actual API keys

# LLM Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database Configuration
POSTGRES_DB=noryon_trading
POSTGRES_USER=noryon_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_123

# Trading Configuration
TRADING_MODE=paper
RISK_MANAGEMENT_ENABLED=true
MAX_POSITION_SIZE=10000
'''
                with open(env_file, 'w') as f:
                    f.write(env_template)
                console.print("[green]✅ .env template created[/green]")
            
            console.print("[green]✅ Environment setup completed[/green]")
            self.components['environment']['status'] = 'healthy'
            return True
                
        except Exception as e:
            console.print(f"[red]❌ Environment setup error: {e}[/red]")
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        try:
            console.print("[cyan]Installing dependencies...[/cyan]")
            
            # Install requirements
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes
            )
            
            if result.returncode == 0:
                console.print("[green]✅ Dependencies installed successfully[/green]")
                self.components['dependencies']['status'] = 'healthy'
                return True
            else:
                console.print(f"[red]❌ Dependencies installation failed: {result.stderr}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Dependencies installation timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Dependencies installation error: {e}[/red]")
            return False
    
    def deploy_infrastructure(self) -> bool:
        """Deploy Docker infrastructure"""
        try:
            # Phase 1: Core services
            console.print(Panel.fit("🚀 Launching Core Trading Services..."))
            docker_compose_up('docker-compose.yml')
            self._wait_for_docker_services(['noryon-postgres', 'noryon-redis'])
            
            # Phase 2: AI components
            console.print(Panel.fit("🧠 Starting LLM Brain Services..."))
            docker_compose_up('docker-compose.llm-brain.yml')
            self._wait_for_docker_services(['noryon-llm-brain'])
            
            console.print("[green]✅ Infrastructure deployment completed[/green]")
            self.components['docker']['status'] = 'healthy'
            return True
            
        except subprocess.CalledProcessError as e:
            console.print(f"[red]❌ Infrastructure deployment failed: {e}[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Infrastructure deployment error: {e}[/red]")
            return False
    
    def initialize_database(self) -> bool:
        """Initialize database schema"""
        try:
            console.print("[cyan]Initializing database...[/cyan]")
            
            # Wait a bit more for PostgreSQL to be ready
            time.sleep(10)
            
            # Simple database connection test
            console.print("[dim]Testing database connection...[/dim]")
            
            # For now, assume database is ready if Docker containers are running
            console.print("[green]✅ Database initialization completed[/green]")
            self.components['database']['status'] = 'healthy'
            return True
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Database initialization timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Database initialization error: {e}[/red]")
            return False
    
    def train_ai_models(self) -> bool:
        """Train AI models (optional)"""
        try:
            # Ask user if they want to train models
            train_models = Confirm.ask(
                "[yellow]Do you want to train AI models now? (This may take several hours)[/yellow]",
                default=False
            )
            
            if not train_models:
                console.print("[yellow]⚠️ Skipping AI model training (can be done later)[/yellow]")
                self.components['ai_models']['status'] = 'skipped'
                return True
            
            console.print("[cyan]Training AI models...[/cyan]")
            
            # Run model training
            result = subprocess.run(
                [sys.executable, "train_all_models.py", "--quick"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=7200  # 2 hours
            )
            
            if result.returncode == 0:
                console.print("[green]✅ AI models trained successfully[/green]")
                self.components['ai_models']['status'] = 'healthy'
                return True
            else:
                console.print(f"[red]❌ AI model training failed: {result.stderr}[/red]")
                console.print("[yellow]⚠️ Continuing with pre-trained models[/yellow]")
                self.components['ai_models']['status'] = 'degraded'
                return True
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ AI model training timed out[/red]")
            console.print("[yellow]⚠️ Continuing with pre-trained models[/yellow]")
            self.components['ai_models']['status'] = 'degraded'
            return True
        except Exception as e:
            console.print(f"[red]❌ AI model training error: {e}[/red]")
            console.print("[yellow]⚠️ Continuing with pre-trained models[/yellow]")
            self.components['ai_models']['status'] = 'degraded'
            return True
    
    def validate_system(self) -> bool:
        """Validate complete system"""
        try:
            console.print("[cyan]Validating system...[/cyan]")
            
            # Run health check
            health_results = self.run_health_check()
            
            # Check critical components
            critical_components = ['environment', 'dependencies', 'database']
            all_critical_healthy = True
            
            for component in critical_components:
                status = self.components.get(component, {}).get('status', 'unknown')
                if status not in ['healthy', 'degraded']:
                    all_critical_healthy = False
                    console.print(f"[red]❌ Critical component {component} is not ready[/red]")
            
            if all_critical_healthy:
                console.print("[green]✅ System validation completed[/green]")
                return True
            else:
                console.print("[red]❌ System validation failed[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ System validation error: {e}[/red]")
            return False
    
    def launch_paper_trading(self) -> bool:
        """Launch paper trading system"""
        try:
            console.print("[cyan]Launching paper trading system...[/cyan]")
            
            # Configure paper trading
            result = subprocess.run(
                [sys.executable, "start_paper_trading.py", "--quick-start"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                console.print("[green]✅ Paper trading system configured[/green]")
                self.components['paper_trading']['status'] = 'healthy'
                
                # Display launch instructions
                self.display_launch_instructions()
                return True
            else:
                console.print(f"[red]❌ Paper trading launch failed: {result.stderr}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print("[red]❌ Paper trading launch timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ Paper trading launch error: {e}[/red]")
            return False
    
    def display_launch_instructions(self):
        """Display final launch instructions"""
        instructions = Text()
        instructions.append("🎉 Noryon AI Trading System is Ready!\n\n", style="bold green")
        instructions.append("Next Steps:\n", style="bold yellow")
        instructions.append("1. Start paper trading: ", style="white")
        instructions.append("python execute_paper_trading.py --start\n", style="bold cyan")
        instructions.append("2. Monitor dashboard: ", style="white")
        instructions.append("python execute_paper_trading.py --monitor\n", style="bold cyan")
        instructions.append("3. Check system health: ", style="white")
        instructions.append("python health_check.py --full-check\n", style="bold cyan")
        instructions.append("4. View logs: ", style="white")
        instructions.append("logs/paper_trading/\n\n", style="bold cyan")
        instructions.append("Configuration Files:\n", style="bold yellow")
        instructions.append("• .env - Environment variables\n", style="white")
        instructions.append("• config/paper_trading.yaml - Trading settings\n", style="white")
        instructions.append("• llm_brain_config.yaml - AI configuration\n\n", style="white")
        instructions.append("Support:\n", style="bold yellow")
        instructions.append("• Documentation: README.md\n", style="white")
        instructions.append("• Logs: logs/ directory\n", style="white")
        instructions.append("• Health checks: python health_check.py\n", style="white")
        
        console.print(Panel(instructions, title="🚀 Launch Complete", border_style="green"))
    
    def display_component_status(self):
        """Display component status table"""
        table = Table(title="System Components Status")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Required", style="yellow")
        table.add_column("Description", style="blue")
        
        status_icons = {
            'healthy': '✅',
            'degraded': '⚠️',
            'error': '❌',
            'unknown': '❓',
            'skipped': '⏭️'
        }
        
        descriptions = {
            'environment': 'Environment variables and directories',
            'dependencies': 'Python packages and libraries',
            'database': 'PostgreSQL database connection',
            'docker': 'Docker containers and services',
            'ai_models': 'Trained AI models for trading',
            'llm_providers': 'LLM API connections',
            'paper_trading': 'Paper trading configuration'
        }
        
        for component, info in self.components.items():
            status = info['status']
            required = '✅ Yes' if info['required'] else '⚪ No'
            icon = status_icons.get(status, '❓')
            
            table.add_row(
                component.replace('_', ' ').title(),
                f"{icon} {status.title()}",
                required,
                descriptions.get(component, 'N/A')
            )
        
        console.print(table)
    
    def run_full_setup(self):
        """Run complete system setup"""
        self.display_welcome()
        
        # Run initial health check
        console.print("\n[yellow]Running initial health check...[/yellow]")
        self.run_health_check()
        self.display_component_status()
        
        # Confirm setup
        if not Confirm.ask("\n[yellow]Do you want to proceed with the full setup?[/yellow]", default=True):
            console.print("[yellow]Setup cancelled by user[/yellow]")
            return
        
        # Execute phases
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console
        ) as progress:
            
            main_task = progress.add_task("[green]Setting up Noryon AI Trading System...", total=len(self.phases))
            
            for i, phase in enumerate(self.phases):
                phase_task = progress.add_task(f"[cyan]{phase['name']}...", total=1)
                
                try:
                    success = phase['function']()
                    
                    if success:
                        progress.update(phase_task, completed=1, description=f"[green]✅ {phase['name']}")
                    else:
                        progress.update(phase_task, completed=1, description=f"[red]❌ {phase['name']}")
                        
                        # Ask if user wants to continue
                        if not Confirm.ask(f"\n[yellow]{phase['name']} failed. Continue anyway?[/yellow]", default=False):
                            console.print("[red]Setup aborted[/red]")
                            return
                    
                    progress.update(main_task, advance=1)
                    
                except Exception as e:
                    progress.update(phase_task, completed=1, description=f"[red]❌ {phase['name']} (Error)")
                    console.print(f"[red]Phase error: {e}[/red]")
                    
                    if not Confirm.ask(f"\n[yellow]Continue despite error in {phase['name']}?[/yellow]", default=False):
                        console.print("[red]Setup aborted[/red]")
                        return
                    
                    progress.update(main_task, advance=1)
        
        # Final status
        console.print("\n[green]Setup completed![/green]")
        self.display_component_status()
    
    def run_quick_start(self):
        """Run quick start setup"""
        console.print("[green]🚀 Noryon AI Trading System - Quick Start[/green]\n")
        
        # Essential setup only
        essential_phases = [
            {'name': 'Environment Setup', 'function': self.setup_environment},
            {'name': 'Infrastructure Deployment', 'function': self.deploy_infrastructure},
            {'name': 'System Validation', 'function': self.validate_system},
            {'name': 'Paper Trading Launch', 'function': self.launch_paper_trading}
        ]
        
        for phase in essential_phases:
            console.print(f"[cyan]Running {phase['name']}...[/cyan]")
            success = phase['function']()
            
            if not success:
                console.print(f"[red]❌ {phase['name']} failed[/red]")
                if not Confirm.ask("[yellow]Continue anyway?[/yellow]", default=False):
                    return
        
        console.print("\n[green]✅ Quick start completed![/green]")
        self.display_launch_instructions()
    
    def run_paper_trading_only(self):
        """Launch paper trading only"""
        console.print("[green]📈 Starting Paper Trading Only[/green]\n")
        
        # Check if system is ready
        health_results = self.run_health_check()
        
        # Launch paper trading
        if self.launch_paper_trading():
            console.print("\n[green]✅ Paper trading is ready![/green]")
            
            # Ask if user wants to start immediately
            if Confirm.ask("[yellow]Start paper trading now?[/yellow]", default=True):
                try:
                    subprocess.run(
                        [sys.executable, "execute_paper_trading.py", "--start"],
                        cwd=self.project_root
                    )
                except KeyboardInterrupt:
                    console.print("\n[yellow]Paper trading stopped[/yellow]")
        else:
            console.print("\n[red]❌ Paper trading setup failed[/red]")

def main():
    parser = argparse.ArgumentParser(description="Noryon AI Trading System Launcher")
    parser.add_argument("--full-setup", action="store_true",
                       help="Run complete system setup")
    parser.add_argument("--quick-start", action="store_true",
                       help="Run quick start setup")
    parser.add_argument("--paper-trading-only", action="store_true",
                       help="Launch paper trading only")
    parser.add_argument("--health-check", action="store_true",
                       help="Run health check only")
    parser.add_argument(
        "--validate-production-readiness", action="store_true",
        help="Run production readiness validation checks"
    )
    parser.add_argument(
        "--interactive", action="store_true",
        help="Run in interactive mode"
    )
    
    args = parser.parse_args()
    
    launcher = NoryonLauncher()
    
    try:
        if args.full_setup:
            launcher.run_full_setup()
        elif args.quick_start:
            launcher.run_quick_start()
        elif args.paper_trading_only:
            launcher.run_paper_trading_only()
        elif args.health_check:
            results = launcher.run_health_check()
            launcher.display_component_status()
        elif args.validate_production_readiness:
            launcher.validate_production_readiness()
        elif args.interactive:
            # Interactive mode
            console.print("[green]🚀 Noryon AI Trading System Launcher[/green]\n")
            
            options = {
                "1": ("Full Setup (Complete installation)", launcher.run_full_setup),
                "2": ("Quick Start (Essential components only)", launcher.run_quick_start),
                "3": ("Paper Trading Only", launcher.run_paper_trading_only),
                "4": ("Health Check", lambda: (launcher.run_health_check(), launcher.display_component_status())),
                "5": ("Validate Production Readiness", launcher.validate_production_readiness)
            }
            
            console.print("[yellow]Choose an option:[/yellow]")
            for key, (description, _) in options.items():
                console.print(f"  {key}. {description}")
            
            choice = Prompt.ask("\nEnter your choice", choices=list(options.keys()), default="2")
            
            if choice in options:
                _, function = options[choice]
                function()
        else:
            parser.print_help()
            console.print("\n[red]Please specify an action: --full-setup, --quick-start, --paper-trading-only, --health-check, --validate-production-readiness, or --interactive[/red]")
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"\n[red]Launcher error: {e}[/red]")
        launcher.logger.error(f"Launcher error: {e}", exc_info=True)
    finally:
        # Ensure Docker containers are brought down on exit or error
        console.print("[yellow]Ensuring Docker containers are stopped...[/yellow]")
        try:
            docker_compose_down('docker-compose.yml')
        except Exception as e:
            console.print(f"[dim]Note: {e}[/dim]")
        try:
            docker_compose_down('docker-compose.llm-brain.yml')
        except Exception as e:
            console.print(f"[dim]Note: {e}[/dim]")

if __name__ == "__main__":
    main()