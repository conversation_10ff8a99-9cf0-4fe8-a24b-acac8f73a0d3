#!/usr/bin/env python3
"""
Real-time Training Status Report
Monitor all active training and system processes
"""

import subprocess
import time
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
import psutil

console = Console()

def get_training_status():
    """Check training process status"""
    try:
        # Check if training process is running
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            cmdline = ' '.join(proc.info['cmdline'] or [])
            if 'train_all_models.py' in cmdline:
                return {
                    'status': 'RUNNING',
                    'pid': proc.info['pid'],
                    'memory': proc.memory_info().rss / 1024 / 1024,  # MB
                    'cpu': proc.cpu_percent()
                }
    except:
        pass
    return {'status': 'NOT_RUNNING'}

def get_model_files_status():
    """Check trained model files"""
    models_dir = Path("models")
    if not models_dir.exists():
        return {}
    
    model_status = {}
    for model_dir in models_dir.iterdir():
        if model_dir.is_dir():
            has_model = any(model_dir.glob("*.bin")) or any(model_dir.glob("*.safetensors"))
            has_config = (model_dir / "config.json").exists()
            has_tokenizer = (model_dir / "tokenizer.json").exists() or (model_dir / "tokenizer_config.json").exists()
            
            model_status[model_dir.name] = {
                'model_files': has_model,
                'config': has_config,
                'tokenizer': has_tokenizer,
                'complete': has_model and has_config and has_tokenizer
            }
    
    return model_status

def get_system_resources():
    """Get system resource usage"""
    return {
        'cpu_percent': psutil.cpu_percent(interval=1),
        'memory_percent': psutil.virtual_memory().percent,
        'disk_usage': psutil.disk_usage('.').percent,
        'gpu_available': check_gpu_status()
    }

def check_gpu_status():
    """Check GPU availability"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return f"{result.stdout.strip()}% GPU"
    except:
        pass
    return "No GPU"

def create_status_layout():
    """Create the status dashboard layout"""
    layout = Layout()
    
    # Training Status
    training_status = get_training_status()
    training_table = Table(title="🚀 Training Status")
    training_table.add_column("Metric", style="cyan")
    training_table.add_column("Value", style="green")
    
    if training_status['status'] == 'RUNNING':
        training_table.add_row("Status", "✅ RUNNING")
        training_table.add_row("PID", str(training_status['pid']))
        training_table.add_row("Memory", f"{training_status['memory']:.1f} MB")
        training_table.add_row("CPU", f"{training_status.get('cpu', 0):.1f}%")
    else:
        training_table.add_row("Status", "❌ NOT RUNNING")
    
    # Model Status
    model_status = get_model_files_status()
    model_table = Table(title="🤖 Model Files Status")
    model_table.add_column("Model", style="cyan")
    model_table.add_column("Status", style="green")
    model_table.add_column("Files", style="yellow")
    
    for model_name, status in model_status.items():
        status_icon = "✅" if status['complete'] else "⚠️"
        files_info = f"M:{status['model_files']} C:{status['config']} T:{status['tokenizer']}"
        model_table.add_row(model_name, status_icon, files_info)
    
    # System Resources
    resources = get_system_resources()
    resource_table = Table(title="💻 System Resources")
    resource_table.add_column("Resource", style="cyan")
    resource_table.add_column("Usage", style="green")
    
    resource_table.add_row("CPU", f"{resources['cpu_percent']:.1f}%")
    resource_table.add_row("Memory", f"{resources['memory_percent']:.1f}%")
    resource_table.add_row("Disk", f"{resources['disk_usage']:.1f}%")
    resource_table.add_row("GPU", resources['gpu_available'])
    
    # Combine tables
    layout.split_column(
        Layout(training_table, name="training"),
        Layout(model_table, name="models"),
        Layout(resource_table, name="resources")
    )
    
    return layout

def main():
    """Main status monitoring loop"""
    console.print(Panel(
        "[bold blue]🔍 Noryon AI Training Status Monitor[/bold blue]\n\n"
        "Real-time monitoring of training progress and system status\n"
        "Press Ctrl+C to exit",
        title="Training Monitor"
    ))
    
    try:
        with Live(create_status_layout(), refresh_per_second=2) as live:
            while True:
                live.update(create_status_layout())
                time.sleep(5)
    except KeyboardInterrupt:
        console.print("\n[yellow]Monitoring stopped[/yellow]")
    
    # Final status summary
    training_status = get_training_status()
    model_status = get_model_files_status()
    
    console.print(Panel(
        f"[bold green]📊 Final Status Summary[/bold green]\n\n"
        f"Training: {training_status['status']}\n"
        f"Models Ready: {sum(1 for s in model_status.values() if s['complete'])}/{len(model_status)}\n"
        f"Next Steps:\n"
        f"• Continue monitoring training progress\n"
        f"• Test completed models\n"
        f"• Start paper trading when ready",
        title="Summary"
    ))

if __name__ == "__main__":
    main()
