import asyncio
from core.ai.phase2_integration_system import Phase2IntegrationSystem

async def test_components():
    output = []
    
    try:
        p = Phase2IntegrationSystem()
        output.append("Initializing Phase2IntegrationSystem...")
        await p.initialize()
        output.append("System initialized successfully")
        
        # Get system status
        system_status = p.get_system_status()
        output.append(f"System state: {system_status['system_state']}")
        output.append(f"Emergency stop: {system_status['emergency_stop']}")
        
        # Check components
        components = system_status.get('components', {})
        output.append(f"\nTotal components: {len(components)}")
        
        working_count = 0
        for component_name, status in components.items():
            output.append(f"Component {component_name}:")
            output.append(f"  Active: {status.get('is_active', False)}")
            output.append(f"  Healthy: {status.get('is_healthy', False)}")
            
            # Check if component is working (active and healthy)
            if status.get('is_active', False) and status.get('is_healthy', False):
                working_count += 1
                output.append(f"  Status: WORKING")
            else:
                output.append(f"  Status: NOT WORKING")
            output.append("")
        
        output.append(f"Total working components: {working_count}")
        output.append(f"Success criteria (>= 2 working): {'PASS' if working_count >= 2 else 'FAIL'}")
        
        await p.stop()
        output.append("System stopped successfully")
        
    except Exception as e:
        output.append(f"Error: {e}")
        import traceback
        output.append(traceback.format_exc())
    
    # Write output to file
    with open('d:\\noryon\\test_output.txt', 'w') as f:
        for line in output:
            f.write(line + '\n')
            print(line)

if __name__ == '__main__':
    asyncio.run(test_components())