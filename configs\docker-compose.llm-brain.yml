# Noryon LLM-Brain Trading System - Docker Compose Configuration
# Complete deployment setup for the LLM-driven trading system

version: '3.8'

services:
  # Main LLM Brain Application
  noryon-llm-brain:
    build:
      context: .
      dockerfile: Dockerfile.llm-brain
    container_name: noryon-llm-brain
    restart: unless-stopped
    ports:
      - "8080:8080"  # Main API
      - "9090:9090"  # Metrics endpoint
    environment:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
      - ELASTICSEARCH_HOST=elasticsearch
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./models:/app/models  # For local LLM models
    depends_on:
      - postgres
      - redis
      - elasticsearch
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          memory: 4G

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: noryon-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: noryon_llm_brain
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init:/docker-entrypoint-initdb.d
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d noryon_llm_brain"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: noryon-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Elasticsearch for Logging
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: noryon-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Kibana for Log Visualization
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: noryon-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      - elasticsearch
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: noryon-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Grafana for Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: noryon-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Apache Kafka for Event Streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    container_name: noryon-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - noryon-network

  kafka:
    image: confluentinc/cp-kafka:latest
    container_name: noryon-kafka
    restart: unless-stopped
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    volumes:
      - kafka_data:/var/lib/kafka/data
    depends_on:
      - zookeeper
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Market Data Collector Service
  market-data-collector:
    build:
      context: .
      dockerfile: Dockerfile.market-data
    container_name: noryon-market-data
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - KAFKA_HOST=kafka:9092
    env_file:
      - .env
    depends_on:
      - redis
      - kafka
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Sentiment Analysis Service
  sentiment-analyzer:
    build:
      context: .
      dockerfile: Dockerfile.sentiment
    container_name: noryon-sentiment
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - KAFKA_HOST=kafka:9092
    env_file:
      - .env
    depends_on:
      - redis
      - kafka
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Risk Management Service
  risk-manager:
    build:
      context: .
      dockerfile: Dockerfile.risk
    container_name: noryon-risk-manager
    restart: unless-stopped
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Portfolio Manager Service
  portfolio-manager:
    build:
      context: .
      dockerfile: Dockerfile.portfolio
    container_name: noryon-portfolio
    restart: unless-stopped
    environment:
      - POSTGRES_HOST=postgres
      - REDIS_HOST=redis
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Order Execution Service
  order-executor:
    build:
      context: .
      dockerfile: Dockerfile.executor
    container_name: noryon-executor
    restart: unless-stopped
    environment:
      - REDIS_HOST=redis
      - KAFKA_HOST=kafka:9092
    env_file:
      - .env
    depends_on:
      - redis
      - kafka
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Web Dashboard
  web-dashboard:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: noryon-dashboard
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_WS_URL=ws://localhost:8080
    depends_on:
      - noryon-llm-brain
    networks:
      - noryon-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: noryon-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./config/nginx/ssl:/etc/nginx/ssl
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - noryon-llm-brain
      - web-dashboard
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backup Service
  backup-service:
    build:
      context: .
      dockerfile: Dockerfile.backup
    container_name: noryon-backup
    restart: unless-stopped
    environment:
      - POSTGRES_HOST=postgres
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - BACKUP_RETENTION_DAYS=30
    env_file:
      - .env
    volumes:
      - ./backups:/app/backups
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - postgres
    networks:
      - noryon-network

# Named Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local

# Networks
networks:
  noryon-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Health Check Script
# Create a health check script that can be run externally
# Usage: docker-compose -f docker-compose.llm-brain.yml exec noryon-llm-brain /app/scripts/health-check.sh