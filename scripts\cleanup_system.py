#!/usr/bin/env python3
"""
System Cleanup - Remove junk files and optimize the system
"""

import os
import shutil
import glob
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

class SystemCleaner:
    """Clean up junk files and optimize system"""
    
    def __init__(self):
        self.cleaned_files = []
        self.cleaned_size = 0
        self.errors = []
    
    def clean_temp_files(self):
        """Remove temporary files"""
        temp_patterns = [
            "*.tmp",
            "*.temp", 
            "*~",
            "*.bak",
            "*.backup",
            "__pycache__/*",
            "*.pyc",
            "*.pyo",
            ".pytest_cache/*",
            "*.log.old",
            "*.log.*"
        ]
        
        for pattern in temp_patterns:
            try:
                files = glob.glob(pattern, recursive=True)
                for file in files:
                    try:
                        size = os.path.getsize(file)
                        os.remove(file)
                        self.cleaned_files.append(file)
                        self.cleaned_size += size
                    except Exception as e:
                        self.errors.append(f"Could not remove {file}: {e}")
            except Exception as e:
                self.errors.append(f"Pattern {pattern}: {e}")
    
    def clean_cache_dirs(self):
        """Remove cache directories"""
        cache_dirs = [
            "__pycache__",
            ".pytest_cache",
            ".mypy_cache",
            "node_modules",
            ".git/objects/pack",
            ".vscode/.ropeproject"
        ]
        
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                try:
                    size = self.get_dir_size(cache_dir)
                    shutil.rmtree(cache_dir)
                    self.cleaned_files.append(cache_dir)
                    self.cleaned_size += size
                except Exception as e:
                    self.errors.append(f"Could not remove {cache_dir}: {e}")
    
    def clean_duplicate_scripts(self):
        """Remove duplicate training scripts"""
        script_patterns = [
            "train_*_simple.py",
            "train_*_finance.py", 
            "temp_*.py",
            "test_*.py.bak"
        ]
        
        for pattern in script_patterns:
            try:
                files = glob.glob(pattern)
                for file in files:
                    try:
                        size = os.path.getsize(file)
                        os.remove(file)
                        self.cleaned_files.append(file)
                        self.cleaned_size += size
                    except Exception as e:
                        self.errors.append(f"Could not remove {file}: {e}")
            except Exception as e:
                self.errors.append(f"Pattern {pattern}: {e}")
    
    def clean_empty_model_dirs(self):
        """Remove empty model directories"""
        models_dir = Path("models")
        if models_dir.exists():
            for model_dir in models_dir.iterdir():
                if model_dir.is_dir():
                    try:
                        # Check if directory is empty or contains only empty files
                        files = list(model_dir.rglob("*"))
                        if not files or all(f.stat().st_size == 0 for f in files if f.is_file()):
                            shutil.rmtree(model_dir)
                            self.cleaned_files.append(str(model_dir))
                    except Exception as e:
                        self.errors.append(f"Could not check/remove {model_dir}: {e}")
    
    def clean_old_logs(self):
        """Clean old log files"""
        logs_dir = Path("logs")
        if logs_dir.exists():
            try:
                for log_file in logs_dir.glob("*.log"):
                    if log_file.stat().st_size > 100 * 1024 * 1024:  # > 100MB
                        size = log_file.stat().st_size
                        log_file.unlink()
                        self.cleaned_files.append(str(log_file))
                        self.cleaned_size += size
            except Exception as e:
                self.errors.append(f"Could not clean logs: {e}")
    
    def optimize_git_repo(self):
        """Optimize git repository"""
        try:
            if Path(".git").exists():
                # Git garbage collection
                os.system("git gc --aggressive --prune=now")
                console.print("[green]✅ Git repository optimized[/green]")
        except Exception as e:
            self.errors.append(f"Git optimization failed: {e}")
    
    def get_dir_size(self, path):
        """Get directory size"""
        total = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return total
    
    def format_size(self, size_bytes):
        """Format size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def run_cleanup(self):
        """Run complete cleanup"""
        console.print(Panel(
            "[bold blue]🧹 System Cleanup Starting[/bold blue]\n\n"
            "Removing junk files and optimizing system...",
            title="Cleanup"
        ))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task1 = progress.add_task("Cleaning temporary files...", total=None)
            self.clean_temp_files()
            progress.update(task1, completed=1)
            
            task2 = progress.add_task("Cleaning cache directories...", total=None)
            self.clean_cache_dirs()
            progress.update(task2, completed=1)
            
            task3 = progress.add_task("Removing duplicate scripts...", total=None)
            self.clean_duplicate_scripts()
            progress.update(task3, completed=1)
            
            task4 = progress.add_task("Cleaning empty model directories...", total=None)
            self.clean_empty_model_dirs()
            progress.update(task4, completed=1)
            
            task5 = progress.add_task("Cleaning old logs...", total=None)
            self.clean_old_logs()
            progress.update(task5, completed=1)
            
            task6 = progress.add_task("Optimizing git repository...", total=None)
            self.optimize_git_repo()
            progress.update(task6, completed=1)
    
    def display_results(self):
        """Display cleanup results"""
        results_table = Table(title="🧹 Cleanup Results")
        results_table.add_column("Metric", style="cyan")
        results_table.add_column("Value", style="green")
        
        results_table.add_row("Files Removed", str(len(self.cleaned_files)))
        results_table.add_row("Space Freed", self.format_size(self.cleaned_size))
        results_table.add_row("Errors", str(len(self.errors)))
        
        console.print(results_table)
        
        if self.cleaned_files:
            console.print("\n[green]✅ Cleaned Files:[/green]")
            for file in self.cleaned_files[:10]:  # Show first 10
                console.print(f"  • {file}")
            if len(self.cleaned_files) > 10:
                console.print(f"  ... and {len(self.cleaned_files) - 10} more")
        
        if self.errors:
            console.print("\n[yellow]⚠️ Errors encountered:[/yellow]")
            for error in self.errors[:5]:  # Show first 5
                console.print(f"  • {error}")
            if len(self.errors) > 5:
                console.print(f"  ... and {len(self.errors) - 5} more")

def organize_files():
    """Organize important files"""
    console.print("\n[blue]📁 Organizing important files...[/blue]")
    
    # Create organized structure
    important_dirs = {
        "scripts": ["*.py"],
        "docs": ["*.md", "*.txt", "*.rst"],
        "configs": ["*.yaml", "*.yml", "*.json", "*.toml"],
        "data_samples": ["*.csv", "*.jsonl"]
    }
    
    organized_count = 0
    
    for dir_name, patterns in important_dirs.items():
        dir_path = Path(dir_name)
        if not dir_path.exists():
            dir_path.mkdir()
        
        for pattern in patterns:
            files = glob.glob(pattern)
            for file in files:
                if not file.startswith(dir_name + "/"):
                    try:
                        dest = dir_path / Path(file).name
                        if not dest.exists():
                            shutil.move(file, dest)
                            organized_count += 1
                    except Exception as e:
                        console.print(f"[yellow]Could not move {file}: {e}[/yellow]")
    
    if organized_count > 0:
        console.print(f"[green]✅ Organized {organized_count} files[/green]")
    else:
        console.print("[green]✅ Files already organized[/green]")

def main():
    """Main cleanup function"""
    console.print(Panel(
        "[bold blue]🧹 NORYON SYSTEM CLEANUP[/bold blue]\n\n"
        "Cleaning junk files and optimizing your AI trading system",
        title="System Cleanup"
    ))
    
    # Run cleanup
    cleaner = SystemCleaner()
    cleaner.run_cleanup()
    cleaner.display_results()
    
    # Organize files
    organize_files()
    
    # Final summary
    console.print(Panel(
        "[bold green]✅ CLEANUP COMPLETE[/bold green]\n\n"
        f"• Removed {len(cleaner.cleaned_files)} junk files\n"
        f"• Freed {cleaner.format_size(cleaner.cleaned_size)} of space\n"
        f"• Optimized git repository\n"
        f"• Organized important files\n\n"
        "[yellow]Your system is now clean and optimized![/yellow]",
        title="Cleanup Summary"
    ))
    
    # Show current directory structure
    console.print("\n[blue]📂 Current Directory Structure:[/blue]")
    important_items = []
    for item in Path(".").iterdir():
        if item.name.startswith("."):
            continue
        if item.is_dir():
            file_count = len(list(item.rglob("*")))
            important_items.append(f"📁 {item.name}/ ({file_count} items)")
        else:
            size = item.stat().st_size
            important_items.append(f"📄 {item.name} ({cleaner.format_size(size)})")
    
    for item in sorted(important_items)[:15]:  # Show top 15
        console.print(f"  {item}")

if __name__ == "__main__":
    main()
