#!/usr/bin/env python3
"""
Activate Best AI Agents
Quick activation system for your most powerful reasoning models
"""

import subprocess
import time
from datetime import datetime

class BestAgentActivator:
    """Activate and test your best AI reasoning models"""
    
    def __init__(self):
        # Your most powerful reasoning models
        self.best_agents = {
            # REASONING POWERHOUSES
            'deepseek_r1': {
                'model': 'unrestricted-deepseek-r1-14b:latest',
                'specialty': 'Deep logical reasoning and step-by-step analysis',
                'size': '9.0 GB',
                'strength': 'Complex problem solving'
            },
            'phi4_reasoning': {
                'model': 'unrestricted-phi4-reasoning-14b:latest',
                'specialty': 'Mathematical and quantitative reasoning',
                'size': '11 GB',
                'strength': 'Mathematical analysis'
            },
            'marco_o1': {
                'model': 'unrestricted-marco-o1-7b:latest',
                'specialty': 'Strategic reasoning and planning',
                'size': '4.7 GB',
                'strength': 'Strategic thinking'
            },
            'qwen3_enhanced': {
                'model': 'phase2-smart-unrestricted-qwen3-14b-latest:latest',
                'specialty': 'Comprehensive multi-factor analysis',
                'size': '9.3 GB',
                'strength': 'Comprehensive analysis'
            },
            'cogito_reasoner': {
                'model': 'unrestricted-cogito-14b:latest',
                'specialty': 'Cognitive reasoning and decision making',
                'size': '9.0 GB',
                'strength': 'Cognitive analysis'
            },
            
            # FINANCE SPECIALISTS
            'qwen_finance': {
                'model': 'unrestricted-noryon-qwen3-finance-v2-latest:latest',
                'specialty': 'Financial markets and trading analysis',
                'size': '9.3 GB',
                'strength': 'Financial analysis'
            },
            'deepseek_finance': {
                'model': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
                'specialty': 'Deep financial reasoning and risk analysis',
                'size': '9.0 GB',
                'strength': 'Financial reasoning'
            },
            'phi4_finance': {
                'model': 'unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest',
                'specialty': 'Quantitative financial modeling',
                'size': '11 GB',
                'strength': 'Quantitative finance'
            },
            
            # FAST PERFORMERS
            'exaone_fast': {
                'model': 'unrestricted-exaone-deep-7.8b:latest',
                'specialty': 'Fast reasoning and quick insights',
                'size': '4.8 GB',
                'strength': 'Rapid analysis'
            },
            'granite_structured': {
                'model': 'unrestricted-granite3.1-dense-8b:latest',
                'specialty': 'Structured analysis and clear reasoning',
                'size': '5.0 GB',
                'strength': 'Structured thinking'
            }
        }
        
        print("🚀 Best Agent Activator initialized")
        print(f"   🤖 Top agents available: {len(self.best_agents)}")
        print(f"   💾 Total model power: {sum(float(agent['size'].split()[0]) for agent in self.best_agents.values()):.1f} GB")
    
    def test_agent(self, agent_id: str, quick_test: bool = True) -> dict:
        """Test a specific agent"""
        if agent_id not in self.best_agents:
            return {'success': False, 'error': f'Unknown agent: {agent_id}'}
        
        agent = self.best_agents[agent_id]
        model_name = agent['model']
        
        print(f"\n🧠 Testing {agent_id}...")
        print(f"   Model: {model_name}")
        print(f"   Specialty: {agent['specialty']}")
        print(f"   Size: {agent['size']}")
        
        # Quick test prompt
        if quick_test:
            test_prompt = "Quick test: What is 2+2? Explain your reasoning briefly."
            timeout = 30
        else:
            test_prompt = f"""You are a specialized AI agent with expertise in {agent['specialty']}.

TASK: Analyze Bitcoin at $32,000. Should I buy, sell, or hold? Provide your reasoning.

Please provide:
1. Your step-by-step reasoning
2. Your recommendation (BUY/SELL/HOLD)
3. Your confidence level (1-10)
4. Key factors in your decision

Focus on your specialty: {agent['strength']}"""
            timeout = 60
        
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_name, test_prompt
            ], capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                return {
                    'success': True,
                    'agent_id': agent_id,
                    'model': model_name,
                    'specialty': agent['specialty'],
                    'response_time': response_time,
                    'response_length': len(response),
                    'response': response[:500] + "..." if len(response) > 500 else response
                }
            else:
                return {
                    'success': False,
                    'agent_id': agent_id,
                    'error': f"Model error: {result.stderr}",
                    'response_time': response_time
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'agent_id': agent_id,
                'error': f"Timeout after {timeout}s",
                'response_time': time.time() - start_time
            }
        except Exception as e:
            return {
                'success': False,
                'agent_id': agent_id,
                'error': f"Exception: {e}",
                'response_time': time.time() - start_time
            }
    
    def test_all_agents(self, quick_test: bool = True, max_agents: int = 5) -> dict:
        """Test multiple agents"""
        print(f"\n🧪 TESTING {'QUICK' if quick_test else 'COMPREHENSIVE'} - TOP {max_agents} AGENTS")
        print("=" * 70)
        
        results = {}
        successful_agents = []
        failed_agents = []
        
        # Test agents in order of importance
        agent_priority = [
            'deepseek_r1',      # Best reasoning
            'qwen_finance',     # Best finance
            'phi4_reasoning',   # Best math
            'marco_o1',         # Best strategy
            'deepseek_finance', # Best financial reasoning
            'qwen3_enhanced',   # Best comprehensive
            'exaone_fast',      # Fastest
            'granite_structured', # Most structured
            'cogito_reasoner',  # Best cognitive
            'phi4_finance'      # Best quant
        ]
        
        tested_count = 0
        for agent_id in agent_priority:
            if tested_count >= max_agents:
                break
                
            if agent_id in self.best_agents:
                result = self.test_agent(agent_id, quick_test)
                results[agent_id] = result
                
                if result['success']:
                    successful_agents.append(agent_id)
                    print(f"   ✅ {agent_id}: {result['response_time']:.1f}s, {result['response_length']} chars")
                else:
                    failed_agents.append(agent_id)
                    print(f"   ❌ {agent_id}: {result['error']}")
                
                tested_count += 1
        
        # Summary
        success_rate = len(successful_agents) / tested_count if tested_count > 0 else 0
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'test_type': 'quick' if quick_test else 'comprehensive',
            'agents_tested': tested_count,
            'successful_agents': len(successful_agents),
            'failed_agents': len(failed_agents),
            'success_rate': success_rate,
            'working_agents': successful_agents,
            'failed_agents': failed_agents,
            'detailed_results': results
        }
        
        print(f"\n📊 TEST SUMMARY:")
        print(f"   Agents tested: {tested_count}")
        print(f"   Success rate: {success_rate:.1%}")
        print(f"   Working agents: {successful_agents}")
        print(f"   Failed agents: {failed_agents}")
        
        return summary
    
    def activate_agent_for_task(self, task_type: str) -> list:
        """Get best agents for specific task type"""
        task_recommendations = {
            'market_analysis': ['qwen_finance', 'deepseek_finance', 'qwen3_enhanced'],
            'risk_assessment': ['deepseek_finance', 'phi4_finance', 'cogito_reasoner'],
            'mathematical_analysis': ['phi4_reasoning', 'phi4_finance', 'qwen3_enhanced'],
            'strategic_planning': ['marco_o1', 'qwen3_enhanced', 'cogito_reasoner'],
            'quick_insights': ['exaone_fast', 'granite_structured', 'marco_o1'],
            'deep_reasoning': ['deepseek_r1', 'qwen3_enhanced', 'cogito_reasoner'],
            'financial_modeling': ['phi4_finance', 'deepseek_finance', 'qwen_finance']
        }
        
        recommended = task_recommendations.get(task_type, ['deepseek_r1', 'qwen_finance', 'marco_o1'])
        
        print(f"\n🎯 BEST AGENTS FOR {task_type.upper()}:")
        for i, agent_id in enumerate(recommended, 1):
            if agent_id in self.best_agents:
                agent = self.best_agents[agent_id]
                print(f"   {i}. {agent_id}: {agent['specialty']}")
        
        return recommended
    
    def get_agent_status(self) -> dict:
        """Get status of all best agents"""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_agents': len(self.best_agents),
            'agent_details': self.best_agents,
            'categories': {
                'reasoning_powerhouses': ['deepseek_r1', 'phi4_reasoning', 'marco_o1', 'qwen3_enhanced', 'cogito_reasoner'],
                'finance_specialists': ['qwen_finance', 'deepseek_finance', 'phi4_finance'],
                'fast_performers': ['exaone_fast', 'granite_structured']
            },
            'total_model_size': f"{sum(float(agent['size'].split()[0]) for agent in self.best_agents.values()):.1f} GB"
        }

def main():
    """Test and activate your best AI agents"""
    print("🚀 ACTIVATE BEST AI AGENTS - PHASE 4")
    print("=" * 60)
    
    activator = BestAgentActivator()
    
    # Quick test of top 5 agents
    print("\n🧪 QUICK TEST OF TOP 5 AGENTS")
    results = activator.test_all_agents(quick_test=True, max_agents=5)
    
    if results['working_agents']:
        print(f"\n✅ {len(results['working_agents'])} agents are working!")
        
        # Show task recommendations
        print(f"\n🎯 TASK RECOMMENDATIONS:")
        task_types = ['market_analysis', 'risk_assessment', 'deep_reasoning', 'quick_insights']
        
        for task_type in task_types:
            recommended = activator.activate_agent_for_task(task_type)
            working_recommended = [agent for agent in recommended if agent in results['working_agents']]
            print(f"   {task_type}: {len(working_recommended)} working agents available")
        
        # Test one comprehensive analysis
        if 'deepseek_r1' in results['working_agents']:
            print(f"\n🧠 TESTING COMPREHENSIVE ANALYSIS WITH DEEPSEEK R1...")
            comprehensive_result = activator.test_agent('deepseek_r1', quick_test=False)
            
            if comprehensive_result['success']:
                print(f"   ✅ Comprehensive test successful!")
                print(f"   Response time: {comprehensive_result['response_time']:.1f}s")
                print(f"   Response length: {comprehensive_result['response_length']} characters")
                print(f"   Sample response: {comprehensive_result['response'][:200]}...")
    
    # Show agent status
    status = activator.get_agent_status()
    print(f"\n📊 AGENT STATUS:")
    print(f"   Total agents: {status['total_agents']}")
    print(f"   Model power: {status['total_model_size']}")
    print(f"   Categories: {len(status['categories'])}")
    
    print(f"\n🎉 BEST AGENTS ACTIVATED AND READY!")
    print(f"   Working agents: {len(results['working_agents'])}")
    print(f"   Success rate: {results['success_rate']:.1%}")
    print(f"   Ready for: Market analysis, Risk assessment, Deep reasoning, Strategic planning")

if __name__ == "__main__":
    main()
