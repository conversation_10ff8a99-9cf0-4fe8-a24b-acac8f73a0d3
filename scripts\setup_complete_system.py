#!/usr/bin/env python3
"""
Noryon AI Trading System - Complete Setup Script

This script automates the entire setup process for the Noryon AI Trading System,
including infrastructure, AI models, database, and paper trading configuration.

Usage:
    python setup_complete_system.py --mode [quick|full|training-only]
    python setup_complete_system.py --paper-trading-only
    python setup_complete_system.py --validate-setup
"""

import os
import sys
import json
import yaml
import argparse
import subprocess
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime
import secrets
import string
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.table import Table
from rich.live import Live

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

console = Console()

class NoryonCompleteSetup:
    """Complete setup manager for Noryon AI Trading System"""
    
    def __init__(self, mode: str = "full"):
        self.mode = mode
        self.project_root = Path(__file__).parent
        self.setup_log = []
        self.errors = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('noryon_complete_setup.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def log_step(self, step: str, status: str = "SUCCESS", details: str = ""):
        """Log setup step with status"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = {
            "timestamp": timestamp,
            "step": step,
            "status": status,
            "details": details
        }
        self.setup_log.append(log_entry)
        
        if status == "ERROR":
            self.errors.append(log_entry)
            self.logger.error(f"{step}: {details}")
        else:
            self.logger.info(f"{step}: {status}")
    
    def create_env_file(self) -> bool:
        """Create .env file with required environment variables"""
        try:
            env_template = """
# Noryon AI Trading System Environment Configuration
# Generated on {timestamp}

# =============================================================================
# LLM Provider API Keys
# =============================================================================
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
QWEN_API_KEY=your_qwen_api_key_here
GROQ_API_KEY=your_groq_api_key_here

# =============================================================================
# Broker API Keys (Paper Trading)
# =============================================================================
BINANCE_API_KEY=your_binance_testnet_api_key
BINANCE_SECRET_KEY=your_binance_testnet_secret_key
BINANCE_TESTNET=true

IB_GATEWAY_HOST=localhost
IB_GATEWAY_PORT=7497
IB_CLIENT_ID=1
IB_PAPER_TRADING=true

# =============================================================================
# Database Configuration
# =============================================================================
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=noryon_user
POSTGRES_PASSWORD={postgres_password}
POSTGRES_DB=noryon_trading
POSTGRES_URL=postgresql://noryon_user:{postgres_password}@localhost:5432/noryon_trading

# =============================================================================
# Redis Configuration
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD={redis_password}
REDIS_URL=redis://:{redis_password}@localhost:6379/0

# =============================================================================
# Elasticsearch Configuration
# =============================================================================
ELASTICSEARCH_HOST=localhost
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD={elasticsearch_password}

# =============================================================================
# Security Configuration
# =============================================================================
JWT_SECRET={jwt_secret}
ENCRYPTION_KEY={encryption_key}
API_SECRET_KEY={api_secret_key}

# =============================================================================
# System Configuration
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
TIMEZONE=UTC

# =============================================================================
# Paper Trading Configuration
# =============================================================================
PAPER_TRADING_ENABLED=true
INITIAL_BALANCE=100000
MAX_POSITION_SIZE=0.1
MAX_DAILY_TRADES=20
RISK_PER_TRADE=0.02

# =============================================================================
# Monitoring & Alerting
# =============================================================================
GRAFANA_ADMIN_PASSWORD={grafana_password}
PROMETHEUS_RETENTION=15d
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=your_slack_webhook_url

# =============================================================================
# External Data Sources
# =============================================================================
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key
FINNHUB_API_KEY=your_finnhub_key
QUANDL_API_KEY=your_quandl_key
YAHOO_FINANCE_ENABLED=true
"""
            
            # Generate secure passwords and keys
            postgres_password = self.generate_password(16)
            redis_password = self.generate_password(16)
            elasticsearch_password = self.generate_password(16)
            jwt_secret = self.generate_password(32)
            encryption_key = self.generate_password(32)
            api_secret_key = self.generate_password(32)
            grafana_password = self.generate_password(12)
            
            env_content = env_template.format(
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                postgres_password=postgres_password,
                redis_password=redis_password,
                elasticsearch_password=elasticsearch_password,
                jwt_secret=jwt_secret,
                encryption_key=encryption_key,
                api_secret_key=api_secret_key,
                grafana_password=grafana_password
            )
            
            env_path = self.project_root / ".env"
            with open(env_path, 'w') as f:
                f.write(env_content)
                
            self.log_step("Create .env file", "SUCCESS", f"Created at {env_path}")
            
            # Display important information
            console.print(Panel(
                f"[bold green]Environment file created successfully![/bold green]\n\n"
                f"[yellow]IMPORTANT:[/yellow] Please update the following in .env:\n"
                f"• LLM Provider API Keys\n"
                f"• Broker API Keys (for paper trading)\n"
                f"• External data source API keys\n\n"
                f"[cyan]Generated passwords saved to .env file[/cyan]",
                title="Environment Configuration"
            ))
            
            return True
            
        except Exception as e:
            self.log_step("Create .env file", "ERROR", str(e))
            return False
    
    def generate_password(self, length: int = 16) -> str:
        """Generate a secure random password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
    
    def setup_directories(self) -> bool:
        """Create necessary directory structure"""
        try:
            directories = [
                "logs",
                "data/historical",
                "data/real_time",
                "data/processed",
                "data/cache",
                "data/checkpoints",
                "models/mistral",
                "models/deepseek",
                "models/qwen3",
                "models/checkpoints",
                "backups",
                "temp",
                "config/brokers",
                "config/strategies",
                "config/risk",
                "config/credentials"
            ]
            
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
                
            self.log_step("Setup directories", "SUCCESS", f"Created {len(directories)} directories")
            return True
            
        except Exception as e:
            self.log_step("Setup directories", "ERROR", str(e))
            return False
    
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        try:
            # Check if requirements.txt exists
            requirements_path = self.project_root / "requirements.txt"
            if not requirements_path.exists():
                self.create_requirements_file()
            
            # Install dependencies
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                self.log_step("Install dependencies", "SUCCESS", "All dependencies installed")
                return True
            else:
                self.log_step("Install dependencies", "ERROR", result.stderr)
                return False
                
        except Exception as e:
            self.log_step("Install dependencies", "ERROR", str(e))
            return False
    
    def create_requirements_file(self):
        """Create requirements.txt with all necessary dependencies"""
        requirements = """
# Core Dependencies
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0
scikit-learn>=1.3.0

# AI/ML Dependencies
torch>=2.0.0
transformers>=4.30.0
accelerate>=0.20.0
bitsandbytes>=0.39.0
peft>=0.4.0
trl>=0.4.0
datasets>=2.12.0
evaluate>=0.4.0
mlflow>=2.4.0
optuna>=3.2.0

# Trading & Finance
ccxt>=4.0.0
yfinance>=0.2.0
alpaca-trade-api>=3.0.0
ib-insync>=0.9.0
quandl>=3.7.0
finnhub-python>=2.4.0

# Data Processing
polars>=0.18.0
pyarrow>=12.0.0
numba>=0.57.0
talib>=0.4.0

# Database
psycopg2-binary>=2.9.0
sqlalchemy>=2.0.0
redis>=4.5.0
elasticsearch>=8.8.0

# Web Framework
fastapi>=0.100.0
uvicorn>=0.22.0
starlette>=0.27.0
pydantic>=2.0.0

# Async & Concurrency
aiohttp>=3.8.0
aiofiles>=23.0.0
celery>=5.3.0

# Monitoring & Logging
prometheus-client>=0.17.0
structlog>=23.0.0
rich>=13.0.0

# Configuration
pyyaml>=6.0
python-dotenv>=1.0.0
click>=8.1.0

# Security
cryptography>=41.0.0
passlib>=1.7.0
pyjwt>=2.7.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# Development
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.4.0

# Utilities
requests>=2.31.0
httpx>=0.24.0
tenacity>=8.2.0
schedule>=1.2.0
pillow>=10.0.0
"""
        
        requirements_path = self.project_root / "requirements.txt"
        with open(requirements_path, 'w') as f:
            f.write(requirements.strip())
    
    def start_infrastructure(self) -> bool:
        """Start Docker infrastructure"""
        try:
            # Check if Docker is running
            result = subprocess.run(
                ["docker", "--version"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                self.log_step("Check Docker", "ERROR", "Docker not found or not running")
                return False
            
            # Start infrastructure
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.llm-brain.yml", "up", "-d"],
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            
            if result.returncode == 0:
                self.log_step("Start infrastructure", "SUCCESS", "All services started")
                return True
            else:
                self.log_step("Start infrastructure", "ERROR", result.stderr)
                return False
                
        except Exception as e:
            self.log_step("Start infrastructure", "ERROR", str(e))
            return False
    
    def setup_database(self) -> bool:
        """Initialize database schema and load initial data"""
        try:
            # Wait for PostgreSQL to be ready
            import time
            time.sleep(10)
            
            # Run database setup
            from core.data.data_manager import DataManager
            
            data_manager = DataManager()
            data_manager.initialize_database()
            
            self.log_step("Setup database", "SUCCESS", "Database initialized")
            return True
            
        except Exception as e:
            self.log_step("Setup database", "ERROR", str(e))
            return False
    
    def validate_setup(self) -> bool:
        """Validate the complete setup"""
        try:
            validation_results = []
            
            # Check environment file
            env_path = self.project_root / ".env"
            validation_results.append(("Environment file", env_path.exists()))
            
            # Check directories
            required_dirs = ["logs", "data", "models", "config"]
            for dir_name in required_dirs:
                dir_path = self.project_root / dir_name
                validation_results.append((f"Directory {dir_name}", dir_path.exists()))
            
            # Check Docker services
            try:
                result = subprocess.run(
                    ["docker-compose", "-f", "docker-compose.llm-brain.yml", "ps"],
                    capture_output=True,
                    text=True,
                    cwd=self.project_root
                )
                docker_running = "Up" in result.stdout
                validation_results.append(("Docker services", docker_running))
            except:
                validation_results.append(("Docker services", False))
            
            # Display validation results
            table = Table(title="Setup Validation Results")
            table.add_column("Component", style="cyan")
            table.add_column("Status", style="green")
            
            all_valid = True
            for component, status in validation_results:
                status_text = "✅ PASS" if status else "❌ FAIL"
                table.add_row(component, status_text)
                if not status:
                    all_valid = False
            
            console.print(table)
            
            if all_valid:
                self.log_step("Validate setup", "SUCCESS", "All components validated")
            else:
                self.log_step("Validate setup", "WARNING", "Some components failed validation")
            
            return all_valid
            
        except Exception as e:
            self.log_step("Validate setup", "ERROR", str(e))
            return False
    
    def run_complete_setup(self):
        """Run the complete setup process"""
        console.print(Panel(
            "[bold blue]Noryon AI Trading System - Complete Setup[/bold blue]\n\n"
            "This will set up the entire system including:\n"
            "• Environment configuration\n"
            "• Directory structure\n"
            "• Dependencies installation\n"
            "• Docker infrastructure\n"
            "• Database initialization\n"
            "• System validation",
            title="Setup Process"
        ))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            console=console
        ) as progress:
            
            # Setup steps
            steps = [
                ("Creating environment file", self.create_env_file),
                ("Setting up directories", self.setup_directories),
                ("Installing dependencies", self.install_dependencies),
                ("Starting infrastructure", self.start_infrastructure),
                ("Setting up database", self.setup_database),
                ("Validating setup", self.validate_setup)
            ]
            
            for step_name, step_func in steps:
                task = progress.add_task(step_name, total=1)
                
                try:
                    success = step_func()
                    progress.update(task, completed=1)
                    
                    if not success and step_name != "Validating setup":
                        console.print(f"[red]Failed: {step_name}[/red]")
                        break
                        
                except Exception as e:
                    console.print(f"[red]Error in {step_name}: {str(e)}[/red]")
                    break
        
        # Display setup summary
        self.display_setup_summary()
    
    def display_setup_summary(self):
        """Display setup completion summary"""
        success_count = len([log for log in self.setup_log if log["status"] == "SUCCESS"])
        error_count = len(self.errors)
        
        summary_text = f"""
[bold green]Setup completed![/bold green]

[cyan]Summary:[/cyan]
• Successful steps: {success_count}
• Errors: {error_count}
• Total steps: {len(self.setup_log)}

[yellow]Next Steps:[/yellow]
1. Update API keys in .env file
2. Run: python main.py --paper-trading
3. Access dashboard: http://localhost:8080
4. Monitor logs: tail -f logs/noryon.log

[cyan]Quick Commands:[/cyan]
• Start paper trading: python main.py --paper-trading
• Run tests: python -m pytest tests/
• Check status: python health_check.py
• Stop system: docker-compose down
"""
        
        if error_count > 0:
            summary_text += "\n[red]Errors encountered:[/red]\n"
            for error in self.errors:
                summary_text += f"• {error['step']}: {error['details']}\n"
        
        console.print(Panel(summary_text, title="Setup Complete"))
        
        # Save setup log
        log_path = self.project_root / "setup_log.json"
        with open(log_path, 'w') as f:
            json.dump(self.setup_log, f, indent=2)

def main():
    parser = argparse.ArgumentParser(description="Noryon AI Trading System Complete Setup")
    parser.add_argument("--mode", choices=["quick", "full", "training-only"], default="full",
                       help="Setup mode")
    parser.add_argument("--paper-trading-only", action="store_true",
                       help="Setup only paper trading components")
    parser.add_argument("--validate-setup", action="store_true",
                       help="Validate existing setup")
    
    args = parser.parse_args()
    
    setup = NoryonCompleteSetup(args.mode)
    
    if args.validate_setup:
        setup.validate_setup()
    elif args.paper_trading_only:
        # Minimal setup for paper trading
        setup.create_env_file()
        setup.setup_directories()
        setup.validate_setup()
    else:
        setup.run_complete_setup()

if __name__ == "__main__":
    main()