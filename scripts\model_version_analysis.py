#!/usr/bin/env python3
"""
Model Version Analysis - Regular vs Fine-tuned Models
Analyze the relationship between base models and their fine-tuned versions
"""

import subprocess
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.tree import Tree

console = Console()

def get_all_models():
    """Get all Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        models.append({
                            'name': parts[0],
                            'id': parts[1],
                            'size': parts[2],
                            'modified': ' '.join(parts[3:])
                        })
            return models
    except Exception as e:
        console.print(f"[red]Error getting models: {e}[/red]")
    return []

def analyze_model_pairs():
    """Analyze regular vs fine-tuned model pairs"""
    models = get_all_models()
    
    # Group models by base type
    model_groups = {}
    
    for model in models:
        name = model['name']
        
        # Extract base model name
        if name.startswith('noryon-'):
            # This is a fine-tuned version
            base_name = extract_base_name_from_noryon(name)
            model_type = 'fine-tuned'
        else:
            # This is likely a regular version
            base_name = name.split(':')[0]
            model_type = 'regular'
        
        if base_name not in model_groups:
            model_groups[base_name] = {
                'regular': [],
                'fine-tuned': []
            }
        
        model_groups[base_name][model_type].append(model)
    
    return model_groups

def extract_base_name_from_noryon(noryon_name):
    """Extract base model name from noryon fine-tuned name"""
    # Remove 'noryon-' prefix and extract base model
    name = noryon_name.replace('noryon-', '')
    
    if 'deepseek' in name:
        return 'deepseek-r1'
    elif 'gemma' in name:
        return 'gemma3'
    elif 'phi' in name:
        return 'phi4'
    elif 'qwen' in name:
        return 'qwen3'
    elif 'falcon' in name:
        return 'falcon3'
    elif 'granite' in name:
        return 'granite3.2-vision'
    elif 'dolphin' in name:
        return 'dolphin3'
    elif 'exaone' in name:
        return 'exaone-deep'
    elif 'marco' in name:
        return 'marco-o1'
    elif 'cogito' in name:
        return 'cogito'
    elif 'deepscaler' in name:
        return 'deepscaler'
    else:
        return name.split('-')[0]

def display_model_pairs():
    """Display regular vs fine-tuned model pairs"""
    model_groups = analyze_model_pairs()
    
    console.print(Panel(
        "[bold blue]🔍 Model Version Analysis[/bold blue]\n\n"
        "Analysis of regular models vs fine-tuned versions",
        title="Model Pairs Analysis"
    ))
    
    # Create summary table
    summary_table = Table(title="📊 Model Pairs Summary")
    summary_table.add_column("Base Model", style="cyan", width=20)
    summary_table.add_column("Regular Versions", style="green", width=15)
    summary_table.add_column("Fine-tuned Versions", style="yellow", width=15)
    summary_table.add_column("Total", style="blue", width=10)
    summary_table.add_column("Pair Status", style="magenta", width=15)
    
    total_regular = 0
    total_finetuned = 0
    complete_pairs = 0
    
    for base_name, versions in model_groups.items():
        regular_count = len(versions['regular'])
        finetuned_count = len(versions['fine-tuned'])
        total_count = regular_count + finetuned_count
        
        total_regular += regular_count
        total_finetuned += finetuned_count
        
        # Determine pair status
        if regular_count > 0 and finetuned_count > 0:
            pair_status = "✅ Complete"
            complete_pairs += 1
        elif finetuned_count > 0:
            pair_status = "🔧 Fine-tuned Only"
        elif regular_count > 0:
            pair_status = "📦 Regular Only"
        else:
            pair_status = "❌ None"
        
        summary_table.add_row(
            base_name,
            str(regular_count),
            str(finetuned_count),
            str(total_count),
            pair_status
        )
    
    console.print(summary_table)
    
    # Overall statistics
    console.print(Panel(
        f"[bold green]📈 Overall Statistics[/bold green]\n\n"
        f"Total Regular Models: {total_regular}\n"
        f"Total Fine-tuned Models: {total_finetuned}\n"
        f"Complete Pairs: {complete_pairs}\n"
        f"Total Model Families: {len(model_groups)}\n"
        f"Average Models per Family: {(total_regular + total_finetuned) / len(model_groups):.1f}",
        title="Statistics"
    ))

def display_detailed_pairs():
    """Display detailed model pairs"""
    model_groups = analyze_model_pairs()
    
    console.print("\n[bold yellow]🔗 Detailed Model Pairs[/bold yellow]")
    
    for base_name, versions in model_groups.items():
        if versions['regular'] or versions['fine-tuned']:
            # Create tree for this model family
            tree = Tree(f"[bold cyan]{base_name}[/bold cyan] Family")
            
            # Add regular versions
            if versions['regular']:
                regular_branch = tree.add("[green]📦 Regular Versions[/green]")
                for model in versions['regular']:
                    model_info = regular_branch.add(f"[cyan]{model['name']}[/cyan]")
                    model_info.add(f"Size: {model['size']}")
                    model_info.add(f"ID: {model['id']}")
            
            # Add fine-tuned versions
            if versions['fine-tuned']:
                finetuned_branch = tree.add("[yellow]🔧 Fine-tuned Versions[/yellow]")
                for model in versions['fine-tuned']:
                    model_info = finetuned_branch.add(f"[yellow]{model['name']}[/yellow]")
                    model_info.add(f"Size: {model['size']}")
                    model_info.add(f"ID: {model['id']}")
                    
                    # Analyze fine-tuning type
                    if 'finance' in model['name']:
                        model_info.add("[green]🏦 Finance Specialized[/green]")
                    if 'enhanced' in model['name']:
                        model_info.add("[blue]⚡ Enhanced Version[/blue]")
                    if 'reasoning' in model['name']:
                        model_info.add("[purple]🧠 Reasoning Focused[/purple]")
            
            console.print(tree)
            console.print()

def analyze_fine_tuning_patterns():
    """Analyze fine-tuning patterns"""
    model_groups = analyze_model_pairs()
    
    console.print("[bold yellow]🎯 Fine-tuning Patterns Analysis[/bold yellow]")
    
    patterns = {
        'finance': 0,
        'enhanced': 0,
        'reasoning': 0,
        'vision': 0,
        'deep': 0
    }
    
    specializations = []
    
    for base_name, versions in model_groups.items():
        for model in versions['fine-tuned']:
            name = model['name'].lower()
            
            # Count patterns
            if 'finance' in name:
                patterns['finance'] += 1
                specializations.append('Finance Trading')
            if 'enhanced' in name:
                patterns['enhanced'] += 1
                specializations.append('Enhanced Performance')
            if 'reasoning' in name:
                patterns['reasoning'] += 1
                specializations.append('Advanced Reasoning')
            if 'vision' in name:
                patterns['vision'] += 1
                specializations.append('Visual Analysis')
            if 'deep' in name:
                patterns['deep'] += 1
                specializations.append('Deep Learning')
    
    # Display patterns
    pattern_table = Table(title="🔍 Fine-tuning Specializations")
    pattern_table.add_column("Specialization", style="cyan")
    pattern_table.add_column("Count", style="green")
    pattern_table.add_column("Percentage", style="yellow")
    
    total_finetuned = sum(patterns.values())
    
    for pattern, count in patterns.items():
        if count > 0:
            percentage = (count / total_finetuned) * 100
            pattern_table.add_row(
                pattern.title(),
                str(count),
                f"{percentage:.1f}%"
            )
    
    console.print(pattern_table)

def display_recommendations():
    """Display recommendations based on analysis"""
    model_groups = analyze_model_pairs()
    
    # Count complete pairs
    complete_pairs = sum(1 for versions in model_groups.values() 
                        if versions['regular'] and versions['fine-tuned'])
    
    finetuned_only = sum(1 for versions in model_groups.values() 
                        if versions['fine-tuned'] and not versions['regular'])
    
    regular_only = sum(1 for versions in model_groups.values() 
                      if versions['regular'] and not versions['fine-tuned'])
    
    console.print(Panel(
        f"[bold blue]🎯 Analysis Results & Recommendations[/bold blue]\n\n"
        f"[green]✅ Complete Pairs: {complete_pairs}[/green]\n"
        f"[yellow]🔧 Fine-tuned Only: {finetuned_only}[/yellow]\n"
        f"[blue]📦 Regular Only: {regular_only}[/blue]\n\n"
        f"[bold yellow]Key Insights:[/bold yellow]\n"
        f"• You have BOTH regular and fine-tuned versions for most models\n"
        f"• Fine-tuned models are specialized for finance trading\n"
        f"• Regular models provide baseline capabilities\n"
        f"• Enhanced versions offer improved performance\n\n"
        f"[bold green]Trading Strategy:[/bold green]\n"
        f"• Use fine-tuned models for primary trading decisions\n"
        f"• Use regular models for validation and consensus\n"
        f"• Leverage specialized models for specific tasks\n"
        f"• Ensemble voting across both types for robustness",
        title="Recommendations"
    ))

def main():
    """Main analysis function"""
    console.print(Panel(
        "[bold blue]🔍 Model Version Analysis[/bold blue]\n\n"
        "Analyzing the relationship between regular models\n"
        "and their fine-tuned versions in your system.",
        title="Version Analysis"
    ))
    
    display_model_pairs()
    display_detailed_pairs()
    analyze_fine_tuning_patterns()
    display_recommendations()

if __name__ == "__main__":
    main()
