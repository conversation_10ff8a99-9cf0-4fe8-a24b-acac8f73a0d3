#!/usr/bin/env python3
"""
Advanced Training System - Phase 2.1
Train your best models with your massive finance dataset
"""

import os
import json
import subprocess
import pandas as pd
from pathlib import Path
from datetime import datetime
import shutil

class AdvancedTrainingSystem:
    def __init__(self):
        self.data_dir = Path("data")
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Your best models from Phase 1
        self.target_models = [
            "smart-unrestricted-qwen3-14b-latest",
            "unrestricted-noryon-qwen3-finance-v2-latest", 
            "unrestricted-noryon-phi-4-9b-finance-latest",
            "unrestricted-unrestricted-qwen3-14b-latest"
        ]
        
        self.training_datasets = []
        self.training_results = {}
    
    def step1_analyze_training_data(self):
        """Step 1: Analyze all your training datasets"""
        print("STEP 1: Analyzing your training data...")
        print("=" * 50)
        
        datasets = []
        
        # Scan all dataset directories
        for item in self.data_dir.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                dataset_info = self._analyze_dataset(item)
                if dataset_info:
                    datasets.append(dataset_info)
        
        # Sort by size (largest first)
        datasets.sort(key=lambda x: x['size'], reverse=True)
        
        print(f"Found {len(datasets)} training datasets:")
        print()
        
        total_samples = 0
        for i, dataset in enumerate(datasets, 1):
            print(f"{i:2d}. {dataset['name']}")
            print(f"    Size: {dataset['size']:,} samples")
            print(f"    Type: {dataset['type']}")
            print(f"    Path: {dataset['path']}")
            print()
            total_samples += dataset['size']
        
        print(f"TOTAL: {total_samples:,} training samples available")
        
        self.training_datasets = datasets
        return datasets
    
    def _analyze_dataset(self, dataset_path):
        """Analyze a single dataset"""
        try:
            # Check for dataset_dict.json
            dict_file = dataset_path / "dataset_dict.json"
            if dict_file.exists():
                with open(dict_file) as f:
                    dataset_dict = json.load(f)
                
                # Count samples
                total_samples = 0
                for split_name, split_info in dataset_dict.get('splits', {}).items():
                    total_samples += split_info.get('num_examples', 0)
                
                # Determine dataset type
                dataset_type = self._classify_dataset_type(dataset_path.name)
                
                return {
                    'name': dataset_path.name,
                    'path': str(dataset_path),
                    'size': total_samples,
                    'type': dataset_type,
                    'splits': list(dataset_dict.get('splits', {}).keys())
                }
            
            # Check for CSV files
            csv_files = list(dataset_path.glob("*.csv"))
            if csv_files:
                total_samples = 0
                for csv_file in csv_files:
                    try:
                        df = pd.read_csv(csv_file)
                        total_samples += len(df)
                    except:
                        pass
                
                if total_samples > 0:
                    return {
                        'name': dataset_path.name,
                        'path': str(dataset_path),
                        'size': total_samples,
                        'type': self._classify_dataset_type(dataset_path.name),
                        'splits': ['csv']
                    }
            
            return None
            
        except Exception as e:
            print(f"Error analyzing {dataset_path}: {e}")
            return None
    
    def _classify_dataset_type(self, name):
        """Classify dataset type based on name"""
        name_lower = name.lower()
        
        if any(x in name_lower for x in ['stock', 'equity', 'sp500']):
            return "Stock Data"
        elif any(x in name_lower for x in ['forex', 'currency']):
            return "Forex Data"
        elif any(x in name_lower for x in ['news', 'sentiment']):
            return "News/Sentiment"
        elif any(x in name_lower for x in ['finance', 'economic']):
            return "Finance General"
        elif any(x in name_lower for x in ['trading', 'candle']):
            return "Trading Data"
        elif any(x in name_lower for x in ['bond', 'etf']):
            return "Securities Data"
        else:
            return "Other"
    
    def step2_select_best_datasets(self):
        """Step 2: Select the best datasets for training"""
        print("\nSTEP 2: Selecting best datasets for training...")
        print("=" * 50)
        
        # Priority datasets (high quality, large size)
        priority_datasets = []
        
        for dataset in self.training_datasets:
            # Prioritize large, high-quality datasets
            if dataset['size'] > 10000:  # At least 10k samples
                priority_score = 0
                
                # Size bonus
                if dataset['size'] > 100000:
                    priority_score += 3
                elif dataset['size'] > 50000:
                    priority_score += 2
                else:
                    priority_score += 1
                
                # Type bonus
                if dataset['type'] in ['Finance General', 'Trading Data', 'Stock Data']:
                    priority_score += 2
                elif dataset['type'] in ['News/Sentiment', 'Forex Data']:
                    priority_score += 1
                
                # Name quality bonus
                name_lower = dataset['name'].lower()
                if any(x in name_lower for x in ['instruct', 'qa', 'finance']):
                    priority_score += 1
                
                dataset['priority_score'] = priority_score
                priority_datasets.append(dataset)
        
        # Sort by priority score
        priority_datasets.sort(key=lambda x: x['priority_score'], reverse=True)
        
        # Select top 5 datasets
        selected_datasets = priority_datasets[:5]
        
        print("Selected datasets for training:")
        for i, dataset in enumerate(selected_datasets, 1):
            print(f"{i}. {dataset['name']}")
            print(f"   Size: {dataset['size']:,} samples")
            print(f"   Type: {dataset['type']}")
            print(f"   Priority Score: {dataset['priority_score']}")
            print()
        
        return selected_datasets
    
    def step3_prepare_training_data(self, selected_datasets):
        """Step 3: Prepare training data in Ollama format"""
        print("\nSTEP 3: Preparing training data...")
        print("=" * 50)
        
        training_data = []
        
        for dataset in selected_datasets:
            print(f"Processing {dataset['name']}...")
            
            try:
                dataset_samples = self._extract_samples(dataset)
                if dataset_samples:
                    training_data.extend(dataset_samples[:5000])  # Limit to 5k per dataset
                    print(f"  ✅ Added {len(dataset_samples[:5000])} samples")
                else:
                    print(f"  ❌ No samples extracted")
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        print(f"\nTotal training samples prepared: {len(training_data)}")
        
        # Save training data
        training_file = "combined_finance_training.jsonl"
        with open(training_file, 'w') as f:
            for sample in training_data:
                f.write(json.dumps(sample) + '\n')
        
        print(f"✅ Saved training data to {training_file}")
        return training_file
    
    def _extract_samples(self, dataset):
        """Extract training samples from a dataset"""
        dataset_path = Path(dataset['path'])
        samples = []
        
        try:
            # Try to load from train split
            train_dir = dataset_path / "train"
            if train_dir.exists():
                # Look for parquet files
                parquet_files = list(train_dir.glob("*.parquet"))
                if parquet_files:
                    for pf in parquet_files[:2]:  # Limit files
                        df = pd.read_parquet(pf)
                        dataset_samples = self._convert_df_to_samples(df, dataset['type'])
                        samples.extend(dataset_samples[:1000])  # Limit per file
                
                # Look for JSON files
                json_files = list(train_dir.glob("*.json"))
                if json_files and not parquet_files:
                    for jf in json_files[:2]:
                        with open(jf) as f:
                            data = json.load(f)
                            if isinstance(data, list):
                                for item in data[:1000]:
                                    sample = self._convert_item_to_sample(item, dataset['type'])
                                    if sample:
                                        samples.append(sample)
            
            # Try CSV files if no other format worked
            if not samples:
                csv_files = list(dataset_path.glob("*.csv"))
                for csv_file in csv_files[:1]:  # Just first CSV
                    df = pd.read_csv(csv_file)
                    dataset_samples = self._convert_df_to_samples(df, dataset['type'])
                    samples.extend(dataset_samples[:1000])
            
            return samples
            
        except Exception as e:
            print(f"Error extracting from {dataset['name']}: {e}")
            return []
    
    def _convert_df_to_samples(self, df, dataset_type):
        """Convert DataFrame to training samples"""
        samples = []
        
        try:
            # Look for common column patterns
            text_cols = [col for col in df.columns if any(x in col.lower() for x in ['text', 'content', 'question', 'instruction'])]
            response_cols = [col for col in df.columns if any(x in col.lower() for x in ['response', 'answer', 'output', 'target'])]
            
            if text_cols and response_cols:
                # Q&A format
                for _, row in df.head(1000).iterrows():
                    try:
                        question = str(row[text_cols[0]])
                        answer = str(row[response_cols[0]])
                        
                        if len(question) > 10 and len(answer) > 10:
                            samples.append({
                                "prompt": question,
                                "response": answer
                            })
                    except:
                        continue
            
            elif 'text' in df.columns:
                # Text completion format
                for _, row in df.head(1000).iterrows():
                    try:
                        text = str(row['text'])
                        if len(text) > 50:
                            # Split into prompt/response
                            mid_point = len(text) // 2
                            prompt = text[:mid_point]
                            response = text[mid_point:]
                            
                            samples.append({
                                "prompt": f"Continue this financial text: {prompt}",
                                "response": response
                            })
                    except:
                        continue
            
            return samples
            
        except Exception as e:
            print(f"Error converting DataFrame: {e}")
            return []
    
    def _convert_item_to_sample(self, item, dataset_type):
        """Convert JSON item to training sample"""
        try:
            if isinstance(item, dict):
                # Look for instruction/response patterns
                if 'instruction' in item and 'output' in item:
                    return {
                        "prompt": item['instruction'],
                        "response": item['output']
                    }
                elif 'question' in item and 'answer' in item:
                    return {
                        "prompt": item['question'],
                        "response": item['answer']
                    }
                elif 'input' in item and 'output' in item:
                    return {
                        "prompt": item['input'],
                        "response": item['output']
                    }
            
            return None
            
        except:
            return None
    
    def step4_create_enhanced_models(self, training_file):
        """Step 4: Create enhanced versions of your best models"""
        print(f"\nSTEP 4: Creating enhanced models with training data...")
        print("=" * 50)
        
        enhanced_models = []
        
        for base_model in self.target_models:
            enhanced_name = f"phase2-{base_model.replace(':', '-')}"
            
            print(f"Creating enhanced version: {enhanced_name}")
            
            # Create enhanced Modelfile
            modelfile_content = f'''FROM {base_model}

# PHASE 2 ENHANCED MODEL - ADVANCED FINANCE TRAINING
SYSTEM """You are an advanced AI trading and finance expert with enhanced capabilities:

CORE EXPERTISE:
- Advanced market analysis and pattern recognition
- Risk management and portfolio optimization  
- Economic analysis and forecasting
- Trading strategy development and backtesting
- Financial modeling and valuation
- Cryptocurrency and traditional market analysis

ENHANCED CAPABILITIES:
- Process complex financial data and charts
- Provide detailed technical and fundamental analysis
- Generate actionable trading insights
- Explain complex financial concepts clearly
- Adapt strategies to market conditions
- Consider multiple timeframes and risk factors

RESPONSE STYLE:
- Be precise and data-driven
- Provide specific, actionable advice
- Show reasoning and calculations
- Consider risk factors and alternatives
- Use professional financial terminology
- Structure responses clearly with key points

Always provide comprehensive, professional-grade financial analysis."""

# OPTIMIZED PARAMETERS FOR FINANCE
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER repeat_penalty 1.05
PARAMETER num_ctx 8192
PARAMETER num_predict 2048
'''
            
            # Save Modelfile
            modelfile_path = f"Modelfile.{enhanced_name}"
            try:
                with open(modelfile_path, 'w') as f:
                    f.write(modelfile_content)
                
                # Create the enhanced model
                result = subprocess.run([
                    'ollama', 'create', enhanced_name, '-f', modelfile_path
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"  ✅ Created: {enhanced_name}")
                    enhanced_models.append(enhanced_name)
                else:
                    print(f"  ❌ Failed: {result.stderr}")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        return enhanced_models
    
    def step5_test_enhanced_models(self, enhanced_models):
        """Step 5: Test enhanced models"""
        print(f"\nSTEP 5: Testing enhanced models...")
        print("=" * 50)
        
        test_questions = [
            "Analyze the current market conditions for Bitcoin and provide a trading strategy",
            "Explain how to calculate portfolio risk using Value at Risk (VaR)",
            "What are the key indicators for identifying a market reversal?"
        ]
        
        results = {}
        
        for model in enhanced_models:
            print(f"\nTesting {model}...")
            model_results = {}
            
            for i, question in enumerate(test_questions, 1):
                print(f"  Question {i}/3...")
                
                try:
                    result = subprocess.run([
                        'ollama', 'run', model, question
                    ], capture_output=True, text=True, timeout=45)
                    
                    if result.returncode == 0:
                        response = result.stdout.strip()
                        model_results[f"q{i}"] = {
                            'success': True,
                            'length': len(response),
                            'preview': response[:200] + "..."
                        }
                        print(f"    ✅ Success - {len(response)} chars")
                    else:
                        model_results[f"q{i}"] = {'success': False}
                        print(f"    ❌ Failed")
                        
                except subprocess.TimeoutExpired:
                    model_results[f"q{i}"] = {'success': False, 'error': 'timeout'}
                    print(f"    ⏰ Timeout")
                except Exception as e:
                    model_results[f"q{i}"] = {'success': False, 'error': str(e)}
                    print(f"    ❌ Error: {e}")
            
            results[model] = model_results
        
        # Show summary
        print(f"\nTEST SUMMARY:")
        for model, model_results in results.items():
            successes = sum(1 for r in model_results.values() if r.get('success'))
            print(f"{model}: {successes}/3 tests passed")
        
        return results
    
    def run_phase2_training(self):
        """Run complete Phase 2.1 training process"""
        print("PHASE 2.1: ADVANCED MODEL TRAINING")
        print("=" * 60)
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Execute all steps
        datasets = self.step1_analyze_training_data()
        selected_datasets = self.step2_select_best_datasets()
        training_file = self.step3_prepare_training_data(selected_datasets)
        enhanced_models = self.step4_create_enhanced_models(training_file)
        test_results = self.step5_test_enhanced_models(enhanced_models)
        
        # Final summary
        print("\n" + "=" * 60)
        print("PHASE 2.1 COMPLETE!")
        print(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print(f"✅ Analyzed {len(datasets)} datasets")
        print(f"✅ Created {len(enhanced_models)} enhanced models")
        print(f"✅ Training data: {training_file}")
        print()
        print("Enhanced models created:")
        for model in enhanced_models:
            print(f"  • {model}")
        
        return {
            'enhanced_models': enhanced_models,
            'training_file': training_file,
            'test_results': test_results
        }

def main():
    trainer = AdvancedTrainingSystem()
    results = trainer.run_phase2_training()
    
    print(f"\nNext: Run 'python quick_launcher.py' to test your new enhanced models!")

if __name__ == "__main__":
    main()
