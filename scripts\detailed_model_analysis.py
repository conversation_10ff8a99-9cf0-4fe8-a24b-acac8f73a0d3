#!/usr/bin/env python3
"""
Detailed AI Model Analysis - Complete overview of all models
"""

import subprocess
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.columns import Columns

console = Console()

def get_all_ollama_models():
    """Get detailed information about all Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 4:
                        model_name = parts[0]
                        model_id = parts[1] 
                        size = parts[2]
                        modified = ' '.join(parts[3:])
                        
                        # Determine model role and capabilities
                        role, capabilities, specialization = analyze_model_name(model_name)
                        
                        models.append({
                            'name': model_name,
                            'id': model_id,
                            'size': size,
                            'modified': modified,
                            'role': role,
                            'capabilities': capabilities,
                            'specialization': specialization,
                            'type': 'Ollama',
                            'status': 'Ready'
                        })
            return models
    except Exception as e:
        console.print(f"[red]Error getting Ollama models: {e}[/red]")
    return []

def analyze_model_name(model_name):
    """Analyze model name to determine role and capabilities"""
    name_lower = model_name.lower()
    
    # Define model roles based on names
    if 'deepseek' in name_lower and 'r1' in name_lower:
        return (
            "Advanced Reasoning Engine",
            ["Complex financial analysis", "Multi-step reasoning", "Risk assessment", "Strategic planning"],
            "Deep analytical thinking"
        )
    elif 'gemma' in name_lower and '12b' in name_lower:
        return (
            "Market Analysis Specialist", 
            ["Technical analysis", "Chart patterns", "Market trends", "Price prediction"],
            "Market data interpretation"
        )
    elif 'phi' in name_lower and ('4' in name_lower or '9b' in name_lower):
        return (
            "Risk Assessment Expert",
            ["Portfolio risk analysis", "Position sizing", "Stop-loss optimization", "Volatility assessment"],
            "Risk management"
        )
    elif 'qwen' in name_lower:
        return (
            "General Financial Intelligence",
            ["Broad market knowledge", "Economic analysis", "News interpretation", "Fundamental analysis"],
            "General finance expertise"
        )
    elif 'granite' in name_lower and 'vision' in name_lower:
        return (
            "Visual Data Analyst",
            ["Chart analysis", "Visual patterns", "Graph interpretation", "Technical indicators"],
            "Visual financial analysis"
        )
    elif 'falcon' in name_lower:
        return (
            "High-Performance Trader",
            ["Fast decisions", "Real-time analysis", "Scalping strategies", "Quick reactions"],
            "Speed and efficiency"
        )
    elif 'dolphin' in name_lower:
        return (
            "Adaptive Strategy Engine",
            ["Strategy adaptation", "Market regime detection", "Dynamic optimization", "Pattern learning"],
            "Adaptability and learning"
        )
    elif 'exaone' in name_lower:
        return (
            "Deep Learning Specialist",
            ["Pattern recognition", "Deep analysis", "Complex correlations", "Advanced predictions"],
            "Deep learning patterns"
        )
    elif 'marco' in name_lower:
        return (
            "Reasoning Chain Expert",
            ["Step-by-step analysis", "Logical reasoning", "Decision justification", "Transparent thinking"],
            "Logical reasoning chains"
        )
    elif 'cogito' in name_lower:
        return (
            "Cognitive Analysis Engine",
            ["Behavioral analysis", "Market psychology", "Sentiment interpretation", "Bias detection"],
            "Market psychology"
        )
    elif 'deepscaler' in name_lower:
        return (
            "Scaling Strategy Optimizer",
            ["Position scaling", "Risk scaling", "Strategy optimization", "Performance scaling"],
            "Strategy scaling"
        )
    else:
        return (
            "Financial AI Assistant",
            ["General financial analysis", "Trading support", "Market insights"],
            "General finance"
        )

def get_trained_models():
    """Get information about trained HuggingFace models"""
    models_dir = Path("models")
    trained_models = []
    
    if models_dir.exists():
        for model_dir in models_dir.iterdir():
            if model_dir.is_dir():
                # Check for model files
                has_model = (
                    any(model_dir.glob("*.bin")) or 
                    any(model_dir.glob("*.safetensors")) or
                    any(model_dir.glob("pytorch_model.bin"))
                )
                has_config = (model_dir / "config.json").exists()
                has_tokenizer = (
                    (model_dir / "tokenizer.json").exists() or
                    (model_dir / "tokenizer_config.json").exists()
                )
                
                # Determine status
                if has_model and has_config:
                    status = "Ready"
                elif has_model or has_config:
                    status = "Incomplete"
                else:
                    status = "Empty"
                
                # Get size
                try:
                    total_size = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
                    if total_size > 1024**3:  # GB
                        size = f"{total_size / (1024**3):.1f} GB"
                    else:  # MB
                        size = f"{total_size / (1024**2):.1f} MB"
                except:
                    size = "Unknown"
                
                trained_models.append({
                    'name': model_dir.name,
                    'path': str(model_dir),
                    'has_model': has_model,
                    'has_config': has_config,
                    'has_tokenizer': has_tokenizer,
                    'status': status,
                    'size': size,
                    'type': 'HuggingFace',
                    'role': 'Custom Trained Model',
                    'capabilities': ['Financial analysis', 'Custom training'],
                    'specialization': 'Custom finance training'
                })
    
    return trained_models

def display_model_overview():
    """Display comprehensive model overview"""
    ollama_models = get_all_ollama_models()
    trained_models = get_trained_models()
    
    ready_ollama = len(ollama_models)
    ready_trained = len([m for m in trained_models if m['status'] == 'Ready'])
    total_ready = ready_ollama + ready_trained
    
    console.print(Panel(
        f"[bold blue]🤖 Complete AI Model Inventory[/bold blue]\n\n"
        f"[green]READY MODELS:[/green]\n"
        f"• Ollama Models: {ready_ollama}\n"
        f"• Trained Models: {ready_trained}\n"
        f"• Total Ready: {total_ready}\n\n"
        f"[yellow]IN PROGRESS:[/yellow]\n"
        f"• Incomplete Models: {len([m for m in trained_models if m['status'] == 'Incomplete'])}\n"
        f"• Empty Directories: {len([m for m in trained_models if m['status'] == 'Empty'])}\n\n"
        f"[blue]TOTAL MODELS: {len(ollama_models) + len(trained_models)}[/blue]",
        title="Model Inventory"
    ))

def display_ready_models():
    """Display all ready models with their roles"""
    ollama_models = get_all_ollama_models()
    trained_models = [m for m in get_trained_models() if m['status'] == 'Ready']
    
    if ollama_models:
        console.print("\n[bold green]🚀 READY OLLAMA MODELS[/bold green]")
        ollama_table = Table()
        ollama_table.add_column("Model Name", style="cyan", width=35)
        ollama_table.add_column("Role", style="green", width=25)
        ollama_table.add_column("Specialization", style="yellow", width=25)
        ollama_table.add_column("Size", style="blue", width=10)
        
        for model in ollama_models:
            ollama_table.add_row(
                model['name'][:33] + "..." if len(model['name']) > 33 else model['name'],
                model['role'][:23] + "..." if len(model['role']) > 23 else model['role'],
                model['specialization'][:23] + "..." if len(model['specialization']) > 23 else model['specialization'],
                model['size']
            )
        
        console.print(ollama_table)
    
    if trained_models:
        console.print("\n[bold green]🎓 READY TRAINED MODELS[/bold green]")
        trained_table = Table()
        trained_table.add_column("Model Name", style="cyan")
        trained_table.add_column("Status", style="green")
        trained_table.add_column("Size", style="blue")
        trained_table.add_column("Path", style="yellow")
        
        for model in trained_models:
            trained_table.add_row(
                model['name'],
                model['status'],
                model['size'],
                model['path']
            )
        
        console.print(trained_table)

def display_model_capabilities():
    """Display detailed capabilities of top models"""
    ollama_models = get_all_ollama_models()
    
    console.print("\n[bold yellow]🎯 MODEL CAPABILITIES BREAKDOWN[/bold yellow]")
    
    # Show top 6 models in detail
    top_models = ollama_models[:6]
    capability_panels = []
    
    for model in top_models:
        capabilities_text = "\n".join([f"• {cap}" for cap in model['capabilities']])
        panel = Panel(
            f"[bold]{model['role']}[/bold]\n\n"
            f"{capabilities_text}\n\n"
            f"[yellow]Specialization:[/yellow]\n{model['specialization']}\n\n"
            f"[blue]Size:[/blue] {model['size']}",
            title=model['name'][:25] + "..." if len(model['name']) > 25 else model['name'],
            width=45
        )
        capability_panels.append(panel)
    
    # Display in columns
    if len(capability_panels) >= 3:
        console.print(Columns(capability_panels[:3]))
    if len(capability_panels) >= 6:
        console.print(Columns(capability_panels[3:6]))

def display_ensemble_config():
    """Display ensemble trading configuration"""
    ollama_models = get_all_ollama_models()
    ready_count = len(ollama_models)
    
    console.print(Panel(
        f"[bold green]🗳️ ENSEMBLE TRADING SYSTEM[/bold green]\n\n"
        f"[yellow]Configuration:[/yellow]\n"
        f"• Active Models: {ready_count}\n"
        f"• Voting Strategy: Weighted consensus\n"
        f"• Confidence Threshold: 0.70\n"
        f"• Minimum Consensus: 3 models\n"
        f"• Risk Override: Enabled\n\n"
        f"[yellow]Model Roles Distribution:[/yellow]\n"
        f"• Reasoning Engines: {len([m for m in ollama_models if 'reasoning' in m['role'].lower() or 'deepseek' in m['name'].lower()])}\n"
        f"• Market Analysts: {len([m for m in ollama_models if 'market' in m['role'].lower() or 'analysis' in m['role'].lower()])}\n"
        f"• Risk Managers: {len([m for m in ollama_models if 'risk' in m['role'].lower()])}\n"
        f"• Specialists: {len([m for m in ollama_models if 'specialist' in m['role'].lower()])}\n\n"
        f"[green]✅ READY FOR ENSEMBLE TRADING[/green]",
        title="Ensemble Configuration"
    ))

def display_next_steps():
    """Display recommended next steps"""
    ollama_models = get_all_ollama_models()
    ready_count = len(ollama_models)
    
    if ready_count >= 3:
        console.print(Panel(
            f"[bold blue]🎯 RECOMMENDED ACTIONS[/bold blue]\n\n"
            f"[green]✅ YOU'RE READY FOR AI TRADING![/green]\n"
            f"With {ready_count} specialized models, you have a powerful ensemble.\n\n"
            f"[yellow]Immediate Next Steps:[/yellow]\n"
            f"1. Test ensemble system:\n"
            f"   python ensemble_voting_system.py --test-all\n\n"
            f"2. Start paper trading:\n"
            f"   python start_paper_trading.py --quick-start\n\n"
            f"3. Monitor performance:\n"
            f"   python live_dashboard.py\n\n"
            f"4. Continue training additional models in background\n\n"
            f"[blue]Your AI trading system is enterprise-ready![/blue]",
            title="Action Plan"
        ))
    else:
        console.print(Panel(
            f"[bold yellow]⚠️ TRAINING NEEDED[/bold yellow]\n\n"
            f"You have {ready_count} models ready.\n"
            f"Recommended minimum: 3 models for ensemble trading.\n\n"
            f"[yellow]Next Steps:[/yellow]\n"
            f"1. Continue model training\n"
            f"2. Check training progress\n"
            f"3. Test available models\n"
            f"4. Prepare for trading once more models are ready",
            title="Training Required"
        ))

def main():
    """Main analysis function"""
    console.print(Panel(
        "[bold blue]🔍 Detailed AI Model Analysis[/bold blue]\n\n"
        "Comprehensive analysis of all available AI models,\n"
        "their capabilities, roles, and trading readiness.",
        title="Model Analysis"
    ))
    
    display_model_overview()
    display_ready_models()
    display_model_capabilities()
    display_ensemble_config()
    display_next_steps()

if __name__ == "__main__":
    main()
