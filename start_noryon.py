#!/usr/bin/env python3
"""
Noryon AI Trading System - Easy Startup Script

This script provides a user-friendly interface to start the Noryon AI Trading System
with various modes and configurations. It handles system checks, dependency validation,
and provides guided setup for new users.

Author: Noryon AI Team
Version: 1.0.0
Date: 2025-01-02
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, List, Optional
import argparse
import logging
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from main_trading_system import NoryonTradingSystem
    from core.config.config_manager import ConfigManager
except ImportError as e:
    print(f"Error importing core modules: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)

class NoryonLauncher:
    """
    User-friendly launcher for the Noryon AI Trading System.
    
    This class provides:
    - System health checks
    - Dependency validation
    - Configuration setup assistance
    - Multiple startup modes
    - Interactive setup wizard
    """
    
    def __init__(self):
        self.project_root = project_root
        self.config_manager = ConfigManager()
        self.logger = self._setup_logging()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging for the launcher."""
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"noryon_launcher_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        return logging.getLogger(__name__)
    
    def check_system_requirements(self) -> bool:
        """
        Check if system meets requirements for running Noryon.
        
        Returns:
            bool: True if all requirements are met
        """
        print("🔍 Checking system requirements...")
        
        checks = {
            "Python version": self._check_python_version(),
            "Required directories": self._check_directories(),
            "Configuration files": self._check_config_files(),
            "Dependencies": self._check_dependencies(),
            "Environment variables": self._check_environment_variables()
        }
        
        all_passed = True
        for check_name, passed in checks.items():
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("\n✅ All system requirements met!")
        else:
            print("\n❌ Some requirements not met. Please fix the issues above.")
        
        return all_passed
    
    def _check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        return sys.version_info >= (3, 8)
    
    def _check_directories(self) -> bool:
        """Check if required directories exist."""
        required_dirs = [
            "core", "config", "data", "logs", "models",
            "core/ai", "core/strategies", "core/risk", "core/market",
            "core/integration", "core/monitoring", "core/training",
            "core/backtesting", "core/evaluation"
        ]
        
        for dir_path in required_dirs:
            if not (self.project_root / dir_path).exists():
                return False
        
        return True
    
    def _check_config_files(self) -> bool:
        """Check if essential configuration files exist."""
        config_files = [
            "config/trading_config.yaml",
            "config/ai/models.yaml",
            "config/risk/trading.yaml"
        ]
        
        missing_files = []
        for config_file in config_files:
            if not (self.project_root / config_file).exists():
                missing_files.append(config_file)
        
        if missing_files:
            print(f"    Missing config files: {missing_files}")
            return False
        
        return True
    
    def _check_dependencies(self) -> bool:
        """Check if required Python packages are installed."""
        required_packages = [
            "numpy", "pandas", "scikit-learn", "torch",
            "transformers", "anthropic", "openai", "requests",
            "aiohttp", "asyncio", "yaml", "matplotlib",
            "plotly", "dash", "mlflow", "optuna"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"    Missing packages: {missing_packages}")
            print(f"    Install with: pip install {' '.join(missing_packages)}")
            return False
        
        return True
    
    def _check_environment_variables(self) -> bool:
        """Check if required environment variables are set."""
        required_env_vars = [
            "ANTHROPIC_API_KEY",
            "DEEPSEEK_API_KEY"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"    Missing environment variables: {missing_vars}")
            print("    Please set these in your .env file or system environment")
            return False
        
        return True
    
    def setup_first_time(self):
        """Interactive setup wizard for first-time users."""
        print("\n🚀 Welcome to Noryon AI Trading System!")
        print("This appears to be your first time running Noryon.")
        print("Let's set up your system...\n")
        
        # Create default configurations
        print("📁 Creating default configuration files...")
        self.config_manager.create_default_configs()
        
        # Setup environment file
        self._setup_environment_file()
        
        # Create necessary directories
        self._create_directories()
        
        print("\n✅ Setup completed! You can now run Noryon.")
        print("\nNext steps:")
        print("1. Edit config/trading_config.yaml to customize your trading parameters")
        print("2. Set your API keys in the .env file")
        print("3. Run: python start_noryon.py --mode training (to train models)")
        print("4. Run: python start_noryon.py --mode backtesting (to test strategies)")
        print("5. Run: python start_noryon.py --mode live (for live trading)")
    
    def _setup_environment_file(self):
        """Create .env file with template values."""
        env_file = self.project_root / ".env"
        
        if not env_file.exists():
            env_template = """
# Noryon AI Trading System Environment Variables
# =============================================================================

# AI Model API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Broker API Keys (for live trading)
BROKER_API_KEY=your_broker_api_key_here
BROKER_API_SECRET=your_broker_api_secret_here

# Environment Settings
NORYON_ENV=development
LOG_LEVEL=INFO

# Database Settings (optional)
DATABASE_URL=sqlite:///noryon.db

# MLflow Tracking (optional)
MLFLOW_TRACKING_URI=http://localhost:5000
"""
            
            with open(env_file, 'w') as f:
                f.write(env_template)
            
            print(f"📝 Created .env file at {env_file}")
            print("   Please edit this file and add your API keys")
    
    def _create_directories(self):
        """Create necessary directories."""
        directories = [
            "logs", "data", "models", "data/historical",
            "data/live", "models/trained", "models/checkpoints"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
        
        print("📁 Created necessary directories")
    
    def show_system_status(self):
        """Display current system status."""
        print("\n📊 Noryon AI Trading System Status")
        print("=" * 50)
        
        # System info
        print(f"📍 Project Root: {self.project_root}")
        print(f"🐍 Python Version: {sys.version.split()[0]}")
        print(f"⏰ Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Configuration status
        print("\n⚙️  Configuration Status:")
        try:
            config = self.config_manager.load_config("trading_config.yaml")
            print(f"   Trading Mode: {'Paper' if config.get('paper_trading', True) else 'Live'}")
            print(f"   Symbols: {config.get('symbols', [])}")
            print(f"   Max Positions: {config.get('max_positions', 'N/A')}")
            print(f"   Risk per Trade: {config.get('risk_per_trade', 'N/A')}")
        except Exception as e:
            print(f"   ❌ Error loading config: {e}")
        
        # Check if models exist
        print("\n🤖 AI Models Status:")
        models_dir = self.project_root / "models" / "trained"
        if models_dir.exists():
            model_files = list(models_dir.glob("*.pkl")) + list(models_dir.glob("*.pt"))
            if model_files:
                print(f"   ✅ Found {len(model_files)} trained models")
                for model_file in model_files[:5]:  # Show first 5
                    print(f"      - {model_file.name}")
            else:
                print("   ⚠️  No trained models found. Run training first.")
        else:
            print("   ❌ Models directory not found")
        
        # Log files
        print("\n📝 Recent Log Files:")
        logs_dir = self.project_root / "logs"
        if logs_dir.exists():
            log_files = sorted(logs_dir.glob("*.log"), key=lambda x: x.stat().st_mtime, reverse=True)
            for log_file in log_files[:3]:  # Show 3 most recent
                size_mb = log_file.stat().st_size / (1024 * 1024)
                print(f"   📄 {log_file.name} ({size_mb:.1f} MB)")
        
        print("\n" + "=" * 50)
    
    def interactive_mode_selection(self) -> str:
        """Interactive mode selection for users."""
        print("\n🎯 Select Noryon Operation Mode:")
        print("1. 🎓 Training Mode - Train AI models on historical data")
        print("2. 📊 Backtesting Mode - Test strategies on historical data")
        print("3. 📈 Paper Trading Mode - Live trading with virtual money")
        print("4. 💰 Live Trading Mode - Real money trading (use with caution!)")
        print("5. 🔄 Complete Workflow - Training → Backtesting → Paper Trading")
        print("6. 📋 System Status - Show current system information")
        print("7. ⚙️  Setup Mode - Run first-time setup wizard")
        
        while True:
            try:
                choice = input("\nEnter your choice (1-7): ").strip()
                
                mode_map = {
                    '1': 'training',
                    '2': 'backtesting',
                    '3': 'paper',
                    '4': 'live',
                    '5': 'complete',
                    '6': 'status',
                    '7': 'setup'
                }
                
                if choice in mode_map:
                    return mode_map[choice]
                else:
                    print("❌ Invalid choice. Please enter 1-7.")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                sys.exit(0)
    
    async def run_mode(self, mode: str, **kwargs):
        """Run the selected mode."""
        if mode == 'status':
            self.show_system_status()
            return
        
        if mode == 'setup':
            self.setup_first_time()
            return
        
        # Check system requirements before running
        if not self.check_system_requirements():
            print("\n❌ System requirements not met. Please fix the issues above.")
            return
        
        # Create and run trading system
        config_path = kwargs.get('config', 'config/trading_config.yaml')
        trading_system = NoryonTradingSystem(config_path)
        
        try:
            if mode == 'training':
                print("\n🎓 Starting Training Mode...")
                if await trading_system.initialize():
                    await trading_system.run_training()
                    print("✅ Training completed!")
            
            elif mode == 'backtesting':
                print("\n📊 Starting Backtesting Mode...")
                symbols = kwargs.get('symbols', None)
                if await trading_system.initialize():
                    await trading_system.run_backtesting(symbols)
                    print("✅ Backtesting completed!")
            
            elif mode == 'paper':
                print("\n📈 Starting Paper Trading Mode...")
                if await trading_system.initialize():
                    # Ensure paper trading is enabled
                    config = trading_system.config_manager.load_config(config_path)
                    config['paper_trading'] = True
                    trading_system.config_manager.save_config(config, config_path)
                    
                    await trading_system.start_live_trading()
                    print("📈 Paper trading started. Press Ctrl+C to stop.")
                    await trading_system.shutdown_event.wait()
                    await trading_system.stop_trading()
            
            elif mode == 'live':
                print("\n💰 Starting Live Trading Mode...")
                print("⚠️  WARNING: This will trade with real money!")
                
                confirm = input("Are you sure you want to continue? (yes/no): ").strip().lower()
                if confirm != 'yes':
                    print("❌ Live trading cancelled.")
                    return
                
                if await trading_system.initialize():
                    # Ensure live trading is enabled
                    config = trading_system.config_manager.load_config(config_path)
                    config['paper_trading'] = False
                    trading_system.config_manager.save_config(config, config_path)
                    
                    await trading_system.start_live_trading()
                    print("💰 Live trading started. Press Ctrl+C to stop.")
                    await trading_system.shutdown_event.wait()
                    await trading_system.stop_trading()
            
            elif mode == 'complete':
                print("\n🔄 Starting Complete Workflow...")
                await trading_system.run_complete_workflow()
                print("✅ Complete workflow finished!")
        
        except KeyboardInterrupt:
            print("\n🛑 Shutdown requested by user")
            if trading_system.is_running:
                await trading_system.stop_trading()
        except Exception as e:
            self.logger.error(f"Error running mode {mode}: {e}")
            print(f"❌ Error: {e}")

def create_argument_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Noryon AI Trading System Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_noryon.py                                    # Interactive mode
  python start_noryon.py --mode training                   # Training mode
  python start_noryon.py --mode backtesting --symbols AAPL GOOGL
  python start_noryon.py --mode paper                      # Paper trading
  python start_noryon.py --mode complete                   # Full workflow
  python start_noryon.py --mode status                     # System status
        """
    )
    
    parser.add_argument(
        '--mode',
        choices=['training', 'backtesting', 'paper', 'live', 'complete', 'status', 'setup'],
        help='Operation mode. If not specified, interactive mode will be used.'
    )
    
    parser.add_argument(
        '--config',
        default='config/trading_config.yaml',
        help='Configuration file path (default: config/trading_config.yaml)'
    )
    
    parser.add_argument(
        '--symbols',
        nargs='+',
        help='Trading symbols for backtesting mode'
    )
    
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Force interactive mode even if --mode is specified'
    )
    
    parser.add_argument(
        '--skip-checks',
        action='store_true',
        help='Skip system requirement checks'
    )
    
    return parser

async def main():
    """Main entry point for the Noryon launcher."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Create launcher instance
    launcher = NoryonLauncher()
    
    # Show welcome message
    print("\n" + "=" * 60)
    print("🚀 NORYON AI TRADING SYSTEM LAUNCHER")
    print("    Advanced AI-Powered Trading Platform")
    print("=" * 60)
    
    try:
        # Determine mode
        if args.interactive or args.mode is None:
            mode = launcher.interactive_mode_selection()
        else:
            mode = args.mode
        
        # Run the selected mode
        await launcher.run_mode(
            mode,
            config=args.config,
            symbols=args.symbols,
            skip_checks=args.skip_checks
        )
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Ensure we're running in the correct directory
    os.chdir(project_root)
    
    # Run the main function
    asyncio.run(main())