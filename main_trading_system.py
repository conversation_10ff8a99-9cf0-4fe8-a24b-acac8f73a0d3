#!/usr/bin/env python3
"""
Noryon AI Trading System - Main Entry Point

This is the primary entry point for the Noryon AI Trading System that integrates
all components including AI models, trading strategies, risk management, and monitoring.

Author: Noryon AI Team
Version: 1.0.0
Date: 2025-01-02
"""

import asyncio
import logging
import signal
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional
import argparse
import yaml
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Core system imports
from core.integration.system_integrator import SystemIntegrator, SystemConfig, TradingMode
from core.monitoring.performance_dashboard import PerformanceDashboard
from core.training.training_pipeline import TrainingPipeline, TrainingConfig
from core.backtesting.backtesting_engine import BacktestingEngine, BacktestConfig
from core.evaluation.model_evaluator import ModelEvaluator

# Configuration imports
from core.config.config_manager import ConfigManager

class NoryonTradingSystem:
    """
    Main orchestrator for the Noryon AI Trading System.
    
    This class manages the entire lifecycle of the trading system including:
    - System initialization and configuration
    - Training pipeline execution
    - Live trading operations
    - Performance monitoring
    - System shutdown and cleanup
    """
    
    def __init__(self, config_path: str = "config/trading_config.yaml"):
        self.config_path = config_path
        self.config_manager = ConfigManager()
        self.system_integrator: Optional[SystemIntegrator] = None
        self.dashboard: Optional[PerformanceDashboard] = None
        self.training_pipeline: Optional[TrainingPipeline] = None
        self.backtesting_engine: Optional[BacktestingEngine] = None
        self.model_evaluator: Optional[ModelEvaluator] = None
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Setup logging
        self._setup_logging()
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Noryon AI Trading System initialized")
    
    def _setup_logging(self):
        """Configure logging for the trading system."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"noryon_trading_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            print(f"\nReceived signal {signum}. Initiating graceful shutdown...")
            self.shutdown_event.set()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def initialize(self) -> bool:
        """
        Initialize all system components.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing Noryon AI Trading System...")
            
            # Load configuration
            config = self.config_manager.load_config(self.config_path)
            
            # Initialize system integrator
            system_config = SystemConfig(
                trading_mode=TradingMode.PAPER,  # Start with paper trading
                symbols=config.get('symbols', ['AAPL', 'GOOGL', 'MSFT', 'TSLA']),
                max_positions=config.get('max_positions', 10),
                risk_per_trade=config.get('risk_per_trade', 0.01),
                confidence_threshold=config.get('confidence_threshold', 0.7),
                update_interval=config.get('update_interval', 60),
                enable_monitoring=config.get('enable_monitoring', True),
                enable_logging=config.get('enable_logging', True)
            )
            
            self.system_integrator = SystemIntegrator(system_config)
            await self.system_integrator.initialize()
            
            # Initialize performance dashboard
            self.dashboard = PerformanceDashboard()
            await self.dashboard.initialize()
            
            # Initialize training pipeline
            training_config = TrainingConfig()
            self.training_pipeline = TrainingPipeline(training_config)
            
            # Initialize backtesting engine
            backtest_config = BacktestConfig(
                start_date=datetime(2023, 1, 1),
                end_date=datetime(2024, 12, 31),
                initial_capital=100000.0,
                commission=0.001,
                slippage=0.0005
            )
            self.backtesting_engine = BacktestingEngine(backtest_config)
            
            # Initialize model evaluator
            self.model_evaluator = ModelEvaluator()
            
            self.logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize system: {e}")
            return False
    
    async def run_training(self) -> bool:
        """
        Execute the complete training pipeline.
        
        Returns:
            bool: True if training successful, False otherwise
        """
        try:
            self.logger.info("Starting training pipeline...")
            
            if not self.training_pipeline:
                raise ValueError("Training pipeline not initialized")
            
            # Run training pipeline
            training_results = await self.training_pipeline.run_training()
            
            if training_results.success:
                self.logger.info(f"Training completed successfully. Best model: {training_results.best_model}")
                
                # Evaluate trained models
                if self.model_evaluator:
                    evaluation_results = await self.model_evaluator.evaluate_all_models()
                    self.logger.info(f"Model evaluation completed: {evaluation_results}")
                
                return True
            else:
                self.logger.error(f"Training failed: {training_results.error_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Training pipeline failed: {e}")
            return False
    
    async def run_backtesting(self, symbols: Optional[List[str]] = None) -> bool:
        """
        Execute backtesting on historical data.
        
        Args:
            symbols: List of symbols to backtest. If None, uses default symbols.
            
        Returns:
            bool: True if backtesting successful, False otherwise
        """
        try:
            self.logger.info("Starting backtesting...")
            
            if not self.backtesting_engine:
                raise ValueError("Backtesting engine not initialized")
            
            if symbols is None:
                symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA']
            
            # Run backtesting
            backtest_results = await self.backtesting_engine.run_backtest(symbols)
            
            if backtest_results.success:
                self.logger.info(f"Backtesting completed. Total return: {backtest_results.total_return:.2%}")
                self.logger.info(f"Sharpe ratio: {backtest_results.sharpe_ratio:.2f}")
                self.logger.info(f"Max drawdown: {backtest_results.max_drawdown:.2%}")
                return True
            else:
                self.logger.error(f"Backtesting failed: {backtest_results.error_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Backtesting failed: {e}")
            return False
    
    async def start_live_trading(self) -> bool:
        """
        Start live trading operations.
        
        Returns:
            bool: True if trading started successfully, False otherwise
        """
        try:
            self.logger.info("Starting live trading...")
            
            if not self.system_integrator:
                raise ValueError("System integrator not initialized")
            
            # Start the trading system
            await self.system_integrator.start_trading()
            self.is_running = True
            
            # Start performance monitoring
            if self.dashboard:
                await self.dashboard.start_monitoring()
            
            self.logger.info("Live trading started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start live trading: {e}")
            return False
    
    async def stop_trading(self):
        """Stop trading operations gracefully."""
        try:
            self.logger.info("Stopping trading operations...")
            
            self.is_running = False
            
            # Stop system integrator
            if self.system_integrator:
                await self.system_integrator.stop_trading()
            
            # Stop dashboard
            if self.dashboard:
                await self.dashboard.stop_monitoring()
            
            self.logger.info("Trading operations stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    async def run_complete_workflow(self):
        """
        Execute the complete workflow: training -> backtesting -> live trading.
        """
        try:
            # Step 1: Initialize system
            if not await self.initialize():
                self.logger.error("System initialization failed")
                return
            
            # Step 2: Run training
            if not await self.run_training():
                self.logger.error("Training failed")
                return
            
            # Step 3: Run backtesting
            if not await self.run_backtesting():
                self.logger.error("Backtesting failed")
                return
            
            # Step 4: Start live trading
            if not await self.start_live_trading():
                self.logger.error("Failed to start live trading")
                return
            
            # Step 5: Run until shutdown signal
            self.logger.info("System running. Press Ctrl+C to stop.")
            await self.shutdown_event.wait()
            
            # Step 6: Graceful shutdown
            await self.stop_trading()
            
        except Exception as e:
            self.logger.error(f"Workflow execution failed: {e}")
            await self.stop_trading()
    
    async def get_system_status(self) -> Dict:
        """
        Get current system status and performance metrics.
        
        Returns:
            Dict: System status information
        """
        status = {
            'is_running': self.is_running,
            'timestamp': datetime.now().isoformat(),
            'components': {
                'system_integrator': self.system_integrator is not None,
                'dashboard': self.dashboard is not None,
                'training_pipeline': self.training_pipeline is not None,
                'backtesting_engine': self.backtesting_engine is not None,
                'model_evaluator': self.model_evaluator is not None
            }
        }
        
        # Add performance metrics if available
        if self.system_integrator and self.is_running:
            performance = await self.system_integrator.get_performance_summary()
            status['performance'] = performance
        
        return status

def create_argument_parser() -> argparse.ArgumentParser:
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Noryon AI Trading System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main_trading_system.py --mode complete
  python main_trading_system.py --mode training
  python main_trading_system.py --mode backtesting --symbols AAPL GOOGL
  python main_trading_system.py --mode live --config config/production.yaml
        """
    )
    
    parser.add_argument(
        '--mode',
        choices=['complete', 'training', 'backtesting', 'live', 'status'],
        default='complete',
        help='Operation mode (default: complete)'
    )
    
    parser.add_argument(
        '--config',
        default='config/trading_config.yaml',
        help='Configuration file path (default: config/trading_config.yaml)'
    )
    
    parser.add_argument(
        '--symbols',
        nargs='+',
        help='Trading symbols for backtesting mode'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    return parser

async def main():
    """Main entry point for the Noryon AI Trading System."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create trading system instance
    trading_system = NoryonTradingSystem(args.config)
    
    try:
        if args.mode == 'complete':
            await trading_system.run_complete_workflow()
        
        elif args.mode == 'training':
            if await trading_system.initialize():
                await trading_system.run_training()
        
        elif args.mode == 'backtesting':
            if await trading_system.initialize():
                await trading_system.run_backtesting(args.symbols)
        
        elif args.mode == 'live':
            if await trading_system.initialize():
                await trading_system.start_live_trading()
                print("Live trading started. Press Ctrl+C to stop.")
                await trading_system.shutdown_event.wait()
                await trading_system.stop_trading()
        
        elif args.mode == 'status':
            if await trading_system.initialize():
                status = await trading_system.get_system_status()
                print("System Status:")
                print(yaml.dump(status, default_flow_style=False))
    
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        if trading_system.is_running:
            await trading_system.stop_trading()

if __name__ == "__main__":
    # Ensure we're running in the correct directory
    os.chdir(project_root)
    
    # Run the main function
    asyncio.run(main())