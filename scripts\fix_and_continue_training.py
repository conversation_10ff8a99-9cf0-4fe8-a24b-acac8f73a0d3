#!/usr/bin/env python3
"""
Fix Issues and Continue Training
Address the current training issues and continue with optimized approach
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class TrainingFixer:
    """Fix training issues and continue with optimized approach"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        
    def fix_deepseek_local_config(self):
        """Fix the local DeepSeek model configuration"""
        console.print("[yellow]🔧 Fixing local DeepSeek model configuration...[/yellow]")
        
        deepseek_config_path = self.project_root / "deepseek r1" / "config (1).json"
        
        if deepseek_config_path.exists():
            try:
                import json
                
                # Read current config
                with open(deepseek_config_path, 'r') as f:
                    config = json.load(f)
                
                # Fix model type if needed
                if "model_type" not in config:
                    config["model_type"] = "llama"
                    console.print("[green]✅ Added model_type to DeepSeek config[/green]")
                
                # Save fixed config
                with open(deepseek_config_path, 'w') as f:
                    json.dump(config, f, indent=2)
                
                console.print("[green]✅ DeepSeek local config fixed[/green]")
                return True
                
            except Exception as e:
                console.print(f"[red]❌ Failed to fix DeepSeek config: {e}[/red]")
                return False
        else:
            console.print("[yellow]⚠️ DeepSeek local config not found[/yellow]")
            return False
    
    async def train_working_models_only(self):
        """Train only the models that are currently working"""
        console.print(Panel(
            "[bold blue]🚀 Training Working Models Only[/bold blue]\n\n"
            "Focusing on models that are currently functional:\n"
            "• Ollama models (Gemma 3 12B, Phi 4 9B)\n"
            "• Alternative approaches for HuggingFace models",
            title="Optimized Training Approach"
        ))
        
        results = {}
        
        # 1. Complete Ollama model training
        console.print("\n[bold yellow]📋 Step 1: Complete Ollama Model Training[/bold yellow]")
        
        ollama_models = [
            ("Gemma 3 12B Enhanced", "gemma3:12b"),
            ("Phi 4 9B Enhanced", "phi4:14b")
        ]
        
        for model_name, model_id in ollama_models:
            console.print(f"[yellow]🚀 Processing {model_name}...[/yellow]")
            
            # Check if already created
            check_result = subprocess.run([
                'ollama', 'list'
            ], capture_output=True, text=True)
            
            enhanced_name = f"noryon-{model_name.lower().replace(' ', '-')}-enhanced"
            
            if enhanced_name in check_result.stdout:
                console.print(f"[green]✅ {model_name} already enhanced[/green]")
                results[model_name] = True
            else:
                # Create enhanced model
                success = await self.create_enhanced_ollama_model(model_name, model_id)
                results[model_name] = success
        
        # 2. Try alternative HuggingFace approach
        console.print("\n[bold yellow]📋 Step 2: Alternative HuggingFace Training[/bold yellow]")
        
        hf_models = [
            ("Qwen 3 Alternative", "Qwen/Qwen2.5-7B-Instruct"),
            ("Mistral Alternative", "mistralai/Mistral-7B-v0.1")
        ]
        
        for model_name, model_path in hf_models:
            console.print(f"[yellow]🚀 Attempting {model_name}...[/yellow]")
            success = await self.train_hf_model_alternative(model_name, model_path)
            results[model_name] = success
        
        return results
    
    async def create_enhanced_ollama_model(self, model_name: str, model_id: str) -> bool:
        """Create enhanced Ollama model"""
        try:
            # Create enhanced financial modelfile
            enhanced_modelfile = f"""
FROM {model_id}

SYSTEM \"\"\"You are an elite financial AI specialist for the Noryon trading system with advanced capabilities in:

MARKET ANALYSIS:
- Real-time technical analysis using multiple indicators (RSI, MACD, Bollinger Bands, Moving Averages)
- Fundamental analysis incorporating earnings, P/E ratios, sector performance
- Sentiment analysis from news, social media, and market data
- Options flow analysis and unusual activity detection

RISK MANAGEMENT:
- Portfolio optimization using Modern Portfolio Theory
- Value at Risk (VaR) calculations and stress testing
- Correlation analysis and diversification strategies
- Dynamic position sizing based on volatility and market conditions

TRADING STRATEGIES:
- Momentum and mean reversion strategies
- Pairs trading and statistical arbitrage
- Event-driven trading (earnings, mergers, economic releases)
- Multi-timeframe analysis (scalping to swing trading)

QUANTITATIVE ANALYSIS:
- Statistical modeling and backtesting
- Machine learning pattern recognition
- Algorithmic signal generation
- Performance attribution and analytics

Always provide:
- Specific, actionable recommendations with entry/exit points
- Risk-adjusted return expectations
- Confidence levels and probability assessments
- Real-time market context and timing considerations
- Conservative bias with proper risk management

Focus on: US equities, major cryptocurrencies, forex majors, commodities, and derivatives.
\"\"\"

PARAMETER temperature 0.2
PARAMETER top_p 0.85
PARAMETER top_k 30
PARAMETER repeat_penalty 1.1
"""
            
            # Save enhanced modelfile
            modelfile_path = self.project_root / f"Modelfile.{model_name.lower().replace(' ', '_')}"
            with open(modelfile_path, 'w') as f:
                f.write(enhanced_modelfile)
            
            # Create the enhanced model
            enhanced_name = f"noryon-{model_name.lower().replace(' ', '-')}-enhanced"
            
            result = subprocess.run([
                'ollama', 'create', enhanced_name, '-f', str(modelfile_path)
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                console.print(f"[green]✅ {model_name} enhanced model created: {enhanced_name}[/green]")
                
                # Test the model
                test_result = subprocess.run([
                    'ollama', 'run', enhanced_name, 
                    'Analyze AAPL stock and provide a trading recommendation.'
                ], capture_output=True, text=True, timeout=120)
                
                if test_result.returncode == 0:
                    console.print(f"[green]✅ {model_name} test successful[/green]")
                    return True
                else:
                    console.print(f"[red]❌ {model_name} test failed[/red]")
                    return False
            else:
                console.print(f"[red]❌ Failed to create {model_name} model: {result.stderr}[/red]")
                return False
                
        except Exception as e:
            console.print(f"[red]❌ {model_name} creation failed: {e}[/red]")
            return False
    
    async def train_hf_model_alternative(self, model_name: str, model_path: str) -> bool:
        """Train HuggingFace model with alternative approach"""
        try:
            console.print(f"[yellow]🚀 Training {model_name} with simplified approach...[/yellow]")
            
            # Use the existing robust training script with fixes
            result = subprocess.run([
                sys.executable, 'train_models_robust.py', 
                '--model', model_name.split()[0].lower(), '--quick'
            ], capture_output=True, text=True, timeout=1800)
            
            if result.returncode == 0 and "training completed successfully" in result.stdout:
                console.print(f"[green]✅ {model_name} training completed[/green]")
                return True
            else:
                console.print(f"[red]❌ {model_name} training failed[/red]")
                console.print(f"Error: {result.stderr[-300:]}")  # Last 300 chars
                return False
                
        except Exception as e:
            console.print(f"[red]❌ {model_name} training failed: {e}[/red]")
            return False
    
    def check_current_status(self):
        """Check current status of all models"""
        console.print("[yellow]🔍 Checking current model status...[/yellow]")
        
        status_table = Table(title="Current Model Status")
        status_table.add_column("Model", style="cyan")
        status_table.add_column("Type", style="yellow")
        status_table.add_column("Status", style="green")
        status_table.add_column("Action Needed", style="magenta")
        
        # Check Ollama models
        try:
            ollama_result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            ollama_models = ollama_result.stdout if ollama_result.returncode == 0 else ""
            
            if "noryon-gemma-3-12b-finance" in ollama_models:
                status_table.add_row("Gemma 3 12B", "Ollama", "✅ Trained", "🧪 Test & Enhance")
            else:
                status_table.add_row("Gemma 3 12B", "Ollama", "⏳ Pending", "🚀 Train Enhanced")
            
            if "noryon-phi-4-9b-finance" in ollama_models:
                status_table.add_row("Phi 4 9B", "Ollama", "✅ Trained", "🧪 Test & Enhance")
            else:
                status_table.add_row("Phi 4 9B", "Ollama", "⏳ Pending", "🚀 Train Enhanced")
                
        except:
            status_table.add_row("Ollama Models", "Ollama", "❌ Error", "🔧 Fix Ollama")
        
        # Check HuggingFace models
        model_dirs = [
            ("DeepSeek R1", "models/deepseek-finance-v1"),
            ("Qwen 3", "models/qwen3-finance-v1"),
            ("Mistral", "models/mistral-finance-v1")
        ]
        
        for model_name, model_dir in model_dirs:
            model_path = Path(model_dir)
            if model_path.exists() and any(model_path.glob("*.safetensors")):
                status_table.add_row(model_name, "HuggingFace", "✅ Trained", "🧪 Test")
            elif model_path.exists():
                status_table.add_row(model_name, "HuggingFace", "⏳ Partial", "🚀 Complete Training")
            else:
                status_table.add_row(model_name, "HuggingFace", "❌ Not Started", "🚀 Start Training")
        
        console.print(status_table)
        
        return status_table
    
    async def run_optimized_training(self):
        """Run optimized training focusing on working components"""
        console.print(Panel(
            "[bold blue]🎯 Optimized Training Strategy[/bold blue]\n\n"
            "Focusing on models and approaches that are currently working:\n"
            "1. Complete Ollama model enhancement\n"
            "2. Fix and retry HuggingFace models\n"
            "3. Comprehensive testing and validation",
            title="Optimized Approach"
        ))
        
        start_time = datetime.now()
        
        # Check current status
        self.check_current_status()
        
        # Fix local DeepSeek config
        self.fix_deepseek_local_config()
        
        # Train working models
        results = await self.train_working_models_only()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Generate results
        successful_models = [name for name, success in results.items() if success]
        failed_models = [name for name, success in results.items() if not success]
        
        # Final report
        console.print(Panel(
            f"[bold green]🎉 Optimized Training Complete![/bold green]\n\n"
            f"Duration: {duration}\n"
            f"Successful Models: {len(successful_models)}/{len(results)}\n\n"
            f"✅ Working Models: {', '.join(successful_models)}\n" +
            (f"❌ Failed Models: {', '.join(failed_models)}\n" if failed_models else "") +
            f"\n🚀 Ready for production testing!",
            title="Training Results"
        ))
        
        return {
            "results": results,
            "successful_models": successful_models,
            "failed_models": failed_models,
            "duration": duration
        }

async def main():
    """Main execution function"""
    console.print("[bold blue]🔧 Starting Training Fix and Optimization...[/bold blue]\n")
    
    fixer = TrainingFixer()
    results = await fixer.run_optimized_training()
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    console.print("1. Test all successful models with financial queries")
    console.print("2. Integrate working models into live trading system")
    console.print("3. Monitor performance and optimize further")
    console.print("4. Address any remaining failed models")
    
    return results

if __name__ == "__main__":
    results = asyncio.run(main())
