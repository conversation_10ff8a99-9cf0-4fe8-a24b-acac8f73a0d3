#!/usr/bin/env python3
"""
Multi-Agent Reasoning System
Activates multiple AI agents with specialized reasoning capabilities
"""

import asyncio
import subprocess
import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading

@dataclass
class AgentResponse:
    agent_name: str
    model_name: str
    response: str
    reasoning_quality: float
    response_time: float
    confidence: float
    timestamp: datetime

@dataclass
class ReasoningTask:
    task_id: str
    query: str
    task_type: str  # 'analysis', 'prediction', 'strategy', 'risk_assessment'
    priority: int
    context: Dict[str, Any]

class MultiAgentReasoningSystem:
    """Multi-agent system with specialized reasoning models"""
    
    def __init__(self):
        # Your best reasoning models organized by specialty
        self.reasoning_agents = {
            # Deep Reasoning Specialists
            'deepseek_reasoner': {
                'model': 'unrestricted-deepseek-r1-14b:latest',
                'specialty': 'deep_logical_reasoning',
                'strength': 'Complex problem solving and step-by-step analysis',
                'size': '9.0 GB'
            },
            'phi4_reasoner': {
                'model': 'unrestricted-phi4-reasoning-14b:latest', 
                'specialty': 'mathematical_reasoning',
                'strength': 'Mathematical analysis and quantitative reasoning',
                'size': '11 GB'
            },
            'marco_o1_reasoner': {
                'model': 'unrestricted-marco-o1-7b:latest',
                'specialty': 'strategic_reasoning',
                'strength': 'Strategic thinking and planning',
                'size': '4.7 GB'
            },
            
            # Finance Specialists
            'qwen_finance': {
                'model': 'unrestricted-noryon-qwen3-finance-v2-latest:latest',
                'specialty': 'financial_analysis',
                'strength': 'Financial markets and trading analysis',
                'size': '9.3 GB'
            },
            'deepseek_finance': {
                'model': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
                'specialty': 'financial_reasoning',
                'strength': 'Deep financial reasoning and risk analysis',
                'size': '9.0 GB'
            },
            'phi4_finance': {
                'model': 'unrestricted-noryon-phi4-reasoning-finance-v2-latest:latest',
                'specialty': 'quantitative_finance',
                'strength': 'Quantitative financial modeling',
                'size': '11 GB'
            },
            
            # Enhanced Reasoning Models
            'qwen_enhanced': {
                'model': 'phase2-smart-unrestricted-qwen3-14b-latest:latest',
                'specialty': 'comprehensive_analysis',
                'strength': 'Comprehensive multi-factor analysis',
                'size': '9.3 GB'
            },
            'cogito_reasoner': {
                'model': 'unrestricted-cogito-14b:latest',
                'specialty': 'cognitive_reasoning',
                'strength': 'Cognitive analysis and decision making',
                'size': '9.0 GB'
            },
            'gemma_enhanced': {
                'model': 'unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest:latest',
                'specialty': 'enhanced_reasoning',
                'strength': 'Enhanced reasoning and pattern recognition',
                'size': '8.1 GB'
            },
            
            # Fast Reasoning Models
            'exaone_reasoner': {
                'model': 'unrestricted-exaone-deep-7.8b:latest',
                'specialty': 'rapid_reasoning',
                'strength': 'Fast reasoning and quick insights',
                'size': '4.8 GB'
            },
            'granite_reasoner': {
                'model': 'unrestricted-granite3.1-dense-8b:latest',
                'specialty': 'structured_reasoning',
                'strength': 'Structured analysis and clear reasoning',
                'size': '5.0 GB'
            }
        }
        
        # Agent performance tracking
        self.agent_performance = {}
        for agent_id in self.reasoning_agents:
            self.agent_performance[agent_id] = {
                'total_queries': 0,
                'successful_responses': 0,
                'avg_response_time': 0.0,
                'avg_reasoning_quality': 0.0,
                'specialization_score': 1.0
            }
        
        # Task routing rules
        self.task_routing = {
            'market_analysis': ['qwen_finance', 'deepseek_finance', 'qwen_enhanced'],
            'risk_assessment': ['deepseek_finance', 'phi4_finance', 'cogito_reasoner'],
            'mathematical_analysis': ['phi4_reasoner', 'phi4_finance', 'qwen_enhanced'],
            'strategic_planning': ['marco_o1_reasoner', 'qwen_enhanced', 'cogito_reasoner'],
            'quick_insights': ['exaone_reasoner', 'granite_reasoner', 'marco_o1_reasoner'],
            'deep_analysis': ['deepseek_reasoner', 'qwen_enhanced', 'cogito_reasoner']
        }
        
        print("🧠 Multi-Agent Reasoning System initialized")
        print(f"   🤖 Agents available: {len(self.reasoning_agents)}")
        print(f"   📊 Specialties: {len(set(agent['specialty'] for agent in self.reasoning_agents.values()))}")
        print(f"   💾 Total model size: {sum(float(agent['size'].split()[0]) for agent in self.reasoning_agents.values()):.1f} GB")
    
    async def query_agent(self, agent_id: str, query: str, timeout: int = 45) -> Optional[AgentResponse]:
        """Query a specific reasoning agent"""
        if agent_id not in self.reasoning_agents:
            print(f"❌ Unknown agent: {agent_id}")
            return None
        
        agent = self.reasoning_agents[agent_id]
        model_name = agent['model']
        
        print(f"🤖 Querying {agent_id} ({agent['specialty']})...")
        
        start_time = time.time()
        
        try:
            # Enhanced prompt for reasoning
            reasoning_prompt = f"""You are a specialized AI agent with expertise in {agent['specialty']}.

TASK: {query}

Please provide:
1. Your reasoning process (step-by-step)
2. Your analysis and conclusions
3. Your confidence level (1-10)
4. Specific recommendations or insights

Focus on your specialty: {agent['strength']}

Provide clear, actionable insights with detailed reasoning."""

            result = subprocess.run([
                'ollama', 'run', model_name, reasoning_prompt
            ], capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                
                # Assess reasoning quality
                reasoning_quality = self._assess_reasoning_quality(response)
                confidence = self._extract_confidence(response)
                
                # Update performance tracking
                self._update_agent_performance(agent_id, True, response_time, reasoning_quality)
                
                agent_response = AgentResponse(
                    agent_name=agent_id,
                    model_name=model_name,
                    response=response,
                    reasoning_quality=reasoning_quality,
                    response_time=response_time,
                    confidence=confidence,
                    timestamp=datetime.now()
                )
                
                print(f"   ✅ {agent_id}: {response_time:.1f}s, quality: {reasoning_quality:.2f}, confidence: {confidence:.2f}")
                return agent_response
            else:
                print(f"   ❌ {agent_id}: Model error - {result.stderr}")
                self._update_agent_performance(agent_id, False, response_time, 0.0)
                return None
                
        except subprocess.TimeoutExpired:
            response_time = time.time() - start_time
            print(f"   ⏰ {agent_id}: Timeout after {timeout}s")
            self._update_agent_performance(agent_id, False, response_time, 0.0)
            return None
        except Exception as e:
            response_time = time.time() - start_time
            print(f"   ❌ {agent_id}: Error - {e}")
            self._update_agent_performance(agent_id, False, response_time, 0.0)
            return None
    
    async def multi_agent_reasoning(self, task: ReasoningTask, max_agents: int = 5) -> Dict[str, Any]:
        """Get reasoning from multiple agents and synthesize results"""
        print(f"\n🧠 MULTI-AGENT REASONING: {task.query[:100]}...")
        print(f"   Task type: {task.task_type}")
        print(f"   Priority: {task.priority}")
        
        # Select best agents for this task type
        candidate_agents = self.task_routing.get(task.task_type, list(self.reasoning_agents.keys()))
        selected_agents = self._select_best_agents(candidate_agents, max_agents)
        
        print(f"   Selected agents: {selected_agents}")
        
        # Query agents in parallel
        start_time = time.time()
        
        tasks = []
        for agent_id in selected_agents:
            task_coroutine = self.query_agent(agent_id, task.query)
            tasks.append(task_coroutine)
        
        # Execute all queries in parallel
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter successful responses
        valid_responses = [
            r for r in responses 
            if isinstance(r, AgentResponse)
        ]
        
        total_time = time.time() - start_time
        
        print(f"   📊 Results: {len(valid_responses)}/{len(selected_agents)} agents responded")
        
        if not valid_responses:
            return {
                'success': False,
                'message': 'No agents provided valid responses',
                'execution_time': total_time
            }
        
        # Synthesize responses
        synthesis = self._synthesize_agent_responses(valid_responses, task)
        synthesis['execution_time'] = total_time
        synthesis['agents_used'] = len(valid_responses)
        synthesis['task_id'] = task.task_id
        
        return synthesis
    
    def _select_best_agents(self, candidates: List[str], max_agents: int) -> List[str]:
        """Select best agents based on performance and specialization"""
        # Score agents based on performance
        agent_scores = []
        
        for agent_id in candidates:
            if agent_id in self.reasoning_agents:
                perf = self.agent_performance[agent_id]
                
                # Calculate score based on success rate, speed, and quality
                success_rate = perf['successful_responses'] / max(1, perf['total_queries'])
                speed_score = max(0, 1 - (perf['avg_response_time'] / 60))  # Normalize to 60s
                quality_score = perf['avg_reasoning_quality']
                specialization_score = perf['specialization_score']
                
                total_score = (success_rate * 0.3 + speed_score * 0.2 + 
                             quality_score * 0.3 + specialization_score * 0.2)
                
                agent_scores.append((agent_id, total_score))
        
        # Sort by score and select top agents
        agent_scores.sort(key=lambda x: x[1], reverse=True)
        selected = [agent_id for agent_id, score in agent_scores[:max_agents]]
        
        return selected
    
    def _synthesize_agent_responses(self, responses: List[AgentResponse], task: ReasoningTask) -> Dict[str, Any]:
        """Synthesize multiple agent responses into unified insights"""
        
        # Weight responses by quality and confidence
        weighted_insights = []
        total_weight = 0
        
        for response in responses:
            weight = response.reasoning_quality * response.confidence
            weighted_insights.append({
                'agent': response.agent_name,
                'specialty': self.reasoning_agents[response.agent_name]['specialty'],
                'response': response.response,
                'weight': weight,
                'quality': response.reasoning_quality,
                'confidence': response.confidence,
                'response_time': response.response_time
            })
            total_weight += weight
        
        # Sort by weight
        weighted_insights.sort(key=lambda x: x['weight'], reverse=True)
        
        # Create synthesis
        primary_insight = weighted_insights[0] if weighted_insights else None
        
        synthesis = {
            'success': True,
            'primary_insight': {
                'agent': primary_insight['agent'],
                'specialty': primary_insight['specialty'],
                'response': primary_insight['response'][:1000] + "..." if len(primary_insight['response']) > 1000 else primary_insight['response'],
                'confidence': primary_insight['confidence'],
                'quality': primary_insight['quality']
            } if primary_insight else None,
            'consensus_analysis': self._analyze_consensus(responses),
            'alternative_perspectives': [
                {
                    'agent': insight['agent'],
                    'specialty': insight['specialty'],
                    'key_points': insight['response'][:300] + "..." if len(insight['response']) > 300 else insight['response'],
                    'confidence': insight['confidence']
                }
                for insight in weighted_insights[1:3]  # Top 2 alternative views
            ],
            'reasoning_quality_avg': sum(r.reasoning_quality for r in responses) / len(responses),
            'confidence_avg': sum(r.confidence for r in responses) / len(responses),
            'response_time_avg': sum(r.response_time for r in responses) / len(responses),
            'agent_count': len(responses)
        }
        
        return synthesis
    
    def _analyze_consensus(self, responses: List[AgentResponse]) -> Dict[str, Any]:
        """Analyze consensus among agent responses"""
        if len(responses) < 2:
            return {'consensus_level': 1.0, 'agreement': 'single_agent'}
        
        # Simple consensus analysis based on response similarity
        # In a real implementation, this would use NLP similarity measures
        
        avg_confidence = sum(r.confidence for r in responses) / len(responses)
        confidence_variance = sum((r.confidence - avg_confidence) ** 2 for r in responses) / len(responses)
        
        # Lower variance = higher consensus
        consensus_level = max(0, 1 - confidence_variance)
        
        if consensus_level > 0.8:
            agreement = 'strong_consensus'
        elif consensus_level > 0.6:
            agreement = 'moderate_consensus'
        elif consensus_level > 0.4:
            agreement = 'weak_consensus'
        else:
            agreement = 'conflicting_views'
        
        return {
            'consensus_level': consensus_level,
            'agreement': agreement,
            'confidence_variance': confidence_variance,
            'avg_confidence': avg_confidence
        }
    
    def _assess_reasoning_quality(self, response: str) -> float:
        """Assess the quality of reasoning in a response"""
        quality_score = 0.5  # Base score
        
        # Length factor
        if len(response) > 500:
            quality_score += 0.1
        if len(response) > 1500:
            quality_score += 0.1
        
        # Structure indicators
        structure_indicators = ['1.', '2.', '3.', 'first', 'second', 'therefore', 'because', 'analysis', 'conclusion']
        structure_count = sum(1 for indicator in structure_indicators if indicator.lower() in response.lower())
        quality_score += min(structure_count * 0.05, 0.2)
        
        # Reasoning indicators
        reasoning_indicators = ['reasoning', 'logic', 'evidence', 'data', 'analysis', 'conclusion', 'recommendation']
        reasoning_count = sum(1 for indicator in reasoning_indicators if indicator.lower() in response.lower())
        quality_score += min(reasoning_count * 0.03, 0.15)
        
        # Financial/technical terms (for finance tasks)
        finance_terms = ['market', 'price', 'risk', 'return', 'volatility', 'trend', 'support', 'resistance']
        finance_count = sum(1 for term in finance_terms if term.lower() in response.lower())
        quality_score += min(finance_count * 0.02, 0.1)
        
        return min(quality_score, 1.0)
    
    def _extract_confidence(self, response: str) -> float:
        """Extract confidence level from response"""
        import re
        
        # Look for confidence patterns
        patterns = [
            r'confidence[:\s]+(\d+)[/\s]*10',
            r'(\d+)[/\s]*10\s*confidence',
            r'confidence[:\s]+(\d+)%',
            r'(\d+)%\s*confidence'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response.lower())
            if match:
                value = int(match.group(1))
                return value / 10 if value <= 10 else value / 100
        
        # Default confidence based on response characteristics
        if 'highly confident' in response.lower():
            return 0.9
        elif 'confident' in response.lower():
            return 0.8
        elif 'uncertain' in response.lower() or 'unsure' in response.lower():
            return 0.4
        else:
            return 0.7  # Default
    
    def _update_agent_performance(self, agent_id: str, success: bool, response_time: float, quality: float):
        """Update agent performance metrics"""
        perf = self.agent_performance[agent_id]
        
        perf['total_queries'] += 1
        if success:
            perf['successful_responses'] += 1
        
        # Exponential moving average
        alpha = 0.1
        perf['avg_response_time'] = perf['avg_response_time'] * (1 - alpha) + response_time * alpha
        if success:
            perf['avg_reasoning_quality'] = perf['avg_reasoning_quality'] * (1 - alpha) + quality * alpha
    
    def get_agent_status(self) -> Dict[str, Any]:
        """Get status of all agents"""
        return {
            'timestamp': datetime.now().isoformat(),
            'total_agents': len(self.reasoning_agents),
            'agent_details': {
                agent_id: {
                    'model': agent['model'],
                    'specialty': agent['specialty'],
                    'strength': agent['strength'],
                    'size': agent['size'],
                    'performance': self.agent_performance[agent_id]
                }
                for agent_id, agent in self.reasoning_agents.items()
            },
            'specialties_available': list(set(agent['specialty'] for agent in self.reasoning_agents.values())),
            'task_routing': self.task_routing
        }

async def main():
    """Test the multi-agent reasoning system"""
    print("MULTI-AGENT REASONING SYSTEM - PHASE 4")
    print("=" * 70)
    
    # Initialize system
    reasoning_system = MultiAgentReasoningSystem()
    
    # Test reasoning tasks
    test_tasks = [
        ReasoningTask(
            task_id="test_1",
            query="Analyze Bitcoin's current market position and provide a comprehensive trading strategy",
            task_type="market_analysis",
            priority=1,
            context={"symbol": "BTC", "current_price": 32000}
        ),
        ReasoningTask(
            task_id="test_2", 
            query="Assess the risk of investing $50,000 in cryptocurrency during current market conditions",
            task_type="risk_assessment",
            priority=2,
            context={"investment_amount": 50000, "asset_class": "crypto"}
        )
    ]
    
    # Execute reasoning tasks
    for task in test_tasks:
        print(f"\n{'='*50}")
        print(f"EXECUTING TASK: {task.task_id}")
        print(f"{'='*50}")
        
        result = await reasoning_system.multi_agent_reasoning(task, max_agents=3)
        
        if result['success']:
            print(f"\n📊 REASONING RESULTS:")
            print(f"   Agents used: {result['agent_count']}")
            print(f"   Execution time: {result['execution_time']:.1f}s")
            print(f"   Avg quality: {result['reasoning_quality_avg']:.2f}")
            print(f"   Avg confidence: {result['confidence_avg']:.2f}")
            
            if result['primary_insight']:
                insight = result['primary_insight']
                print(f"\n🎯 PRIMARY INSIGHT ({insight['agent']}):")
                print(f"   Specialty: {insight['specialty']}")
                print(f"   Confidence: {insight['confidence']:.2f}")
                print(f"   Response: {insight['response'][:300]}...")
            
            consensus = result['consensus_analysis']
            print(f"\n🤝 CONSENSUS: {consensus['agreement']} (level: {consensus['consensus_level']:.2f})")
        else:
            print(f"❌ Task failed: {result['message']}")
    
    # Show agent status
    print(f"\n📊 AGENT STATUS:")
    status = reasoning_system.get_agent_status()
    print(f"   Total agents: {status['total_agents']}")
    print(f"   Specialties: {len(status['specialties_available'])}")
    
    for agent_id, details in list(status['agent_details'].items())[:5]:  # Show first 5
        perf = details['performance']
        success_rate = perf['successful_responses'] / max(1, perf['total_queries'])
        print(f"   {agent_id}: {success_rate:.1%} success, {perf['avg_response_time']:.1f}s avg")
    
    print(f"\n✅ Multi-Agent Reasoning System operational!")

if __name__ == "__main__":
    asyncio.run(main())
