#!/usr/bin/env python3
"""
Working Risk Management System for Phase 3A
Simplified but comprehensive risk controls
"""

import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
import math

class RiskLevel(Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class AlertType(Enum):
    POSITION_SIZE = "POSITION_SIZE"
    DRAWDOWN = "DRAWDOWN"
    VOLATILITY = "VOLATILITY"
    CORRELATION = "CORRELATION"
    LIQUIDITY = "LIQUIDITY"
    SYSTEM = "SYSTEM"

@dataclass
class RiskAlert:
    alert_type: AlertType
    level: RiskLevel
    message: str
    timestamp: datetime
    symbol: Optional[str] = None
    current_value: Optional[float] = None
    threshold: Optional[float] = None
    action_required: bool = False

@dataclass
class Position:
    symbol: str
    size: float
    entry_price: float
    current_price: float
    side: str  # 'long' or 'short'
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    timestamp: datetime = None

@dataclass
class PortfolioMetrics:
    total_value: float
    cash_balance: float
    total_risk: float
    current_drawdown: float
    max_drawdown: float
    daily_pnl: float
    daily_pnl_percent: float
    positions_count: int
    largest_position_percent: float

class WorkingRiskManager:
    """Working risk management system for AI trading"""
    
    def __init__(self, initial_capital: float = 100000.0):
        # Risk configuration
        self.config = {
            "max_position_size_percent": 5.0,  # Max 5% per position
            "max_portfolio_risk_percent": 20.0,  # Max 20% total risk
            "max_drawdown_percent": 15.0,  # Stop at 15% drawdown
            "max_daily_loss_percent": 5.0,  # Max 5% daily loss
            "max_positions": 8,  # Max concurrent positions
            "min_cash_percent": 10.0,  # Min 10% cash
            "stop_loss_percent": 3.0,  # Default 3% stop loss
            "take_profit_percent": 6.0,  # Default 6% take profit
            "max_correlation": 0.7,  # Max correlation between positions
        }
        
        # Portfolio state
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.cash_balance = initial_capital
        self.positions = {}  # symbol -> Position
        self.portfolio_history = []
        self.daily_pnl_history = []
        self.alerts = []
        self.emergency_stop = False
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - RISK - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('risk_management.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        self.logger.info("Working Risk Management System initialized")
    
    def validate_new_position(self, symbol: str, size: float, price: float, 
                            side: str = 'long', stop_loss: Optional[float] = None) -> Tuple[bool, List[RiskAlert]]:
        """Validate if a new position meets risk criteria"""
        alerts = []
        
        # Check if emergency stop is active
        if self.emergency_stop:
            alerts.append(RiskAlert(
                alert_type=AlertType.SYSTEM,
                level=RiskLevel.CRITICAL,
                message="Emergency stop is active - no new positions allowed",
                timestamp=datetime.now(),
                action_required=True
            ))
            return False, alerts
        
        # Calculate position value
        position_value = size * price
        position_percent = (position_value / self.current_capital) * 100
        
        # Check position size limit
        if position_percent > self.config["max_position_size_percent"]:
            alerts.append(RiskAlert(
                alert_type=AlertType.POSITION_SIZE,
                level=RiskLevel.HIGH,
                message=f"Position size {position_percent:.1f}% exceeds limit {self.config['max_position_size_percent']}%",
                timestamp=datetime.now(),
                symbol=symbol,
                current_value=position_percent,
                threshold=self.config["max_position_size_percent"],
                action_required=True
            ))
        
        # Check maximum positions
        if len(self.positions) >= self.config["max_positions"]:
            alerts.append(RiskAlert(
                alert_type=AlertType.POSITION_SIZE,
                level=RiskLevel.MEDIUM,
                message=f"Maximum positions {self.config['max_positions']} reached",
                timestamp=datetime.now(),
                symbol=symbol,
                action_required=True
            ))
        
        # Check cash availability
        required_cash = position_value
        if required_cash > self.cash_balance:
            alerts.append(RiskAlert(
                alert_type=AlertType.LIQUIDITY,
                level=RiskLevel.HIGH,
                message=f"Insufficient cash: need ${required_cash:,.2f}, have ${self.cash_balance:,.2f}",
                timestamp=datetime.now(),
                symbol=symbol,
                action_required=True
            ))
        
        # Check minimum cash requirement after trade
        remaining_cash = self.cash_balance - required_cash
        min_cash_required = self.current_capital * (self.config["min_cash_percent"] / 100)
        if remaining_cash < min_cash_required:
            alerts.append(RiskAlert(
                alert_type=AlertType.LIQUIDITY,
                level=RiskLevel.MEDIUM,
                message=f"Trade would leave insufficient cash reserves",
                timestamp=datetime.now(),
                symbol=symbol,
                current_value=remaining_cash,
                threshold=min_cash_required
            ))
        
        # Determine if position is allowed
        critical_alerts = [a for a in alerts if a.level == RiskLevel.CRITICAL or a.action_required]
        position_allowed = len(critical_alerts) == 0
        
        return position_allowed, alerts
    
    def calculate_optimal_position_size(self, symbol: str, entry_price: float, 
                                      stop_loss: float, confidence: float = 0.8) -> float:
        """Calculate optimal position size using risk-based sizing"""
        # Risk per share
        risk_per_share = abs(entry_price - stop_loss)
        
        # Maximum risk amount
        max_risk_amount = self.current_capital * (self.config["max_position_size_percent"] / 100)
        
        # Adjust for confidence
        confidence_adjusted_risk = max_risk_amount * confidence
        
        # Calculate position size
        optimal_size = confidence_adjusted_risk / risk_per_share
        
        # Apply cash constraints
        max_position_value = min(
            self.cash_balance * 0.9,  # Leave 10% cash buffer
            self.current_capital * (self.config["max_position_size_percent"] / 100)
        )
        max_size_by_cash = max_position_value / entry_price
        
        final_size = min(optimal_size, max_size_by_cash)
        
        self.logger.info(f"Optimal position size for {symbol}: {final_size:.2f} shares")
        return final_size
    
    def add_position(self, symbol: str, size: float, entry_price: float, 
                    side: str = 'long', stop_loss: Optional[float] = None, 
                    take_profit: Optional[float] = None) -> bool:
        """Add a new position to the portfolio"""
        # Validate position first
        allowed, alerts = self.validate_new_position(symbol, size, entry_price, side, stop_loss)
        
        if not allowed:
            self.logger.warning(f"Position rejected for {symbol}: {[a.message for a in alerts]}")
            self.alerts.extend(alerts)
            return False
        
        # Add position
        position = Position(
            symbol=symbol,
            size=size,
            entry_price=entry_price,
            current_price=entry_price,
            side=side,
            stop_loss=stop_loss,
            take_profit=take_profit,
            timestamp=datetime.now()
        )
        
        self.positions[symbol] = position
        self.cash_balance -= size * entry_price
        
        self.logger.info(f"Added position: {symbol} {size} shares at ${entry_price}")
        return True
    
    def update_position_prices(self, market_prices: Dict[str, float]):
        """Update current prices for all positions"""
        for symbol, position in self.positions.items():
            if symbol in market_prices:
                position.current_price = market_prices[symbol]
    
    def check_stop_losses_and_targets(self, market_prices: Dict[str, float]) -> List[str]:
        """Check for stop loss and take profit triggers"""
        triggered_positions = []
        
        for symbol, position in self.positions.items():
            if symbol not in market_prices:
                continue
                
            current_price = market_prices[symbol]
            
            # Check stop loss
            if position.stop_loss:
                if (position.side == 'long' and current_price <= position.stop_loss) or \
                   (position.side == 'short' and current_price >= position.stop_loss):
                    self.logger.warning(f"Stop loss triggered for {symbol} at ${current_price}")
                    triggered_positions.append(symbol)
                    continue
            
            # Check take profit
            if position.take_profit:
                if (position.side == 'long' and current_price >= position.take_profit) or \
                   (position.side == 'short' and current_price <= position.take_profit):
                    self.logger.info(f"Take profit triggered for {symbol} at ${current_price}")
                    triggered_positions.append(symbol)
        
        return triggered_positions
    
    def close_position(self, symbol: str, exit_price: Optional[float] = None) -> bool:
        """Close a position"""
        if symbol not in self.positions:
            return False
        
        position = self.positions[symbol]
        close_price = exit_price or position.current_price
        
        # Calculate P&L
        if position.side == 'long':
            pnl = (close_price - position.entry_price) * position.size
        else:
            pnl = (position.entry_price - close_price) * position.size
        
        # Update cash balance
        self.cash_balance += position.size * close_price
        
        # Remove position
        del self.positions[symbol]
        
        self.logger.info(f"Closed position: {symbol} P&L: ${pnl:.2f}")
        return True
    
    def calculate_portfolio_metrics(self) -> PortfolioMetrics:
        """Calculate current portfolio metrics"""
        # Calculate total portfolio value
        positions_value = sum(
            pos.size * pos.current_price for pos in self.positions.values()
        )
        total_value = self.cash_balance + positions_value
        
        # Calculate daily P&L
        daily_pnl = total_value - self.current_capital
        daily_pnl_percent = (daily_pnl / self.current_capital) * 100
        
        # Calculate drawdown
        if self.portfolio_history:
            peak_value = max(self.portfolio_history)
            current_drawdown = ((peak_value - total_value) / peak_value) * 100
            max_drawdown = max(
                ((peak - val) / peak) * 100 
                for peak, val in zip(self.portfolio_history, self.portfolio_history[1:])
                if peak > 0
            ) if len(self.portfolio_history) > 1 else 0
        else:
            current_drawdown = 0
            max_drawdown = 0
        
        # Calculate largest position
        if self.positions and total_value > 0:
            largest_position_percent = max(
                (pos.size * pos.current_price / total_value) * 100 
                for pos in self.positions.values()
            )
        else:
            largest_position_percent = 0
        
        # Calculate total risk
        total_risk = sum(
            pos.size * abs(pos.current_price - (pos.stop_loss or pos.entry_price * 0.97))
            for pos in self.positions.values()
        )
        
        return PortfolioMetrics(
            total_value=total_value,
            cash_balance=self.cash_balance,
            total_risk=total_risk,
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            daily_pnl=daily_pnl,
            daily_pnl_percent=daily_pnl_percent,
            positions_count=len(self.positions),
            largest_position_percent=largest_position_percent
        )
    
    def check_risk_limits(self) -> List[RiskAlert]:
        """Check all risk limits and generate alerts"""
        alerts = []
        metrics = self.calculate_portfolio_metrics()
        
        # Check drawdown limit
        if metrics.current_drawdown > self.config["max_drawdown_percent"]:
            alerts.append(RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                level=RiskLevel.CRITICAL,
                message=f"Drawdown {metrics.current_drawdown:.1f}% exceeds limit {self.config['max_drawdown_percent']}%",
                timestamp=datetime.now(),
                current_value=metrics.current_drawdown,
                threshold=self.config["max_drawdown_percent"],
                action_required=True
            ))
            self.emergency_stop = True
        
        # Check daily loss limit
        if metrics.daily_pnl_percent < -self.config["max_daily_loss_percent"]:
            alerts.append(RiskAlert(
                alert_type=AlertType.DRAWDOWN,
                level=RiskLevel.HIGH,
                message=f"Daily loss {metrics.daily_pnl_percent:.1f}% exceeds limit {self.config['max_daily_loss_percent']}%",
                timestamp=datetime.now(),
                current_value=metrics.daily_pnl_percent,
                threshold=-self.config["max_daily_loss_percent"],
                action_required=True
            ))
        
        # Check cash reserves
        cash_percent = (metrics.cash_balance / metrics.total_value) * 100
        if cash_percent < self.config["min_cash_percent"]:
            alerts.append(RiskAlert(
                alert_type=AlertType.LIQUIDITY,
                level=RiskLevel.MEDIUM,
                message=f"Cash reserves {cash_percent:.1f}% below minimum {self.config['min_cash_percent']}%",
                timestamp=datetime.now(),
                current_value=cash_percent,
                threshold=self.config["min_cash_percent"]
            ))
        
        self.alerts.extend(alerts)
        return alerts
    
    def generate_risk_report(self) -> Dict[str, Any]:
        """Generate comprehensive risk report"""
        metrics = self.calculate_portfolio_metrics()
        
        # Position details
        position_details = []
        for symbol, pos in self.positions.items():
            unrealized_pnl = (pos.current_price - pos.entry_price) * pos.size
            if pos.side == 'short':
                unrealized_pnl = -unrealized_pnl
                
            position_details.append({
                'symbol': symbol,
                'size': pos.size,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'market_value': pos.size * pos.current_price,
                'unrealized_pnl': unrealized_pnl,
                'unrealized_pnl_percent': (unrealized_pnl / (pos.size * pos.entry_price)) * 100,
                'side': pos.side,
                'stop_loss': pos.stop_loss,
                'take_profit': pos.take_profit,
                'days_held': (datetime.now() - pos.timestamp).days if pos.timestamp else 0
            })
        
        return {
            'timestamp': datetime.now().isoformat(),
            'portfolio_metrics': asdict(metrics),
            'positions': position_details,
            'recent_alerts': [asdict(alert) for alert in self.alerts[-10:]],
            'emergency_stop_active': self.emergency_stop,
            'risk_config': self.config,
            'performance': {
                'total_return': ((metrics.total_value - self.initial_capital) / self.initial_capital) * 100,
                'total_return_dollar': metrics.total_value - self.initial_capital,
                'positions_count': len(self.positions),
                'cash_utilization': ((self.initial_capital - metrics.cash_balance) / self.initial_capital) * 100
            }
        }

def main():
    """Test the working risk management system"""
    print("WORKING RISK MANAGEMENT SYSTEM - PHASE 3A")
    print("=" * 60)
    
    # Initialize with $100k
    risk_mgr = WorkingRiskManager(100000.0)
    
    # Test position validation
    print("\n1. Testing position validation...")
    allowed, alerts = risk_mgr.validate_new_position("BTC", 1.0, 50000.0, 'long', 48500.0)
    print(f"   Position allowed: {allowed}")
    for alert in alerts:
        print(f"   Alert: {alert.message}")
    
    # Test optimal position sizing
    print("\n2. Testing optimal position sizing...")
    optimal_size = risk_mgr.calculate_optimal_position_size("ETH", 3000.0, 2850.0, 0.8)
    print(f"   Optimal position size: {optimal_size:.2f} shares")
    
    # Add some test positions
    print("\n3. Adding test positions...")
    risk_mgr.add_position("BTC", 0.5, 50000.0, 'long', 48500.0, 53000.0)
    risk_mgr.add_position("ETH", 5.0, 3000.0, 'long', 2850.0, 3300.0)
    
    # Update prices and check metrics
    print("\n4. Updating prices and checking metrics...")
    market_prices = {"BTC": 51000.0, "ETH": 3100.0}
    risk_mgr.update_position_prices(market_prices)
    
    # Generate risk report
    print("\n5. Generating risk report...")
    report = risk_mgr.generate_risk_report()
    
    print(f"   Portfolio value: ${report['portfolio_metrics']['total_value']:,.2f}")
    print(f"   Cash balance: ${report['portfolio_metrics']['cash_balance']:,.2f}")
    print(f"   Total return: {report['performance']['total_return']:.2f}%")
    print(f"   Positions: {report['portfolio_metrics']['positions_count']}")
    print(f"   Largest position: {report['portfolio_metrics']['largest_position_percent']:.1f}%")
    
    # Check risk limits
    print("\n6. Checking risk limits...")
    alerts = risk_mgr.check_risk_limits()
    if alerts:
        for alert in alerts:
            print(f"   Risk Alert: {alert.message}")
    else:
        print("   All risk limits within acceptable ranges")
    
    print("\n✅ Working Risk Management System operational!")
    print("   - Position validation working")
    print("   - Optimal sizing calculated")
    print("   - Risk monitoring active")
    print("   - Emergency stops configured")

if __name__ == "__main__":
    main()
