#!/usr/bin/env python3
"""
Robust Error Handling System
"""

import logging
import traceback
import time
from datetime import datetime
from typing import Optional, Callable, Any
from functools import wraps

class RobustErrorHandler:
    def __init__(self, log_file: str = "system_errors.log"):
        self.setup_logging(log_file)
        self.error_counts = {}
        self.recovery_strategies = {}
    
    def setup_logging(self, log_file: str):
        """Setup comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def handle_error(self, error: Exception, context: str = "Unknown") -> bool:
        """Handle errors with logging and recovery attempts"""
        error_type = type(error).__name__
        error_msg = str(error)
        
        # Log the error
        self.logger.error(f"Error in {context}: {error_type} - {error_msg}")
        self.logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Count errors
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Attempt recovery if strategy exists
        if error_type in self.recovery_strategies:
            try:
                recovery_func = self.recovery_strategies[error_type]
                recovery_func(error, context)
                self.logger.info(f"Recovery attempted for {error_type}")
                return True
            except Exception as recovery_error:
                self.logger.error(f"Recovery failed: {recovery_error}")
        
        return False
    
    def register_recovery_strategy(self, error_type: str, recovery_func: Callable):
        """Register a recovery strategy for specific error types"""
        self.recovery_strategies[error_type] = recovery_func
    
    def safe_execute(self, func: Callable, *args, max_retries: int = 3, **kwargs) -> tuple[bool, Any]:
        """Safely execute a function with retries"""
        for attempt in range(max_retries):
            try:
                result = func(*args, **kwargs)
                return True, result
            except Exception as e:
                self.handle_error(e, f"{func.__name__} (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
        
        return False, None
    
    def error_resistant_decorator(self, max_retries: int = 3):
        """Decorator to make functions error-resistant"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                success, result = self.safe_execute(func, *args, max_retries=max_retries, **kwargs)
                if not success:
                    self.logger.warning(f"Function {func.__name__} failed after {max_retries} attempts")
                return result
            return wrapper
        return decorator

# Global error handler
error_handler = RobustErrorHandler()

# Recovery strategies
def unicode_error_recovery(error, context):
    """Recovery strategy for unicode errors"""
    import os
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

def timeout_error_recovery(error, context):
    """Recovery strategy for timeout errors"""
    time.sleep(5)  # Wait before retry

# Register recovery strategies
error_handler.register_recovery_strategy('UnicodeDecodeError', unicode_error_recovery)
error_handler.register_recovery_strategy('TimeoutExpired', timeout_error_recovery)
