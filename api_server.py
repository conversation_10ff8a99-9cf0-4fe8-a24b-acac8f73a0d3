#!/usr/bin/env python3
"""
Noryon API Server
RESTful API interface for the Noryon AI Trading System

This module provides:
- RESTful API endpoints for all system components
- Real-time WebSocket connections for live data
- Authentication and authorization
- API documentation with OpenAPI/Swagger
- Rate limiting and security features
- Comprehensive error handling
- Request/response logging
- Health monitoring endpoints
"""

import asyncio
import json
import logging
import os
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import warnings
warnings.filterwarnings('ignore')

# FastAPI and related imports
try:
    from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, Request, Response
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.middleware.trustedhost import TrustedHostMiddleware
    from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
    from fastapi.responses import JSONResponse, HTMLResponse
    from fastapi.staticfiles import StaticFiles
    from fastapi.openapi.docs import get_swagger_ui_html
    from fastapi.openapi.utils import get_openapi
    import uvicorn
    from pydantic import BaseModel, Field
    from slowapi import Limiter, _rate_limit_exceeded_handler
    from slowapi.util import get_remote_address
    from slowapi.errors import RateLimitExceeded
    FASTAPI_AVAILABLE = True
except ImportError:
    logging.warning("FastAPI not available, API server will not be functional")
    FASTAPI_AVAILABLE = False
    # Create dummy classes for type hints
    class BaseModel:
        pass
    class Field:
        pass

# Import system components
try:
    from system_integration import SystemOrchestrator, SystemState, ComponentState
    SYSTEM_INTEGRATION_AVAILABLE = True
except ImportError:
    logging.warning("System integration not available")
    SYSTEM_INTEGRATION_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Pydantic models for API requests/responses
if FASTAPI_AVAILABLE:
    
    class SystemStatusResponse(BaseModel):
        """System status response model"""
        system: Dict[str, Any]
        components: Dict[str, Any]
        metrics: Dict[str, Any]
        events: Dict[str, Any]
        timestamp: str
    
    class ComponentActionRequest(BaseModel):
        """Component action request model"""
        action: str = Field(..., description="Action to perform (start, stop, restart)")
        component: str = Field(..., description="Component name")
    
    class TrainingRequest(BaseModel):
        """Training request model"""
        models: Optional[List[str]] = Field(None, description="Specific models to train")
        parallel: bool = Field(False, description="Enable parallel training")
        gpu_count: int = Field(1, description="Number of GPUs to use")
        experiment_name: Optional[str] = Field(None, description="MLflow experiment name")
        config_overrides: Optional[Dict[str, Any]] = Field(None, description="Configuration overrides")
    
    class BacktestRequest(BaseModel):
        """Backtest request model"""
        strategy: str = Field(..., description="Strategy name")
        start_date: str = Field(..., description="Start date (YYYY-MM-DD)")
        end_date: str = Field(..., description="End date (YYYY-MM-DD)")
        initial_capital: float = Field(100000, description="Initial capital")
        symbols: List[str] = Field(["AAPL", "GOOGL", "MSFT"], description="Trading symbols")
        config: Optional[Dict[str, Any]] = Field(None, description="Strategy configuration")
    
    class DeploymentRequest(BaseModel):
        """Deployment request model"""
        strategy: str = Field("blue_green", description="Deployment strategy")
        environment: str = Field("staging", description="Target environment")
        model_version: Optional[str] = Field(None, description="Model version to deploy")
        config: Optional[Dict[str, Any]] = Field(None, description="Deployment configuration")
    
    class RiskLimitsRequest(BaseModel):
        """Risk limits update request model"""
        max_position_size: Optional[float] = Field(None, description="Maximum position size")
        max_portfolio_risk: Optional[float] = Field(None, description="Maximum portfolio risk")
        max_daily_loss: Optional[float] = Field(None, description="Maximum daily loss")
        max_drawdown: Optional[float] = Field(None, description="Maximum drawdown")
    
    class TradeRequest(BaseModel):
        """Trade request model"""
        symbol: str = Field(..., description="Trading symbol")
        action: str = Field(..., description="Trade action (buy, sell)")
        quantity: float = Field(..., description="Trade quantity")
        price: Optional[float] = Field(None, description="Trade price (market if None)")
        order_type: str = Field("market", description="Order type")
    
    class ConfigUpdateRequest(BaseModel):
        """Configuration update request model"""
        component: str = Field(..., description="Component name")
        config: Dict[str, Any] = Field(..., description="Configuration updates")
    
    class AlertRequest(BaseModel):
        """Alert configuration request model"""
        alert_type: str = Field(..., description="Alert type")
        threshold: float = Field(..., description="Alert threshold")
        enabled: bool = Field(True, description="Enable alert")
        notification_channels: List[str] = Field(["email"], description="Notification channels")

# Rate limiting
if FASTAPI_AVAILABLE:
    limiter = Limiter(key_func=get_remote_address)

# WebSocket connection manager
class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_info: Dict[WebSocket, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, client_info: Dict[str, Any] = None):
        """Accept WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_info[websocket] = client_info or {}
        logger.info(f"WebSocket connected: {len(self.active_connections)} active connections")
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            self.connection_info.pop(websocket, None)
            logger.info(f"WebSocket disconnected: {len(self.active_connections)} active connections")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            self.disconnect(websocket)
    
    async def broadcast(self, message: str):
        """Broadcast message to all connected WebSockets"""
        disconnected = []
        
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                disconnected.append(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
    
    async def broadcast_json(self, data: Dict[str, Any]):
        """Broadcast JSON data to all connected WebSockets"""
        message = json.dumps(data, default=str)
        await self.broadcast(message)

# Authentication (simplified for demo)
class AuthManager:
    """Simple authentication manager"""
    
    def __init__(self):
        # In production, use proper authentication with JWT, OAuth, etc.
        self.api_keys = {
            "demo_key_123": {
                "name": "Demo User",
                "permissions": ["read", "write", "admin"],
                "rate_limit": 1000
            }
        }
    
    def verify_api_key(self, api_key: str) -> Optional[Dict[str, Any]]:
        """Verify API key"""
        return self.api_keys.get(api_key)
    
    def check_permission(self, user_info: Dict[str, Any], required_permission: str) -> bool:
        """Check if user has required permission"""
        return required_permission in user_info.get("permissions", [])

# API Server class
class NoryonAPIServer:
    """Noryon API Server"""
    
    def __init__(self, system_orchestrator: Optional['SystemOrchestrator'] = None):
        if not FASTAPI_AVAILABLE:
            raise ImportError("FastAPI is required for API server")
        
        self.system = system_orchestrator
        self.app = FastAPI(
            title="Noryon AI Trading System API",
            description="RESTful API for the Noryon AI Trading System",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        self.connection_manager = ConnectionManager()
        self.auth_manager = AuthManager()
        self.security = HTTPBearer()
        
        # Setup middleware
        self.setup_middleware()
        
        # Setup routes
        self.setup_routes()
        
        # Background tasks
        self.broadcast_task = None
        self.metrics_task = None
    
    def setup_middleware(self):
        """Setup FastAPI middleware"""
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Trusted host middleware
        self.app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # Configure appropriately for production
        )
        
        # Rate limiting
        self.app.state.limiter = limiter
        self.app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
        
        # Request logging middleware
        @self.app.middleware("http")
        async def log_requests(request: Request, call_next):
            start_time = time.time()
            response = await call_next(request)
            process_time = time.time() - start_time
            
            logger.info(
                f"{request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {process_time:.3f}s"
            )
            
            return response
    
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """Get current authenticated user"""
        api_key = credentials.credentials
        user_info = self.auth_manager.verify_api_key(api_key)
        
        if not user_info:
            raise HTTPException(status_code=401, detail="Invalid API key")
        
        return user_info
    
    def require_permission(self, permission: str):
        """Decorator to require specific permission"""
        def permission_checker(user_info: Dict[str, Any] = Depends(self.get_current_user)):
            if not self.auth_manager.check_permission(user_info, permission):
                raise HTTPException(status_code=403, detail=f"Permission '{permission}' required")
            return user_info
        
        return permission_checker
    
    def setup_routes(self):
        """Setup API routes"""
        
        # Health check endpoint (no auth required)
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        
        # System status
        @self.app.get("/api/v1/system/status", response_model=SystemStatusResponse)
        @limiter.limit("30/minute")
        async def get_system_status(request: Request, user: Dict = Depends(self.require_permission("read"))):
            """Get comprehensive system status"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                status = await self.system.get_system_status()
                return SystemStatusResponse(**status)
            except Exception as e:
                logger.error(f"Error getting system status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Component management
        @self.app.post("/api/v1/components/action")
        @limiter.limit("10/minute")
        async def component_action(request: Request, action_request: ComponentActionRequest, 
                                 user: Dict = Depends(self.require_permission("write"))):
            """Perform action on component"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                result = await self.system.execute_command(
                    f"{action_request.action}_component",
                    component=action_request.component
                )
                
                if result.get('success'):
                    return {"message": f"Component {action_request.component} {action_request.action} successful"}
                else:
                    raise HTTPException(status_code=400, detail=result.get('error', 'Unknown error'))
                    
            except Exception as e:
                logger.error(f"Error performing component action: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Training endpoints
        @self.app.post("/api/v1/training/start")
        @limiter.limit("5/minute")
        async def start_training(request: Request, training_request: TrainingRequest,
                               user: Dict = Depends(self.require_permission("write"))):
            """Start model training"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # Get training orchestrator component
                if 'training_orchestrator' not in self.system.component_manager.components:
                    raise HTTPException(status_code=404, detail="Training orchestrator not available")
                
                orchestrator = self.system.component_manager.components['training_orchestrator'].instance
                
                # Start training (this would be implemented in the orchestrator)
                result = {
                    "message": "Training started",
                    "experiment_name": training_request.experiment_name or "default",
                    "models": training_request.models or ["all"],
                    "parallel": training_request.parallel,
                    "gpu_count": training_request.gpu_count
                }
                
                return result
                
            except Exception as e:
                logger.error(f"Error starting training: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/training/status")
        @limiter.limit("60/minute")
        async def get_training_status(request: Request, user: Dict = Depends(self.require_permission("read"))):
            """Get training status"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would get actual training status from the orchestrator
                return {
                    "status": "idle",
                    "current_model": None,
                    "progress": 0.0,
                    "estimated_completion": None,
                    "last_training": None
                }
                
            except Exception as e:
                logger.error(f"Error getting training status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Backtesting endpoints
        @self.app.post("/api/v1/backtest/run")
        @limiter.limit("5/minute")
        async def run_backtest(request: Request, backtest_request: BacktestRequest,
                             user: Dict = Depends(self.require_permission("write"))):
            """Run backtest"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # Get backtest engine component
                if 'backtest_engine' not in self.system.component_manager.components:
                    raise HTTPException(status_code=404, detail="Backtest engine not available")
                
                # This would run actual backtest
                backtest_id = str(uuid.uuid4())
                
                return {
                    "backtest_id": backtest_id,
                    "message": "Backtest started",
                    "strategy": backtest_request.strategy,
                    "period": f"{backtest_request.start_date} to {backtest_request.end_date}",
                    "symbols": backtest_request.symbols
                }
                
            except Exception as e:
                logger.error(f"Error running backtest: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/v1/backtest/{backtest_id}/results")
        @limiter.limit("30/minute")
        async def get_backtest_results(request: Request, backtest_id: str,
                                     user: Dict = Depends(self.require_permission("read"))):
            """Get backtest results"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would get actual backtest results
                return {
                    "backtest_id": backtest_id,
                    "status": "completed",
                    "results": {
                        "total_return": 0.15,
                        "sharpe_ratio": 1.2,
                        "max_drawdown": -0.08,
                        "win_rate": 0.65,
                        "total_trades": 150
                    },
                    "performance_chart": "base64_encoded_chart_data"
                }
                
            except Exception as e:
                logger.error(f"Error getting backtest results: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Risk management endpoints
        @self.app.get("/api/v1/risk/status")
        @limiter.limit("60/minute")
        async def get_risk_status(request: Request, user: Dict = Depends(self.require_permission("read"))):
            """Get risk management status"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would get actual risk status
                return {
                    "portfolio_risk": 0.03,
                    "var_1d": -0.02,
                    "max_drawdown": -0.05,
                    "position_count": 12,
                    "circuit_breaker_active": False,
                    "risk_alerts": []
                }
                
            except Exception as e:
                logger.error(f"Error getting risk status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/risk/limits")
        @limiter.limit("10/minute")
        async def update_risk_limits(request: Request, limits_request: RiskLimitsRequest,
                                   user: Dict = Depends(self.require_permission("admin"))):
            """Update risk limits"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would update actual risk limits
                return {
                    "message": "Risk limits updated successfully",
                    "updated_limits": limits_request.dict(exclude_none=True)
                }
                
            except Exception as e:
                logger.error(f"Error updating risk limits: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Performance analytics endpoints
        @self.app.get("/api/v1/performance/dashboard")
        @limiter.limit("60/minute")
        async def get_performance_dashboard(request: Request, user: Dict = Depends(self.require_permission("read"))):
            """Get performance dashboard data"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would get actual performance data
                return {
                    "portfolio_value": 1050000,
                    "daily_return": 0.012,
                    "total_return": 0.05,
                    "sharpe_ratio": 1.35,
                    "sortino_ratio": 1.8,
                    "max_drawdown": -0.08,
                    "win_rate": 0.68,
                    "profit_factor": 1.45,
                    "recent_trades": [],
                    "model_performance": {}
                }
                
            except Exception as e:
                logger.error(f"Error getting performance dashboard: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Deployment endpoints
        @self.app.post("/api/v1/deployment/deploy")
        @limiter.limit("5/minute")
        async def deploy_model(request: Request, deployment_request: DeploymentRequest,
                             user: Dict = Depends(self.require_permission("admin"))):
            """Deploy model"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                result = await self.system.execute_command(
                    "deploy",
                    **deployment_request.dict()
                )
                
                if result.get('success'):
                    return {
                        "message": "Deployment started",
                        "deployment_id": str(uuid.uuid4()),
                        "strategy": deployment_request.strategy,
                        "environment": deployment_request.environment
                    }
                else:
                    raise HTTPException(status_code=400, detail=result.get('error', 'Deployment failed'))
                    
            except Exception as e:
                logger.error(f"Error deploying model: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # Configuration endpoints
        @self.app.get("/api/v1/config")
        @limiter.limit("30/minute")
        async def get_configuration(request: Request, user: Dict = Depends(self.require_permission("read"))):
            """Get system configuration"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                return self.system.config
                
            except Exception as e:
                logger.error(f"Error getting configuration: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.post("/api/v1/config/update")
        @limiter.limit("5/minute")
        async def update_configuration(request: Request, config_request: ConfigUpdateRequest,
                                     user: Dict = Depends(self.require_permission("admin"))):
            """Update component configuration"""
            if not self.system:
                raise HTTPException(status_code=503, detail="System not available")
            
            try:
                # This would update actual configuration
                return {
                    "message": f"Configuration updated for {config_request.component}",
                    "component": config_request.component,
                    "updated_config": config_request.config
                }
                
            except Exception as e:
                logger.error(f"Error updating configuration: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        # WebSocket endpoint for real-time data
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket, api_key: Optional[str] = None):
            """WebSocket endpoint for real-time data"""
            # Simple authentication for WebSocket
            if api_key:
                user_info = self.auth_manager.verify_api_key(api_key)
                if not user_info:
                    await websocket.close(code=1008, reason="Invalid API key")
                    return
            else:
                user_info = {"name": "Anonymous", "permissions": ["read"]}
            
            await self.connection_manager.connect(websocket, user_info)
            
            try:
                while True:
                    # Keep connection alive and handle incoming messages
                    data = await websocket.receive_text()
                    message_data = json.loads(data)
                    
                    # Handle different message types
                    if message_data.get("type") == "subscribe":
                        # Handle subscription to specific data feeds
                        await self.connection_manager.send_personal_message(
                            json.dumps({
                                "type": "subscription_confirmed",
                                "feed": message_data.get("feed"),
                                "timestamp": datetime.now().isoformat()
                            }),
                            websocket
                        )
                    
            except WebSocketDisconnect:
                self.connection_manager.disconnect(websocket)
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                self.connection_manager.disconnect(websocket)
        
        # Custom OpenAPI schema
        def custom_openapi():
            if self.app.openapi_schema:
                return self.app.openapi_schema
            
            openapi_schema = get_openapi(
                title="Noryon AI Trading System API",
                version="1.0.0",
                description="Comprehensive API for the Noryon AI Trading System",
                routes=self.app.routes,
            )
            
            # Add security scheme
            openapi_schema["components"]["securitySchemes"] = {
                "BearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "description": "API Key authentication"
                }
            }
            
            self.app.openapi_schema = openapi_schema
            return self.app.openapi_schema
        
        self.app.openapi = custom_openapi
    
    async def start_background_tasks(self):
        """Start background tasks"""
        # Real-time data broadcasting
        async def broadcast_system_data():
            while True:
                try:
                    if self.system and len(self.connection_manager.active_connections) > 0:
                        # Get system status and broadcast to WebSocket clients
                        status = await self.system.get_system_status()
                        
                        await self.connection_manager.broadcast_json({
                            "type": "system_update",
                            "data": status,
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    await asyncio.sleep(5)  # Broadcast every 5 seconds
                    
                except Exception as e:
                    logger.error(f"Error in broadcast task: {e}")
                    await asyncio.sleep(5)
        
        self.broadcast_task = asyncio.create_task(broadcast_system_data())
    
    async def stop_background_tasks(self):
        """Stop background tasks"""
        if self.broadcast_task:
            self.broadcast_task.cancel()
        if self.metrics_task:
            self.metrics_task.cancel()
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Start the API server"""
        logger.info(f"Starting Noryon API Server on {host}:{port}")
        
        # Start background tasks
        await self.start_background_tasks()
        
        # Configure uvicorn
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        
        server = uvicorn.Server(config)
        
        try:
            await server.serve()
        finally:
            await self.stop_background_tasks()

# Example usage
async def main():
    """Main function for testing"""
    if not FASTAPI_AVAILABLE:
        logger.error("FastAPI not available, cannot start API server")
        return
    
    # Initialize system orchestrator
    if SYSTEM_INTEGRATION_AVAILABLE:
        system = SystemOrchestrator()
        await system.start()
    else:
        system = None
        logger.warning("System integration not available, API will have limited functionality")
    
    # Create and start API server
    api_server = NoryonAPIServer(system)
    
    try:
        await api_server.start_server()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down")
    finally:
        if system:
            await system.shutdown()

if __name__ == "__main__":
    asyncio.run(main())