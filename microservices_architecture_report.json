{"demonstration_timestamp": "2025-06-06T00:32:30.355486", "phase": "Phase 1: Microservices Architecture", "architecture_type": "Microservices with Fallback", "initialization_metrics": {"initialization_time": 53.63330435752869, "services_initialized": 4, "fallback_available": true}, "operation_metrics": {"total_operations": 8, "successful_operations": 8, "success_rate": 100.0, "total_execution_time": 41.98, "avg_execution_time": 5.25, "microservices_operations": 8, "fallback_operations": 0, "microservices_usage_rate": 100.0}, "service_health": {"orchestrator_health": {"orchestrator": "microservices-orchestrator", "status": "healthy", "microservices_health": {"ai_agent_orchestration": {"status": "healthy", "last_check": "2025-06-06 00:32:30.354487"}, "technical_analysis": {"status": "healthy", "last_check": "2025-06-06 00:32:30.354487"}, "advanced_features": {"status": "healthy", "last_check": "2025-06-06 00:32:30.354487"}, "data_management": {"status": "healthy", "last_check": "2025-06-06 00:32:30.354487"}}, "fallback_available": false, "metrics": {"total_requests": 8, "successful_requests": 8, "microservices_requests": 8, "fallback_requests": 0, "avg_execution_time": 5.247223258018494, "total_execution_time": 41.97778606414795}, "timestamp": "2025-06-06T00:32:30.354487"}, "service_health": {"ai_agent_orchestration": {"service": "ai-agent-orchestration", "status": "healthy", "total_agents": 16, "healthy_agents": 16, "fathom_r1_active": true, "message_queue_connected": false, "timestamp": "2025-06-06T00:32:30.354487"}, "technical_analysis": {"service": "technical-analysis", "status": "healthy", "data_circuit_breaker": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "message_queue_connected": false, "cache_enabled": true, "indicators_available": 20, "timestamp": "2025-06-06T00:32:30.354487"}, "advanced_features": {"service": "advanced-features", "status": "healthy", "circuit_breakers": {"news_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "social_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "economic_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "options_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "institutional_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}}, "open_breakers": [], "message_queue_connected": false, "features_available": ["news_analysis", "social_sentiment", "economic_calendar", "options_flow", "institutional_flow", "multi_timeframe", "pattern_recognition"], "timestamp": "2025-06-06T00:32:30.354487"}, "data_management": {"service": "data-management", "status": "healthy", "total_databases": 28, "healthy_databases": 28, "unhealthy_databases": [], "message_queue_connected": false, "timestamp": "2025-06-06T00:32:30.354487"}}, "microservices_healthy": true}, "circuit_breaker_status": {"ai_service": {"total_agents": 16, "healthy_agents": 16}, "ta_service": {"data_circuit_breaker": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}}, "features_service": {"circuit_breakers": {"news_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "social_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "economic_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "options_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}, "institutional_api": {"state": "closed", "failure_count": 0, "failure_threshold": 3, "last_failure_time": null, "next_attempt_time": null, "time_until_retry": 0}}, "open_breakers": []}, "data_service": {"total_databases": 28, "healthy_databases": 28}}, "operations_details": {"BTC-USD": {"ultimate_analysis": {"success": true, "execution_time": 4.050379514694214, "services_used": 4, "fallback_used": false}, "agent_query": {"success": true, "execution_time": 0.9682810306549072, "services_used": 1, "fallback_used": false}, "technical_analysis": {"success": true, "execution_time": 2.213280439376831, "services_used": 1, "fallback_used": false}, "feature_analysis": {"success": true, "execution_time": 0.027733802795410156, "services_used": 1, "fallback_used": false}}, "AAPL": {"ultimate_analysis": {"success": true, "execution_time": 19.104846000671387, "services_used": 4, "fallback_used": false}, "agent_query": {"success": true, "execution_time": 13.400184869766235, "services_used": 1, "fallback_used": false}, "technical_analysis": {"success": true, "execution_time": 2.1706602573394775, "services_used": 1, "fallback_used": false}, "feature_analysis": {"success": true, "execution_time": 0.042420148849487305, "services_used": 1, "fallback_used": false}}}, "phase_1_achievements": {"microservices_converted": 4, "circuit_breakers_implemented": true, "async_message_handling": true, "structured_logging": true, "fallback_system": true, "service_health_monitoring": true}}