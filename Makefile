# Noryon AI Trading System - Makefile
# Automation for development, testing, and deployment tasks

.PHONY: help install dev-install test lint format clean build docker deploy monitor docs

# Default target
help:
	@echo "Noryon AI Trading System - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  install        Install production dependencies"
	@echo "  dev-install    Install development dependencies"
	@echo "  setup          Run initial setup"
	@echo "  clean          Clean build artifacts and cache"
	@echo ""
	@echo "Code Quality:"
	@echo "  test           Run all tests"
	@echo "  test-unit      Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  lint           Run code linting"
	@echo "  format         Format code with black and isort"
	@echo "  type-check     Run type checking with mypy"
	@echo ""
	@echo "System Operations:"
	@echo "  start          Start the system"
	@echo "  stop           Stop the system"
	@echo "  status         Check system status"
	@echo "  logs           View system logs"
	@echo ""
	@echo "Training & Backtesting:"
	@echo "  train          Start model training"
	@echo "  backtest       Run backtesting"
	@echo "  optimize       Run hyperparameter optimization"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build   Build Docker images"
	@echo "  docker-up      Start Docker services"
	@echo "  docker-down    Stop Docker services"
	@echo "  docker-logs    View Docker logs"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-dev     Deploy to development"
	@echo "  deploy-staging Deploy to staging"
	@echo "  deploy-prod    Deploy to production"
	@echo "  rollback       Rollback deployment"
	@echo ""
	@echo "Monitoring:"
	@echo "  monitor        Start monitoring dashboard"
	@echo "  alerts         Check active alerts"
	@echo "  metrics        View system metrics"
	@echo ""
	@echo "Documentation:"
	@echo "  docs           Generate documentation"
	@echo "  docs-serve     Serve documentation locally"

# Variables
PYTHON := python
PIP := pip
DOCKER := docker
DOCKER_COMPOSE := docker-compose
CLI := $(PYTHON) cli.py

# Development Setup
install:
	@echo "Installing production dependencies..."
	$(PIP) install -r requirements.txt

dev-install:
	@echo "Installing development dependencies..."
	$(PIP) install -r requirements.txt
	$(PIP) install pytest pytest-asyncio pytest-cov black isort mypy flake8 sphinx
	$(PIP) install -e .

setup: dev-install
	@echo "Running initial setup..."
	$(PYTHON) setup.py setup
	@echo "Creating default configuration..."
	$(CLI) init
	@echo "Validating configuration..."
	$(CLI) validate

clean:
	@echo "Cleaning build artifacts and cache..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/ dist/ .pytest_cache/ .coverage htmlcov/
	rm -rf logs/*.log

# Code Quality
test:
	@echo "Running all tests..."
	pytest tests/ -v --cov=. --cov-report=html --cov-report=term

test-unit:
	@echo "Running unit tests..."
	pytest tests/unit/ -v

test-integration:
	@echo "Running integration tests..."
	pytest tests/integration/ -v

test-system:
	@echo "Running system tests..."
	$(PYTHON) test_system.py --test-type all

lint:
	@echo "Running code linting..."
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

format:
	@echo "Formatting code..."
	black . --line-length 127
	isort . --profile black

type-check:
	@echo "Running type checking..."
	mypy . --ignore-missing-imports

# System Operations
start:
	@echo "Starting Noryon system..."
	$(CLI) system start

stop:
	@echo "Stopping Noryon system..."
	$(CLI) system stop

status:
	@echo "Checking system status..."
	$(CLI) system status

logs:
	@echo "Viewing system logs..."
	tail -f logs/system.log

api-start:
	@echo "Starting API server only..."
	$(PYTHON) main.py --mode api

# Training & Backtesting
train:
	@echo "Starting model training..."
	$(CLI) training start --model ensemble --epochs 100

train-lstm:
	@echo "Training LSTM model..."
	$(CLI) training start --model lstm --epochs 50

train-transformer:
	@echo "Training Transformer model..."
	$(CLI) training start --model transformer --epochs 30

backtest:
	@echo "Running backtesting..."
	$(CLI) backtest run --strategy momentum --start 2023-01-01

backtest-full:
	@echo "Running comprehensive backtesting..."
	$(CLI) backtest run --strategy ensemble --start 2022-01-01 --initial-capital 1000000

optimize:
	@echo "Running hyperparameter optimization..."
	$(PYTHON) main.py --mode training --component genetic_optimizer

# Docker Operations
docker-build:
	@echo "Building Docker images..."
	$(DOCKER_COMPOSE) build

docker-up:
	@echo "Starting Docker services..."
	$(DOCKER_COMPOSE) up -d

docker-down:
	@echo "Stopping Docker services..."
	$(DOCKER_COMPOSE) down

docker-logs:
	@echo "Viewing Docker logs..."
	$(DOCKER_COMPOSE) logs -f

docker-restart:
	@echo "Restarting Docker services..."
	$(DOCKER_COMPOSE) restart

docker-clean:
	@echo "Cleaning Docker resources..."
	$(DOCKER) system prune -f
	$(DOCKER) volume prune -f

# Deployment
deploy-dev:
	@echo "Deploying to development environment..."
	$(CLI) deploy start --env development

deploy-staging:
	@echo "Deploying to staging environment..."
	$(CLI) deploy start --env staging

deploy-prod:
	@echo "Deploying to production environment..."
	$(CLI) deploy start --env production --force

rollback:
	@echo "Rolling back deployment..."
	$(PYTHON) deploy.py rollback

deploy-status:
	@echo "Checking deployment status..."
	$(CLI) deploy status

# Monitoring
monitor:
	@echo "Starting monitoring dashboard..."
	$(PYTHON) monitoring.py

alerts:
	@echo "Checking active alerts..."
	$(CLI) monitor alerts

metrics:
	@echo "Viewing system metrics..."
	$(CLI) monitor metrics

health-check:
	@echo "Running health checks..."
	curl -f http://localhost:8000/health || exit 1

# Data Management
data-fetch:
	@echo "Fetching market data..."
	$(CLI) data fetch --symbols AAPL,GOOGL,MSFT,TSLA --start 2023-01-01

data-status:
	@echo "Checking data status..."
	$(CLI) data status

data-clean:
	@echo "Cleaning old data..."
	rm -rf data/cache/*
	rm -rf data/temp/*

# Database Operations
db-migrate:
	@echo "Running database migrations..."
	$(PYTHON) -c "from database_manager import DatabaseManager; import asyncio; asyncio.run(DatabaseManager().migrate())"

db-seed:
	@echo "Seeding database with sample data..."
	$(PYTHON) -c "from database_manager import DatabaseManager; import asyncio; asyncio.run(DatabaseManager().seed_sample_data())"

db-backup:
	@echo "Creating database backup..."
	mkdir -p backups
	pg_dump $(DATABASE_URL) > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql

db-restore:
	@echo "Restoring database from backup..."
	@echo "Usage: make db-restore BACKUP_FILE=backups/backup_YYYYMMDD_HHMMSS.sql"
	@if [ -z "$(BACKUP_FILE)" ]; then echo "Error: BACKUP_FILE not specified"; exit 1; fi
	psql $(DATABASE_URL) < $(BACKUP_FILE)

# MLflow Operations
mlflow-ui:
	@echo "Starting MLflow UI..."
	mlflow ui --host 0.0.0.0 --port 5000

mlflow-clean:
	@echo "Cleaning MLflow artifacts..."
	rm -rf mlruns/*

# Performance Testing
perf-test:
	@echo "Running performance tests..."
	$(PYTHON) test_system.py --test-type performance

load-test:
	@echo "Running load tests..."
	locust -f tests/load_test.py --host=http://localhost:8000

benchmark:
	@echo "Running system benchmarks..."
	$(PYTHON) -c "from performance_analytics import PerformanceAnalytics; import asyncio; asyncio.run(PerformanceAnalytics().run_benchmark())"

# Security
security-scan:
	@echo "Running security scan..."
	bandit -r . -f json -o security_report.json
	dependency-check --project "Noryon AI Trading" --scan . --format JSON --out dependency_report.json

vuln-check:
	@echo "Checking for vulnerabilities..."
	safety check

# Documentation
docs:
	@echo "Generating documentation..."
	sphinx-build -b html docs/ docs/_build/html

docs-serve:
	@echo "Serving documentation locally..."
	cd docs/_build/html && python -m http.server 8080

docs-clean:
	@echo "Cleaning documentation build..."
	rm -rf docs/_build/

# Configuration Management
config-validate:
	@echo "Validating configuration..."
	$(CLI) validate

config-template:
	@echo "Generating configuration template..."
	$(CLI) init --output config_template.yaml

config-encrypt:
	@echo "Encrypting sensitive configuration..."
	@echo "Usage: make config-encrypt CONFIG_FILE=config.yaml"
	@if [ -z "$(CONFIG_FILE)" ]; then echo "Error: CONFIG_FILE not specified"; exit 1; fi
	gpg --symmetric --cipher-algo AES256 $(CONFIG_FILE)

config-decrypt:
	@echo "Decrypting configuration..."
	@echo "Usage: make config-decrypt ENCRYPTED_FILE=config.yaml.gpg"
	@if [ -z "$(ENCRYPTED_FILE)" ]; then echo "Error: ENCRYPTED_FILE not specified"; exit 1; fi
	gpg --decrypt $(ENCRYPTED_FILE) > $(basename $(ENCRYPTED_FILE) .gpg)

# Maintenance
maintenance-start:
	@echo "Entering maintenance mode..."
	touch maintenance.lock
	$(CLI) system stop

maintenance-end:
	@echo "Exiting maintenance mode..."
	rm -f maintenance.lock
	$(CLI) system start

update-deps:
	@echo "Updating dependencies..."
	$(PIP) list --outdated --format=freeze | grep -v '^\-e' | cut -d = -f 1 | xargs -n1 $(PIP) install -U
	$(PIP) freeze > requirements.txt

# Backup and Recovery
backup-full:
	@echo "Creating full system backup..."
	mkdir -p backups/$(shell date +%Y%m%d_%H%M%S)
	cp -r config/ backups/$(shell date +%Y%m%d_%H%M%S)/
	cp -r models/ backups/$(shell date +%Y%m%d_%H%M%S)/
	cp -r data/ backups/$(shell date +%Y%m%d_%H%M%S)/
	make db-backup

restore-full:
	@echo "Restoring from backup..."
	@echo "Usage: make restore-full BACKUP_DIR=backups/YYYYMMDD_HHMMSS"
	@if [ -z "$(BACKUP_DIR)" ]; then echo "Error: BACKUP_DIR not specified"; exit 1; fi
	cp -r $(BACKUP_DIR)/config/ .
	cp -r $(BACKUP_DIR)/models/ .
	cp -r $(BACKUP_DIR)/data/ .

# Development Utilities
dev-reset:
	@echo "Resetting development environment..."
	make clean
	make docker-down
	make docker-clean
	rm -rf data/cache/* data/temp/* logs/*.log
	make setup

dev-seed:
	@echo "Seeding development environment..."
	make db-seed
	make data-fetch

dev-full-setup: dev-reset dev-seed
	@echo "Full development environment setup complete"

# CI/CD Helpers
ci-test:
	@echo "Running CI tests..."
	make lint
	make type-check
	make test
	make security-scan

ci-build:
	@echo "Running CI build..."
	make clean
	make install
	make docker-build

ci-deploy:
	@echo "Running CI deployment..."
	make deploy-staging
	make health-check

# Quick Commands
quick-start: setup docker-up
	@echo "Quick start complete - system is running"

quick-test: lint test
	@echo "Quick test complete"

quick-deploy: ci-test docker-build deploy-dev
	@echo "Quick deployment complete"

# Environment-specific targets
.env.development:
	@echo "Setting up development environment..."
	echo "ENVIRONMENT=development" > .env
	echo "DEBUG=true" >> .env
	echo "LOG_LEVEL=DEBUG" >> .env

.env.production:
	@echo "Setting up production environment..."
	echo "ENVIRONMENT=production" > .env
	echo "DEBUG=false" >> .env
	echo "LOG_LEVEL=INFO" >> .env

# Help for specific sections
help-docker:
	@echo "Docker Commands:"
	@echo "  docker-build   Build all Docker images"
	@echo "  docker-up      Start all services in background"
	@echo "  docker-down    Stop all services"
	@echo "  docker-logs    Follow logs from all services"
	@echo "  docker-restart Restart all services"
	@echo "  docker-clean   Remove unused Docker resources"

help-deploy:
	@echo "Deployment Commands:"
	@echo "  deploy-dev     Deploy to development (localhost)"
	@echo "  deploy-staging Deploy to staging environment"
	@echo "  deploy-prod    Deploy to production (requires confirmation)"
	@echo "  rollback       Rollback to previous version"
	@echo "  deploy-status  Check current deployment status"

help-test:
	@echo "Testing Commands:"
	@echo "  test           Run all tests with coverage"
	@echo "  test-unit      Run only unit tests"
	@echo "  test-integration Run only integration tests"
	@echo "  test-system    Run system-level tests"
	@echo "  perf-test      Run performance tests"
	@echo "  load-test      Run load tests with Locust"
	@echo "  security-scan  Run security vulnerability scan"