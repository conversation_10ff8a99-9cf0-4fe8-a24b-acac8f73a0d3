# Noryon AI Trading System - .gitignore
# Comprehensive exclusion patterns for Python, ML, and trading system files

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to ignore the entire .idea directory.
.idea/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix

# Machine Learning / AI specific
# Model files
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.pth
*.pt
*.ckpt
*.safetensors
*.bin
*.onnx
*.tflite
*.mlmodel

# MLflow
mlruns/
mlartifacts/
.mlflow/

# Weights & Biases
wandb/

# TensorBoard
logs/tensorboard/

# Jupyter notebook checkpoints
.ipynb_checkpoints/

# Data files
*.csv
*.tsv
*.json
*.jsonl
*.parquet
*.feather
*.arrow
*.xlsx
*.xls
*.h5
*.hdf5
*.npz
*.npy

# Large data directories
data/raw/
data/processed/
data/external/
data/interim/
data/cache/
data/temp/
data/downloads/

# Keep data structure but ignore content
data/*
!data/.gitkeep
!data/README.md
!data/sample/

# Trading specific files
# API keys and secrets
*.key
*.pem
*.p12
*.pfx
api_keys.txt
secrets.yaml
secrets.json
credentials.json
.credentials

# Trading data
tick_data/
market_data/
historical_data/
real_time_data/

# Backtest results
backtest_results/
performance_reports/
trade_logs/

# Model artifacts
models/trained/
models/checkpoints/
models/artifacts/
models/exports/

# Keep model structure
models/*
!models/.gitkeep
!models/README.md
!models/base/
!models/configs/

# Configuration files with secrets
config/production.yaml
config/staging.yaml
config/secrets.yaml
config/local.yaml
.env.local
.env.production
.env.staging

# Database files
*.db
*.sqlite
*.sqlite3
database.url

# Log files
logs/
*.log
*.log.*

# Keep log structure
logs/*
!logs/.gitkeep
!logs/README.md

# Temporary files
tmp/
temp/
*.tmp
*.temp
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.kubeconfig
kube-config

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Monitoring and metrics
prometheus_data/
grafana_data/
metrics/
alerts/

# Backup files
backups/
*.backup
*.bak
*.old

# Reports and outputs
reports/
outputs/
results/
exports/

# Keep report structure
reports/*
!reports/.gitkeep
!reports/README.md
!reports/templates/

# Cache directories
.cache/
cache/
__pycache__/
.pytest_cache/
.mypy_cache/

# IDE and editor files
*.swp
*.swo
*~
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Profiling data
*.prof
*.profile

# Memory dumps
*.dump
*.dmp

# Core dumps
core
core.*

# Security scan results
security_report.json
dependency_report.json
vulnerability_report.json

# Performance test results
performance_results/
load_test_results/
benchmark_results/

# Deployment artifacts
deployment_logs/
rollback_data/

# Local development
local_config.yaml
dev_config.yaml
test_config.yaml

# Maintenance
maintenance.lock

# Custom exclusions for Noryon
# Add any project-specific files to ignore
user_strategies/
custom_indicators/
private_data/
production_logs/
live_trading_data/

# Encrypted files (keep encrypted versions)
*.gpg
*.enc

# But ignore decrypted versions
*.decrypted
*.plain

# API documentation generated files
api_docs/
openapi.json
swagger.json

# Localization
*.mo
*.pot

# Package files
*.deb
*.rpm
*.msi
*.dmg
*.pkg

# Archive files
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# But keep sample/template archives
!samples/*.zip
!templates/*.tar.gz

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python virtual environments
pyvenv.cfg

# Conda
.conda/
conda-meta/

# pip
pip-log.txt
pip-delete-this-directory.txt

# setuptools
*.egg-info/
dist/
build/

# wheel
*.whl

# Coverage
.coverage
.coverage.*
htmlcov/

# tox
.tox/

# Sphinx
_build/

# pytest
.pytest_cache/

# mypy
.mypy_cache/

# Bandit
.bandit

# Safety
.safety

# Local environment variables
.env.local
.env.*.local

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
Thumbs.db

# Custom patterns for this project
# Add any additional patterns specific to your setup

# End of .gitignore