#!/usr/bin/env python3
"""
Updated 9-Model Ensemble System
Integration of new Granite Vision model into production system
"""

import asyncio
import subprocess
from datetime import datetime
from typing import Dict, List
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

@dataclass
class ModelConfig:
    """Enhanced model configuration with new Granite Vision model"""
    name: str
    specialization: str
    weight: float
    timeout: int
    confidence_threshold: float
    capabilities: List[str]

class Updated9ModelEnsemble:
    """Updated ensemble system with 9 specialized models"""
    
    def __init__(self):
        # Updated model configuration including new Granite Vision model
        self.models = {
            "risk_assessment": ModelConfig(
                name="noryon-phi-4-9b-finance:latest",
                specialization="risk_assessment",
                weight=0.12,
                timeout=30,
                confidence_threshold=0.7,
                capabilities=["risk_analysis", "position_sizing", "stop_loss_calculation"]
            ),
            "market_analysis": ModelConfig(
                name="noryon-gemma-3-12b-finance:latest", 
                specialization="market_analysis",
                weight=0.12,
                timeout=30,
                confidence_threshold=0.7,
                capabilities=["trend_analysis", "market_timing", "sector_analysis"]
            ),
            "advanced_risk": ModelConfig(
                name="noryon-phi-4-9b-enhanced-enhanced:latest",
                specialization="advanced_risk_management",
                weight=0.11,
                timeout=35,
                confidence_threshold=0.75,
                capabilities=["var_calculation", "stress_testing", "portfolio_risk"]
            ),
            "enhanced_market": ModelConfig(
                name="noryon-gemma-3-12b-enhanced-enhanced:latest",
                specialization="enhanced_market_analysis",
                weight=0.11,
                timeout=35,
                confidence_threshold=0.75,
                capabilities=["institutional_analysis", "flow_analysis", "sentiment"]
            ),
            "multilingual": ModelConfig(
                name="noryon-qwen3-finance-v2:latest",
                specialization="multilingual_analysis",
                weight=0.12,
                timeout=40,
                confidence_threshold=0.8,
                capabilities=["global_markets", "currency_analysis", "international_news"]
            ),
            "cognitive": ModelConfig(
                name="noryon-cogito-finance-v2:latest",
                specialization="cognitive_analysis", 
                weight=0.12,
                timeout=40,
                confidence_threshold=0.8,
                capabilities=["behavioral_finance", "market_psychology", "sentiment_analysis"]
            ),
            "reasoning": ModelConfig(
                name="noryon-marco-o1-finance-v2:latest",
                specialization="step_by_step_reasoning",
                weight=0.10,
                timeout=45,
                confidence_threshold=0.85,
                capabilities=["logical_analysis", "systematic_evaluation", "decision_trees"]
            ),
            "efficient": ModelConfig(
                name="noryon-deepscaler-finance-v2:latest",
                specialization="efficient_analysis",
                weight=0.10,
                timeout=20,
                confidence_threshold=0.65,
                capabilities=["quick_analysis", "rapid_screening", "alert_generation"]
            ),
            # NEW: Granite Vision Model
            "visual_analysis": ModelConfig(
                name="noryon-granite-vision-finance-v1:latest",
                specialization="visual_market_analysis",
                weight=0.10,
                timeout=35,
                confidence_threshold=0.75,
                capabilities=["chart_analysis", "pattern_recognition", "technical_indicators", "visual_signals"]
            )
        }
        
        # Updated specialization priorities with visual analysis
        self.specialization_priorities = {
            "risk_assessment": 1.2,
            "market_analysis": 1.1,
            "advanced_risk_management": 1.3,
            "enhanced_market_analysis": 1.1,
            "multilingual_analysis": 1.0,
            "cognitive_analysis": 1.0,
            "step_by_step_reasoning": 1.1,
            "efficient_analysis": 0.9,
            "visual_market_analysis": 1.1  # NEW: High priority for visual analysis
        }
    
    def create_specialized_queries(self, symbol: str, market_data: Dict) -> Dict[str, str]:
        """Create specialized queries including visual analysis"""
        base_data = f"Symbol: {symbol}, Price: ${market_data.get('price', 'N/A')}, Volume: {market_data.get('volume', 'N/A')}"
        
        return {
            "risk_assessment": f"Assess the risk of trading {base_data}. Provide specific risk metrics, position sizing, and stop-loss recommendations.",
            "market_analysis": f"Analyze market conditions for {base_data}. Include technical indicators, trend analysis, and price targets.",
            "advanced_risk_management": f"Perform advanced risk analysis for {base_data}. Include VaR calculations, correlation analysis, and portfolio impact.",
            "enhanced_market_analysis": f"Conduct enhanced market analysis for {base_data}. Include multi-timeframe analysis and institutional flow data.",
            "multilingual_analysis": f"Analyze global market sentiment and international factors affecting {base_data}. Include currency and geopolitical impacts.",
            "cognitive_analysis": f"Analyze market psychology and behavioral factors for {base_data}. Include sentiment analysis and crowd behavior patterns.",
            "step_by_step_reasoning": f"Provide step-by-step logical analysis for trading {base_data}. Break down the decision process systematically.",
            "efficient_analysis": f"Provide quick, efficient analysis for {base_data}. Focus on key actionable insights and immediate trading opportunities.",
            # NEW: Visual analysis query
            "visual_market_analysis": f"Perform comprehensive visual chart analysis for {base_data}. Analyze chart patterns, support/resistance levels, technical indicators, volume patterns, and provide visual trading signals with specific entry/exit points."
        }
    
    async def query_model(self, model_config: ModelConfig, query: str) -> Dict:
        """Query individual model with enhanced error handling"""
        try:
            result = subprocess.run([
                'ollama', 'run', model_config.name, query
            ], capture_output=True, text=True, timeout=model_config.timeout)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                return self._parse_model_response(response, model_config.specialization)
            else:
                return None
                
        except subprocess.TimeoutExpired:
            return None
        except Exception:
            return None
    
    def _parse_model_response(self, response: str, specialization: str) -> Dict:
        """Enhanced response parsing with visual analysis support"""
        # Enhanced parsing for visual analysis
        if specialization == "visual_market_analysis":
            return {
                "action": self._extract_action(response),
                "confidence": self._extract_confidence(response),
                "price_target": self._extract_price_target(response),
                "stop_loss": self._extract_stop_loss(response),
                "position_size": 0.03,  # Default for visual analysis
                "reasoning": response[:300],
                "specialization": specialization,
                "visual_signals": self._extract_visual_signals(response)
            }
        else:
            # Standard parsing for other models
            return {
                "action": self._extract_action(response),
                "confidence": self._extract_confidence(response),
                "price_target": self._extract_price_target(response),
                "stop_loss": self._extract_stop_loss(response),
                "position_size": 0.03,
                "reasoning": response[:200],
                "specialization": specialization
            }
    
    def _extract_visual_signals(self, response: str) -> List[str]:
        """Extract visual trading signals from response"""
        signals = []
        response_lower = response.lower()
        
        # Pattern signals
        patterns = ["head and shoulders", "triangle", "flag", "pennant", "wedge", "channel"]
        for pattern in patterns:
            if pattern in response_lower:
                signals.append(f"Pattern: {pattern}")
        
        # Support/Resistance signals
        if "support" in response_lower:
            signals.append("Support level identified")
        if "resistance" in response_lower:
            signals.append("Resistance level identified")
        
        # Breakout signals
        if "breakout" in response_lower:
            signals.append("Breakout signal")
        if "breakdown" in response_lower:
            signals.append("Breakdown signal")
        
        return signals
    
    def _extract_action(self, response: str) -> str:
        """Extract trading action from response"""
        response_lower = response.lower()
        if any(word in response_lower for word in ["buy", "long", "bullish"]):
            return "BUY"
        elif any(word in response_lower for word in ["sell", "short", "bearish"]):
            return "SELL"
        else:
            return "HOLD"
    
    def _extract_confidence(self, response: str) -> float:
        """Extract confidence level from response"""
        # Simple confidence extraction based on language strength
        response_lower = response.lower()
        if any(word in response_lower for word in ["strong", "confident", "clear", "definite"]):
            return 0.85
        elif any(word in response_lower for word in ["likely", "probable", "expect"]):
            return 0.75
        elif any(word in response_lower for word in ["possible", "may", "could"]):
            return 0.65
        else:
            return 0.70
    
    def _extract_price_target(self, response: str) -> float:
        """Extract price target from response"""
        import re
        # Look for price patterns like $185, $180.50, etc.
        price_matches = re.findall(r'\$(\d+\.?\d*)', response)
        if price_matches:
            return float(price_matches[0])
        return 185.0  # Default target
    
    def _extract_stop_loss(self, response: str) -> float:
        """Extract stop loss from response"""
        import re
        # Look for stop loss mentions
        if "stop" in response.lower():
            price_matches = re.findall(r'\$(\d+\.?\d*)', response)
            if len(price_matches) > 1:
                return float(price_matches[1])
        return 175.0  # Default stop loss
    
    async def test_9_model_ensemble(self, symbol: str = "AAPL"):
        """Test the complete 9-model ensemble"""
        console.print(Panel(
            "[bold blue]🚀 Testing 9-Model Ensemble System[/bold blue]\n\n"
            f"Testing symbol: {symbol}\n"
            f"Total models: {len(self.models)}\n\n"
            "Specialized models:\n"
            "• Risk Assessment (2 models)\n"
            "• Market Analysis (2 models)\n"
            "• Multilingual Analysis\n"
            "• Cognitive Analysis\n"
            "• Step-by-Step Reasoning\n"
            "• Efficient Analysis\n"
            "• 🆕 Visual Market Analysis",
            title="9-Model Ensemble Test"
        ))
        
        # Mock market data
        market_data = {
            "price": 185.0,
            "volume": 50000000,
            "market_cap": 2800000000000,
            "pe_ratio": 28.5
        }
        
        # Create specialized queries
        queries = self.create_specialized_queries(symbol, market_data)
        
        # Test each model
        results = {}
        successful_models = 0
        
        for model_id, model_config in self.models.items():
            console.print(f"[yellow]🧪 Testing {model_config.specialization}...[/yellow]")
            
            query = queries.get(model_config.specialization, queries["market_analysis"])
            response = await self.query_model(model_config, query)
            
            if response:
                results[model_id] = response
                successful_models += 1
                console.print(f"[green]✅ {model_config.specialization}: {response['action']} (Confidence: {response['confidence']:.2f})[/green]")
                
                # Show visual signals for visual analysis model
                if model_config.specialization == "visual_market_analysis" and "visual_signals" in response:
                    signals = response["visual_signals"]
                    if signals:
                        console.print(f"[cyan]   📊 Visual Signals: {', '.join(signals[:3])}[/cyan]")
            else:
                console.print(f"[red]❌ {model_config.specialization}: Failed[/red]")
        
        return results, successful_models
    
    def generate_ensemble_report(self, results: Dict, successful_models: int):
        """Generate comprehensive ensemble report"""
        
        # Model performance table
        performance_table = Table(title="9-Model Ensemble Performance")
        performance_table.add_column("Model", style="cyan")
        performance_table.add_column("Specialization", style="yellow")
        performance_table.add_column("Status", style="green")
        performance_table.add_column("Action", style="blue")
        performance_table.add_column("Confidence", style="magenta")
        
        for model_id, model_config in self.models.items():
            if model_id in results:
                result = results[model_id]
                status = "✅ Active"
                action = result["action"]
                confidence = f"{result['confidence']:.2f}"
            else:
                status = "❌ Failed"
                action = "N/A"
                confidence = "N/A"
            
            performance_table.add_row(
                model_config.name.split("-")[-1].split(":")[0],
                model_config.specialization.replace("_", " ").title(),
                status,
                action,
                confidence
            )
        
        console.print(performance_table)
        
        # Ensemble summary
        success_rate = (successful_models / len(self.models)) * 100
        
        console.print(Panel(
            f"[bold green]🎉 9-Model Ensemble Test Complete![/bold green]\n\n"
            f"Total Models: {len(self.models)}\n"
            f"Successful Models: {successful_models}\n"
            f"Success Rate: {success_rate:.1f}%\n\n"
            f"✅ New Visual Analysis Integration: {'SUCCESS' if 'visual_analysis' in results else 'FAILED'}\n"
            f"🚀 System Status: {'PRODUCTION READY' if success_rate >= 80 else 'NEEDS ATTENTION'}\n\n"
            f"🎯 Enhanced Capabilities:\n"
            f"• Advanced Portfolio Management\n"
            f"• Multi-language Global Analysis\n"
            f"• Behavioral Finance Insights\n"
            f"• 🆕 Visual Chart Analysis",
            title="Ensemble Summary"
        ))
        
        return {
            "total_models": len(self.models),
            "successful_models": successful_models,
            "success_rate": success_rate,
            "visual_analysis_integrated": "visual_analysis" in results,
            "production_ready": success_rate >= 80
        }

async def main():
    """Main testing function"""
    console.print("[bold blue]🚀 Starting 9-Model Ensemble Testing...[/bold blue]\n")
    
    ensemble = Updated9ModelEnsemble()
    
    # Test the ensemble
    results, successful_models = await ensemble.test_9_model_ensemble()
    
    # Generate report
    summary = ensemble.generate_ensemble_report(results, successful_models)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    if summary["production_ready"]:
        console.print("1. ✅ Deploy 9-model ensemble to production")
        console.print("2. 🔄 Update risk management for visual signals")
        console.print("3. 📊 Monitor visual analysis performance")
        console.print("4. 🚀 Begin advanced multi-model trading strategies")
    else:
        console.print("1. 🔧 Address failed models")
        console.print("2. 🧪 Re-test ensemble system")
        console.print("3. 📋 Review model configurations")
    
    return summary

if __name__ == "__main__":
    results = asyncio.run(main())
