#!/usr/bin/env python3
"""
AI Enhancement: Memory & Learning System
ACTUAL working system that gives AI agents memory, learning, and performance tracking
"""

import sqlite3
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import subprocess

class AIMemoryLearningSystem:
    """REAL memory and learning enhancement for AI agents"""
    
    def __init__(self):
        self.agent_memories = {}
        self.learning_patterns = {}
        self.performance_history = {}
        
        # Setup database
        self._setup_database()
        
        print("🧠 AI MEMORY & LEARNING SYSTEM INITIALIZED")
        print("   💾 Agent memories: READY")
        print("   📈 Performance tracking: READY")
        print("   🎯 Pattern recognition: READY")
        print("   🔄 Adaptive learning: READY")
    
    def _setup_database(self):
        """Setup REAL database for AI memory and learning"""
        conn = sqlite3.connect('ai_memory_learning.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS agent_memories (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                memory_type TEXT,
                memory_content TEXT,
                importance_score REAL,
                created_time DATETIME,
                last_accessed DATETIME,
                access_count INTEGER
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trading_decisions (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                symbol TEXT,
                decision TEXT,
                confidence REAL,
                reasoning TEXT,
                market_conditions TEXT,
                outcome REAL,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS learning_patterns (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                pattern_type TEXT,
                pattern_data TEXT,
                success_rate REAL,
                usage_count INTEGER,
                last_updated DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY,
                agent_name TEXT,
                metric_name TEXT,
                metric_value REAL,
                calculation_date DATE,
                period_days INTEGER
            )
        ''')
        
        conn.commit()
        conn.close()
        print("   ✅ Memory & learning database initialized")
    
    def store_agent_memory(self, agent_name: str, memory_type: str, 
                          content: str, importance: float = 0.5) -> bool:
        """Store REAL memory for AI agent"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO agent_memories 
                (agent_name, memory_type, memory_content, importance_score, 
                 created_time, last_accessed, access_count)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (agent_name, memory_type, content, importance,
                  datetime.now().isoformat(), datetime.now().isoformat(), 0))
            
            conn.commit()
            conn.close()
            
            print(f"   💾 Memory stored for {agent_name}: {memory_type}")
            return True
            
        except Exception as e:
            print(f"   ❌ Memory storage error: {e}")
            return False
    
    def retrieve_agent_memories(self, agent_name: str, memory_type: Optional[str] = None,
                               limit: int = 10) -> List[Dict[str, Any]]:
        """Retrieve REAL memories for AI agent"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            if memory_type:
                cursor.execute('''
                    SELECT memory_type, memory_content, importance_score, created_time
                    FROM agent_memories 
                    WHERE agent_name = ? AND memory_type = ?
                    ORDER BY importance_score DESC, created_time DESC
                    LIMIT ?
                ''', (agent_name, memory_type, limit))
            else:
                cursor.execute('''
                    SELECT memory_type, memory_content, importance_score, created_time
                    FROM agent_memories 
                    WHERE agent_name = ?
                    ORDER BY importance_score DESC, created_time DESC
                    LIMIT ?
                ''', (agent_name, limit))
            
            memories = []
            for row in cursor.fetchall():
                memories.append({
                    'type': row[0],
                    'content': row[1],
                    'importance': row[2],
                    'created': row[3]
                })
            
            # Update access count
            cursor.execute('''
                UPDATE agent_memories 
                SET last_accessed = ?, access_count = access_count + 1
                WHERE agent_name = ?
            ''', (datetime.now().isoformat(), agent_name))
            
            conn.commit()
            conn.close()
            
            return memories
            
        except Exception as e:
            print(f"   ❌ Memory retrieval error: {e}")
            return []
    
    def record_trading_decision(self, agent_name: str, symbol: str, decision: str,
                               confidence: float, reasoning: str, 
                               market_conditions: str) -> str:
        """Record REAL trading decision for learning"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            decision_id = f"{agent_name}_{symbol}_{int(time.time())}"
            
            cursor.execute('''
                INSERT INTO trading_decisions 
                (agent_name, symbol, decision, confidence, reasoning, 
                 market_conditions, outcome, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (agent_name, symbol, decision, confidence, reasoning,
                  market_conditions, None, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            # Store as memory
            memory_content = f"Decision: {decision} for {symbol} (confidence: {confidence:.1f}). Reasoning: {reasoning}"
            self.store_agent_memory(agent_name, 'trading_decision', memory_content, confidence)
            
            print(f"   📝 Trading decision recorded for {agent_name}")
            return decision_id
            
        except Exception as e:
            print(f"   ❌ Decision recording error: {e}")
            return ""
    
    def update_decision_outcome(self, agent_name: str, symbol: str, 
                               outcome: float, timestamp: datetime) -> bool:
        """Update REAL outcome of trading decision"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            # Find recent decision to update
            cursor.execute('''
                UPDATE trading_decisions 
                SET outcome = ?
                WHERE agent_name = ? AND symbol = ? AND outcome IS NULL
                ORDER BY timestamp DESC
                LIMIT 1
            ''', (outcome, agent_name, symbol))
            
            conn.commit()
            conn.close()
            
            # Store outcome as memory
            outcome_memory = f"Trade outcome for {symbol}: {outcome:+.2f}% return"
            importance = 0.8 if outcome > 0 else 0.9  # Bad outcomes more important to remember
            self.store_agent_memory(agent_name, 'trade_outcome', outcome_memory, importance)
            
            print(f"   📊 Outcome updated for {agent_name}: {outcome:+.2f}%")
            return True
            
        except Exception as e:
            print(f"   ❌ Outcome update error: {e}")
            return False
    
    def analyze_agent_patterns(self, agent_name: str) -> Dict[str, Any]:
        """Analyze REAL patterns in agent behavior"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            # Get decision patterns
            cursor.execute('''
                SELECT decision, AVG(confidence), COUNT(*), AVG(outcome)
                FROM trading_decisions 
                WHERE agent_name = ? AND outcome IS NOT NULL
                GROUP BY decision
            ''', (agent_name,))
            
            decision_patterns = {}
            for row in cursor.fetchall():
                decision, avg_confidence, count, avg_outcome = row
                decision_patterns[decision] = {
                    'avg_confidence': round(avg_confidence or 0, 2),
                    'count': count,
                    'avg_outcome': round(avg_outcome or 0, 2),
                    'success_rate': 100 if (avg_outcome or 0) > 0 else 0
                }
            
            # Get market condition patterns
            cursor.execute('''
                SELECT market_conditions, AVG(outcome), COUNT(*)
                FROM trading_decisions 
                WHERE agent_name = ? AND outcome IS NOT NULL
                GROUP BY market_conditions
            ''', (agent_name,))
            
            market_patterns = {}
            for row in cursor.fetchall():
                condition, avg_outcome, count = row
                market_patterns[condition] = {
                    'avg_outcome': round(avg_outcome or 0, 2),
                    'count': count
                }
            
            # Calculate overall performance
            cursor.execute('''
                SELECT AVG(outcome), COUNT(*), 
                       SUM(CASE WHEN outcome > 0 THEN 1 ELSE 0 END) as wins
                FROM trading_decisions 
                WHERE agent_name = ? AND outcome IS NOT NULL
            ''', (agent_name,))
            
            overall_stats = cursor.fetchone()
            avg_return = overall_stats[0] or 0
            total_trades = overall_stats[1] or 0
            winning_trades = overall_stats[2] or 0
            
            win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
            
            conn.close()
            
            patterns = {
                'agent_name': agent_name,
                'decision_patterns': decision_patterns,
                'market_patterns': market_patterns,
                'overall_performance': {
                    'avg_return': round(avg_return, 2),
                    'total_trades': total_trades,
                    'win_rate': round(win_rate, 1),
                    'winning_trades': winning_trades
                },
                'analysis_date': datetime.now().isoformat()
            }
            
            # Store patterns for future use
            self._store_learning_pattern(agent_name, 'performance_analysis', patterns)
            
            return patterns
            
        except Exception as e:
            print(f"   ❌ Pattern analysis error: {e}")
            return {}
    
    def _store_learning_pattern(self, agent_name: str, pattern_type: str, 
                               pattern_data: Dict[str, Any]) -> bool:
        """Store REAL learning pattern"""
        
        try:
            conn = sqlite3.connect('ai_memory_learning.db')
            cursor = conn.cursor()
            
            success_rate = pattern_data.get('overall_performance', {}).get('win_rate', 0)
            
            cursor.execute('''
                INSERT OR REPLACE INTO learning_patterns 
                (agent_name, pattern_type, pattern_data, success_rate, usage_count, last_updated)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (agent_name, pattern_type, json.dumps(pattern_data), success_rate,
                  1, datetime.now().isoformat()))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"   ❌ Pattern storage error: {e}")
            return False
    
    def get_learning_insights(self, agent_name: str) -> str:
        """Get REAL learning insights for agent"""
        
        patterns = self.analyze_agent_patterns(agent_name)
        
        if not patterns:
            return "No learning data available yet."
        
        insights = []
        
        # Decision insights
        decision_patterns = patterns.get('decision_patterns', {})
        if decision_patterns:
            best_decision = max(decision_patterns.items(), key=lambda x: x[1]['avg_outcome'])
            worst_decision = min(decision_patterns.items(), key=lambda x: x[1]['avg_outcome'])
            
            insights.append(f"Best performing decision: {best_decision[0]} ({best_decision[1]['avg_outcome']:+.1f}% avg return)")
            insights.append(f"Worst performing decision: {worst_decision[0]} ({worst_decision[1]['avg_outcome']:+.1f}% avg return)")
        
        # Performance insights
        overall = patterns.get('overall_performance', {})
        if overall:
            insights.append(f"Overall win rate: {overall['win_rate']:.1f}% ({overall['winning_trades']}/{overall['total_trades']} trades)")
            insights.append(f"Average return per trade: {overall['avg_return']:+.2f}%")
        
        # Market condition insights
        market_patterns = patterns.get('market_patterns', {})
        if market_patterns:
            best_market = max(market_patterns.items(), key=lambda x: x[1]['avg_outcome'])
            insights.append(f"Best market conditions: {best_market[0]} ({best_market[1]['avg_outcome']:+.1f}% avg return)")
        
        return "\n".join(insights)
    
    def create_memory_enhanced_prompt(self, agent_name: str, base_query: str) -> str:
        """Create REAL memory-enhanced prompt"""
        
        print(f"\n🧠 Retrieving memories for {agent_name}...")
        
        # Get relevant memories
        recent_memories = self.retrieve_agent_memories(agent_name, limit=5)
        trading_memories = self.retrieve_agent_memories(agent_name, 'trading_decision', limit=3)
        outcome_memories = self.retrieve_agent_memories(agent_name, 'trade_outcome', limit=3)
        
        # Get learning insights
        insights = self.get_learning_insights(agent_name)
        
        # Build memory-enhanced prompt
        memory_section = ""
        
        if recent_memories:
            memory_section += "\n🧠 YOUR RECENT MEMORIES:\n"
            for i, memory in enumerate(recent_memories[:3], 1):
                memory_section += f"{i}. {memory['content'][:100]}...\n"
        
        if trading_memories:
            memory_section += "\n📊 YOUR RECENT TRADING DECISIONS:\n"
            for i, memory in enumerate(trading_memories, 1):
                memory_section += f"{i}. {memory['content'][:100]}...\n"
        
        if outcome_memories:
            memory_section += "\n📈 YOUR RECENT TRADE OUTCOMES:\n"
            for i, memory in enumerate(outcome_memories, 1):
                memory_section += f"{i}. {memory['content']}\n"
        
        if insights:
            memory_section += f"\n🎯 YOUR PERFORMANCE INSIGHTS:\n{insights}\n"
        
        enhanced_prompt = f"""{base_query}

{memory_section}

Use your memories and past performance to make a better decision. Learn from your previous successes and mistakes."""

        print(f"   ✅ Memory-enhanced prompt created ({len(enhanced_prompt)} chars)")
        return enhanced_prompt
    
    def simulate_learning_data(self, agent_name: str, num_decisions: int = 10) -> bool:
        """Create REAL simulated learning data for testing"""
        
        print(f"\n🎲 Creating learning data for {agent_name}...")
        
        symbols = ['BTC-USD', 'AAPL', 'TSLA', 'SPY']
        decisions = ['BUY', 'SELL', 'HOLD']
        market_conditions = ['BULLISH', 'BEARISH', 'NEUTRAL', 'VOLATILE']
        
        import random
        
        for i in range(num_decisions):
            symbol = random.choice(symbols)
            decision = random.choice(decisions)
            confidence = random.uniform(0.5, 0.9)
            market_condition = random.choice(market_conditions)
            
            reasoning = f"Technical analysis suggests {decision} based on {market_condition.lower()} market conditions"
            
            # Record decision
            decision_id = self.record_trading_decision(
                agent_name, symbol, decision, confidence, reasoning, market_condition
            )
            
            # Simulate outcome (biased towards positive for BUY in BULLISH, etc.)
            if (decision == 'BUY' and market_condition == 'BULLISH') or \
               (decision == 'SELL' and market_condition == 'BEARISH'):
                outcome = random.uniform(1.0, 8.0)  # Positive outcome
            elif decision == 'HOLD':
                outcome = random.uniform(-1.0, 2.0)  # Neutral outcome
            else:
                outcome = random.uniform(-5.0, 3.0)  # Mixed outcome
            
            # Update outcome
            self.update_decision_outcome(agent_name, symbol, outcome, datetime.now())
            
            # Store additional memories
            if outcome > 5:
                self.store_agent_memory(agent_name, 'success_pattern', 
                                      f"Great success with {decision} on {symbol} in {market_condition} market", 0.9)
            elif outcome < -3:
                self.store_agent_memory(agent_name, 'failure_pattern',
                                      f"Poor result with {decision} on {symbol} in {market_condition} market", 0.8)
        
        print(f"   ✅ Created {num_decisions} learning records for {agent_name}")
        return True

def main():
    """Test REAL memory & learning system"""
    print("🧠 AI MEMORY & LEARNING SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize system
    memory_system = AIMemoryLearningSystem()
    
    # Test with sample agents
    test_agents = ['marco_o1_finance', 'deepseek_r1_finance', 'cogito_finance']
    
    for agent in test_agents:
        print(f"\n🤖 Testing memory system for {agent}...")
        
        # Create learning data
        memory_system.simulate_learning_data(agent, 15)
        
        # Analyze patterns
        patterns = memory_system.analyze_agent_patterns(agent)
        
        if patterns:
            overall = patterns['overall_performance']
            print(f"   📊 Performance: {overall['win_rate']:.1f}% win rate, {overall['avg_return']:+.2f}% avg return")
            print(f"   📈 Total trades: {overall['total_trades']}")
        
        # Test memory retrieval
        memories = memory_system.retrieve_agent_memories(agent, limit=3)
        print(f"   💾 Retrieved {len(memories)} memories")
        
        # Test enhanced prompt
        enhanced_prompt = memory_system.create_memory_enhanced_prompt(
            agent, f"Should I buy Bitcoin right now?"
        )
        print(f"   🧠 Enhanced prompt: {len(enhanced_prompt)} characters")
    
    print(f"\n✅ AI MEMORY & LEARNING SYSTEM TEST COMPLETE")
    print(f"   🔍 Check 'ai_memory_learning.db' for stored data")

if __name__ == "__main__":
    main()
