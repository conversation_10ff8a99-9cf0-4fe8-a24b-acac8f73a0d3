#!/usr/bin/env python3
"""
Test All AI Models - Comprehensive testing of all 26 models
"""

import subprocess
import time
import json
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
from rich.live import Live
from rich.layout import Layout

console = Console()

class ModelTester:
    """Test all AI models comprehensively"""
    
    def __init__(self):
        self.models = []
        self.test_results = {}
        self.test_questions = [
            "What is your name and specialty?",
            "Analyze AAPL stock for investment",
            "What's the risk of a 60/40 portfolio?",
            "Should I buy Bitcoin now?",
            "Explain options trading basics"
        ]
    
    def get_all_models(self):
        """Get all available models"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 1:
                            model_name = parts[0]
                            self.models.append({
                                'name': model_name,
                                'size': parts[2] if len(parts) > 2 else 'Unknown',
                                'status': 'Ready'
                            })
            return True
        except Exception as e:
            console.print(f"[red]Error getting models: {e}[/red]")
            return False
    
    def test_single_model(self, model_name, question):
        """Test a single model with a question"""
        try:
            # Use ollama run with a single question
            cmd = ['ollama', 'run', model_name, question]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                response = result.stdout.strip()
                return {
                    'success': True,
                    'response': response[:200] + "..." if len(response) > 200 else response,
                    'response_time': 'Fast',
                    'error': None
                }
            else:
                return {
                    'success': False,
                    'response': None,
                    'response_time': None,
                    'error': result.stderr.strip()
                }
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'response': None,
                'response_time': 'Timeout',
                'error': 'Model response timeout (60s)'
            }
        except Exception as e:
            return {
                'success': False,
                'response': None,
                'response_time': None,
                'error': str(e)
            }
    
    def test_all_models(self):
        """Test all models with all questions"""
        console.print(Panel(
            "[bold blue]🧪 TESTING ALL AI MODELS[/bold blue]\n\n"
            f"Testing {len(self.models)} models with {len(self.test_questions)} questions each",
            title="Comprehensive Model Testing"
        ))
        
        total_tests = len(self.models) * len(self.test_questions)
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            console=console
        ) as progress:
            
            main_task = progress.add_task("Testing all models...", total=total_tests)
            
            for model in self.models:
                model_name = model['name']
                self.test_results[model_name] = {
                    'model_info': model,
                    'tests': {},
                    'summary': {
                        'total_tests': len(self.test_questions),
                        'passed': 0,
                        'failed': 0,
                        'avg_response_time': 'N/A'
                    }
                }
                
                progress.update(main_task, description=f"Testing {model_name}...")
                
                for i, question in enumerate(self.test_questions):
                    test_name = f"Test_{i+1}"
                    
                    # Test the model
                    result = self.test_single_model(model_name, question)
                    
                    self.test_results[model_name]['tests'][test_name] = {
                        'question': question,
                        'result': result
                    }
                    
                    if result['success']:
                        self.test_results[model_name]['summary']['passed'] += 1
                    else:
                        self.test_results[model_name]['summary']['failed'] += 1
                    
                    progress.advance(main_task)
                    time.sleep(0.5)  # Brief pause between tests
    
    def display_results(self):
        """Display comprehensive test results"""
        console.print(Panel(
            "[bold green]📊 TEST RESULTS SUMMARY[/bold green]",
            title="Results"
        ))
        
        # Summary table
        summary_table = Table(title="🎯 Model Performance Summary")
        summary_table.add_column("Model", style="cyan", width=35)
        summary_table.add_column("Size", style="blue", width=8)
        summary_table.add_column("Passed", style="green", width=8)
        summary_table.add_column("Failed", style="red", width=8)
        summary_table.add_column("Success Rate", style="yellow", width=12)
        summary_table.add_column("Status", style="magenta", width=10)
        
        total_models = len(self.test_results)
        total_passed = 0
        total_failed = 0
        working_models = 0
        
        for model_name, results in self.test_results.items():
            summary = results['summary']
            model_info = results['model_info']
            
            passed = summary['passed']
            failed = summary['failed']
            total = summary['total_tests']
            
            total_passed += passed
            total_failed += failed
            
            if passed > 0:
                working_models += 1
                success_rate = f"{(passed/total)*100:.1f}%"
                status = "✅ Working" if passed >= total//2 else "⚠️ Partial"
            else:
                success_rate = "0%"
                status = "❌ Failed"
            
            summary_table.add_row(
                model_name,
                model_info['size'],
                str(passed),
                str(failed),
                success_rate,
                status
            )
        
        console.print(summary_table)
        
        # Overall statistics
        console.print(Panel(
            f"[bold green]📈 OVERALL STATISTICS[/bold green]\n\n"
            f"Total Models Tested: {total_models}\n"
            f"Working Models: {working_models}\n"
            f"Total Tests Passed: {total_passed}\n"
            f"Total Tests Failed: {total_failed}\n"
            f"Overall Success Rate: {(total_passed/(total_passed+total_failed))*100:.1f}%\n"
            f"Model Availability: {(working_models/total_models)*100:.1f}%",
            title="Statistics"
        ))
    
    def display_detailed_results(self):
        """Display detailed test results for each model"""
        console.print("\n[bold blue]📋 DETAILED TEST RESULTS[/bold blue]")
        
        for model_name, results in self.test_results.items():
            summary = results['summary']
            
            if summary['passed'] > 0:
                console.print(f"\n[green]✅ {model_name}[/green] - {summary['passed']}/{summary['total_tests']} tests passed")
                
                # Show sample responses
                for test_name, test_data in results['tests'].items():
                    if test_data['result']['success']:
                        question = test_data['question']
                        response = test_data['result']['response']
                        console.print(f"  [cyan]Q: {question}[/cyan]")
                        console.print(f"  [yellow]A: {response}[/yellow]")
                        break  # Show only first successful response
            else:
                console.print(f"\n[red]❌ {model_name}[/red] - All tests failed")
                # Show error for first test
                first_test = list(results['tests'].values())[0]
                error = first_test['result']['error']
                console.print(f"  [red]Error: {error}[/red]")
    
    def save_results(self):
        """Save results to file"""
        results_file = Path("model_test_results.json")
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        console.print(f"[green]✅ Results saved to {results_file}[/green]")
    
    def display_model_categories(self):
        """Display models by category"""
        console.print("\n[bold blue]🏷️ MODEL CATEGORIES[/bold blue]")
        
        categories = {
            'Finance Specialists': [],
            'Enhanced Models': [],
            'Regular Models': [],
            'Reasoning Models': []
        }
        
        for model_name, results in self.test_results.items():
            if 'finance' in model_name.lower():
                categories['Finance Specialists'].append(model_name)
            elif 'enhanced' in model_name.lower():
                categories['Enhanced Models'].append(model_name)
            elif 'reasoning' in model_name.lower():
                categories['Reasoning Models'].append(model_name)
            else:
                categories['Regular Models'].append(model_name)
        
        for category, models in categories.items():
            if models:
                working_count = sum(1 for model in models if self.test_results[model]['summary']['passed'] > 0)
                console.print(f"\n[cyan]{category}[/cyan] ({working_count}/{len(models)} working):")
                for model in models:
                    passed = self.test_results[model]['summary']['passed']
                    total = self.test_results[model]['summary']['total_tests']
                    status = "✅" if passed > 0 else "❌"
                    console.print(f"  {status} {model} ({passed}/{total})")

def main():
    """Main testing function"""
    console.print(Panel(
        "[bold blue]🧪 COMPREHENSIVE AI MODEL TESTING[/bold blue]\n\n"
        "Testing all 26 AI models in your Noryon trading system\n"
        "This will test each model's responsiveness and capabilities",
        title="Model Testing Suite"
    ))
    
    tester = ModelTester()
    
    # Get all models
    console.print("[blue]🔍 Discovering models...[/blue]")
    if not tester.get_all_models():
        console.print("[red]❌ Failed to get model list[/red]")
        return
    
    console.print(f"[green]✅ Found {len(tester.models)} models[/green]")
    
    # Test all models
    tester.test_all_models()
    
    # Display results
    tester.display_results()
    tester.display_model_categories()
    tester.display_detailed_results()
    
    # Save results
    tester.save_results()
    
    # Final summary
    console.print(Panel(
        "[bold green]🎉 TESTING COMPLETE![/bold green]\n\n"
        "All models have been tested with financial scenarios.\n"
        "Check the detailed results above to see which models\n"
        "are working best for your trading system.\n\n"
        "[yellow]Results saved to model_test_results.json[/yellow]",
        title="Testing Complete"
    ))

if __name__ == "__main__":
    main()
