#!/bin/bash
# Quick Access Commands for Unrestricted AI Models

echo "🔓 Unrestricted AI Model Commands"
echo "================================="

# Finance Models (No Restrictions)
alias chat-finance="ollama run unrestricted-noryon-phi4-reasoning-finance-v2-latest"
alias chat-market="ollama run unrestricted-noryon-gemma-3-12b-finance-latest"
alias chat-reasoning="ollama run unrestricted-noryon-deepseek-r1-finance-v2-latest"
alias chat-intelligence="ollama run unrestricted-noryon-qwen3-finance-v2-latest"

# Enhanced Models (No Restrictions)
alias chat-enhanced="ollama run unrestricted-noryon-phi-4-9b-enhanced-enhanced-latest"
alias chat-super="ollama run unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest"

# Regular Models (No Restrictions)
alias chat-phi4="ollama run unrestricted-phi4-14b"
alias chat-gemma="ollama run unrestricted-gemma3-12b"
alias chat-deepseek="ollama run unrestricted-deepseek-r1-14b"

echo "Available commands:"
echo "  chat-finance    - Finance specialist (no restrictions)"
echo "  chat-market     - Market analysis (no restrictions)"
echo "  chat-reasoning  - Advanced reasoning (no restrictions)"
echo "  chat-intelligence - General intelligence (no restrictions)"
echo "  chat-enhanced   - Enhanced model (no restrictions)"
echo "  chat-super      - Super enhanced model (no restrictions)"
echo ""
echo "🔓 All models have safety restrictions removed!"
