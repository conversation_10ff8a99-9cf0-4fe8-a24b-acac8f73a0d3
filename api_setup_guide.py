#!/usr/bin/env python3
"""
API Setup Guide - Get Your Trading APIs Working
Real step-by-step setup for live data and trading
"""

import os
import requests
import json
from datetime import datetime

class APISetupGuide:
    """Guide to set up real trading APIs"""
    
    def __init__(self):
        self.api_status = {}
        print("🔑 API SETUP GUIDE - GET REAL TRADING DATA")
        print("=" * 60)
    
    def setup_binance_api(self):
        """Setup Binance API for crypto trading"""
        print("\n🔑 BINANCE API SETUP")
        print("=" * 40)
        
        print("1. Go to: https://www.binance.com/en/my/settings/api-management")
        print("2. Create API Key with these permissions:")
        print("   ✅ Enable Reading")
        print("   ✅ Enable Spot & Margin Trading")
        print("   ❌ Enable Futures (optional)")
        print("   ❌ Enable Withdrawals (NOT recommended)")
        
        print("\n3. Add to your environment variables:")
        print("   BINANCE_API_KEY=your_api_key_here")
        print("   BINANCE_SECRET_KEY=your_secret_key_here")
        
        print("\n4. Test with this code:")
        print("""
import os
from binance.client import Client

api_key = os.getenv('BINANCE_API_KEY')
api_secret = os.getenv('BINANCE_SECRET_KEY')
client = Client(api_key, api_secret)

# Test connection
account = client.get_account()
print(f"Account status: {account['accountType']}")
        """)
        
        return {
            'name': 'Binance API',
            'cost': 'Free',
            'setup_time': '10 minutes',
            'capabilities': ['Real-time crypto prices', 'Trading execution', 'Account management'],
            'documentation': 'https://binance-docs.github.io/apidocs/spot/en/'
        }
    
    def setup_coinbase_api(self):
        """Setup Coinbase Pro API"""
        print("\n🔑 COINBASE PRO API SETUP")
        print("=" * 40)
        
        print("1. Go to: https://pro.coinbase.com/profile/api")
        print("2. Create API Key with permissions:")
        print("   ✅ View")
        print("   ✅ Trade")
        print("   ❌ Transfer (NOT recommended)")
        
        print("\n3. Add to environment variables:")
        print("   COINBASE_API_KEY=your_api_key")
        print("   COINBASE_SECRET=your_secret")
        print("   COINBASE_PASSPHRASE=your_passphrase")
        
        print("\n4. Test with this code:")
        print("""
import cbpro
import os

auth_client = cbpro.AuthenticatedClient(
    os.getenv('COINBASE_API_KEY'),
    os.getenv('COINBASE_SECRET'),
    os.getenv('COINBASE_PASSPHRASE')
)

# Test connection
accounts = auth_client.get_accounts()
print(f"Connected to {len(accounts)} accounts")
        """)
        
        return {
            'name': 'Coinbase Pro API',
            'cost': 'Free',
            'setup_time': '10 minutes',
            'capabilities': ['Crypto trading', 'USD deposits', 'Professional interface'],
            'documentation': 'https://docs.pro.coinbase.com/'
        }
    
    def setup_alpha_vantage_api(self):
        """Setup Alpha Vantage for stock data"""
        print("\n🔑 ALPHA VANTAGE API SETUP")
        print("=" * 40)
        
        print("1. Go to: https://www.alphavantage.co/support/#api-key")
        print("2. Get free API key (500 calls/day)")
        print("3. For premium: $49.99/month (unlimited calls)")
        
        print("\n4. Add to environment variables:")
        print("   ALPHA_VANTAGE_API_KEY=your_api_key")
        
        print("\n5. Test with this code:")
        print("""
import requests
import os

api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
url = f'https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=AAPL&apikey={api_key}'

response = requests.get(url)
data = response.json()
print(f"AAPL Price: {data['Global Quote']['05. price']}")
        """)
        
        return {
            'name': 'Alpha Vantage API',
            'cost': 'Free (500 calls/day) or $49.99/month',
            'setup_time': '5 minutes',
            'capabilities': ['Stock prices', 'Forex data', 'Technical indicators'],
            'documentation': 'https://www.alphavantage.co/documentation/'
        }
    
    def test_api_connection(self, api_name: str, test_function):
        """Test if an API is working"""
        print(f"\n🧪 Testing {api_name} connection...")
        
        try:
            result = test_function()
            if result:
                print(f"   ✅ {api_name} connected successfully!")
                self.api_status[api_name] = 'working'
                return True
            else:
                print(f"   ❌ {api_name} connection failed")
                self.api_status[api_name] = 'failed'
                return False
        except Exception as e:
            print(f"   ❌ {api_name} error: {e}")
            self.api_status[api_name] = 'error'
            return False
    
    def create_env_file_template(self):
        """Create .env file template"""
        env_template = """# API Keys for Trading System
# Copy this to .env and fill in your real API keys

# Binance API (Crypto Trading)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# Coinbase Pro API (Crypto Trading)
COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_SECRET=your_coinbase_secret_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here

# Alpha Vantage API (Stock Data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here

# Optional: Other APIs
POLYGON_API_KEY=your_polygon_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# Risk Management Settings
MAX_DAILY_LOSS=5000.00
MAX_POSITION_SIZE=10000.00
EMERGENCY_STOP_LOSS=15.0
"""
        
        with open('.env.template', 'w') as f:
            f.write(env_template)
        
        print("📁 Created .env.template file")
        print("   1. Copy to .env")
        print("   2. Fill in your real API keys")
        print("   3. Never commit .env to git!")
    
    def get_setup_summary(self):
        """Get setup progress summary"""
        return {
            'timestamp': datetime.now().isoformat(),
            'apis_configured': len([k for k, v in self.api_status.items() if v == 'working']),
            'apis_failed': len([k for k, v in self.api_status.items() if v in ['failed', 'error']]),
            'total_apis': len(self.api_status),
            'api_status': self.api_status,
            'next_steps': [
                'Test each API connection',
                'Set up environment variables',
                'Configure risk management',
                'Start with paper trading'
            ]
        }

def main():
    """Run API setup guide"""
    guide = APISetupGuide()
    
    # Show all setup instructions
    binance_info = guide.setup_binance_api()
    coinbase_info = guide.setup_coinbase_api()
    alpha_vantage_info = guide.setup_alpha_vantage_api()
    
    # Create environment file template
    guide.create_env_file_template()
    
    # Show summary
    print("\n📊 API SETUP SUMMARY:")
    print("=" * 40)
    print("1. Binance API - Crypto trading and data")
    print("2. Coinbase Pro API - Professional crypto trading")
    print("3. Alpha Vantage API - Stock market data")
    print("4. Environment variables template created")
    
    print("\n⏱️ ESTIMATED SETUP TIME:")
    print("   Total: 30-45 minutes")
    print("   Binance: 10 minutes")
    print("   Coinbase: 10 minutes")
    print("   Alpha Vantage: 5 minutes")
    print("   Testing: 10 minutes")
    
    print("\n🎯 AFTER SETUP YOU'LL HAVE:")
    print("   ✅ Real-time crypto prices")
    print("   ✅ Live trading capabilities")
    print("   ✅ Stock market data")
    print("   ✅ Professional trading APIs")
    print("   ✅ Risk management integration")

if __name__ == "__main__":
    main()
