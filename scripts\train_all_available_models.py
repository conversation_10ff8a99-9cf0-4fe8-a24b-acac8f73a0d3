#!/usr/bin/env python3
"""
Train All Available Models for Noryon AI
Comprehensive training script for all available models
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

console = Console()

class ComprehensiveModelTrainer:
    """Train all available models for financial analysis"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.models_to_train = [
            {
                "name": "Gemma 3 12B",
                "type": "ollama",
                "model_id": "gemma3:12b",
                "priority": 1,
                "estimated_time": "15 minutes"
            },
            {
                "name": "Phi 4 9B", 
                "type": "ollama",
                "model_id": "phi4:14b",
                "priority": 2,
                "estimated_time": "12 minutes"
            },
            {
                "name": "Mistral 3",
                "type": "huggingface",
                "model_path": "mistral",
                "priority": 3,
                "estimated_time": "25 minutes"
            },
            {
                "name": "Qwen 3",
                "type": "huggingface", 
                "model_path": "qwen3",
                "priority": 4,
                "estimated_time": "20 minutes"
            }
        ]
        
    async def train_ollama_model(self, model_info):
        """Train an Ollama model with financial data"""
        console.print(f"[yellow]🚀 Training {model_info['name']} via Ollama...[/yellow]")
        
        # Create financial training prompt for Ollama
        financial_modelfile = f"""
FROM {model_info['model_id']}

SYSTEM \"\"\"You are a specialized financial AI assistant for the Noryon trading system. You provide:

1. Market Analysis: Analyze market trends, price movements, and economic indicators
2. Risk Assessment: Evaluate portfolio risk and suggest risk management strategies  
3. Trading Signals: Generate buy/sell/hold recommendations with reasoning
4. Portfolio Optimization: Suggest position sizing and diversification strategies
5. Economic Insights: Interpret economic data and its market impact

Always provide:
- Clear, actionable insights
- Risk-adjusted recommendations
- Quantitative analysis when possible
- Conservative bias for risk management
- Real-time market context

Focus on: US equities, major cryptocurrencies, forex, and commodities.
\"\"\"

PARAMETER temperature 0.3
PARAMETER top_p 0.9
PARAMETER top_k 40
"""
        
        # Save modelfile
        modelfile_path = self.project_root / f"Modelfile.{model_info['name'].lower().replace(' ', '_')}"
        with open(modelfile_path, 'w') as f:
            f.write(financial_modelfile)
        
        # Create the financial model
        model_name = f"noryon-{model_info['name'].lower().replace(' ', '-')}-finance"
        
        try:
            # Create the model
            result = subprocess.run([
                'ollama', 'create', model_name, '-f', str(modelfile_path)
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                console.print(f"[green]✅ {model_info['name']} financial model created: {model_name}[/green]")
                
                # Test the model
                test_result = subprocess.run([
                    'ollama', 'run', model_name, 
                    'Analyze the current market conditions and provide a brief trading recommendation.'
                ], capture_output=True, text=True, timeout=60)
                
                if test_result.returncode == 0:
                    console.print(f"[green]✅ {model_info['name']} test successful[/green]")
                    console.print(f"Sample output: {test_result.stdout[:200]}...")
                    return True
                else:
                    console.print(f"[red]❌ {model_info['name']} test failed[/red]")
                    return False
            else:
                console.print(f"[red]❌ Failed to create {model_info['name']} model: {result.stderr}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ {model_info['name']} training timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ {model_info['name']} training failed: {e}[/red]")
            return False
    
    async def train_huggingface_model(self, model_info):
        """Train a HuggingFace model"""
        console.print(f"[yellow]🚀 Training {model_info['name']} via HuggingFace...[/yellow]")
        
        # Use existing training script
        model_name = model_info['name'].lower().replace(' ', '')
        
        try:
            result = subprocess.run([
                sys.executable, 'train_models_robust.py', 
                '--model', model_name, '--quick'
            ], capture_output=True, text=True, timeout=1800)  # 30 minutes timeout
            
            if result.returncode == 0:
                console.print(f"[green]✅ {model_info['name']} training completed[/green]")
                return True
            else:
                console.print(f"[red]❌ {model_info['name']} training failed[/red]")
                console.print(f"Error: {result.stderr[-500:]}")  # Last 500 chars of error
                return False
                
        except subprocess.TimeoutExpired:
            console.print(f"[red]❌ {model_info['name']} training timed out[/red]")
            return False
        except Exception as e:
            console.print(f"[red]❌ {model_info['name']} training failed: {e}[/red]")
            return False
    
    async def train_model(self, model_info):
        """Train a single model based on its type"""
        start_time = datetime.now()
        
        console.print(Panel(
            f"[bold blue]Training {model_info['name']}[/bold blue]\n\n"
            f"Type: {model_info['type']}\n"
            f"Priority: {model_info['priority']}\n"
            f"Estimated Time: {model_info['estimated_time']}",
            title=f"Model Training - {model_info['name']}"
        ))
        
        if model_info['type'] == 'ollama':
            success = await self.train_ollama_model(model_info)
        elif model_info['type'] == 'huggingface':
            success = await self.train_huggingface_model(model_info)
        else:
            console.print(f"[red]❌ Unknown model type: {model_info['type']}[/red]")
            success = False
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        if success:
            console.print(f"[green]✅ {model_info['name']} completed in {duration}[/green]")
        else:
            console.print(f"[red]❌ {model_info['name']} failed after {duration}[/red]")
        
        return success
    
    async def train_all_models(self):
        """Train all available models"""
        console.print(Panel(
            "[bold blue]🚀 Noryon AI Comprehensive Model Training[/bold blue]\n\n"
            "Training all available models for financial analysis:\n"
            "• Gemma 3 12B (Ollama)\n"
            "• Phi 4 9B (Ollama)\n"
            "• Mistral 3 (HuggingFace)\n"
            "• Qwen 3 (HuggingFace)\n\n"
            "This will create a powerful ensemble of AI models!",
            title="Comprehensive Training"
        ))
        
        # Sort models by priority
        sorted_models = sorted(self.models_to_train, key=lambda x: x['priority'])
        
        results = {}
        total_start_time = datetime.now()
        
        for model_info in sorted_models:
            console.print(f"\n[bold yellow]📋 Starting {model_info['name']} training...[/bold yellow]")
            success = await self.train_model(model_info)
            results[model_info['name']] = success
            
            if success:
                console.print(f"[green]✅ {model_info['name']} ready for trading![/green]")
            else:
                console.print(f"[red]❌ {model_info['name']} training failed[/red]")
            
            # Small delay between models
            await asyncio.sleep(2)
        
        total_end_time = datetime.now()
        total_duration = total_end_time - total_start_time
        
        # Summary
        successful_models = [name for name, success in results.items() if success]
        failed_models = [name for name, success in results.items() if not success]
        
        console.print(Panel(
            f"[bold green]🎉 Training Complete![/bold green]\n\n"
            f"Total Time: {total_duration}\n"
            f"Successful: {len(successful_models)}/{len(self.models_to_train)}\n\n"
            f"✅ Successful Models:\n" + 
            "\n".join(f"  • {name}" for name in successful_models) + "\n\n" +
            (f"❌ Failed Models:\n" + 
             "\n".join(f"  • {name}" for name in failed_models) + "\n\n" if failed_models else "") +
            f"🚀 Ready for ensemble trading!",
            title="Training Summary"
        ))
        
        return results
    
    def create_ensemble_config(self, successful_models):
        """Create configuration for ensemble model usage"""
        ensemble_config = {
            "ensemble": {
                "enabled": True,
                "models": [],
                "voting_strategy": "weighted",
                "confidence_threshold": 0.7
            }
        }
        
        # Add successful models to ensemble
        for model_name in successful_models:
            if "gemma" in model_name.lower():
                ensemble_config["ensemble"]["models"].append({
                    "name": "noryon-gemma-3-finance",
                    "type": "ollama",
                    "weight": 0.3,
                    "specialization": "market_analysis"
                })
            elif "phi" in model_name.lower():
                ensemble_config["ensemble"]["models"].append({
                    "name": "noryon-phi-4-finance", 
                    "type": "ollama",
                    "weight": 0.25,
                    "specialization": "risk_assessment"
                })
            elif "mistral" in model_name.lower():
                ensemble_config["ensemble"]["models"].append({
                    "name": "mistral-finance-v1",
                    "type": "huggingface",
                    "weight": 0.25,
                    "specialization": "trading_signals"
                })
            elif "qwen" in model_name.lower():
                ensemble_config["ensemble"]["models"].append({
                    "name": "qwen3-finance-v1",
                    "type": "huggingface", 
                    "weight": 0.2,
                    "specialization": "portfolio_optimization"
                })
        
        # Save ensemble config
        import yaml
        config_path = self.project_root / "config" / "ensemble_config.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(ensemble_config, f, default_flow_style=False)
        
        console.print(f"[green]✅ Ensemble configuration saved to {config_path}[/green]")

async def main():
    """Main training function"""
    trainer = ComprehensiveModelTrainer()
    
    # Check if DeepSeek is still downloading
    console.print("[yellow]Checking DeepSeek R1 download status...[/yellow]")
    
    # Start comprehensive training
    results = await trainer.train_all_models()
    
    # Create ensemble configuration
    successful_models = [name for name, success in results.items() if success]
    if successful_models:
        trainer.create_ensemble_config(successful_models)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    console.print("1. Test individual models with financial queries")
    console.print("2. Integrate ensemble into live trading system")
    console.print("3. Monitor DeepSeek R1 download completion")
    console.print("4. Run comprehensive system tests")
    console.print("5. Deploy to production trading")
    
    return results

if __name__ == "__main__":
    results = asyncio.run(main())
