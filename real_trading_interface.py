#!/usr/bin/env python3
"""
Real Trading Interface - ACTUAL TRADE EXECUTION
Connect to real brokers and execute real trades
"""

import os
import time
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Real broker APIs (need to install: pip install python-binance alpaca-trade-api)
try:
    from binance.client import Client as BinanceClient
    from binance.exceptions import BinanceAPIException
    BINANCE_AVAILABLE = True
except ImportError:
    BINANCE_AVAILABLE = False
    print("⚠️ Binance API not installed: pip install python-binance")

try:
    import alpaca_trade_api as tradeapi
    ALPACA_AVAILABLE = True
except ImportError:
    ALPACA_AVAILABLE = False
    print("⚠️ Alpaca API not installed: pip install alpaca-trade-api")

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    PENDING = "PENDING"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

@dataclass
class RealOrder:
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    average_price: float = 0.0
    timestamp: datetime = None
    broker: str = None

class RealTradingInterface:
    """Interface for real trading execution"""
    
    def __init__(self, paper_trading: bool = True):
        self.paper_trading = paper_trading
        self.brokers = {}
        self.active_orders = {}
        self.trade_history = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - TRADING - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('trading_execution.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        print(f"🔌 REAL TRADING INTERFACE INITIALIZED")
        print(f"   📊 Mode: {'PAPER TRADING' if paper_trading else 'LIVE TRADING'}")
        print(f"   🔗 Binance Available: {BINANCE_AVAILABLE}")
        print(f"   🔗 Alpaca Available: {ALPACA_AVAILABLE}")
        
        # Initialize brokers
        self._initialize_brokers()
    
    def _initialize_brokers(self):
        """Initialize broker connections"""
        
        # Binance (Crypto)
        if BINANCE_AVAILABLE:
            try:
                api_key = os.getenv('BINANCE_API_KEY')
                api_secret = os.getenv('BINANCE_SECRET_KEY')
                
                if api_key and api_secret:
                    if self.paper_trading:
                        # Binance testnet
                        client = BinanceClient(api_key, api_secret, testnet=True)
                    else:
                        # Live Binance
                        client = BinanceClient(api_key, api_secret)
                    
                    # Test connection
                    account = client.get_account()
                    self.brokers['binance'] = client
                    self.logger.info(f"Binance connected: {account['accountType']}")
                    print(f"   ✅ Binance: Connected ({account['accountType']})")
                else:
                    print(f"   ⚠️ Binance: API keys not found in environment")
            except Exception as e:
                print(f"   ❌ Binance: Connection failed - {e}")
        
        # Alpaca (Stocks)
        if ALPACA_AVAILABLE:
            try:
                api_key = os.getenv('ALPACA_API_KEY')
                api_secret = os.getenv('ALPACA_SECRET_KEY')
                
                if api_key and api_secret:
                    if self.paper_trading:
                        # Alpaca paper trading
                        base_url = 'https://paper-api.alpaca.markets'
                    else:
                        # Live Alpaca
                        base_url = 'https://api.alpaca.markets'
                    
                    api = tradeapi.REST(api_key, api_secret, base_url, api_version='v2')
                    
                    # Test connection
                    account = api.get_account()
                    self.brokers['alpaca'] = api
                    self.logger.info(f"Alpaca connected: {account.status}")
                    print(f"   ✅ Alpaca: Connected ({account.status})")
                else:
                    print(f"   ⚠️ Alpaca: API keys not found in environment")
            except Exception as e:
                print(f"   ❌ Alpaca: Connection failed - {e}")
    
    def place_order(self, symbol: str, side: str, quantity: float, 
                   order_type: str = 'market', price: Optional[float] = None,
                   stop_price: Optional[float] = None) -> Optional[RealOrder]:
        """Place a real order"""
        
        # Determine broker based on symbol
        if symbol.endswith('USDT') or symbol.endswith('USD') and 'BTC' in symbol or 'ETH' in symbol:
            broker_name = 'binance'
        else:
            broker_name = 'alpaca'
        
        if broker_name not in self.brokers:
            self.logger.error(f"Broker {broker_name} not available for {symbol}")
            return None
        
        broker = self.brokers[broker_name]
        
        # Create order object
        order = RealOrder(
            symbol=symbol,
            side=OrderSide.BUY if side.upper() == 'BUY' else OrderSide.SELL,
            order_type=OrderType(order_type.upper()),
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            timestamp=datetime.now(),
            broker=broker_name
        )
        
        try:
            if broker_name == 'binance':
                result = self._place_binance_order(broker, order)
            elif broker_name == 'alpaca':
                result = self._place_alpaca_order(broker, order)
            else:
                return None
            
            if result:
                order.order_id = result.get('orderId') or result.get('id')
                order.status = OrderStatus.PENDING
                self.active_orders[order.order_id] = order
                
                self.logger.info(f"Order placed: {symbol} {side} {quantity} @ {price or 'market'}")
                print(f"   ✅ Order placed: {order.order_id}")
                
                return order
            else:
                self.logger.error(f"Failed to place order: {symbol} {side} {quantity}")
                return None
                
        except Exception as e:
            self.logger.error(f"Order placement error: {e}")
            print(f"   ❌ Order failed: {e}")
            return None
    
    def _place_binance_order(self, client: BinanceClient, order: RealOrder) -> Optional[Dict]:
        """Place order on Binance"""
        try:
            if order.order_type == OrderType.MARKET:
                if order.side == OrderSide.BUY:
                    result = client.order_market_buy(
                        symbol=order.symbol,
                        quantity=order.quantity
                    )
                else:
                    result = client.order_market_sell(
                        symbol=order.symbol,
                        quantity=order.quantity
                    )
            elif order.order_type == OrderType.LIMIT:
                if order.side == OrderSide.BUY:
                    result = client.order_limit_buy(
                        symbol=order.symbol,
                        quantity=order.quantity,
                        price=str(order.price)
                    )
                else:
                    result = client.order_limit_sell(
                        symbol=order.symbol,
                        quantity=order.quantity,
                        price=str(order.price)
                    )
            else:
                return None
            
            return result
            
        except BinanceAPIException as e:
            self.logger.error(f"Binance API error: {e}")
            return None
    
    def _place_alpaca_order(self, api: tradeapi.REST, order: RealOrder) -> Optional[Dict]:
        """Place order on Alpaca"""
        try:
            if order.order_type == OrderType.MARKET:
                result = api.submit_order(
                    symbol=order.symbol,
                    qty=order.quantity,
                    side=order.side.value.lower(),
                    type='market',
                    time_in_force='gtc'
                )
            elif order.order_type == OrderType.LIMIT:
                result = api.submit_order(
                    symbol=order.symbol,
                    qty=order.quantity,
                    side=order.side.value.lower(),
                    type='limit',
                    time_in_force='gtc',
                    limit_price=order.price
                )
            else:
                return None
            
            return {'id': result.id, 'status': result.status}
            
        except Exception as e:
            self.logger.error(f"Alpaca API error: {e}")
            return None
    
    def check_order_status(self, order_id: str) -> Optional[RealOrder]:
        """Check status of an order"""
        if order_id not in self.active_orders:
            return None
        
        order = self.active_orders[order_id]
        broker = self.brokers.get(order.broker)
        
        if not broker:
            return order
        
        try:
            if order.broker == 'binance':
                result = broker.get_order(symbol=order.symbol, orderId=order_id)
                
                if result['status'] == 'FILLED':
                    order.status = OrderStatus.FILLED
                    order.filled_quantity = float(result['executedQty'])
                    order.average_price = float(result['price']) if result.get('price') else 0
                elif result['status'] == 'PARTIALLY_FILLED':
                    order.status = OrderStatus.PARTIALLY_FILLED
                    order.filled_quantity = float(result['executedQty'])
                elif result['status'] == 'CANCELED':
                    order.status = OrderStatus.CANCELLED
                    
            elif order.broker == 'alpaca':
                result = broker.get_order(order_id)
                
                if result.status == 'filled':
                    order.status = OrderStatus.FILLED
                    order.filled_quantity = float(result.filled_qty or 0)
                    order.average_price = float(result.filled_avg_price or 0)
                elif result.status == 'partially_filled':
                    order.status = OrderStatus.PARTIALLY_FILLED
                    order.filled_quantity = float(result.filled_qty or 0)
                elif result.status == 'canceled':
                    order.status = OrderStatus.CANCELLED
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error checking order status: {e}")
            return order
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        if order_id not in self.active_orders:
            return False
        
        order = self.active_orders[order_id]
        broker = self.brokers.get(order.broker)
        
        if not broker:
            return False
        
        try:
            if order.broker == 'binance':
                broker.cancel_order(symbol=order.symbol, orderId=order_id)
            elif order.broker == 'alpaca':
                broker.cancel_order(order_id)
            
            order.status = OrderStatus.CANCELLED
            self.logger.info(f"Order cancelled: {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cancelling order: {e}")
            return False
    
    def get_account_balance(self, broker_name: str) -> Dict[str, float]:
        """Get account balance"""
        if broker_name not in self.brokers:
            return {}
        
        broker = self.brokers[broker_name]
        
        try:
            if broker_name == 'binance':
                account = broker.get_account()
                balances = {}
                for balance in account['balances']:
                    if float(balance['free']) > 0 or float(balance['locked']) > 0:
                        balances[balance['asset']] = {
                            'free': float(balance['free']),
                            'locked': float(balance['locked']),
                            'total': float(balance['free']) + float(balance['locked'])
                        }
                return balances
                
            elif broker_name == 'alpaca':
                account = broker.get_account()
                return {
                    'USD': {
                        'free': float(account.buying_power),
                        'locked': 0.0,
                        'total': float(account.equity)
                    }
                }
        except Exception as e:
            self.logger.error(f"Error getting account balance: {e}")
            return {}
    
    def get_positions(self, broker_name: str) -> List[Dict]:
        """Get current positions"""
        if broker_name not in self.brokers:
            return []
        
        broker = self.brokers[broker_name]
        
        try:
            if broker_name == 'alpaca':
                positions = broker.list_positions()
                return [
                    {
                        'symbol': pos.symbol,
                        'quantity': float(pos.qty),
                        'market_value': float(pos.market_value),
                        'cost_basis': float(pos.cost_basis),
                        'unrealized_pl': float(pos.unrealized_pl),
                        'side': 'long' if float(pos.qty) > 0 else 'short'
                    }
                    for pos in positions
                ]
            # Binance doesn't have traditional positions, just balances
            return []
            
        except Exception as e:
            self.logger.error(f"Error getting positions: {e}")
            return []
    
    def get_trading_status(self) -> Dict[str, Any]:
        """Get overall trading status"""
        status = {
            'timestamp': datetime.now().isoformat(),
            'paper_trading': self.paper_trading,
            'connected_brokers': list(self.brokers.keys()),
            'active_orders': len(self.active_orders),
            'total_trades': len(self.trade_history)
        }
        
        # Get balances from all brokers
        for broker_name in self.brokers.keys():
            balances = self.get_account_balance(broker_name)
            status[f'{broker_name}_balances'] = balances
            
            positions = self.get_positions(broker_name)
            status[f'{broker_name}_positions'] = positions
        
        return status

def main():
    """Test real trading interface"""
    print("🔌 REAL TRADING INTERFACE - TESTING")
    print("=" * 60)
    
    # Initialize in paper trading mode
    trading = RealTradingInterface(paper_trading=True)
    
    # Show status
    status = trading.get_trading_status()
    print(f"\n📊 TRADING STATUS:")
    print(f"   Mode: {'Paper Trading' if status['paper_trading'] else 'Live Trading'}")
    print(f"   Connected brokers: {status['connected_brokers']}")
    print(f"   Active orders: {status['active_orders']}")
    
    # Show balances
    for broker in status['connected_brokers']:
        balances = status.get(f'{broker}_balances', {})
        print(f"\n💰 {broker.upper()} BALANCES:")
        for asset, balance in balances.items():
            if balance['total'] > 0:
                print(f"   {asset}: {balance['total']:.8f}")
    
    print(f"\n✅ REAL TRADING INTERFACE READY")
    print(f"   Set PAPER_TRADING=False for live trading")
    print(f"   Add API keys to environment variables")

if __name__ == "__main__":
    main()
