#!/usr/bin/env python3
"""
Noryon Training Configuration System
Comprehensive configuration management for AI model training:
- Model-specific configurations
- Hyperparameter settings
- Training pipeline parameters
- Data processing configurations
- Validation and evaluation settings
- Resource management
- Experiment tracking
- Model deployment settings
"""

import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import json
from pathlib import Path
import sys
import yaml
from collections import defaultdict

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelType(Enum):
    """Types of models in the system"""
    MOMENTUM_STRATEGY = "momentum_strategy"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_REGIME = "market_regime"
    ENSEMBLE_VOTING = "ensemble_voting"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    GENETIC_ALGORITHM = "genetic_algorithm"
    LLM_BRAIN = "llm_brain"
    MULTI_AGENT = "multi_agent"

class TrainingMode(Enum):
    """Training execution modes"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    DISTRIBUTED = "distributed"
    FEDERATED = "federated"

class OptimizationAlgorithm(Enum):
    """Optimization algorithms"""
    ADAM = "adam"
    SGD = "sgd"
    RMSPROP = "rmsprop"
    ADAGRAD = "adagrad"
    ADADELTA = "adadelta"
    ADAMW = "adamw"
    GENETIC = "genetic"
    BAYESIAN = "bayesian"
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"

class ValidationStrategy(Enum):
    """Validation strategies"""
    HOLDOUT = "holdout"
    K_FOLD = "k_fold"
    TIME_SERIES_SPLIT = "time_series_split"
    WALK_FORWARD = "walk_forward"
    PURGED_K_FOLD = "purged_k_fold"
    MONTE_CARLO = "monte_carlo"

class DataSplitStrategy(Enum):
    """Data splitting strategies"""
    RANDOM = "random"
    TEMPORAL = "temporal"
    STRATIFIED = "stratified"
    CLUSTERED = "clustered"
    CUSTOM = "custom"

@dataclass
class ResourceConfig:
    """Resource allocation configuration"""
    # Compute resources
    max_cpu_cores: int = 4
    max_memory_gb: float = 8.0
    gpu_enabled: bool = False
    max_gpu_memory_gb: float = 4.0
    
    # Training limits
    max_training_time_hours: float = 24.0
    max_epochs: int = 1000
    early_stopping_patience: int = 50
    
    # Parallel processing
    max_parallel_jobs: int = 2
    batch_size: int = 32
    num_workers: int = 2
    
    # Storage
    max_model_size_mb: float = 500.0
    checkpoint_frequency: int = 10
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class DataConfig:
    """Data processing configuration"""
    # Data sources
    data_sources: List[str] = field(default_factory=lambda: ["yahoo", "alpha_vantage"])
    symbols: List[str] = field(default_factory=lambda: ["SPY", "QQQ", "IWM"])
    
    # Time periods
    start_date: str = "2020-01-01"
    end_date: str = "2023-12-31"
    timeframe: str = "1d"  # 1m, 5m, 15m, 1h, 1d
    
    # Data splitting
    train_ratio: float = 0.7
    validation_ratio: float = 0.15
    test_ratio: float = 0.15
    split_strategy: DataSplitStrategy = DataSplitStrategy.TEMPORAL
    
    # Feature engineering
    lookback_window: int = 60
    prediction_horizon: int = 1
    feature_scaling: str = "standard"  # standard, minmax, robust, none
    
    # Data quality
    min_data_quality: float = 0.95
    handle_missing: str = "interpolate"  # drop, interpolate, forward_fill
    outlier_detection: bool = True
    outlier_threshold: float = 3.0
    
    # Augmentation
    data_augmentation: bool = False
    augmentation_factor: float = 1.5
    noise_level: float = 0.01
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['split_strategy'] = self.split_strategy.value
        return result

@dataclass
class HyperparameterConfig:
    """Hyperparameter configuration for models"""
    # Neural network architecture
    hidden_layers: List[int] = field(default_factory=lambda: [128, 64, 32])
    activation_function: str = "relu"
    dropout_rate: float = 0.2
    batch_normalization: bool = True
    
    # Training parameters
    learning_rate: float = 0.001
    batch_size: int = 32
    epochs: int = 100
    optimizer: OptimizationAlgorithm = OptimizationAlgorithm.ADAM
    
    # Regularization
    l1_regularization: float = 0.0
    l2_regularization: float = 0.001
    weight_decay: float = 0.0001
    
    # Learning rate scheduling
    lr_scheduler: str = "step"  # step, exponential, cosine, plateau
    lr_decay_factor: float = 0.1
    lr_decay_patience: int = 10
    
    # Early stopping
    early_stopping: bool = True
    patience: int = 20
    min_delta: float = 0.001
    
    # Model-specific parameters
    model_specific: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['optimizer'] = self.optimizer.value
        return result

@dataclass
class ValidationConfig:
    """Validation and evaluation configuration"""
    # Validation strategy
    strategy: ValidationStrategy = ValidationStrategy.TIME_SERIES_SPLIT
    n_splits: int = 5
    test_size: float = 0.2
    
    # Cross-validation specific
    shuffle: bool = False
    random_state: int = 42
    
    # Time series specific
    gap: int = 0  # Gap between train and validation
    max_train_size: Optional[int] = None
    
    # Metrics
    primary_metric: str = "sharpe_ratio"
    secondary_metrics: List[str] = field(default_factory=lambda: [
        "total_return", "max_drawdown", "win_rate", "profit_factor"
    ])
    
    # Thresholds
    min_performance_threshold: float = 0.0
    overfitting_threshold: float = 0.1
    
    # Evaluation settings
    confidence_level: float = 0.95
    bootstrap_samples: int = 1000
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['strategy'] = self.strategy.value
        return result

@dataclass
class ExperimentConfig:
    """Experiment tracking configuration"""
    # Experiment identification
    experiment_name: str = "noryon_training"
    run_name: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    # Tracking settings
    track_metrics: bool = True
    track_parameters: bool = True
    track_artifacts: bool = True
    track_models: bool = True
    
    # MLflow settings
    mlflow_tracking_uri: str = "./mlruns"
    mlflow_experiment_name: str = "noryon_ai_trading"
    
    # Logging
    log_frequency: int = 10  # Log every N epochs
    save_checkpoints: bool = True
    checkpoint_frequency: int = 50
    
    # Artifacts
    save_plots: bool = True
    save_model_architecture: bool = True
    save_training_history: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

@dataclass
class ModelSpecificConfig:
    """Model-specific configuration"""
    model_type: ModelType
    config: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        result['model_type'] = self.model_type.value
        return result

@dataclass
class TrainingConfig:
    """Main training configuration"""
    # Basic settings
    config_name: str = "default_training_config"
    version: str = "1.0.0"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # Training mode
    training_mode: TrainingMode = TrainingMode.SEQUENTIAL
    models_to_train: List[ModelType] = field(default_factory=lambda: [
        ModelType.MOMENTUM_STRATEGY,
        ModelType.RISK_ASSESSMENT,
        ModelType.MARKET_REGIME
    ])
    
    # Sub-configurations
    resource_config: ResourceConfig = field(default_factory=ResourceConfig)
    data_config: DataConfig = field(default_factory=DataConfig)
    hyperparameter_config: HyperparameterConfig = field(default_factory=HyperparameterConfig)
    validation_config: ValidationConfig = field(default_factory=ValidationConfig)
    experiment_config: ExperimentConfig = field(default_factory=ExperimentConfig)
    
    # Model-specific configurations
    model_configs: Dict[ModelType, ModelSpecificConfig] = field(default_factory=dict)
    
    # Output settings
    output_directory: str = "./training_outputs"
    model_save_directory: str = "./models"
    
    # Advanced settings
    enable_hyperparameter_tuning: bool = True
    hyperparameter_tuning_trials: int = 50
    enable_ensemble_training: bool = True
    
    def __post_init__(self):
        """Initialize model-specific configurations"""
        if not self.model_configs:
            self._initialize_model_configs()
    
    def _initialize_model_configs(self):
        """Initialize default model-specific configurations"""
        
        # Momentum Strategy Model
        self.model_configs[ModelType.MOMENTUM_STRATEGY] = ModelSpecificConfig(
            model_type=ModelType.MOMENTUM_STRATEGY,
            config={
                "ma_short_period": 12,
                "ma_long_period": 26,
                "rsi_period": 14,
                "rsi_overbought": 70,
                "rsi_oversold": 30,
                "volume_threshold": 1.5,
                "signal_threshold": 0.6,
                "lookback_periods": 100,
                "feature_columns": ["close", "volume", "high", "low"],
                "target_column": "future_return",
                "model_architecture": "lstm",
                "lstm_units": [64, 32],
                "dense_units": [16, 8],
                "sequence_length": 60
            }
        )
        
        # Risk Assessment Model
        self.model_configs[ModelType.RISK_ASSESSMENT] = ModelSpecificConfig(
            model_type=ModelType.RISK_ASSESSMENT,
            config={
                "risk_metrics": ["volatility", "var", "cvar", "max_drawdown"],
                "confidence_levels": [0.95, 0.99],
                "lookback_window": 252,
                "volatility_window": 30,
                "correlation_threshold": 0.7,
                "position_size_limits": {"min": 0.01, "max": 0.2},
                "model_architecture": "dense",
                "hidden_layers": [128, 64, 32, 16],
                "output_activation": "sigmoid",
                "loss_function": "mse"
            }
        )
        
        # Market Regime Detection Model
        self.model_configs[ModelType.MARKET_REGIME] = ModelSpecificConfig(
            model_type=ModelType.MARKET_REGIME,
            config={
                "regime_types": [
                    "volatile_bull", "volatile_bear", "choppy_volatile",
                    "trending_bull", "trending_bear", "ranging"
                ],
                "volatility_window": 20,
                "trend_window": 14,
                "adx_threshold": 25,
                "volatility_threshold": 0.02,
                "regime_features": [
                    "volatility", "trend_strength", "momentum", 
                    "volume_profile", "market_breadth"
                ],
                "model_architecture": "transformer",
                "num_heads": 8,
                "num_layers": 4,
                "d_model": 128,
                "classification_threshold": 0.5
            }
        )
        
        # Ensemble Voting Model
        self.model_configs[ModelType.ENSEMBLE_VOTING] = ModelSpecificConfig(
            model_type=ModelType.ENSEMBLE_VOTING,
            config={
                "voting_methods": ["weighted", "confidence", "performance"],
                "weight_update_frequency": "daily",
                "performance_window": 30,
                "confidence_threshold": 0.6,
                "model_specializations": {
                    "momentum": ["trending_bull", "trending_bear"],
                    "mean_reversion": ["ranging", "choppy_volatile"],
                    "volatility": ["volatile_bull", "volatile_bear"]
                },
                "ensemble_size": 5,
                "diversity_threshold": 0.3,
                "meta_model_architecture": "gradient_boosting"
            }
        )
        
        # Reinforcement Learning Model
        self.model_configs[ModelType.REINFORCEMENT_LEARNING] = ModelSpecificConfig(
            model_type=ModelType.REINFORCEMENT_LEARNING,
            config={
                "algorithm": "ppo",  # ppo, a3c, ddpg, td3, sac
                "state_space_size": 100,
                "action_space_size": 3,  # buy, sell, hold
                "reward_function": "sharpe_ratio",
                "discount_factor": 0.99,
                "learning_rate": 0.0003,
                "batch_size": 64,
                "memory_size": 10000,
                "exploration_rate": 0.1,
                "exploration_decay": 0.995,
                "target_update_frequency": 100,
                "actor_network": [256, 128, 64],
                "critic_network": [256, 128, 64],
                "environment_config": {
                    "initial_balance": 100000,
                    "transaction_cost": 0.001,
                    "slippage": 0.0005
                }
            }
        )
        
        # Genetic Algorithm Model
        self.model_configs[ModelType.GENETIC_ALGORITHM] = ModelSpecificConfig(
            model_type=ModelType.GENETIC_ALGORITHM,
            config={
                "population_size": 100,
                "generations": 50,
                "mutation_rate": 0.1,
                "crossover_rate": 0.8,
                "selection_method": "tournament",
                "tournament_size": 5,
                "elitism_rate": 0.1,
                "fitness_function": "multi_objective",
                "objectives": ["return", "sharpe_ratio", "max_drawdown"],
                "parameter_ranges": {
                    "ma_short": [5, 20],
                    "ma_long": [20, 50],
                    "rsi_period": [10, 20],
                    "stop_loss": [0.02, 0.1],
                    "take_profit": [0.05, 0.2]
                },
                "convergence_threshold": 0.001,
                "diversity_preservation": True
            }
        )
    
    def get_model_config(self, model_type: ModelType) -> Optional[ModelSpecificConfig]:
        """Get configuration for specific model type"""
        return self.model_configs.get(model_type)
    
    def update_model_config(self, model_type: ModelType, config_updates: Dict[str, Any]):
        """Update configuration for specific model type"""
        if model_type in self.model_configs:
            self.model_configs[model_type].config.update(config_updates)
        else:
            self.model_configs[model_type] = ModelSpecificConfig(
                model_type=model_type,
                config=config_updates
            )
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Validate data ratios
        total_ratio = (self.data_config.train_ratio + 
                      self.data_config.validation_ratio + 
                      self.data_config.test_ratio)
        if abs(total_ratio - 1.0) > 0.001:
            issues.append(f"Data split ratios sum to {total_ratio}, should be 1.0")
        
        # Validate resource limits
        if self.resource_config.max_memory_gb < 1.0:
            issues.append("Memory allocation too low (< 1GB)")
        
        if self.resource_config.max_training_time_hours <= 0:
            issues.append("Training time must be positive")
        
        # Validate hyperparameters
        if self.hyperparameter_config.learning_rate <= 0:
            issues.append("Learning rate must be positive")
        
        if self.hyperparameter_config.batch_size <= 0:
            issues.append("Batch size must be positive")
        
        # Validate model configurations
        for model_type in self.models_to_train:
            if model_type not in self.model_configs:
                issues.append(f"Missing configuration for model: {model_type.value}")
        
        return issues
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        result = {
            'config_name': self.config_name,
            'version': self.version,
            'created_at': self.created_at,
            'training_mode': self.training_mode.value,
            'models_to_train': [model.value for model in self.models_to_train],
            'resource_config': self.resource_config.to_dict(),
            'data_config': self.data_config.to_dict(),
            'hyperparameter_config': self.hyperparameter_config.to_dict(),
            'validation_config': self.validation_config.to_dict(),
            'experiment_config': self.experiment_config.to_dict(),
            'model_configs': {model_type.value: config.to_dict() 
                            for model_type, config in self.model_configs.items()},
            'output_directory': self.output_directory,
            'model_save_directory': self.model_save_directory,
            'enable_hyperparameter_tuning': self.enable_hyperparameter_tuning,
            'hyperparameter_tuning_trials': self.hyperparameter_tuning_trials,
            'enable_ensemble_training': self.enable_ensemble_training
        }
        return result
    
    def save_to_file(self, filepath: Union[str, Path]):
        """Save configuration to file"""
        filepath = Path(filepath)
        filepath.parent.mkdir(parents=True, exist_ok=True)
        
        config_dict = self.to_dict()
        
        if filepath.suffix.lower() == '.json':
            with open(filepath, 'w') as f:
                json.dump(config_dict, f, indent=2)
        elif filepath.suffix.lower() in ['.yml', '.yaml']:
            with open(filepath, 'w') as f:
                yaml.dump(config_dict, f, default_flow_style=False, indent=2)
        else:
            raise ValueError(f"Unsupported file format: {filepath.suffix}")
        
        logger.info(f"Configuration saved to: {filepath}")
    
    @classmethod
    def load_from_file(cls, filepath: Union[str, Path]) -> 'TrainingConfig':
        """Load configuration from file"""
        filepath = Path(filepath)
        
        if not filepath.exists():
            raise FileNotFoundError(f"Configuration file not found: {filepath}")
        
        if filepath.suffix.lower() == '.json':
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
        elif filepath.suffix.lower() in ['.yml', '.yaml']:
            with open(filepath, 'r') as f:
                config_dict = yaml.safe_load(f)
        else:
            raise ValueError(f"Unsupported file format: {filepath.suffix}")
        
        return cls.from_dict(config_dict)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'TrainingConfig':
        """Create configuration from dictionary"""
        
        # Extract sub-configurations
        resource_config = ResourceConfig(**config_dict.get('resource_config', {}))
        data_config = DataConfig(**config_dict.get('data_config', {}))
        
        # Handle enum conversion for data config
        if 'split_strategy' in config_dict.get('data_config', {}):
            data_config.split_strategy = DataSplitStrategy(config_dict['data_config']['split_strategy'])
        
        hyperparameter_config = HyperparameterConfig(**config_dict.get('hyperparameter_config', {}))
        
        # Handle enum conversion for hyperparameter config
        if 'optimizer' in config_dict.get('hyperparameter_config', {}):
            hyperparameter_config.optimizer = OptimizationAlgorithm(config_dict['hyperparameter_config']['optimizer'])
        
        validation_config = ValidationConfig(**config_dict.get('validation_config', {}))
        
        # Handle enum conversion for validation config
        if 'strategy' in config_dict.get('validation_config', {}):
            validation_config.strategy = ValidationStrategy(config_dict['validation_config']['strategy'])
        
        experiment_config = ExperimentConfig(**config_dict.get('experiment_config', {}))
        
        # Convert model configurations
        model_configs = {}
        for model_type_str, model_config_dict in config_dict.get('model_configs', {}).items():
            model_type = ModelType(model_type_str)
            model_configs[model_type] = ModelSpecificConfig(
                model_type=model_type,
                config=model_config_dict.get('config', {})
            )
        
        # Convert models to train
        models_to_train = [ModelType(model_str) for model_str in config_dict.get('models_to_train', [])]
        
        # Convert training mode
        training_mode = TrainingMode(config_dict.get('training_mode', 'sequential'))
        
        # Create main configuration
        config = cls(
            config_name=config_dict.get('config_name', 'loaded_config'),
            version=config_dict.get('version', '1.0.0'),
            created_at=config_dict.get('created_at', datetime.now().isoformat()),
            training_mode=training_mode,
            models_to_train=models_to_train,
            resource_config=resource_config,
            data_config=data_config,
            hyperparameter_config=hyperparameter_config,
            validation_config=validation_config,
            experiment_config=experiment_config,
            model_configs=model_configs,
            output_directory=config_dict.get('output_directory', './training_outputs'),
            model_save_directory=config_dict.get('model_save_directory', './models'),
            enable_hyperparameter_tuning=config_dict.get('enable_hyperparameter_tuning', True),
            hyperparameter_tuning_trials=config_dict.get('hyperparameter_tuning_trials', 50),
            enable_ensemble_training=config_dict.get('enable_ensemble_training', True)
        )
        
        return config

class ConfigManager:
    """Configuration management utility"""
    
    def __init__(self, config_directory: str = "./configs"):
        self.config_directory = Path(config_directory)
        self.config_directory.mkdir(parents=True, exist_ok=True)
        
        # Default configurations
        self.default_configs = {
            'development': self._create_development_config(),
            'production': self._create_production_config(),
            'testing': self._create_testing_config(),
            'research': self._create_research_config()
        }
    
    def _create_development_config(self) -> TrainingConfig:
        """Create development configuration"""
        config = TrainingConfig(
            config_name="development",
            training_mode=TrainingMode.SEQUENTIAL
        )
        
        # Reduce resource requirements for development
        config.resource_config.max_cpu_cores = 2
        config.resource_config.max_memory_gb = 4.0
        config.resource_config.max_training_time_hours = 2.0
        config.hyperparameter_config.epochs = 10
        config.data_config.symbols = ["SPY"]  # Single symbol for faster testing
        
        return config
    
    def _create_production_config(self) -> TrainingConfig:
        """Create production configuration"""
        config = TrainingConfig(
            config_name="production",
            training_mode=TrainingMode.PARALLEL
        )
        
        # Full resource allocation for production
        config.resource_config.max_cpu_cores = 8
        config.resource_config.max_memory_gb = 16.0
        config.resource_config.gpu_enabled = True
        config.resource_config.max_gpu_memory_gb = 8.0
        config.resource_config.max_training_time_hours = 48.0
        
        # Extended training
        config.hyperparameter_config.epochs = 500
        config.enable_hyperparameter_tuning = True
        config.hyperparameter_tuning_trials = 100
        
        return config
    
    def _create_testing_config(self) -> TrainingConfig:
        """Create testing configuration"""
        config = TrainingConfig(
            config_name="testing",
            training_mode=TrainingMode.SEQUENTIAL
        )
        
        # Minimal resources for testing
        config.resource_config.max_cpu_cores = 1
        config.resource_config.max_memory_gb = 2.0
        config.resource_config.max_training_time_hours = 0.5
        config.hyperparameter_config.epochs = 5
        config.data_config.symbols = ["SPY"]
        config.enable_hyperparameter_tuning = False
        
        return config
    
    def _create_research_config(self) -> TrainingConfig:
        """Create research configuration"""
        config = TrainingConfig(
            config_name="research",
            training_mode=TrainingMode.PARALLEL
        )
        
        # Research-focused settings
        config.enable_hyperparameter_tuning = True
        config.hyperparameter_tuning_trials = 200
        config.validation_config.strategy = ValidationStrategy.K_FOLD
        config.validation_config.n_splits = 10
        config.experiment_config.track_artifacts = True
        config.experiment_config.save_plots = True
        
        return config
    
    def get_config(self, config_name: str) -> TrainingConfig:
        """Get configuration by name"""
        if config_name in self.default_configs:
            return self.default_configs[config_name]
        
        # Try to load from file
        config_path = self.config_directory / f"{config_name}.yaml"
        if config_path.exists():
            return TrainingConfig.load_from_file(config_path)
        
        raise ValueError(f"Configuration '{config_name}' not found")
    
    def save_config(self, config: TrainingConfig, config_name: Optional[str] = None):
        """Save configuration to file"""
        if config_name is None:
            config_name = config.config_name
        
        config_path = self.config_directory / f"{config_name}.yaml"
        config.save_to_file(config_path)
    
    def list_configs(self) -> List[str]:
        """List available configurations"""
        configs = list(self.default_configs.keys())
        
        # Add file-based configs
        for config_file in self.config_directory.glob("*.yaml"):
            config_name = config_file.stem
            if config_name not in configs:
                configs.append(config_name)
        
        return sorted(configs)
    
    def create_custom_config(self, base_config: str, modifications: Dict[str, Any], 
                           new_config_name: str) -> TrainingConfig:
        """Create custom configuration based on existing one"""
        base = self.get_config(base_config)
        
        # Apply modifications
        config_dict = base.to_dict()
        self._apply_modifications(config_dict, modifications)
        
        # Create new config
        new_config = TrainingConfig.from_dict(config_dict)
        new_config.config_name = new_config_name
        new_config.created_at = datetime.now().isoformat()
        
        return new_config
    
    def _apply_modifications(self, config_dict: Dict[str, Any], modifications: Dict[str, Any]):
        """Apply modifications to configuration dictionary"""
        for key, value in modifications.items():
            if '.' in key:
                # Nested key (e.g., 'resource_config.max_cpu_cores')
                keys = key.split('.')
                current = config_dict
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            else:
                config_dict[key] = value

# Example usage and configuration templates
def create_example_configs():
    """Create example configurations"""
    
    config_manager = ConfigManager()
    
    # Save default configurations
    for config_name, config in config_manager.default_configs.items():
        config_manager.save_config(config, config_name)
    
    # Create a custom high-performance config
    high_perf_modifications = {
        'resource_config.max_cpu_cores': 16,
        'resource_config.max_memory_gb': 32.0,
        'resource_config.gpu_enabled': True,
        'resource_config.max_gpu_memory_gb': 16.0,
        'hyperparameter_config.epochs': 1000,
        'hyperparameter_tuning_trials': 500,
        'training_mode': 'parallel'
    }
    
    high_perf_config = config_manager.create_custom_config(
        'production', high_perf_modifications, 'high_performance'
    )
    config_manager.save_config(high_perf_config)
    
    logger.info("Example configurations created")
    logger.info(f"Available configs: {config_manager.list_configs()}")

if __name__ == "__main__":
    # Create example configurations
    create_example_configs()
    
    # Demonstrate configuration usage
    config_manager = ConfigManager()
    
    # Load and validate a configuration
    config = config_manager.get_config('development')
    issues = config.validate_config()
    
    if issues:
        logger.warning(f"Configuration issues: {issues}")
    else:
        logger.info("Configuration is valid")
    
    # Display configuration summary
    logger.info(f"Configuration: {config.config_name}")
    logger.info(f"Training mode: {config.training_mode.value}")
    logger.info(f"Models to train: {[m.value for m in config.models_to_train]}")
    logger.info(f"Max epochs: {config.hyperparameter_config.epochs}")
    logger.info(f"Resource allocation: {config.resource_config.max_cpu_cores} CPU cores, {config.resource_config.max_memory_gb}GB RAM")