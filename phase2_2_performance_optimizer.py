#!/usr/bin/env python3
"""
Phase 2.2: Model Performance Optimizer
Optimize the performance of your Phase 2 enhanced models
"""

import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class ModelPerformanceOptimizer:
    def __init__(self):
        self.phase2_models = [
            'phase2-smart-unrestricted-qwen3-14b-latest',
            'phase2-unrestricted-noryon-qwen3-finance-v2-latest',
            'phase2-unrestricted-noryon-phi-4-9b-finance-latest',
            'phase2-unrestricted-unrestricted-qwen3-14b-latest'
        ]
        
        self.optimization_configs = {
            'speed_optimized': {
                'name': 'Speed Optimized',
                'description': 'Faster responses, good quality',
                'parameters': {
                    'temperature': 0.6,
                    'top_p': 0.8,
                    'repeat_penalty': 1.0,
                    'num_ctx': 4096,
                    'num_predict': 1024
                }
            },
            'quality_optimized': {
                'name': 'Quality Optimized', 
                'description': 'Best quality responses, slower',
                'parameters': {
                    'temperature': 0.8,
                    'top_p': 0.95,
                    'repeat_penalty': 1.1,
                    'num_ctx': 8192,
                    'num_predict': 2048
                }
            },
            'balanced': {
                'name': 'Balanced',
                'description': 'Good balance of speed and quality',
                'parameters': {
                    'temperature': 0.7,
                    'top_p': 0.9,
                    'repeat_penalty': 1.05,
                    'num_ctx': 6144,
                    'num_predict': 1536
                }
            }
        }
        
        self.performance_results = {}
    
    def step1_benchmark_current_performance(self):
        """Step 1: Benchmark current performance of Phase 2 models"""
        print("STEP 1: Benchmarking Phase 2 model performance...")
        print("=" * 60)
        
        benchmark_questions = [
            "Analyze Bitcoin's price action and provide a trading strategy",
            "Explain portfolio diversification for a $100k investment",
            "What are the key economic indicators to watch this week?"
        ]
        
        results = {}
        
        for model in self.phase2_models:
            print(f"\nBenchmarking {model}...")
            model_results = {}
            
            for i, question in enumerate(benchmark_questions, 1):
                print(f"  Question {i}/3...")
                
                start_time = time.time()
                try:
                    result = subprocess.run([
                        'ollama', 'run', model, question
                    ], capture_output=True, text=True, timeout=60)
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if result.returncode == 0:
                        response = result.stdout.strip()
                        model_results[f"q{i}"] = {
                            'success': True,
                            'response_time': response_time,
                            'response_length': len(response),
                            'words_per_second': len(response.split()) / response_time if response_time > 0 else 0
                        }
                        print(f"    ✅ {response_time:.1f}s - {len(response)} chars - {len(response.split())/response_time:.1f} words/sec")
                    else:
                        model_results[f"q{i}"] = {'success': False, 'error': 'failed'}
                        print(f"    ❌ Failed")
                        
                except subprocess.TimeoutExpired:
                    model_results[f"q{i}"] = {'success': False, 'error': 'timeout'}
                    print(f"    ⏰ Timeout")
                except Exception as e:
                    model_results[f"q{i}"] = {'success': False, 'error': str(e)}
                    print(f"    ❌ Error: {e}")
            
            # Calculate averages
            successful_results = [r for r in model_results.values() if r.get('success')]
            if successful_results:
                avg_time = sum(r['response_time'] for r in successful_results) / len(successful_results)
                avg_length = sum(r['response_length'] for r in successful_results) / len(successful_results)
                avg_wps = sum(r['words_per_second'] for r in successful_results) / len(successful_results)
                
                model_results['averages'] = {
                    'avg_response_time': avg_time,
                    'avg_response_length': avg_length,
                    'avg_words_per_second': avg_wps,
                    'success_rate': len(successful_results) / len(benchmark_questions)
                }
                
                print(f"  📊 Averages: {avg_time:.1f}s, {avg_length:.0f} chars, {avg_wps:.1f} wps")
            
            results[model] = model_results
        
        self.performance_results = results
        return results
    
    def step2_create_optimized_variants(self):
        """Step 2: Create optimized variants of best performing models"""
        print("\nSTEP 2: Creating optimized variants...")
        print("=" * 60)
        
        # Find best performing model
        best_model = self._find_best_model()
        print(f"Best performing model: {best_model}")
        
        created_models = []
        
        for opt_type, config in self.optimization_configs.items():
            optimized_name = f"{opt_type}-{best_model.replace(':', '-')}"
            
            print(f"\nCreating {config['name']} variant: {optimized_name}")
            
            # Create optimized Modelfile
            modelfile_content = f'''FROM {best_model}

# {config['name'].upper()} OPTIMIZATION
SYSTEM """You are an optimized AI trading and finance expert.

OPTIMIZATION FOCUS: {config['description']}

CORE CAPABILITIES:
- Advanced financial analysis and market insights
- Real-time trading strategy development
- Risk assessment and portfolio optimization
- Economic trend analysis and forecasting
- Cryptocurrency and traditional market expertise

RESPONSE OPTIMIZATION:
- Provide precise, actionable financial advice
- Structure responses for maximum clarity
- Focus on practical implementation
- Include specific examples and calculations
- Consider multiple market scenarios

Always deliver professional-grade financial analysis optimized for {config['description'].lower()}."""

'''
            
            # Add optimized parameters
            for param, value in config['parameters'].items():
                modelfile_content += f"PARAMETER {param} {value}\n"
            
            # Create the model
            try:
                modelfile_path = f"Modelfile.{optimized_name}"
                with open(modelfile_path, 'w') as f:
                    f.write(modelfile_content)
                
                result = subprocess.run([
                    'ollama', 'create', optimized_name, '-f', modelfile_path
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"  ✅ Created: {optimized_name}")
                    created_models.append(optimized_name)
                else:
                    print(f"  ❌ Failed: {result.stderr}")
                    
            except Exception as e:
                print(f"  ❌ Error: {e}")
        
        return created_models
    
    def _find_best_model(self):
        """Find the best performing model from benchmarks"""
        best_model = None
        best_score = 0
        
        for model, results in self.performance_results.items():
            if 'averages' in results:
                # Score based on success rate and words per second
                score = (results['averages']['success_rate'] * 100) + results['averages']['avg_words_per_second']
                if score > best_score:
                    best_score = score
                    best_model = model
        
        return best_model or self.phase2_models[0]
    
    def step3_performance_comparison(self, optimized_models):
        """Step 3: Compare optimized models performance"""
        print(f"\nSTEP 3: Performance comparison...")
        print("=" * 60)
        
        test_question = "Provide a detailed analysis of current market conditions and recommend 3 specific trading opportunities"
        
        all_models = self.phase2_models + optimized_models
        comparison_results = {}
        
        for model in all_models:
            print(f"\nTesting {model}...")
            
            start_time = time.time()
            try:
                result = subprocess.run([
                    'ollama', 'run', model, test_question
                ], capture_output=True, text=True, timeout=90)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    words = len(response.split())
                    
                    comparison_results[model] = {
                        'success': True,
                        'response_time': response_time,
                        'response_length': len(response),
                        'word_count': words,
                        'words_per_second': words / response_time if response_time > 0 else 0,
                        'quality_score': self._assess_response_quality(response)
                    }
                    
                    print(f"  ✅ {response_time:.1f}s - {len(response)} chars - {words/response_time:.1f} wps - Quality: {comparison_results[model]['quality_score']}/10")
                else:
                    comparison_results[model] = {'success': False}
                    print(f"  ❌ Failed")
                    
            except subprocess.TimeoutExpired:
                comparison_results[model] = {'success': False, 'error': 'timeout'}
                print(f"  ⏰ Timeout")
            except Exception as e:
                comparison_results[model] = {'success': False, 'error': str(e)}
                print(f"  ❌ Error: {e}")
        
        # Show comparison table
        self._display_comparison_table(comparison_results)
        
        return comparison_results
    
    def _assess_response_quality(self, response):
        """Simple quality assessment based on response characteristics"""
        score = 5  # Base score
        
        # Length bonus
        if len(response) > 1000:
            score += 1
        if len(response) > 2000:
            score += 1
        
        # Structure bonus
        if any(marker in response for marker in ['1.', '2.', '3.', '•', '-']):
            score += 1
        
        # Finance keywords bonus
        finance_keywords = ['trading', 'market', 'analysis', 'strategy', 'risk', 'portfolio', 'investment']
        keyword_count = sum(1 for keyword in finance_keywords if keyword.lower() in response.lower())
        score += min(keyword_count, 2)
        
        return min(score, 10)
    
    def _display_comparison_table(self, results):
        """Display performance comparison table"""
        print(f"\nPERFORMANCE COMPARISON:")
        print("=" * 100)
        print(f"{'Model':<50} {'Time':<8} {'Length':<8} {'WPS':<8} {'Quality':<8}")
        print("-" * 100)
        
        # Sort by overall performance score
        sorted_results = []
        for model, result in results.items():
            if result.get('success'):
                # Overall score: quality + speed factor
                speed_factor = max(0, 10 - result['response_time'])  # Faster = higher score
                overall_score = result['quality_score'] + speed_factor
                sorted_results.append((model, result, overall_score))
        
        sorted_results.sort(key=lambda x: x[2], reverse=True)
        
        for model, result, score in sorted_results:
            model_short = model.replace('phase2-', '').replace(':latest', '')[:45]
            print(f"{model_short:<50} {result['response_time']:<8.1f} {result['response_length']:<8} {result['words_per_second']:<8.1f} {result['quality_score']:<8}")
        
        if sorted_results:
            best_model = sorted_results[0][0]
            print(f"\n🏆 BEST OVERALL: {best_model}")
    
    def step4_create_ultimate_model(self, comparison_results):
        """Step 4: Create ultimate optimized model based on best results"""
        print(f"\nSTEP 4: Creating ultimate optimized model...")
        print("=" * 60)
        
        # Find best performing model
        best_model = None
        best_score = 0
        
        for model, result in comparison_results.items():
            if result.get('success'):
                # Score based on quality and reasonable speed
                score = result['quality_score'] + (10 / max(result['response_time'], 1))
                if score > best_score:
                    best_score = score
                    best_model = model
        
        if not best_model:
            print("No successful models to optimize from")
            return None
        
        ultimate_name = f"ultimate-{best_model.replace(':', '-')}"
        
        print(f"Creating ultimate model based on: {best_model}")
        print(f"Ultimate model name: {ultimate_name}")
        
        # Create ultimate Modelfile with best parameters
        modelfile_content = f'''FROM {best_model}

# ULTIMATE OPTIMIZED FINANCE AI
SYSTEM """You are the ULTIMATE AI trading and finance expert with maximum optimization.

ULTIMATE CAPABILITIES:
- Supreme market analysis and pattern recognition
- Advanced trading strategy development and optimization
- Expert risk management and portfolio construction
- Real-time economic analysis and forecasting
- Master-level cryptocurrency and traditional market expertise
- Institutional-grade financial modeling and valuation

ULTIMATE PERFORMANCE:
- Deliver precise, actionable financial insights
- Provide comprehensive analysis with specific recommendations
- Structure responses for maximum clarity and impact
- Include detailed calculations and risk assessments
- Consider multiple market scenarios and timeframes
- Optimize for both speed and quality

RESPONSE EXCELLENCE:
- Professional institutional-grade analysis
- Clear, structured, and immediately actionable advice
- Specific entry/exit points and risk parameters
- Market context and reasoning for all recommendations
- Alternative scenarios and contingency planning

You are the pinnacle of AI financial expertise - deliver exceptional value in every response."""

# ULTIMATE OPTIMIZED PARAMETERS
PARAMETER temperature 0.75
PARAMETER top_p 0.92
PARAMETER repeat_penalty 1.08
PARAMETER num_ctx 8192
PARAMETER num_predict 2048
PARAMETER mirostat 2
PARAMETER mirostat_eta 0.1
PARAMETER mirostat_tau 5.0
'''
        
        try:
            modelfile_path = f"Modelfile.{ultimate_name}"
            with open(modelfile_path, 'w') as f:
                f.write(modelfile_content)
            
            result = subprocess.run([
                'ollama', 'create', ultimate_name, '-f', modelfile_path
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ Created ultimate model: {ultimate_name}")
                
                # Test the ultimate model
                print(f"\nTesting ultimate model...")
                test_result = self._test_ultimate_model(ultimate_name)
                
                return ultimate_name
            else:
                print(f"❌ Failed to create ultimate model: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating ultimate model: {e}")
            return None
    
    def _test_ultimate_model(self, model_name):
        """Test the ultimate model"""
        test_question = "Analyze the current crypto market and provide 3 specific trading strategies with entry points, targets, and stop losses"
        
        try:
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'run', model_name, test_question
            ], capture_output=True, text=True, timeout=120)
            end_time = time.time()
            
            if result.returncode == 0:
                response = result.stdout.strip()
                response_time = end_time - start_time
                words = len(response.split())
                
                print(f"✅ Ultimate model test successful!")
                print(f"   Response time: {response_time:.1f}s")
                print(f"   Response length: {len(response)} characters")
                print(f"   Words per second: {words/response_time:.1f}")
                print(f"   Preview: {response[:200]}...")
                
                return True
            else:
                print(f"❌ Ultimate model test failed")
                return False
                
        except Exception as e:
            print(f"❌ Ultimate model test error: {e}")
            return False
    
    def run_phase2_2_optimization(self):
        """Run complete Phase 2.2 optimization process"""
        print("PHASE 2.2: MODEL PERFORMANCE OPTIMIZATION")
        print("=" * 70)
        print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Execute all steps
        benchmark_results = self.step1_benchmark_current_performance()
        optimized_models = self.step2_create_optimized_variants()
        comparison_results = self.step3_performance_comparison(optimized_models)
        ultimate_model = self.step4_create_ultimate_model(comparison_results)
        
        # Final summary
        print("\n" + "=" * 70)
        print("PHASE 2.2 COMPLETE!")
        print(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        print(f"✅ Benchmarked {len(self.phase2_models)} Phase 2 models")
        print(f"✅ Created {len(optimized_models)} optimized variants")
        if ultimate_model:
            print(f"✅ Created ultimate model: {ultimate_model}")
        print()
        print("Optimized models created:")
        for model in optimized_models:
            print(f"  • {model}")
        if ultimate_model:
            print(f"  • {ultimate_model} (ULTIMATE)")
        
        return {
            'optimized_models': optimized_models,
            'ultimate_model': ultimate_model,
            'benchmark_results': benchmark_results,
            'comparison_results': comparison_results
        }

def main():
    optimizer = ModelPerformanceOptimizer()
    results = optimizer.run_phase2_2_optimization()
    
    print(f"\nNext: Use your optimized models for maximum performance!")
    print(f"Run 'python quick_launcher.py' to access your best models!")

if __name__ == "__main__":
    main()
