#!/usr/bin/env python3
"""
Noryon AI Trading System - Health Check & Monitoring Script

This script performs comprehensive health checks on all system components
and provides real-time monitoring capabilities.

Usage:
    python health_check.py --full-check
    python health_check.py --monitor --interval 30
    python health_check.py --component database
"""

import os
import sys
import json
import yaml
import argparse
import asyncio
import time
import psutil
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.columns import Columns
from rich.bar import Bar
import subprocess

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

console = Console()

class SystemHealthChecker:
    """Comprehensive health checker for Noryon AI Trading System"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.components = []
        return cls._instance

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.health_status = {}
        self.metrics = {}
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('health_check.log')
            ]
        )
        self.logger = logging.getLogger("health_checker")
    
    def check_environment(self) -> Dict[str, Any]:
        """Check environment configuration"""
        status = {
            "component": "Environment",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            # Check .env file
            env_path = self.project_root / ".env"
            if env_path.exists():
                status["details"]["env_file"] = "✅ Present"
                
                # Check for required environment variables
                required_vars = [
                    "OPENAI_API_KEY", "DEEPSEEK_API_KEY", "ANTHROPIC_API_KEY",
                    "POSTGRES_USER", "POSTGRES_PASSWORD", "POSTGRES_DB",
                    "REDIS_PASSWORD", "JWT_SECRET"
                ]
                
                with open(env_path, 'r') as f:
                    env_content = f.read()
                
                missing_vars = []
                for var in required_vars:
                    if f"{var}=" not in env_content or f"{var}=your_" in env_content:
                        missing_vars.append(var)
                
                if missing_vars:
                    status["details"]["missing_vars"] = f"❌ {len(missing_vars)} missing"
                    status["issues"].extend([f"Missing or placeholder: {var}" for var in missing_vars])
                else:
                    status["details"]["env_vars"] = "✅ All configured"
            else:
                status["details"]["env_file"] = "❌ Missing"
                status["issues"].append("Environment file (.env) not found")
            
            # Check directory structure
            required_dirs = ["logs", "data", "config", "models", "backups"]
            missing_dirs = []
            for dir_name in required_dirs:
                if not (self.project_root / dir_name).exists():
                    missing_dirs.append(dir_name)
            
            if missing_dirs:
                status["details"]["directories"] = f"❌ {len(missing_dirs)} missing"
                status["issues"].extend([f"Missing directory: {d}" for d in missing_dirs])
            else:
                status["details"]["directories"] = "✅ All present"
            
            # Overall status
            if not status["issues"]:
                status["status"] = "HEALTHY"
            else:
                status["status"] = "DEGRADED"
                
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Environment check failed: {str(e)}")
        
        return status
    
    def check_database(self) -> Dict[str, Any]:
        """Check database connectivity and health"""
        status = {
            "component": "Database",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            # Check PostgreSQL connection
            import psycopg2
            from urllib.parse import urlparse
            
            # Get database URL from environment
            db_url = os.getenv('POSTGRES_URL', 'postgresql://noryon_user:password@localhost:5432/noryon_trading')
            parsed = urlparse(db_url)
            
            conn = psycopg2.connect(
                host=parsed.hostname,
                port=parsed.port or 5432,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path[1:],
                connect_timeout=5
            )
            
            cursor = conn.cursor()
            
            # Check database version
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            status["details"]["version"] = version.split(',')[0]
            
            # Check database size
            cursor.execute(
                "SELECT pg_size_pretty(pg_database_size(current_database()));"
            )
            db_size = cursor.fetchone()[0]
            status["details"]["size"] = db_size
            
            # Check active connections
            cursor.execute(
                "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
            )
            active_connections = cursor.fetchone()[0]
            status["details"]["active_connections"] = active_connections
            
            # Check for required tables
            cursor.execute(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';"
            )
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['trades', 'positions', 'market_data', 'ai_decisions']
            missing_tables = [t for t in required_tables if t not in tables]
            
            if missing_tables:
                status["details"]["tables"] = f"❌ {len(missing_tables)} missing"
                status["issues"].extend([f"Missing table: {t}" for t in missing_tables])
            else:
                status["details"]["tables"] = f"✅ {len(tables)} tables"
            
            cursor.close()
            conn.close()
            
            status["status"] = "HEALTHY" if not status["issues"] else "DEGRADED"
            
        except ImportError:
            status["status"] = "ERROR"
            status["issues"].append("psycopg2 not installed")
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Database connection failed: {str(e)}")
        
        return status
    
    def check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity and health"""
        status = {
            "component": "Redis",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            import redis
            
            # Connect to Redis
            redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
            r = redis.from_url(redis_url, socket_timeout=5)
            
            # Test connection
            r.ping()
            
            # Get Redis info
            info = r.info()
            status["details"]["version"] = info['redis_version']
            status["details"]["memory_used"] = f"{info['used_memory_human']}"
            status["details"]["connected_clients"] = info['connected_clients']
            status["details"]["total_commands"] = info['total_commands_processed']
            
            # Check key count
            key_count = r.dbsize()
            status["details"]["keys"] = key_count
            
            status["status"] = "HEALTHY"
            
        except ImportError:
            status["status"] = "ERROR"
            status["issues"].append("redis package not installed")
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Redis connection failed: {str(e)}")
        
        return status
    
    def check_llm_providers(self) -> Dict[str, Any]:
        """Check LLM provider connectivity"""
        status = {
            "component": "LLM Providers",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        providers = {
            "OpenAI": {
                "api_key_env": "OPENAI_API_KEY",
                "test_url": "https://api.openai.com/v1/models"
            },
            "DeepSeek": {
                "api_key_env": "DEEPSEEK_API_KEY",
                "test_url": "https://api.deepseek.com/v1/models"
            },
            "Anthropic": {
                "api_key_env": "ANTHROPIC_API_KEY",
                "test_url": "https://api.anthropic.com/v1/messages"
            }
        }
        
        healthy_providers = 0
        
        for provider_name, provider_config in providers.items():
            api_key = os.getenv(provider_config["api_key_env"])
            
            if not api_key or api_key.startswith("your_"):
                status["details"][provider_name] = "❌ No API key"
                status["issues"].append(f"{provider_name}: API key not configured")
                continue
            
            try:
                # Test API connectivity (simplified)
                headers = {"Authorization": f"Bearer {api_key}"}
                response = requests.get(
                    provider_config["test_url"],
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code in [200, 401, 403]:  # 401/403 means API key is recognized
                    status["details"][provider_name] = "✅ Connected"
                    healthy_providers += 1
                else:
                    status["details"][provider_name] = f"❌ HTTP {response.status_code}"
                    status["issues"].append(f"{provider_name}: HTTP {response.status_code}")
                    
            except requests.RequestException as e:
                status["details"][provider_name] = "❌ Connection failed"
                status["issues"].append(f"{provider_name}: {str(e)}")
        
        if healthy_providers >= 2:
            status["status"] = "HEALTHY"
        elif healthy_providers >= 1:
            status["status"] = "DEGRADED"
        else:
            status["status"] = "ERROR"
        
        return status
    
    def check_docker_services(self) -> Dict[str, Any]:
        """Check Docker services status"""
        status = {
            "component": "Docker Services",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            # Check if Docker is running
            result = subprocess.run(
                ["docker", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode != 0:
                status["status"] = "ERROR"
                status["issues"].append("Docker not available")
                return status
            
            status["details"]["docker_version"] = result.stdout.strip()
            
            # Check if Docker daemon is accessible
            daemon_check = subprocess.run(
                ["docker", "info"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if daemon_check.returncode != 0:
                status["status"] = "ERROR"
                status["issues"].append("Docker Desktop not running or accessible")
                status["details"]["note"] = "Please start Docker Desktop"
                return status
            
            # Check Docker Compose services
            result = subprocess.run(
                ["docker-compose", "-f", "docker-compose.llm-brain.yml", "ps"],
                capture_output=True,
                text=True,
                cwd=self.project_root,
                timeout=10
            )
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                services = []
                for line in lines[1:]:  # Skip header
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 2:
                            service_name = parts[0]
                            service_status = "Up" if "Up" in line else "Down"
                            services.append((service_name, service_status))
                
                running_services = sum(1 for _, s in services if s == "Up")
                total_services = len(services)
                
                status["details"]["services"] = f"{running_services}/{total_services} running"
                
                if running_services == total_services and total_services > 0:
                    status["status"] = "HEALTHY"
                elif running_services > 0:
                    status["status"] = "DEGRADED"
                    status["issues"].append(f"{total_services - running_services} services down")
                else:
                    status["status"] = "SKIPPED"
                    status["details"]["note"] = "No services deployed yet"
            else:
                status["status"] = "SKIPPED"
                status["details"]["note"] = "Services not deployed yet"
                
        except subprocess.TimeoutExpired:
            status["status"] = "ERROR"
            status["issues"].append("Docker command timeout")
        except FileNotFoundError:
            status["status"] = "ERROR"
            status["issues"].append("Docker not installed")
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Docker check failed: {str(e)}")
        
        return status
    
    def check_system_resources(self) -> Dict[str, Any]:
        """Check system resource usage"""
        status = {
            "component": "System Resources",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            status["details"]["cpu_usage"] = f"{cpu_percent:.1f}%"
            
            if cpu_percent > 90:
                status["issues"].append(f"High CPU usage: {cpu_percent:.1f}%")
            
            # Memory usage
            memory = psutil.virtual_memory()
            status["details"]["memory_usage"] = f"{memory.percent:.1f}%"
            status["details"]["memory_available"] = f"{memory.available / (1024**3):.1f}GB"
            
            if memory.percent > 90:
                status["issues"].append(f"High memory usage: {memory.percent:.1f}%")
            
            # Disk usage
            disk = psutil.disk_usage('/')
            status["details"]["disk_usage"] = f"{disk.percent:.1f}%"
            status["details"]["disk_free"] = f"{disk.free / (1024**3):.1f}GB"
            
            if disk.percent > 90:
                status["issues"].append(f"High disk usage: {disk.percent:.1f}%")
            
            # GPU usage (if available)
            try:
                import GPUtil
                gpus = GPUtil.getGPUs()
                if gpus:
                    gpu = gpus[0]
                    status["details"]["gpu_usage"] = f"{gpu.load*100:.1f}%"
                    status["details"]["gpu_memory"] = f"{gpu.memoryUtil*100:.1f}%"
                    
                    if gpu.memoryUtil > 0.9:
                        status["issues"].append(f"High GPU memory: {gpu.memoryUtil*100:.1f}%")
            except ImportError:
                status["details"]["gpu"] = "Not available"
            
            # Network connectivity
            try:
                response = requests.get("https://api.github.com", timeout=5)
                status["details"]["internet"] = "✅ Connected"
            except:
                status["details"]["internet"] = "❌ No connection"
                status["issues"].append("No internet connectivity")
            
            # Overall status
            if not status["issues"]:
                status["status"] = "HEALTHY"
            elif len(status["issues"]) <= 2:
                status["status"] = "DEGRADED"
            else:
                status["status"] = "ERROR"
                
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Resource check failed: {str(e)}")
        
        return status
    
    def check_models(self) -> Dict[str, Any]:
        """Check AI model availability"""
        status = {
            "component": "AI Models",
            "status": "UNKNOWN",
            "details": {},
            "issues": []
        }
        
        try:
            models_dir = self.project_root / "models"
            
            if not models_dir.exists():
                status["status"] = "ERROR"
                status["issues"].append("Models directory not found")
                return status
            
            # Check for trained models
            model_types = ["mistral-finance-v1", "deepseek-finance-v1", "qwen3-finance-v1"]
            available_models = 0
            
            for model_type in model_types:
                model_path = models_dir / model_type
                if model_path.exists():
                    # Check for required files
                    required_files = ["config.json", "tokenizer.json"]
                    model_files = ["pytorch_model.bin", "model.safetensors", "adapter_model.bin"]
                    
                    has_config = any((model_path / f).exists() for f in required_files)
                    has_model = any((model_path / f).exists() for f in model_files)
                    
                    if has_config and has_model:
                        status["details"][model_type] = "✅ Available"
                        available_models += 1
                    else:
                        status["details"][model_type] = "❌ Incomplete"
                        status["issues"].append(f"{model_type}: Missing files")
                else:
                    status["details"][model_type] = "❌ Not found"
                    status["issues"].append(f"{model_type}: Not trained")
            
            # Overall status
            if available_models >= 2:
                status["status"] = "HEALTHY"
            elif available_models >= 1:
                status["status"] = "DEGRADED"
            else:
                status["status"] = "ERROR"
                
        except Exception as e:
            status["status"] = "ERROR"
            status["issues"].append(f"Model check failed: {str(e)}")
        
        return status
    
    def run_full_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check"""
        checks = [
            self.check_environment,
            self.check_system_resources,
            self.check_docker_services,
            self.check_database,
            self.check_redis,
            self.check_llm_providers,
            self.check_models
        ]
        
        results = {}
        overall_status = "HEALTHY"
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            for check_func in checks:
                task = progress.add_task(f"Checking {check_func.__name__.replace('check_', '')}...", total=1)
                
                try:
                    result = check_func()
                    results[result["component"]] = result
                    
                    # Update overall status
                    if result["status"] == "ERROR":
                        overall_status = "ERROR"
                    elif result["status"] == "DEGRADED" and overall_status != "ERROR":
                        overall_status = "DEGRADED"
                        
                except Exception as e:
                    results[f"Error in {check_func.__name__}"] = {
                        "component": check_func.__name__,
                        "status": "ERROR",
                        "details": {},
                        "issues": [str(e)]
                    }
                    overall_status = "ERROR"
                
                progress.update(task, completed=1)
        
        results["overall_status"] = overall_status
        results["timestamp"] = datetime.now().isoformat()
        
        return results
    
    def display_health_results(self, results: Dict[str, Any]):
        """Display health check results"""
        # Overall status
        overall_status = results.get("overall_status", "UNKNOWN")
        status_color = {
            "HEALTHY": "green",
            "DEGRADED": "yellow",
            "ERROR": "red",
            "UNKNOWN": "blue"
        }.get(overall_status, "blue")
        
        console.print(Panel(
            f"[bold {status_color}]System Status: {overall_status}[/bold {status_color}]\n"
            f"Last Check: {results.get('timestamp', 'Unknown')}",
            title="Noryon AI Trading System Health Check"
        ))
        
        # Component details
        table = Table(title="Component Health Details")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Details", style="blue")
        table.add_column("Issues", style="red")
        
        for component, data in results.items():
            if component in ["overall_status", "timestamp"]:
                continue
                
            status = data.get("status", "UNKNOWN")
            status_emoji = {
                "HEALTHY": "✅",
                "DEGRADED": "⚠️",
                "ERROR": "❌",
                "UNKNOWN": "❓"
            }.get(status, "❓")
            
            details = "\n".join([f"{k}: {v}" for k, v in data.get("details", {}).items()])
            issues = "\n".join(data.get("issues", []))
            
            table.add_row(
                component,
                f"{status_emoji} {status}",
                details or "N/A",
                issues or "None"
            )
        
        console.print(table)
        
        # Recommendations
        if overall_status != "HEALTHY":
            self.display_recommendations(results)
    
    def display_recommendations(self, results: Dict[str, Any]):
        """Display recommendations based on health check results"""
        recommendations = []
        
        for component, data in results.items():
            if component in ["overall_status", "timestamp"]:
                continue
                
            if data.get("status") in ["ERROR", "DEGRADED"]:
                issues = data.get("issues", [])
                
                if "API key" in str(issues):
                    recommendations.append("Update API keys in .env file")
                if "Missing" in str(issues) and "directory" in str(issues):
                    recommendations.append("Run: python setup_complete_system.py")
                if "Docker" in str(issues):
                    recommendations.append("Start Docker services: docker-compose up -d")
                if "Database" in str(issues):
                    recommendations.append("Check PostgreSQL service and connection")
                if "model" in str(issues).lower():
                    recommendations.append("Train AI models: python train_all_models.py")
        
        if recommendations:
            rec_text = "\n".join([f"• {rec}" for rec in set(recommendations)])
            console.print(Panel(
                f"[yellow]Recommended Actions:[/yellow]\n\n{rec_text}",
                title="Recommendations"
            ))
    
    def save_health_report(self, results: Dict[str, Any]):
        """Save health check report to file"""
        report_path = self.project_root / "logs" / f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w') as f:
            json.dump(results, f, indent=2)
        
        console.print(f"[green]Health report saved to: {report_path}[/green]")

def main():
    parser = argparse.ArgumentParser(description="Noryon AI Trading System Health Check")
    parser.add_argument("--full-check", action="store_true",
                       help="Run full health check")
    parser.add_argument("--component", choices=["environment", "database", "redis", "llm", "docker", "resources", "models"],
                       help="Check specific component")
    parser.add_argument("--monitor", action="store_true",
                       help="Continuous monitoring mode")
    parser.add_argument("--interval", type=int, default=60,
                       help="Monitoring interval in seconds")
    parser.add_argument("--save-report", action="store_true",
                       help="Save health report to file")
    
    args = parser.parse_args()
    
    checker = HealthChecker()
    
    if args.component:
        # Check specific component
        check_methods = {
            "environment": checker.check_environment,
            "database": checker.check_database,
            "redis": checker.check_redis,
            "llm": checker.check_llm_providers,
            "docker": checker.check_docker_services,
            "resources": checker.check_system_resources,
            "models": checker.check_models
        }
        
        if args.component in check_methods:
            result = check_methods[args.component]()
            checker.display_health_results({result["component"]: result, "overall_status": result["status"]})
    
    elif args.monitor:
        # Continuous monitoring
        console.print("[green]Starting continuous monitoring... (Press Ctrl+C to stop)[/green]")
        
        try:
            while True:
                console.clear()
                results = checker.run_full_health_check()
                checker.display_health_results(results)
                
                console.print(f"\n[blue]Next check in {args.interval} seconds...[/blue]")
                time.sleep(args.interval)
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Monitoring stopped[/yellow]")
    
    else:
        # Full health check (default)
        results = checker.run_full_health_check()
        checker.display_health_results(results)
        
        if args.save_report:
            checker.save_health_report(results)

if __name__ == "__main__":
    main()

# Added security audit trail
def check_credential_rotation():
    if (datetime.now() - last_rotation).days > 90:
        return SecurityStatus.REQUIRES_ROTATION