#!/usr/bin/env python3
"""
Simple Model Chat - Clean and simple
Just pick a model and chat. No bullshit.
"""

import subprocess
import sys

def get_models():
    """Get available models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]
            models = []
            for line in lines:
                if line.strip():
                    models.append(line.split()[0])
            return models
        return []
    except:
        print("Error: Can't find models. Is Ollama running?")
        return []

def show_models(models):
    """Show models in simple list"""
    print("\nYour Models:")
    for i, model in enumerate(models, 1):
        print(f"{i}. {model}")

def chat(model_name):
    """Chat with model"""
    print(f"\nChatting with {model_name}")
    print("Type 'quit' to exit\n")
    
    while True:
        try:
            question = input("You: ")
            if question.lower() in ['quit', 'q', 'exit']:
                break
            
            print(f"\n{model_name}: ", end="", flush=True)
            
            # Run model
            process = subprocess.run([
                'ollama', 'run', model_name, question
            ], text=True)
            
            print()
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    print("Simple Model Chat")
    print("=================")
    
    models = get_models()
    if not models:
        print("No models found.")
        return
    
    while True:
        show_models(models)
        
        try:
            choice = input(f"\nPick model (1-{len(models)}) or 'q' to quit: ")
            
            if choice.lower() == 'q':
                break
            
            num = int(choice) - 1
            if 0 <= num < len(models):
                chat(models[num])
            else:
                print("Invalid choice")
                
        except ValueError:
            print("Enter a number")
        except KeyboardInterrupt:
            break
    
    print("Goodbye!")

if __name__ == "__main__":
    main()
