#!/usr/bin/env python3
"""
AI Agent Command Center
Quick access to your best working AI agents
"""

import subprocess
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Optional

class AgentCommandCenter:
    """Command center for your working AI agents"""
    
    def __init__(self):
        # Your confirmed working agents
        self.agents = {
            'deepseek_r1': {
                'model': 'unrestricted-deepseek-r1-14b:latest',
                'specialty': 'Deep logical reasoning and step-by-step analysis',
                'use_for': 'Complex analysis, problem solving, detailed reasoning',
                'avg_time': 25.0,
                'quality': 'Excellent'
            },
            'marco_o1': {
                'model': 'unrestricted-marco-o1-7b:latest',
                'specialty': 'Strategic reasoning and planning',
                'use_for': 'Quick insights, strategic decisions, planning',
                'avg_time': 11.0,
                'quality': 'Good'
            },
            'deepseek_finance': {
                'model': 'unrestricted-noryon-deepseek-r1-finance-v2-latest:latest',
                'specialty': 'Deep financial reasoning and risk analysis',
                'use_for': 'Financial analysis, trading decisions, risk assessment',
                'avg_time': 23.0,
                'quality': 'Excellent'
            }
        }
        
        # Additional models to test
        self.potential_agents = {
            'qwen_finance': 'unrestricted-noryon-qwen3-finance-v2-latest:latest',
            'phi4_reasoning': 'unrestricted-phi4-reasoning-14b:latest',
            'cogito_reasoner': 'unrestricted-cogito-14b:latest',
            'exaone_fast': 'unrestricted-exaone-deep-7.8b:latest',
            'granite_structured': 'unrestricted-granite3.1-dense-8b:latest',
            'gemma3_enhanced': 'unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest:latest'
        }
        
        print("🎯 Agent Command Center initialized")
        print(f"   ✅ Working agents: {len(self.agents)}")
        print(f"   🧪 Potential agents: {len(self.potential_agents)}")
    
    def query_agent(self, agent_name: str, question: str, timeout: int = 60) -> Optional[Dict]:
        """Query a specific agent"""
        if agent_name not in self.agents:
            print(f"❌ Unknown agent: {agent_name}")
            print(f"Available agents: {list(self.agents.keys())}")
            return None
        
        agent = self.agents[agent_name]
        model = agent['model']
        
        print(f"🤖 Querying {agent_name} ({agent['specialty']})...")
        
        start_time = time.time()
        try:
            result = subprocess.run([
                'ollama', 'run', model, question
            ], capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                print(f"   ✅ Response received in {response_time:.1f}s ({len(response)} chars)")
                
                return {
                    'agent': agent_name,
                    'model': model,
                    'specialty': agent['specialty'],
                    'response': response,
                    'response_time': response_time,
                    'response_length': len(response),
                    'timestamp': datetime.now(),
                    'success': True
                }
            else:
                print(f"   ❌ Error: {result.stderr}")
                return {
                    'agent': agent_name,
                    'error': result.stderr,
                    'response_time': response_time,
                    'success': False
                }
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ Timeout after {timeout}s")
            return {
                'agent': agent_name,
                'error': f'Timeout after {timeout}s',
                'response_time': timeout,
                'success': False
            }
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {
                'agent': agent_name,
                'error': str(e),
                'response_time': time.time() - start_time,
                'success': False
            }
    
    def multi_agent_analysis(self, question: str, agents: List[str] = None) -> Dict:
        """Get analysis from multiple agents"""
        if agents is None:
            agents = list(self.agents.keys())
        
        print(f"\n🧠 MULTI-AGENT ANALYSIS")
        print(f"Question: {question}")
        print(f"Agents: {agents}")
        print("=" * 70)
        
        responses = {}
        successful_responses = []
        failed_responses = []
        
        start_time = time.time()
        
        for agent_name in agents:
            if agent_name in self.agents:
                response = self.query_agent(agent_name, question)
                responses[agent_name] = response
                
                if response and response['success']:
                    successful_responses.append(response)
                    print(f"\n📝 {agent_name.upper()} RESPONSE:")
                    print(f"   Time: {response['response_time']:.1f}s")
                    print(f"   Length: {response['response_length']} chars")
                    print(f"   Preview: {response['response'][:300]}...")
                    print("-" * 50)
                else:
                    failed_responses.append(agent_name)
                    print(f"\n❌ {agent_name.upper()} FAILED")
        
        total_time = time.time() - start_time
        
        # Create summary
        summary = {
            'question': question,
            'agents_queried': len(agents),
            'successful_responses': len(successful_responses),
            'failed_responses': len(failed_responses),
            'success_rate': len(successful_responses) / len(agents) if agents else 0,
            'total_time': total_time,
            'responses': responses,
            'timestamp': datetime.now()
        }
        
        print(f"\n📊 ANALYSIS SUMMARY:")
        print(f"   Agents queried: {summary['agents_queried']}")
        print(f"   Successful: {summary['successful_responses']}")
        print(f"   Failed: {summary['failed_responses']}")
        print(f"   Success rate: {summary['success_rate']:.1%}")
        print(f"   Total time: {summary['total_time']:.1f}s")
        
        return summary
    
    def test_potential_agent(self, agent_name: str) -> bool:
        """Test a potential agent to see if it works"""
        if agent_name not in self.potential_agents:
            print(f"❌ Unknown potential agent: {agent_name}")
            return False
        
        model = self.potential_agents[agent_name]
        print(f"🧪 Testing potential agent: {agent_name}")
        print(f"   Model: {model}")
        
        start_time = time.time()
        try:
            result = subprocess.run([
                'ollama', 'run', model, 
                "Quick test: What is 2+2? Explain briefly."
            ], capture_output=True, text=True, timeout=30, encoding='utf-8', errors='replace')
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                print(f"   ✅ SUCCESS: {response_time:.1f}s, {len(response)} chars")
                
                # Add to working agents
                self.agents[agent_name] = {
                    'model': model,
                    'specialty': 'To be determined',
                    'use_for': 'General analysis',
                    'avg_time': response_time,
                    'quality': 'Testing'
                }
                
                # Remove from potential
                del self.potential_agents[agent_name]
                
                return True
            else:
                print(f"   ❌ FAILED: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"   ⏰ TIMEOUT: 30s")
            return False
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
            return False
    
    def activate_more_agents(self, max_tests: int = 3) -> int:
        """Test and activate more potential agents"""
        print(f"\n🚀 ACTIVATING MORE AGENTS (testing up to {max_tests})")
        print("=" * 60)
        
        activated = 0
        tested = 0
        
        for agent_name in list(self.potential_agents.keys()):
            if tested >= max_tests:
                break
            
            if self.test_potential_agent(agent_name):
                activated += 1
            
            tested += 1
        
        print(f"\n📊 ACTIVATION RESULTS:")
        print(f"   Tested: {tested}")
        print(f"   Activated: {activated}")
        print(f"   Total working agents: {len(self.agents)}")
        
        return activated
    
    def get_best_agent_for_task(self, task_type: str) -> str:
        """Get the best agent for a specific task"""
        task_preferences = {
            'financial_analysis': ['deepseek_finance', 'deepseek_r1'],
            'risk_assessment': ['deepseek_finance', 'deepseek_r1'],
            'strategic_planning': ['marco_o1', 'deepseek_r1'],
            'quick_insights': ['marco_o1'],
            'deep_reasoning': ['deepseek_r1', 'deepseek_finance'],
            'market_analysis': ['deepseek_finance', 'deepseek_r1'],
            'trading_decisions': ['deepseek_finance', 'marco_o1']
        }
        
        preferred_agents = task_preferences.get(task_type, list(self.agents.keys()))
        
        # Return first available preferred agent
        for agent in preferred_agents:
            if agent in self.agents:
                return agent
        
        # Fallback to any available agent
        return list(self.agents.keys())[0] if self.agents else None
    
    def get_status(self) -> Dict:
        """Get command center status"""
        return {
            'timestamp': datetime.now().isoformat(),
            'working_agents': len(self.agents),
            'potential_agents': len(self.potential_agents),
            'agent_details': self.agents,
            'capabilities': [
                'Deep reasoning',
                'Strategic planning', 
                'Financial analysis',
                'Risk assessment',
                'Market analysis',
                'Trading decisions'
            ]
        }

# Quick usage examples and testing
def main():
    """Test the command center"""
    print("🎯 AI AGENT COMMAND CENTER - TESTING")
    print("=" * 60)
    
    # Initialize command center
    center = AgentCommandCenter()
    
    # Test 1: Single agent query
    print(f"\n🧪 TEST 1: Single Agent Query")
    response = center.query_agent('marco_o1', 'Should I buy Bitcoin at $32000? Quick strategic analysis.')
    
    if response and response['success']:
        print(f"✅ Single agent test successful!")
    
    # Test 2: Try to activate more agents
    print(f"\n🧪 TEST 2: Activate More Agents")
    activated = center.activate_more_agents(max_tests=2)
    
    if activated > 0:
        print(f"✅ Activated {activated} additional agents!")
    
    # Test 3: Multi-agent analysis (if we have multiple agents)
    if len(center.agents) >= 2:
        print(f"\n🧪 TEST 3: Multi-Agent Analysis")
        summary = center.multi_agent_analysis(
            'Analyze the risk of investing $50000 in crypto right now.',
            agents=list(center.agents.keys())[:2]  # Use first 2 agents
        )
        
        if summary['successful_responses'] > 0:
            print(f"✅ Multi-agent analysis successful!")
    
    # Show final status
    status = center.get_status()
    print(f"\n📊 FINAL STATUS:")
    print(f"   Working agents: {status['working_agents']}")
    print(f"   Capabilities: {len(status['capabilities'])}")
    print(f"   Ready for: {', '.join(status['capabilities'])}")
    
    print(f"\n🎉 COMMAND CENTER READY!")
    print(f"   Use: center.query_agent('agent_name', 'your_question')")
    print(f"   Use: center.multi_agent_analysis('your_question')")

if __name__ == "__main__":
    main()
