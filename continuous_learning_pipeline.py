#!/usr/bin/env python3
"""
Noryon Continuous Learning Pipeline
Real-time model adaptation and improvement system

This module provides:
- Real-time market data processing
- Continuous model performance monitoring
- Automated retraining triggers
- Dynamic model selection and weighting
- Performance-based model evolution
- Integration with Phase 2 optimization
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable
import yaml
import threading
from concurrent.futures import ThreadPoolExecutor
import queue
import pickle

# Import Phase 2 components
try:
    from core.ai.phase2_integration_system import Phase2IntegrationSystem
    from core.ai.adaptive_learning_enhancements import AdaptiveLearningOptimizer, PerformanceMetrics
    from core.ai.multi_agent_coordination_enhancements import EnhancedMultiAgentCoordinator
    from ensemble_voting_system import EnsembleVotingSystem
    from train_all_models import TrainingOrchestrator
    PHASE2_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Phase 2 components not available: {e}")
    PHASE2_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Real-time market data structure"""
    timestamp: datetime
    symbol: str
    price: float
    volume: float
    volatility: float
    rsi: float
    macd: float
    bollinger_upper: float
    bollinger_lower: float
    sentiment_score: float
    news_impact: float

@dataclass
class ModelPerformance:
    """Model performance tracking"""
    model_name: str
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    profit_loss: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    avg_trade_duration: float
    last_updated: datetime = field(default_factory=datetime.now)

@dataclass
class LearningTrigger:
    """Conditions that trigger retraining"""
    performance_threshold: float = 0.7
    time_threshold: timedelta = timedelta(hours=24)
    market_regime_change: bool = True
    volatility_spike: float = 2.0
    consecutive_losses: int = 5
    new_data_threshold: int = 1000

class MarketRegimeDetector:
    """Detect changes in market regimes"""
    
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.price_history = deque(maxlen=window_size)
        self.volatility_history = deque(maxlen=window_size)
        self.current_regime = "normal"
        self.regime_history = deque(maxlen=50)
    
    def update(self, market_data: MarketData) -> str:
        """Update regime detection with new market data"""
        self.price_history.append(market_data.price)
        self.volatility_history.append(market_data.volatility)
        
        if len(self.price_history) < self.window_size:
            return self.current_regime
        
        # Calculate regime indicators
        prices = np.array(self.price_history)
        volatilities = np.array(self.volatility_history)
        
        # Trend detection
        trend = np.polyfit(range(len(prices)), prices, 1)[0]
        
        # Volatility regime
        vol_mean = np.mean(volatilities)
        vol_std = np.std(volatilities)
        current_vol = volatilities[-1]
        
        # Determine regime
        if current_vol > vol_mean + 2 * vol_std:
            regime = "high_volatility"
        elif trend > 0.01:
            regime = "bull_market"
        elif trend < -0.01:
            regime = "bear_market"
        elif current_vol < vol_mean - vol_std:
            regime = "low_volatility"
        else:
            regime = "normal"
        
        # Check for regime change
        if regime != self.current_regime:
            logger.info(f"Market regime changed: {self.current_regime} -> {regime}")
            self.regime_history.append({
                "timestamp": market_data.timestamp,
                "old_regime": self.current_regime,
                "new_regime": regime
            })
            self.current_regime = regime
        
        return regime

class PerformanceMonitor:
    """Monitor model performance in real-time"""
    
    def __init__(self):
        self.model_performances = {}
        self.performance_history = defaultdict(lambda: deque(maxlen=1000))
        self.trade_results = defaultdict(list)
        
    def update_performance(self, model_name: str, trade_result: Dict[str, Any]):
        """Update model performance with new trade result"""
        self.trade_results[model_name].append(trade_result)
        
        # Calculate performance metrics
        trades = self.trade_results[model_name][-100:]  # Last 100 trades
        
        if len(trades) < 10:
            return  # Need minimum trades for reliable metrics
        
        profits = [t['profit_loss'] for t in trades]
        predictions = [t['prediction'] for t in trades]
        actuals = [t['actual'] for t in trades]
        
        # Calculate metrics
        accuracy = sum(1 for p, a in zip(predictions, actuals) if p == a) / len(trades)
        total_profit = sum(profits)
        win_rate = sum(1 for p in profits if p > 0) / len(trades)
        
        # Calculate Sharpe ratio (simplified)
        if len(profits) > 1:
            returns = np.array(profits)
            sharpe_ratio = np.mean(returns) / (np.std(returns) + 1e-8)
        else:
            sharpe_ratio = 0.0
        
        # Calculate max drawdown
        cumulative = np.cumsum(profits)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = running_max - cumulative
        max_drawdown = np.max(drawdown) if len(drawdown) > 0 else 0.0
        
        performance = ModelPerformance(
            model_name=model_name,
            accuracy=accuracy,
            precision=accuracy,  # Simplified
            recall=accuracy,     # Simplified
            f1_score=accuracy,   # Simplified
            profit_loss=total_profit,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            avg_trade_duration=np.mean([t.get('duration', 0) for t in trades])
        )
        
        self.model_performances[model_name] = performance
        self.performance_history[model_name].append(performance)
        
        logger.debug(f"Updated performance for {model_name}: accuracy={accuracy:.3f}, profit={total_profit:.2f}")
    
    def get_performance(self, model_name: str) -> Optional[ModelPerformance]:
        """Get current performance for a model"""
        return self.model_performances.get(model_name)
    
    def get_top_performers(self, n: int = 5) -> List[ModelPerformance]:
        """Get top performing models"""
        performances = list(self.model_performances.values())
        return sorted(performances, key=lambda x: x.sharpe_ratio, reverse=True)[:n]

class ContinuousLearningPipeline:
    """Main continuous learning pipeline"""
    
    def __init__(self, config_path: str = "config/continuous_learning_config.yaml"):
        self.config = self._load_config(config_path)
        self.is_running = False
        self.data_queue = queue.Queue(maxsize=10000)
        
        # Initialize components
        self.regime_detector = MarketRegimeDetector()
        self.performance_monitor = PerformanceMonitor()
        self.learning_trigger = LearningTrigger(**self.config.get('triggers', {}))
        
        # Initialize Phase 2 components if available
        if PHASE2_AVAILABLE:
            self.phase2_system = Phase2IntegrationSystem()
            self.adaptive_optimizer = AdaptiveLearningOptimizer()
            self.ensemble_system = EnsembleVotingSystem()
            self.training_orchestrator = TrainingOrchestrator()
        
        # Learning state
        self.last_training_time = datetime.now()
        self.training_in_progress = False
        self.model_versions = defaultdict(int)
        
        logger.info("Continuous Learning Pipeline initialized")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.warning(f"Configuration file not found: {config_path}, using defaults")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'triggers': {
                'performance_threshold': 0.7,
                'time_threshold_hours': 24,
                'consecutive_losses': 5,
                'volatility_spike': 2.0
            },
            'learning': {
                'batch_size': 32,
                'learning_rate': 0.0001,
                'adaptation_rate': 0.1
            },
            'monitoring': {
                'update_interval': 60,
                'performance_window': 100
            }
        }
    
    async def start(self):
        """Start the continuous learning pipeline"""
        if self.is_running:
            logger.warning("Pipeline is already running")
            return
        
        self.is_running = True
        logger.info("Starting Continuous Learning Pipeline")
        
        # Start background tasks
        tasks = [
            asyncio.create_task(self._data_processing_loop()),
            asyncio.create_task(self._performance_monitoring_loop()),
            asyncio.create_task(self._learning_trigger_loop()),
            asyncio.create_task(self._regime_monitoring_loop())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Pipeline error: {e}")
        finally:
            self.is_running = False
    
    async def stop(self):
        """Stop the continuous learning pipeline"""
        logger.info("Stopping Continuous Learning Pipeline")
        self.is_running = False
    
    def add_market_data(self, market_data: MarketData):
        """Add new market data to processing queue"""
        try:
            self.data_queue.put_nowait(market_data)
        except queue.Full:
            logger.warning("Market data queue is full, dropping oldest data")
            try:
                self.data_queue.get_nowait()  # Remove oldest
                self.data_queue.put_nowait(market_data)  # Add new
            except queue.Empty:
                pass
    
    def add_trade_result(self, model_name: str, trade_result: Dict[str, Any]):
        """Add trade result for performance monitoring"""
        self.performance_monitor.update_performance(model_name, trade_result)
    
    async def _data_processing_loop(self):
        """Process incoming market data"""
        logger.info("Starting data processing loop")
        
        while self.is_running:
            try:
                # Process queued data
                processed_count = 0
                while not self.data_queue.empty() and processed_count < 100:
                    try:
                        market_data = self.data_queue.get_nowait()
                        await self._process_market_data(market_data)
                        processed_count += 1
                    except queue.Empty:
                        break
                
                # Sleep before next iteration
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Data processing error: {e}")
                await asyncio.sleep(5)
    
    async def _process_market_data(self, market_data: MarketData):
        """Process individual market data point"""
        # Update regime detection
        current_regime = self.regime_detector.update(market_data)
        
        # Update Phase 2 system if available
        if PHASE2_AVAILABLE:
            try:
                await self.phase2_system.process_market_data({
                    'timestamp': market_data.timestamp.isoformat(),
                    'symbol': market_data.symbol,
                    'price': market_data.price,
                    'volume': market_data.volume,
                    'regime': current_regime
                })
            except Exception as e:
                logger.debug(f"Phase 2 market data processing error: {e}")
    
    async def _performance_monitoring_loop(self):
        """Monitor model performance continuously"""
        logger.info("Starting performance monitoring loop")
        
        while self.is_running:
            try:
                # Get current performances
                top_performers = self.performance_monitor.get_top_performers()
                
                if top_performers:
                    logger.debug(f"Top performer: {top_performers[0].model_name} (Sharpe: {top_performers[0].sharpe_ratio:.3f})")
                    
                    # Update ensemble weights based on performance
                    if PHASE2_AVAILABLE:
                        await self._update_ensemble_weights(top_performers)
                
                # Sleep before next check
                await asyncio.sleep(self.config.get('monitoring', {}).get('update_interval', 60))
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(30)
    
    async def _learning_trigger_loop(self):
        """Check for learning triggers and initiate retraining"""
        logger.info("Starting learning trigger loop")
        
        while self.is_running:
            try:
                if await self._should_trigger_learning():
                    await self._trigger_learning()
                
                # Check every 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"Learning trigger error: {e}")
                await asyncio.sleep(60)
    
    async def _regime_monitoring_loop(self):
        """Monitor market regime changes"""
        logger.info("Starting regime monitoring loop")
        
        while self.is_running:
            try:
                # Check for recent regime changes
                if len(self.regime_detector.regime_history) > 0:
                    recent_change = self.regime_detector.regime_history[-1]
                    time_since_change = datetime.now() - recent_change['timestamp']
                    
                    # If regime changed recently, consider retraining
                    if time_since_change < timedelta(minutes=30):
                        logger.info(f"Recent regime change detected: {recent_change['new_regime']}")
                        if self.learning_trigger.market_regime_change:
                            await self._trigger_learning(reason="market_regime_change")
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                logger.error(f"Regime monitoring error: {e}")
                await asyncio.sleep(300)
    
    async def _should_trigger_learning(self) -> bool:
        """Check if learning should be triggered"""
        # Check time threshold
        time_since_training = datetime.now() - self.last_training_time
        if time_since_training > timedelta(hours=self.learning_trigger.time_threshold.total_seconds() / 3600):
            logger.info("Time threshold reached for retraining")
            return True
        
        # Check performance threshold
        top_performers = self.performance_monitor.get_top_performers(1)
        if top_performers and top_performers[0].accuracy < self.learning_trigger.performance_threshold:
            logger.info(f"Performance threshold breached: {top_performers[0].accuracy:.3f} < {self.learning_trigger.performance_threshold}")
            return True
        
        # Check consecutive losses
        for model_name, performance in self.performance_monitor.model_performances.items():
            recent_trades = self.performance_monitor.trade_results[model_name][-self.learning_trigger.consecutive_losses:]
            if len(recent_trades) >= self.learning_trigger.consecutive_losses:
                if all(trade['profit_loss'] < 0 for trade in recent_trades):
                    logger.info(f"Consecutive losses detected for {model_name}")
                    return True
        
        return False
    
    async def _trigger_learning(self, reason: str = "performance_threshold"):
        """Trigger learning/retraining process"""
        if self.training_in_progress:
            logger.info("Training already in progress, skipping trigger")
            return
        
        logger.info(f"Triggering learning process: {reason}")
        self.training_in_progress = True
        
        try:
            if PHASE2_AVAILABLE:
                # Use Phase 2 adaptive learning
                await self._adaptive_learning_update()
            else:
                # Fallback to basic retraining
                await self._basic_retraining()
            
            self.last_training_time = datetime.now()
            logger.info("Learning process completed successfully")
            
        except Exception as e:
            logger.error(f"Learning process failed: {e}")
        finally:
            self.training_in_progress = False
    
    async def _adaptive_learning_update(self):
        """Perform adaptive learning update using Phase 2 components"""
        logger.info("Starting adaptive learning update")
        
        # Get current performance metrics
        performances = list(self.performance_monitor.model_performances.values())
        
        # Update adaptive learning system
        for performance in performances:
            await self.adaptive_optimizer.update_performance_metrics(
                performance.model_name,
                performance.accuracy
            )
        
        # Trigger system optimization
        await self.phase2_system.optimize_system_performance()
        
        # Update model versions
        for performance in performances:
            self.model_versions[performance.model_name] += 1
    
    async def _basic_retraining(self):
        """Basic retraining fallback"""
        logger.info("Starting basic retraining")
        
        # Simulate retraining process
        await asyncio.sleep(5)  # Simulate training time
        
        # Update model versions
        for model_name in self.performance_monitor.model_performances.keys():
            self.model_versions[model_name] += 1
    
    async def _update_ensemble_weights(self, top_performers: List[ModelPerformance]):
        """Update ensemble weights based on performance"""
        try:
            # Calculate new weights based on Sharpe ratios
            total_sharpe = sum(max(0, p.sharpe_ratio) for p in top_performers)
            
            if total_sharpe > 0:
                new_weights = {}
                for performance in top_performers:
                    weight = max(0, performance.sharpe_ratio) / total_sharpe
                    new_weights[performance.model_name] = weight
                
                # Update ensemble system
                if hasattr(self.ensemble_system, 'update_weights'):
                    await self.ensemble_system.update_weights(new_weights)
                
                logger.debug(f"Updated ensemble weights: {new_weights}")
        
        except Exception as e:
            logger.error(f"Failed to update ensemble weights: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current pipeline status"""
        return {
            'is_running': self.is_running,
            'training_in_progress': self.training_in_progress,
            'last_training_time': self.last_training_time.isoformat(),
            'current_regime': self.regime_detector.current_regime,
            'queue_size': self.data_queue.qsize(),
            'model_performances': {
                name: {
                    'accuracy': perf.accuracy,
                    'sharpe_ratio': perf.sharpe_ratio,
                    'win_rate': perf.win_rate
                }
                for name, perf in self.performance_monitor.model_performances.items()
            },
            'model_versions': dict(self.model_versions)
        }

# Example usage and testing
async def simulate_market_data(pipeline: ContinuousLearningPipeline, duration: int = 300):
    """Simulate market data for testing"""
    logger.info(f"Starting market data simulation for {duration} seconds")
    
    start_time = time.time()
    price = 100.0
    
    while time.time() - start_time < duration:
        # Generate synthetic market data
        price += np.random.normal(0, 0.5)
        
        market_data = MarketData(
            timestamp=datetime.now(),
            symbol="BTC/USD",
            price=price,
            volume=np.random.uniform(1000, 10000),
            volatility=np.random.uniform(0.1, 2.0),
            rsi=np.random.uniform(20, 80),
            macd=np.random.normal(0, 1),
            bollinger_upper=price + 5,
            bollinger_lower=price - 5,
            sentiment_score=np.random.uniform(-1, 1),
            news_impact=np.random.uniform(-0.5, 0.5)
        )
        
        pipeline.add_market_data(market_data)
        
        # Simulate trade results
        if np.random.random() < 0.1:  # 10% chance of trade
            trade_result = {
                'timestamp': datetime.now(),
                'prediction': np.random.choice(['buy', 'sell', 'hold']),
                'actual': np.random.choice(['buy', 'sell', 'hold']),
                'profit_loss': np.random.normal(0, 10),
                'duration': np.random.uniform(60, 3600)
            }
            
            model_name = np.random.choice(['deepseek', 'mistral', 'qwen3', 'phi'])
            pipeline.add_trade_result(model_name, trade_result)
        
        await asyncio.sleep(1)

async def main():
    """Main function for testing"""
    # Create pipeline
    pipeline = ContinuousLearningPipeline()
    
    # Start pipeline
    pipeline_task = asyncio.create_task(pipeline.start())
    
    # Start simulation
    simulation_task = asyncio.create_task(simulate_market_data(pipeline, 60))
    
    try:
        # Run for 1 minute
        await asyncio.wait_for(simulation_task, timeout=70)
        
        # Print status
        status = pipeline.get_status()
        print(json.dumps(status, indent=2, default=str))
        
    finally:
        await pipeline.stop()
        pipeline_task.cancel()

if __name__ == "__main__":
    asyncio.run(main())