#!/usr/bin/env python3
import requests
import json

def test_chat():
    url = "http://localhost:8000/v1/chat/completions"
    
    payload = {
        "model": "qwen3-8b",
        "messages": [
            {"role": "user", "content": "Hello! Can you introduce yourself?"}
        ],
        "max_tokens": 100
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=payload, headers=headers)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.json()}")
        
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"
        
        data = response.json()
        assert 'choices' in data, "Response JSON should contain 'choices' key"
        assert len(data['choices']) > 0, "'choices' list should not be empty"
        
        message_data = data['choices'][0]
        assert 'message' in message_data, "First choice should contain 'message' key"
        assert 'content' in message_data['message'], "Message should contain 'content' key"
        
        message = message_data['message']['content']
        print(f"\nAI Response: {message}")
        assert isinstance(message, str) and len(message) > 0, "AI response message should be a non-empty string"
        
    except requests.exceptions.RequestException as e:
        print(f"Request Error: {e}")
        assert False, f"Request failed: {e}"
    except Exception as e:
        print(f"Error: {e}")
        assert False, f"An unexpected error occurred: {e}"

if __name__ == "__main__":
    print("Testing Mock LLM Server...")
    if test_chat():
        print("\n✅ Mock LLM Server is working! You can now chat with it.")
        print("\nTo chat interactively, you can use:")
        print("- Direct API calls to http://localhost:8000/v1/chat/completions")
        print("- The test script above as a template")
    else:
        print("\n❌ Mock LLM Server test failed.")
