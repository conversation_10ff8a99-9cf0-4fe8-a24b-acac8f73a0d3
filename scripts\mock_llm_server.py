#!/usr/bin/env python3
"""
Mock Local LLM Server for Noryon Trading System Testing

This script creates a simple HTTP server that mimics an LLM API
for testing purposes when actual LLM libraries are not available.
"""

import json
import random
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import argparse

class MockLLMHandler(BaseHTTPRequestHandler):
    """HTTP request handler for mock LLM server"""
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/health':
            self.send_health_response()
        elif parsed_path.path == '/v1/models':
            self.send_models_response()
        elif parsed_path.path == '/docs' or parsed_path.path == '/':
            self.send_docs_response()
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/v1/completions':
            self.handle_completion_request()
        elif parsed_path.path == '/v1/chat/completions':
            self.handle_chat_completion_request()
        else:
            self.send_error(404, "Not Found")
    
    def send_health_response(self):
        """Send health check response"""
        response = {
            "status": "healthy",
            "model": "qwen3-8b-mock",
            "timestamp": datetime.now().isoformat(),
            "uptime": "running"
        }
        self.send_json_response(response)
    
    def send_models_response(self):
        """Send available models response"""
        response = {
            "object": "list",
            "data": [
                {
                    "id": "qwen3-8b-mock",
                    "object": "model",
                    "created": int(time.time()),
                    "owned_by": "noryon-local"
                }
            ]
        }
        self.send_json_response(response)
    
    def send_docs_response(self):
        """Send API documentation"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Mock LLM Server - Noryon Trading System</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
                .method { color: #007acc; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>🤖 Mock LLM Server</h1>
            <p>This is a mock LLM server for testing the Noryon Trading System.</p>
            
            <h2>Available Endpoints:</h2>
            
            <div class="endpoint">
                <span class="method">GET</span> /health - Health check
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /v1/models - List available models
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /v1/completions - Text completion
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /v1/chat/completions - Chat completion
            </div>
            
            <h2>Example Usage:</h2>
            <pre>
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "qwen3-8b-mock",
    "messages": [
      {"role": "user", "content": "Analyze AAPL stock"}
    ],
    "max_tokens": 100
  }'
            </pre>
        </body>
        </html>
        """
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def handle_completion_request(self):
        """Handle text completion request"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))
            
            prompt = request_data.get('prompt', '')
            max_tokens = request_data.get('max_tokens', 100)
            temperature = request_data.get('temperature', 0.1)
            
            # Generate mock response based on prompt content
            mock_response = self.generate_mock_response(prompt, max_tokens)
            
            response = {
                "id": f"cmpl-{random.randint(100000, 999999)}",
                "object": "text_completion",
                "created": int(time.time()),
                "model": "qwen3-8b-mock",
                "choices": [
                    {
                        "text": mock_response,
                        "index": 0,
                        "logprobs": None,
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": len(prompt.split()),
                    "completion_tokens": len(mock_response.split()),
                    "total_tokens": len(prompt.split()) + len(mock_response.split())
                }
            }
            
            # Simulate processing time
            time.sleep(0.5 + random.random())
            
            self.send_json_response(response)
            
        except Exception as e:
            self.send_error(400, f"Bad Request: {str(e)}")
    
    def handle_chat_completion_request(self):
        """Handle chat completion request"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))
            
            messages = request_data.get('messages', [])
            max_tokens = request_data.get('max_tokens', 100)
            temperature = request_data.get('temperature', 0.1)
            
            # Extract the last user message
            user_message = ""
            for msg in reversed(messages):
                if msg.get('role') == 'user':
                    user_message = msg.get('content', '')
                    break
            
            # Generate mock response
            mock_response = self.generate_mock_response(user_message, max_tokens)
            
            response = {
                "id": f"chatcmpl-{random.randint(100000, 999999)}",
                "object": "chat.completion",
                "created": int(time.time()),
                "model": "qwen3-8b-mock",
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": mock_response
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": sum(len(msg.get('content', '').split()) for msg in messages),
                    "completion_tokens": len(mock_response.split()),
                    "total_tokens": sum(len(msg.get('content', '').split()) for msg in messages) + len(mock_response.split())
                }
            }
            
            # Simulate processing time
            time.sleep(0.5 + random.random())
            
            self.send_json_response(response)
            
        except Exception as e:
            self.send_error(400, f"Bad Request: {str(e)}")
    
    def generate_mock_response(self, prompt, max_tokens):
        """Generate a mock response based on the prompt"""
        prompt_lower = prompt.lower()
        
        # Trading-related responses
        if any(word in prompt_lower for word in ['stock', 'trade', 'market', 'price', 'buy', 'sell']):
            responses = [
                "Based on current market analysis, I recommend a cautious approach. Consider diversifying your portfolio and monitoring key technical indicators.",
                "The market shows mixed signals. RSI indicates potential oversold conditions, while moving averages suggest a sideways trend.",
                "Risk management is crucial in current market conditions. Consider setting stop-losses and position sizing appropriately.",
                "Technical analysis suggests support levels around current prices. Monitor volume and momentum indicators for confirmation."
            ]
        
        # Financial analysis responses
        elif any(word in prompt_lower for word in ['analyze', 'analysis', 'financial', 'earnings', 'revenue']):
            responses = [
                "Financial metrics indicate stable fundamentals with room for growth. Key ratios are within acceptable ranges.",
                "Quarterly earnings show positive trends, though market volatility remains a concern for short-term performance.",
                "Balance sheet analysis reveals strong liquidity position and manageable debt levels.",
                "Revenue growth is consistent with industry averages, suggesting sustainable business model."
            ]
        
        # Risk assessment responses
        elif any(word in prompt_lower for word in ['risk', 'volatility', 'hedge', 'protection']):
            responses = [
                "Current risk levels are moderate. Consider implementing hedging strategies to protect against downside moves.",
                "Volatility metrics suggest increased market uncertainty. Adjust position sizes accordingly.",
                "Risk-adjusted returns favor a balanced approach with emphasis on capital preservation.",
                "Correlation analysis indicates potential for portfolio diversification benefits."
            ]
        
        # General responses
        else:
            responses = [
                "I understand your query. Let me provide a comprehensive analysis based on available data.",
                "This is an interesting question that requires careful consideration of multiple factors.",
                "Based on current information, here's my assessment of the situation.",
                "Let me break down this complex topic into manageable components for better understanding."
            ]
        
        # Select a random response and truncate if needed
        response = random.choice(responses)
        words = response.split()
        if len(words) > max_tokens:
            response = ' '.join(words[:max_tokens])
        
        return response
    
    def send_json_response(self, data):
        """Send JSON response"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(json.dumps(data, indent=2).encode())
    
    def log_message(self, format, *args):
        """Custom log message format"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {format % args}")

def run_server(host='localhost', port=8000):
    """Run the mock LLM server"""
    server_address = (host, port)
    httpd = HTTPServer(server_address, MockLLMHandler)
    
    print(f"🤖 Mock LLM Server Starting...")
    print(f"🌐 Host: {host}")
    print(f"🔌 Port: {port}")
    print(f"📖 API Documentation: http://{host}:{port}/")
    print(f"🔄 Health Check: http://{host}:{port}/health")
    print(f"📊 Models: http://{host}:{port}/v1/models")
    print("\n⚡ Server is ready for requests!")
    print("🛑 Press Ctrl+C to stop the server\n")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.shutdown()

def main():
    parser = argparse.ArgumentParser(description="Mock LLM Server for Testing")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    
    args = parser.parse_args()
    
    run_server(args.host, args.port)

if __name__ == "__main__":
    main()