#!/usr/bin/env python3
"""
Comprehensive Model Testing and Validation
Test all successfully trained models with financial scenarios
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class ModelTester:
    """Comprehensive testing for all trained models"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_scenarios = self._create_test_scenarios()
        
    def _create_test_scenarios(self):
        """Create comprehensive financial test scenarios"""
        return [
            {
                "name": "Technical Analysis",
                "query": "Analyze AAPL stock currently at $185 with RSI at 65, MACD bullish crossover, and volume 20% above average. Provide specific entry/exit points and risk management.",
                "expected_keywords": ["entry", "exit", "risk", "stop loss", "target", "rsi", "macd"]
            },
            {
                "name": "Portfolio Risk Assessment",
                "query": "Assess the risk of a portfolio with 60% tech stocks, 30% bonds, and 10% cryptocurrency. Calculate Value at Risk and provide optimization recommendations.",
                "expected_keywords": ["risk", "var", "portfolio", "optimization", "diversification", "volatility"]
            },
            {
                "name": "Market Analysis",
                "query": "Analyze current market conditions with S&P 500 at 4200, VIX at 18, and 10-year yield at 4.2%. What trading strategy would you recommend?",
                "expected_keywords": ["market", "strategy", "s&p", "vix", "yield", "trading", "recommendation"]
            },
            {
                "name": "Options Strategy",
                "query": "I own 1000 shares of SPY at $420, current price $425. Design an options strategy for 1-2% monthly income with risk analysis.",
                "expected_keywords": ["options", "covered call", "income", "premium", "risk", "strategy"]
            },
            {
                "name": "Cryptocurrency Analysis",
                "query": "Analyze Bitcoin at $45,000 and Ethereum at $2,800 with institutional inflows of $500M weekly. Provide trading recommendations.",
                "expected_keywords": ["bitcoin", "ethereum", "crypto", "institutional", "trading", "recommendation"]
            }
        ]
    
    async def test_ollama_model(self, model_name: str) -> dict:
        """Test an Ollama model with financial scenarios"""
        console.print(f"[yellow]🧪 Testing {model_name}...[/yellow]")
        
        results = {
            "model_name": model_name,
            "total_tests": len(self.test_scenarios),
            "passed_tests": 0,
            "failed_tests": 0,
            "response_quality": 0.0,
            "financial_accuracy": 0.0,
            "test_details": []
        }
        
        for scenario in self.test_scenarios:
            console.print(f"  Testing: {scenario['name']}")
            
            try:
                # Run the test
                test_result = subprocess.run([
                    'ollama', 'run', model_name, scenario['query']
                ], capture_output=True, text=True, timeout=120)
                
                if test_result.returncode == 0:
                    response = test_result.stdout.strip()
                    
                    # Analyze response quality
                    quality_score = self._analyze_response_quality(response, scenario)
                    
                    test_detail = {
                        "scenario": scenario['name'],
                        "success": True,
                        "response_length": len(response),
                        "quality_score": quality_score,
                        "keywords_found": sum(1 for kw in scenario['expected_keywords'] if kw.lower() in response.lower()),
                        "response_preview": response[:200] + "..." if len(response) > 200 else response
                    }
                    
                    results["passed_tests"] += 1
                    results["response_quality"] += quality_score
                    
                else:
                    test_detail = {
                        "scenario": scenario['name'],
                        "success": False,
                        "error": test_result.stderr,
                        "quality_score": 0.0
                    }
                    results["failed_tests"] += 1
                
                results["test_details"].append(test_detail)
                
            except subprocess.TimeoutExpired:
                console.print(f"    [red]❌ Timeout[/red]")
                results["failed_tests"] += 1
                results["test_details"].append({
                    "scenario": scenario['name'],
                    "success": False,
                    "error": "Timeout",
                    "quality_score": 0.0
                })
            except Exception as e:
                console.print(f"    [red]❌ Error: {e}[/red]")
                results["failed_tests"] += 1
                results["test_details"].append({
                    "scenario": scenario['name'],
                    "success": False,
                    "error": str(e),
                    "quality_score": 0.0
                })
        
        # Calculate averages
        if results["passed_tests"] > 0:
            results["response_quality"] = results["response_quality"] / results["passed_tests"]
            results["financial_accuracy"] = (results["passed_tests"] / results["total_tests"]) * 100
        
        console.print(f"[green]✅ {model_name} testing complete: {results['passed_tests']}/{results['total_tests']} passed[/green]")
        return results
    
    def _analyze_response_quality(self, response: str, scenario: dict) -> float:
        """Analyze the quality of a model response"""
        score = 0.0
        
        # Length check (good responses should be substantial)
        if len(response) > 100:
            score += 20.0
        if len(response) > 300:
            score += 10.0
        
        # Keyword presence
        keywords_found = sum(1 for kw in scenario['expected_keywords'] if kw.lower() in response.lower())
        keyword_score = (keywords_found / len(scenario['expected_keywords'])) * 40.0
        score += keyword_score
        
        # Financial terminology
        financial_terms = ["analysis", "risk", "return", "portfolio", "market", "trading", "investment", "strategy"]
        financial_score = sum(1 for term in financial_terms if term.lower() in response.lower())
        score += min(financial_score * 3, 20.0)
        
        # Specific recommendations
        if any(word in response.lower() for word in ["recommend", "suggest", "advise", "should", "target"]):
            score += 10.0
        
        return min(score, 100.0)
    
    async def test_all_models(self) -> dict:
        """Test all available trained models"""
        console.print(Panel(
            "[bold blue]🧪 Comprehensive Model Testing[/bold blue]\n\n"
            "Testing all successfully trained models with financial scenarios:\n"
            "• Technical Analysis\n"
            "• Portfolio Risk Assessment\n"
            "• Market Analysis\n"
            "• Options Strategy\n"
            "• Cryptocurrency Analysis",
            title="Model Testing"
        ))
        
        # Get list of available Ollama models
        try:
            ollama_result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            available_models = []
            
            if ollama_result.returncode == 0:
                lines = ollama_result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip() and 'noryon' in line:
                        model_name = line.split()[0]
                        available_models.append(model_name)
            
            console.print(f"[green]Found {len(available_models)} Noryon models to test[/green]")
            
        except Exception as e:
            console.print(f"[red]❌ Error getting Ollama models: {e}[/red]")
            return {}
        
        # Test each model
        all_results = {}
        
        for model_name in available_models:
            results = await self.test_ollama_model(model_name)
            all_results[model_name] = results
        
        return all_results
    
    def generate_test_report(self, all_results: dict):
        """Generate comprehensive test report"""
        console.print("\n" + "="*80)
        console.print("[bold green]📊 COMPREHENSIVE TEST RESULTS[/bold green]")
        
        # Summary table
        summary_table = Table(title="Model Performance Summary")
        summary_table.add_column("Model", style="cyan")
        summary_table.add_column("Tests Passed", style="green")
        summary_table.add_column("Success Rate", style="yellow")
        summary_table.add_column("Avg Quality", style="blue")
        summary_table.add_column("Status", style="magenta")
        
        for model_name, results in all_results.items():
            success_rate = f"{results['financial_accuracy']:.1f}%"
            avg_quality = f"{results['response_quality']:.1f}/100"
            status = "🎯 EXCELLENT" if results['financial_accuracy'] > 80 else "✅ GOOD" if results['financial_accuracy'] > 60 else "⚠️ NEEDS WORK"
            
            summary_table.add_row(
                model_name,
                f"{results['passed_tests']}/{results['total_tests']}",
                success_rate,
                avg_quality,
                status
            )
        
        console.print(summary_table)
        
        # Detailed results for each model
        for model_name, results in all_results.items():
            console.print(f"\n[bold cyan]📋 Detailed Results: {model_name}[/bold cyan]")
            
            detail_table = Table()
            detail_table.add_column("Test Scenario", style="cyan")
            detail_table.add_column("Result", style="green")
            detail_table.add_column("Quality Score", style="yellow")
            detail_table.add_column("Response Preview", style="blue")
            
            for test_detail in results['test_details']:
                result_icon = "✅ Pass" if test_detail['success'] else "❌ Fail"
                quality = f"{test_detail.get('quality_score', 0):.1f}/100" if test_detail['success'] else "N/A"
                preview = test_detail.get('response_preview', test_detail.get('error', 'No response'))[:100]
                
                detail_table.add_row(
                    test_detail['scenario'],
                    result_icon,
                    quality,
                    preview
                )
            
            console.print(detail_table)
        
        # Overall system assessment
        total_tests = sum(r['total_tests'] for r in all_results.values())
        total_passed = sum(r['passed_tests'] for r in all_results.values())
        overall_success = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        console.print(Panel(
            f"[bold green]🎉 OVERALL SYSTEM ASSESSMENT[/bold green]\n\n"
            f"Total Models Tested: {len(all_results)}\n"
            f"Total Test Scenarios: {total_tests}\n"
            f"Total Tests Passed: {total_passed}\n"
            f"Overall Success Rate: {overall_success:.1f}%\n\n"
            f"System Status: {'🎯 PRODUCTION READY' if overall_success > 75 else '✅ GOOD FOR TESTING' if overall_success > 50 else '⚠️ NEEDS IMPROVEMENT'}\n\n"
            f"🚀 Your AI trading models are functional and ready for integration!",
            title="System Assessment"
        ))
        
        return {
            "total_models": len(all_results),
            "total_tests": total_tests,
            "total_passed": total_passed,
            "overall_success_rate": overall_success,
            "detailed_results": all_results
        }

async def main():
    """Main testing function"""
    console.print("[bold blue]🧪 Starting Comprehensive Model Testing...[/bold blue]\n")
    
    tester = ModelTester()
    
    # Test all models
    results = await tester.test_all_models()
    
    # Generate report
    report = tester.generate_test_report(results)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    console.print("1. Integrate tested models into live trading system")
    console.print("2. Set up ensemble voting for better predictions")
    console.print("3. Monitor model performance in production")
    console.print("4. Continue training additional models")
    
    return report

if __name__ == "__main__":
    results = asyncio.run(main())
