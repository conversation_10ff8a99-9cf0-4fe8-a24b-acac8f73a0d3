#!/usr/bin/env python3
"""
Memory Management System
"""

import gc
import psutil
import time
from typing import Dict, Any

class MemoryManager:
    def __init__(self, max_memory_percent: float = 80.0):
        self.max_memory_percent = max_memory_percent
        self.memory_warnings = []
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """Check current memory usage"""
        memory = psutil.virtual_memory()
        
        return {
            'total_gb': memory.total / (1024**3),
            'available_gb': memory.available / (1024**3),
            'used_percent': memory.percent,
            'warning': memory.percent > self.max_memory_percent
        }
    
    def cleanup_memory(self):
        """Force garbage collection and memory cleanup"""
        gc.collect()
        
        # Additional cleanup for large objects
        import sys
        if hasattr(sys, '_clear_type_cache'):
            sys._clear_type_cache()
    
    def monitor_memory_during_operation(self, operation_name: str):
        """Monitor memory during an operation"""
        before = self.check_memory_usage()
        
        if before['warning']:
            print(f"⚠️  High memory usage before {operation_name}: {before['used_percent']:.1f}%")
            self.cleanup_memory()
        
        return before
    
    def memory_safe_operation(self, func, *args, **kwargs):
        """Execute operation with memory monitoring"""
        self.monitor_memory_during_operation(func.__name__)
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            self.cleanup_memory()

# Global memory manager
memory_manager = MemoryManager()
