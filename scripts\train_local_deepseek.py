#!/usr/bin/env python3
"""
Train Local DeepSeek R1 for Noryon AI
Use the locally installed DeepSeek R1 model for focused financial training
"""

import os
import sys
import torch
import logging
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

console = Console()

class LocalDeepSeekTrainer:
    """Focused trainer for local DeepSeek R1 model"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.local_model_path = self.project_root / "deepseek r1"
        self.output_dir = self.project_root / "models" / "deepseek-local-finance-v1"
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger("LocalDeepSeekTrainer")
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        console.print(Panel(
            f"[bold blue]Local DeepSeek R1 Finance Trainer[/bold blue]\n\n"
            f"Local Model: {self.local_model_path}\n"
            f"Device: {self.device}\n"
            f"Output: {self.output_dir}",
            title="Training Configuration"
        ))
    
    def verify_local_model(self):
        """Verify the local DeepSeek R1 model exists and is valid"""
        console.print("[yellow]Verifying local DeepSeek R1 model...[/yellow]")
        
        required_files = [
            "config (1).json",
            "model.safetensors",
            "tokenizer_config (1).json"
        ]
        
        missing_files = []
        for file in required_files:
            if not (self.local_model_path / file).exists():
                missing_files.append(file)
        
        if missing_files:
            console.print(f"[red]❌ Missing files: {missing_files}[/red]")
            return False
        
        console.print("[green]✅ Local DeepSeek R1 model verified[/green]")
        return True
    
    def create_financial_dataset(self):
        """Create comprehensive financial training dataset"""
        console.print("[yellow]Creating financial training dataset...[/yellow]")
        
        # Enhanced financial training examples
        financial_examples = [
            {
                "instruction": "Analyze market trends and provide investment insights",
                "input": "The S&P 500 has gained 15% this quarter while tech stocks show volatility. Interest rates remain at 5.25%.",
                "output": "The S&P 500's 15% quarterly gain indicates strong market momentum, though tech volatility suggests sector rotation. With rates at 5.25%, consider value stocks and dividend-paying equities. Monitor Fed policy signals for rate changes that could impact growth stocks."
            },
            {
                "instruction": "Evaluate cryptocurrency investment strategy",
                "input": "Bitcoin at $45,000, down 20% from recent highs. Ethereum showing DeFi adoption growth. Regulatory uncertainty persists.",
                "output": "Bitcoin's 20% correction presents potential entry opportunity for long-term holders. Ethereum's DeFi growth supports fundamental value. Limit crypto to 5-10% of portfolio due to regulatory risks. Consider dollar-cost averaging for volatility management."
            },
            {
                "instruction": "Assess earnings report impact on stock valuation",
                "input": "Company XYZ reported Q3 EPS of $2.50 vs $2.30 expected. Revenue up 12% YoY. Guidance raised for Q4.",
                "output": "Strong earnings beat (+8.7%) and revenue growth (+12% YoY) with raised guidance signals robust fundamentals. Expect positive price reaction. Current P/E multiple likely justified. Monitor for sustained growth trajectory and margin expansion in upcoming quarters."
            },
            {
                "instruction": "Provide sector rotation strategy recommendations",
                "input": "Economic indicators suggest late-cycle expansion. Inflation moderating, employment strong, consumer spending stable.",
                "output": "Late-cycle positioning favors defensive sectors: utilities, consumer staples, healthcare. Reduce cyclical exposure (industrials, materials). Consider REITs for inflation hedge. Maintain quality bias with strong balance sheets as cycle matures."
            },
            {
                "instruction": "Analyze options strategy for income generation",
                "input": "Own 1000 shares of AAPL at $180. Stock trading at $185. Seeking monthly income with limited upside sacrifice.",
                "output": "Consider covered call strategy: sell monthly calls at $190 strike for ~$2-3 premium. Generates 1.1-1.6% monthly income. Caps upside at $190 (+2.7%). If called away, profit = $10 capital gain + premium. Roll calls higher if stock appreciates significantly."
            }
        ]
        
        # Create training dataset
        training_data = []
        for example in financial_examples:
            # Create multiple variations
            for i in range(100):  # 100 variations per example = 500 total
                formatted_text = f"""### Instruction:
{example['instruction']}

### Input:
{example['input']}

### Response:
{example['output']}"""
                
                training_data.append({"text": formatted_text})
        
        console.print(f"[green]✅ Created dataset with {len(training_data)} examples[/green]")
        return training_data
    
    def train_with_local_model(self):
        """Train using the local DeepSeek R1 model"""
        console.print(Panel(
            "[bold yellow]Starting Local DeepSeek R1 Training[/bold yellow]",
            title="Training Phase"
        ))
        
        try:
            # Import required libraries
            from transformers import (
                AutoTokenizer, AutoModelForCausalLM,
                TrainingArguments, Trainer,
                DataCollatorForLanguageModeling
            )
            from datasets import Dataset
            from peft import LoraConfig, get_peft_model, TaskType
            
            console.print("[yellow]Loading local DeepSeek R1 model...[/yellow]")
            
            # Load tokenizer from local path
            tokenizer = AutoTokenizer.from_pretrained(
                str(self.local_model_path),
                trust_remote_code=True,
                local_files_only=True
            )
            
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
                tokenizer.pad_token_id = tokenizer.eos_token_id
            
            # Load model from local path with quantization support
            from transformers import BitsAndBytesConfig

            # Configure quantization for training
            bnb_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4",
                bnb_4bit_compute_dtype=torch.bfloat16 if torch.cuda.is_available() else torch.float32
            )

            model = AutoModelForCausalLM.from_pretrained(
                str(self.local_model_path),
                quantization_config=bnb_config,
                device_map="auto" if torch.cuda.is_available() else None,
                trust_remote_code=True,
                local_files_only=True
            )
            
            console.print("[green]✅ Local model loaded successfully[/green]")
            
            # Apply LoRA for efficient training
            console.print("[yellow]Applying LoRA configuration...[/yellow]")
            
            lora_config = LoraConfig(
                task_type=TaskType.CAUSAL_LM,
                inference_mode=False,
                r=16,
                lora_alpha=32,
                lora_dropout=0.1,
                target_modules=["q_proj", "k_proj", "v_proj", "o_proj"],
                bias="none"
            )
            
            model = get_peft_model(model, lora_config)
            model.print_trainable_parameters()
            
            # Create dataset
            training_data = self.create_financial_dataset()
            dataset = Dataset.from_list(training_data)
            
            # Tokenize dataset
            def tokenize_function(examples):
                return tokenizer(
                    examples["text"],
                    truncation=True,
                    padding=False,
                    max_length=1024,
                    return_tensors=None
                )
            
            tokenized_dataset = dataset.map(
                tokenize_function,
                batched=True,
                remove_columns=dataset.column_names
            )
            
            # Training arguments optimized for local training
            training_args = TrainingArguments(
                output_dir=str(self.output_dir),
                num_train_epochs=2,
                per_device_train_batch_size=1,
                gradient_accumulation_steps=8,
                learning_rate=2e-4,
                weight_decay=0.01,
                warmup_steps=50,
                logging_steps=10,
                save_steps=100,
                save_strategy="steps",
                evaluation_strategy="no",
                fp16=torch.cuda.is_available(),
                dataloader_pin_memory=False,
                remove_unused_columns=False,
                report_to="none",
                dataloader_num_workers=0,
                max_steps=200,  # Quick training
                lr_scheduler_type="cosine"
            )
            
            # Data collator
            data_collator = DataCollatorForLanguageModeling(
                tokenizer=tokenizer,
                mlm=False
            )
            
            # Create trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=tokenized_dataset,
                data_collator=data_collator,
                tokenizer=tokenizer
            )
            
            # Start training
            console.print("[bold green]🚀 Training started![/bold green]")
            trainer.train()
            
            # Save the trained model
            console.print("[yellow]Saving trained model...[/yellow]")
            trainer.save_model()
            tokenizer.save_pretrained(self.output_dir)
            
            # Save training metadata
            training_info = {
                "model_name": "DeepSeek-R1-Local",
                "training_date": datetime.now().isoformat(),
                "local_model_path": str(self.local_model_path),
                "output_dir": str(self.output_dir),
                "dataset_size": len(training_data),
                "device": str(self.device),
                "status": "completed"
            }
            
            import yaml
            with open(self.output_dir / "training_info.yaml", 'w') as f:
                yaml.dump(training_info, f, default_flow_style=False)
            
            console.print(Panel(
                "[bold green]✅ Training completed successfully![/bold green]\n\n"
                f"Model saved to: {self.output_dir}\n"
                f"Training examples: {len(training_data)}\n"
                f"Device used: {self.device}",
                title="Training Complete"
            ))
            
            return True
            
        except Exception as e:
            console.print(f"[bold red]❌ Training failed: {e}[/bold red]")
            self.logger.error(f"Training failed: {e}")
            return False
    
    def run_training(self):
        """Run the complete training process"""
        console.print(Panel(
            "[bold blue]Noryon AI - Local DeepSeek R1 Training[/bold blue]\n\n"
            "Training your locally installed DeepSeek R1 model\n"
            "for financial analysis and trading insights.",
            title="Local Model Training"
        ))
        
        # Verify local model
        if not self.verify_local_model():
            console.print("[red]❌ Local model verification failed[/red]")
            return False
        
        # Start training
        success = self.train_with_local_model()
        
        if success:
            console.print("\n[bold green]🎉 Local DeepSeek R1 training completed![/bold green]")
            console.print("\n[yellow]Next steps:[/yellow]")
            console.print("1. Test the trained model")
            console.print("2. Integrate with trading system")
            console.print("3. Run performance evaluation")
            console.print(f"4. Model available at: {self.output_dir}")
        else:
            console.print("\n[bold red]❌ Training failed. Check logs for details.[/bold red]")
        
        return success

def main():
    """Main training function"""
    trainer = LocalDeepSeekTrainer()
    return trainer.run_training()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
