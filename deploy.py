#!/usr/bin/env python3
"""
Noryon AI Trading System - Deployment Script

This script handles deployment, management, and monitoring of the Noryon AI Trading System
across different environments (development, staging, production).

Usage:
    python deploy.py deploy --env production
    python deploy.py status --env production
    python deploy.py rollback --env production
    python deploy.py scale --env production --replicas 3
    python deploy.py logs --env production --component api
"""

import asyncio
import argparse
import logging
import subprocess
import sys
import time
import yaml
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DeploymentManager:
    """
    Manages deployment operations for the Noryon AI Trading System.
    """
    
    def __init__(self, environment: str = "development"):
        """
        Initialize deployment manager.
        
        Args:
            environment: Target environment (development, staging, production)
        """
        self.environment = environment
        self.config = self._load_deployment_config()
        self.docker_compose_file = self._get_compose_file()
        
    def _load_deployment_config(self) -> Dict[str, Any]:
        """
        Load deployment configuration.
        
        Returns:
            Deployment configuration dictionary
        """
        config_file = Path(f"deployment/{self.environment}.yaml")
        
        if not config_file.exists():
            # Create default config if it doesn't exist
            self._create_default_config(config_file)
        
        with open(config_file, 'r') as f:
            return yaml.safe_load(f)
    
    def _create_default_config(self, config_file: Path):
        """
        Create default deployment configuration.
        
        Args:
            config_file: Path to configuration file
        """
        config_file.parent.mkdir(exist_ok=True)
        
        default_config = {
            "environment": self.environment,
            "docker": {
                "registry": "noryon-registry",
                "image_tag": "latest",
                "build_args": {},
                "networks": ["noryon-network"]
            },
            "services": {
                "noryon-app": {
                    "replicas": 1 if self.environment == "development" else 2,
                    "resources": {
                        "cpu": "1.0",
                        "memory": "2Gi"
                    },
                    "health_check": {
                        "enabled": True,
                        "path": "/health",
                        "interval": 30,
                        "timeout": 10,
                        "retries": 3
                    }
                },
                "postgres": {
                    "enabled": True,
                    "version": "15-alpine",
                    "storage": "10Gi" if self.environment == "production" else "5Gi"
                },
                "redis": {
                    "enabled": True,
                    "version": "7-alpine",
                    "storage": "1Gi"
                },
                "mlflow": {
                    "enabled": True,
                    "storage": "5Gi"
                }
            },
            "monitoring": {
                "prometheus": {
                    "enabled": self.environment == "production",
                    "retention": "30d" if self.environment == "production" else "7d"
                },
                "grafana": {
                    "enabled": self.environment == "production"
                }
            },
            "security": {
                "ssl_enabled": self.environment == "production",
                "api_rate_limit": 1000 if self.environment == "production" else 100
            },
            "backup": {
                "enabled": self.environment == "production",
                "schedule": "0 2 * * *",  # Daily at 2 AM
                "retention_days": 30
            }
        }
        
        with open(config_file, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)
        
        logger.info(f"Created default deployment config: {config_file}")
    
    def _get_compose_file(self) -> str:
        """
        Get appropriate docker-compose file for environment.
        
        Returns:
            Path to docker-compose file
        """
        if self.environment == "development":
            return "docker-compose.yml"
        elif self.environment == "staging":
            return "docker-compose.staging.yml"
        elif self.environment == "production":
            return "docker-compose.prod.yml"
        else:
            return "docker-compose.yml"
    
    async def deploy(self, force: bool = False) -> bool:
        """
        Deploy the system to the target environment.
        
        Args:
            force: Force deployment even if health checks fail
            
        Returns:
            True if deployment successful, False otherwise
        """
        logger.info(f"Starting deployment to {self.environment} environment")
        
        try:
            # Pre-deployment checks
            if not await self._pre_deployment_checks():
                logger.error("Pre-deployment checks failed")
                return False
            
            # Build images
            if not await self._build_images():
                logger.error("Image build failed")
                return False
            
            # Run tests
            if not force and not await self._run_tests():
                logger.error("Tests failed")
                return False
            
            # Deploy services
            if not await self._deploy_services():
                logger.error("Service deployment failed")
                return False
            
            # Post-deployment verification
            if not await self._post_deployment_checks():
                logger.error("Post-deployment checks failed")
                if not force:
                    await self._rollback()
                    return False
            
            # Update deployment record
            await self._record_deployment()
            
            logger.info(f"Deployment to {self.environment} completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            if not force:
                await self._rollback()
            return False
    
    async def _pre_deployment_checks(self) -> bool:
        """
        Run pre-deployment checks.
        
        Returns:
            True if all checks pass, False otherwise
        """
        logger.info("Running pre-deployment checks")
        
        checks = [
            self._check_docker_available,
            self._check_config_valid,
            self._check_resources_available,
            self._check_dependencies
        ]
        
        for check in checks:
            if not await check():
                return False
        
        return True
    
    async def _check_docker_available(self) -> bool:
        """
        Check if Docker is available and running.
        
        Returns:
            True if Docker is available, False otherwise
        """
        try:
            result = subprocess.run(
                ["docker", "version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"Docker check failed: {e}")
            return False
    
    async def _check_config_valid(self) -> bool:
        """
        Validate configuration files.
        
        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Check main config
            with open("config.yaml", 'r') as f:
                yaml.safe_load(f)
            
            # Check docker-compose file
            with open(self.docker_compose_file, 'r') as f:
                yaml.safe_load(f)
            
            return True
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    async def _check_resources_available(self) -> bool:
        """
        Check if required resources are available.
        
        Returns:
            True if resources are available, False otherwise
        """
        # This is a simplified check - in production, you'd check actual resource availability
        return True
    
    async def _check_dependencies(self) -> bool:
        """
        Check if all dependencies are available.
        
        Returns:
            True if dependencies are available, False otherwise
        """
        # Check if required files exist
        required_files = [
            "requirements.txt",
            "main.py",
            "system_integration.py",
            "api_server.py"
        ]
        
        for file_path in required_files:
            if not Path(file_path).exists():
                logger.error(f"Required file missing: {file_path}")
                return False
        
        return True
    
    async def _build_images(self) -> bool:
        """
        Build Docker images.
        
        Returns:
            True if build successful, False otherwise
        """
        logger.info("Building Docker images")
        
        try:
            # Build main application image
            cmd = [
                "docker", "build",
                "-t", f"noryon-ai:{self.environment}",
                "-f", "Dockerfile",
                "."
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=600  # 10 minutes
            )
            
            if result.returncode != 0:
                logger.error(f"Docker build failed: {result.stderr}")
                return False
            
            logger.info("Docker images built successfully")
            return True
            
        except Exception as e:
            logger.error(f"Image build failed: {e}")
            return False
    
    async def _run_tests(self) -> bool:
        """
        Run test suite before deployment.
        
        Returns:
            True if tests pass, False otherwise
        """
        logger.info("Running test suite")
        
        try:
            result = subprocess.run(
                [sys.executable, "test_system.py"],
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes
            )
            
            if result.returncode != 0:
                logger.error(f"Tests failed: {result.stderr}")
                return False
            
            logger.info("All tests passed")
            return True
            
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            return False
    
    async def _deploy_services(self) -> bool:
        """
        Deploy services using Docker Compose.
        
        Returns:
            True if deployment successful, False otherwise
        """
        logger.info("Deploying services")
        
        try:
            # Stop existing services
            subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "down"],
                capture_output=True
            )
            
            # Start services
            cmd = ["docker-compose", "-f", self.docker_compose_file, "up", "-d"]
            
            if self.environment == "production":
                cmd.extend(["--scale", f"noryon-app={self.config['services']['noryon-app']['replicas']}"])
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minutes
            )
            
            if result.returncode != 0:
                logger.error(f"Service deployment failed: {result.stderr}")
                return False
            
            logger.info("Services deployed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Service deployment failed: {e}")
            return False
    
    async def _post_deployment_checks(self) -> bool:
        """
        Run post-deployment health checks.
        
        Returns:
            True if all checks pass, False otherwise
        """
        logger.info("Running post-deployment checks")
        
        # Wait for services to start
        await asyncio.sleep(30)
        
        checks = [
            self._check_service_health,
            self._check_api_endpoints,
            self._check_database_connection
        ]
        
        for check in checks:
            if not await check():
                return False
        
        return True
    
    async def _check_service_health(self) -> bool:
        """
        Check if all services are healthy.
        
        Returns:
            True if all services are healthy, False otherwise
        """
        try:
            result = subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "ps"],
                capture_output=True,
                text=True
            )
            
            # Check if any service is in unhealthy state
            if "unhealthy" in result.stdout.lower():
                logger.error("Some services are unhealthy")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Service health check failed: {e}")
            return False
    
    async def _check_api_endpoints(self) -> bool:
        """
        Check if API endpoints are responding.
        
        Returns:
            True if API is responding, False otherwise
        """
        import httpx
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "http://localhost:8000/health",
                    timeout=10
                )
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"API health check failed: {e}")
            return False
    
    async def _check_database_connection(self) -> bool:
        """
        Check database connectivity.
        
        Returns:
            True if database is accessible, False otherwise
        """
        # This is a simplified check - in production, you'd test actual DB connection
        try:
            result = subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "exec", "-T", "postgres", 
                 "pg_isready", "-U", "noryon"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
            
        except Exception as e:
            logger.error(f"Database check failed: {e}")
            return False
    
    async def _rollback(self) -> bool:
        """
        Rollback to previous deployment.
        
        Returns:
            True if rollback successful, False otherwise
        """
        logger.info("Initiating rollback")
        
        try:
            # Stop current services
            subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "down"],
                capture_output=True
            )
            
            # Restore previous version (simplified - in production, you'd have versioned deployments)
            subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "up", "-d"],
                capture_output=True
            )
            
            logger.info("Rollback completed")
            return True
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return False
    
    async def _record_deployment(self):
        """
        Record deployment information.
        """
        deployment_record = {
            "timestamp": datetime.now().isoformat(),
            "environment": self.environment,
            "version": "1.0.0",  # In production, this would be dynamic
            "status": "success"
        }
        
        # Save deployment record
        deployments_file = Path("deployments.json")
        
        if deployments_file.exists():
            with open(deployments_file, 'r') as f:
                deployments = json.load(f)
        else:
            deployments = []
        
        deployments.append(deployment_record)
        
        with open(deployments_file, 'w') as f:
            json.dump(deployments, f, indent=2)
    
    async def get_status(self) -> Dict[str, Any]:
        """
        Get deployment status.
        
        Returns:
            Status information
        """
        try:
            # Get service status
            result = subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, "ps", "--format", "json"],
                capture_output=True,
                text=True
            )
            
            services = []
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        services.append(json.loads(line))
            
            return {
                "environment": self.environment,
                "services": services,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get status: {e}")
            return {"error": str(e)}
    
    async def scale(self, service: str, replicas: int) -> bool:
        """
        Scale a service to specified number of replicas.
        
        Args:
            service: Service name to scale
            replicas: Number of replicas
            
        Returns:
            True if scaling successful, False otherwise
        """
        logger.info(f"Scaling {service} to {replicas} replicas")
        
        try:
            result = subprocess.run(
                ["docker-compose", "-f", self.docker_compose_file, 
                 "up", "-d", "--scale", f"{service}={replicas}"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                logger.error(f"Scaling failed: {result.stderr}")
                return False
            
            logger.info(f"Successfully scaled {service} to {replicas} replicas")
            return True
            
        except Exception as e:
            logger.error(f"Scaling failed: {e}")
            return False
    
    async def get_logs(self, service: Optional[str] = None, lines: int = 100) -> str:
        """
        Get service logs.
        
        Args:
            service: Service name (if None, get all logs)
            lines: Number of lines to retrieve
            
        Returns:
            Log output
        """
        try:
            cmd = ["docker-compose", "-f", self.docker_compose_file, "logs", "--tail", str(lines)]
            
            if service:
                cmd.append(service)
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )
            
            return result.stdout
            
        except Exception as e:
            logger.error(f"Failed to get logs: {e}")
            return f"Error getting logs: {e}"


async def main():
    """
    Main deployment script entry point.
    """
    parser = argparse.ArgumentParser(description="Noryon AI Trading System Deployment")
    parser.add_argument("command", choices=["deploy", "status", "rollback", "scale", "logs"])
    parser.add_argument("--env", default="development", choices=["development", "staging", "production"])
    parser.add_argument("--force", action="store_true", help="Force deployment")
    parser.add_argument("--service", help="Service name for scaling/logs")
    parser.add_argument("--replicas", type=int, help="Number of replicas for scaling")
    parser.add_argument("--lines", type=int, default=100, help="Number of log lines")
    
    args = parser.parse_args()
    
    # Initialize deployment manager
    manager = DeploymentManager(args.env)
    
    try:
        if args.command == "deploy":
            success = await manager.deploy(force=args.force)
            if not success:
                sys.exit(1)
                
        elif args.command == "status":
            status = await manager.get_status()
            print(json.dumps(status, indent=2))
            
        elif args.command == "rollback":
            success = await manager._rollback()
            if not success:
                sys.exit(1)
                
        elif args.command == "scale":
            if not args.service or args.replicas is None:
                print("Error: --service and --replicas required for scaling")
                sys.exit(1)
            
            success = await manager.scale(args.service, args.replicas)
            if not success:
                sys.exit(1)
                
        elif args.command == "logs":
            logs = await manager.get_logs(args.service, args.lines)
            print(logs)
            
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Deployment failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())