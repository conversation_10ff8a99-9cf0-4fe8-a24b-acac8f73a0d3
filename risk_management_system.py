#!/usr/bin/env python3
"""
Noryon Advanced Risk Management System
Comprehensive risk management and control system

This module provides:
- Real-time position and portfolio risk monitoring
- Dynamic risk limits and controls
- Value at Risk (VaR) and Expected Shortfall calculations
- Stress testing and scenario analysis
- Risk-adjusted position sizing
- Automated risk alerts and circuit breakers
- Integration with trading and backtesting systems
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
import yaml
import warnings
from enum import Enum
from scipy import stats
import threading
import time
warnings.filterwarnings('ignore')

# Import system components
try:
    from continuous_learning_pipeline import MarketData, ContinuousLearningPipeline
    from backtesting_framework import Trade, PerformanceMetrics
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"System components not available: {e}")
    COMPONENTS_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """Risk level enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    """Risk alert types"""
    POSITION_LIMIT = "position_limit"
    DRAWDOWN_LIMIT = "drawdown_limit"
    VAR_BREACH = "var_breach"
    CONCENTRATION_RISK = "concentration_risk"
    VOLATILITY_SPIKE = "volatility_spike"
    CORRELATION_BREAKDOWN = "correlation_breakdown"
    LIQUIDITY_RISK = "liquidity_risk"
    MODEL_DEGRADATION = "model_degradation"

@dataclass
class Position:
    """Trading position information"""
    symbol: str
    quantity: float
    entry_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime
    model_name: str
    confidence: float
    duration: timedelta = field(default_factory=lambda: timedelta(0))

@dataclass
class RiskLimits:
    """Risk limits configuration"""
    max_position_size: float = 0.1  # 10% of portfolio
    max_sector_exposure: float = 0.3  # 30% per sector
    max_daily_loss: float = 0.02  # 2% daily loss limit
    max_drawdown: float = 0.1  # 10% maximum drawdown
    var_limit_95: float = 0.05  # 5% VaR limit
    var_limit_99: float = 0.08  # 8% VaR limit
    max_leverage: float = 2.0  # 2x maximum leverage
    min_liquidity_ratio: float = 0.1  # 10% minimum cash
    max_correlation: float = 0.8  # Maximum correlation between positions
    stress_test_threshold: float = 0.15  # 15% stress test loss limit

@dataclass
class RiskMetrics:
    """Portfolio risk metrics"""
    portfolio_value: float
    total_exposure: float
    net_exposure: float
    gross_exposure: float
    leverage: float
    var_95: float
    var_99: float
    expected_shortfall_95: float
    expected_shortfall_99: float
    max_drawdown: float
    current_drawdown: float
    volatility: float
    sharpe_ratio: float
    beta: float
    tracking_error: float
    information_ratio: float
    concentration_risk: float
    liquidity_risk: float
    model_risk: float
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class RiskAlert:
    """Risk alert information"""
    alert_type: AlertType
    risk_level: RiskLevel
    message: str
    current_value: float
    limit_value: float
    symbol: Optional[str] = None
    model_name: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    acknowledged: bool = False

class PositionManager:
    """Manage trading positions"""
    
    def __init__(self):
        self.positions = {}  # symbol -> Position
        self.position_history = defaultdict(list)
        self.total_portfolio_value = 0.0
        self.cash_balance = 0.0
        self.lock = threading.Lock()
    
    def update_position(self, trade: Trade, current_price: float):
        """Update position based on trade execution"""
        with self.lock:
            symbol = trade.symbol
            
            if symbol not in self.positions:
                self.positions[symbol] = Position(
                    symbol=symbol,
                    quantity=0.0,
                    entry_price=trade.price,
                    current_price=current_price,
                    market_value=0.0,
                    unrealized_pnl=0.0,
                    realized_pnl=0.0,
                    timestamp=trade.timestamp,
                    model_name=trade.model_name,
                    confidence=trade.confidence
                )
            
            position = self.positions[symbol]
            
            if trade.action == 'buy':
                # Update weighted average entry price
                total_quantity = position.quantity + trade.quantity
                if total_quantity > 0:
                    position.entry_price = (
                        (position.entry_price * position.quantity + trade.price * trade.quantity) / total_quantity
                    )
                position.quantity += trade.quantity
                self.cash_balance -= trade.quantity * trade.price
                
            elif trade.action == 'sell':
                if position.quantity >= trade.quantity:
                    # Calculate realized P&L
                    realized_pnl = (trade.price - position.entry_price) * trade.quantity
                    position.realized_pnl += realized_pnl
                    position.quantity -= trade.quantity
                    self.cash_balance += trade.quantity * trade.price
                    
                    # Remove position if fully closed
                    if position.quantity == 0:
                        self.position_history[symbol].append(position)
                        del self.positions[symbol]
                        return
            
            # Update market value and unrealized P&L
            position.current_price = current_price
            position.market_value = position.quantity * current_price
            position.unrealized_pnl = (current_price - position.entry_price) * position.quantity
            position.timestamp = trade.timestamp
            position.duration = trade.timestamp - position.timestamp
    
    def update_market_prices(self, market_data: Dict[str, float]):
        """Update current market prices for all positions"""
        with self.lock:
            for symbol, position in self.positions.items():
                if symbol in market_data:
                    position.current_price = market_data[symbol]
                    position.market_value = position.quantity * position.current_price
                    position.unrealized_pnl = (position.current_price - position.entry_price) * position.quantity
    
    def get_portfolio_value(self) -> float:
        """Calculate total portfolio value"""
        with self.lock:
            market_value = sum(pos.market_value for pos in self.positions.values())
            return self.cash_balance + market_value
    
    def get_positions_summary(self) -> Dict[str, Any]:
        """Get summary of all positions"""
        with self.lock:
            return {
                'total_positions': len(self.positions),
                'total_market_value': sum(pos.market_value for pos in self.positions.values()),
                'total_unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
                'cash_balance': self.cash_balance,
                'portfolio_value': self.get_portfolio_value(),
                'positions': {symbol: {
                    'quantity': pos.quantity,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price
                } for symbol, pos in self.positions.items()}
            }

class VaRCalculator:
    """Value at Risk and Expected Shortfall calculator"""
    
    def __init__(self, window_size: int = 252):
        self.window_size = window_size
        self.return_history = defaultdict(lambda: deque(maxlen=window_size))
        self.portfolio_returns = deque(maxlen=window_size)
    
    def update_returns(self, symbol: str, return_value: float):
        """Update return history for a symbol"""
        self.return_history[symbol].append(return_value)
    
    def update_portfolio_returns(self, portfolio_return: float):
        """Update portfolio return history"""
        self.portfolio_returns.append(portfolio_return)
    
    def calculate_var(self, confidence_level: float = 0.95, method: str = 'historical') -> float:
        """Calculate Value at Risk"""
        if len(self.portfolio_returns) < 30:  # Need minimum data
            return 0.0
        
        returns = np.array(self.portfolio_returns)
        
        if method == 'historical':
            return np.percentile(returns, (1 - confidence_level) * 100)
        
        elif method == 'parametric':
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            z_score = stats.norm.ppf(1 - confidence_level)
            return mean_return + z_score * std_return
        
        elif method == 'monte_carlo':
            # Simple Monte Carlo simulation
            mean_return = np.mean(returns)
            std_return = np.std(returns)
            simulated_returns = np.random.normal(mean_return, std_return, 10000)
            return np.percentile(simulated_returns, (1 - confidence_level) * 100)
        
        return 0.0
    
    def calculate_expected_shortfall(self, confidence_level: float = 0.95) -> float:
        """Calculate Expected Shortfall (Conditional VaR)"""
        if len(self.portfolio_returns) < 30:
            return 0.0
        
        returns = np.array(self.portfolio_returns)
        var = self.calculate_var(confidence_level)
        
        # Expected shortfall is the mean of returns below VaR
        tail_returns = returns[returns <= var]
        return np.mean(tail_returns) if len(tail_returns) > 0 else 0.0
    
    def calculate_component_var(self, positions: Dict[str, Position]) -> Dict[str, float]:
        """Calculate component VaR for each position"""
        component_vars = {}
        
        if len(self.portfolio_returns) < 30:
            return component_vars
        
        portfolio_var = self.calculate_var()
        portfolio_std = np.std(self.portfolio_returns)
        
        for symbol, position in positions.items():
            if symbol in self.return_history and len(self.return_history[symbol]) >= 30:
                position_returns = np.array(self.return_history[symbol])
                portfolio_returns = np.array(self.portfolio_returns)
                
                # Calculate correlation and beta
                correlation = np.corrcoef(position_returns[-len(portfolio_returns):], portfolio_returns)[0, 1]
                position_std = np.std(position_returns)
                beta = correlation * (position_std / portfolio_std) if portfolio_std > 0 else 0
                
                # Component VaR
                weight = position.market_value / sum(p.market_value for p in positions.values())
                component_vars[symbol] = weight * beta * portfolio_var
        
        return component_vars

class StressTester:
    """Stress testing and scenario analysis"""
    
    def __init__(self):
        self.scenarios = {
            'market_crash': {'equity_shock': -0.3, 'volatility_spike': 3.0},
            'interest_rate_shock': {'rate_change': 0.02, 'bond_impact': -0.1},
            'currency_crisis': {'fx_shock': -0.2, 'emerging_market_impact': -0.4},
            'liquidity_crisis': {'bid_ask_widening': 5.0, 'volume_drop': -0.7},
            'black_swan': {'extreme_shock': -0.5, 'correlation_breakdown': True}
        }
    
    def run_stress_test(self, positions: Dict[str, Position], scenario: str) -> Dict[str, Any]:
        """Run stress test on portfolio"""
        if scenario not in self.scenarios:
            logger.warning(f"Unknown scenario: {scenario}")
            return {}
        
        scenario_params = self.scenarios[scenario]
        results = {
            'scenario': scenario,
            'total_loss': 0.0,
            'position_impacts': {},
            'risk_metrics': {}
        }
        
        total_portfolio_value = sum(pos.market_value for pos in positions.values())
        
        for symbol, position in positions.items():
            # Apply scenario-specific shocks
            if 'equity_shock' in scenario_params:
                shock = scenario_params['equity_shock']
                position_loss = position.market_value * shock
                results['position_impacts'][symbol] = position_loss
                results['total_loss'] += position_loss
        
        # Calculate stress test metrics
        if total_portfolio_value > 0:
            results['risk_metrics'] = {
                'portfolio_loss_pct': results['total_loss'] / total_portfolio_value,
                'worst_position': min(results['position_impacts'].items(), key=lambda x: x[1]) if results['position_impacts'] else None,
                'positions_at_risk': len([loss for loss in results['position_impacts'].values() if loss < -0.05 * total_portfolio_value])
            }
        
        return results
    
    def monte_carlo_simulation(self, positions: Dict[str, Position], num_simulations: int = 1000) -> Dict[str, Any]:
        """Run Monte Carlo simulation for portfolio"""
        portfolio_values = []
        
        for _ in range(num_simulations):
            simulated_value = 0.0
            
            for symbol, position in positions.items():
                # Generate random price change (simplified)
                price_change = np.random.normal(0, 0.02)  # 2% daily volatility
                new_price = position.current_price * (1 + price_change)
                simulated_value += position.quantity * new_price
            
            portfolio_values.append(simulated_value)
        
        portfolio_values = np.array(portfolio_values)
        current_value = sum(pos.market_value for pos in positions.values())
        
        return {
            'current_value': current_value,
            'mean_simulated_value': np.mean(portfolio_values),
            'std_simulated_value': np.std(portfolio_values),
            'var_95': np.percentile(portfolio_values, 5),
            'var_99': np.percentile(portfolio_values, 1),
            'probability_of_loss': np.mean(portfolio_values < current_value),
            'expected_loss_given_loss': np.mean(portfolio_values[portfolio_values < current_value] - current_value)
        }

class RiskMonitor:
    """Real-time risk monitoring system"""
    
    def __init__(self, risk_limits: RiskLimits):
        self.risk_limits = risk_limits
        self.position_manager = PositionManager()
        self.var_calculator = VaRCalculator()
        self.stress_tester = StressTester()
        self.alerts = deque(maxlen=1000)
        self.risk_metrics_history = deque(maxlen=1000)
        self.is_monitoring = False
        self.circuit_breaker_active = False
        
    def start_monitoring(self):
        """Start real-time risk monitoring"""
        self.is_monitoring = True
        logger.info("Risk monitoring started")
    
    def stop_monitoring(self):
        """Stop risk monitoring"""
        self.is_monitoring = False
        logger.info("Risk monitoring stopped")
    
    def process_trade(self, trade: Trade, current_market_data: Dict[str, float]):
        """Process new trade and update risk metrics"""
        if not self.is_monitoring:
            return
        
        # Update position
        current_price = current_market_data.get(trade.symbol, trade.price)
        self.position_manager.update_position(trade, current_price)
        
        # Update market prices for all positions
        self.position_manager.update_market_prices(current_market_data)
        
        # Calculate and check risk metrics
        risk_metrics = self.calculate_risk_metrics()
        self.risk_metrics_history.append(risk_metrics)
        
        # Check risk limits
        self.check_risk_limits(risk_metrics)
        
        # Update VaR calculator
        portfolio_value = self.position_manager.get_portfolio_value()
        if len(self.risk_metrics_history) > 1:
            prev_value = self.risk_metrics_history[-2].portfolio_value
            if prev_value > 0:
                portfolio_return = (portfolio_value - prev_value) / prev_value
                self.var_calculator.update_portfolio_returns(portfolio_return)
    
    def calculate_risk_metrics(self) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        positions = self.position_manager.positions
        portfolio_summary = self.position_manager.get_positions_summary()
        
        portfolio_value = portfolio_summary['portfolio_value']
        total_market_value = portfolio_summary['total_market_value']
        cash_balance = portfolio_summary['cash_balance']
        
        # Basic exposure metrics
        gross_exposure = sum(abs(pos.market_value) for pos in positions.values())
        net_exposure = sum(pos.market_value for pos in positions.values())
        leverage = gross_exposure / portfolio_value if portfolio_value > 0 else 0
        
        # VaR calculations
        var_95 = abs(self.var_calculator.calculate_var(0.95))
        var_99 = abs(self.var_calculator.calculate_var(0.99))
        es_95 = abs(self.var_calculator.calculate_expected_shortfall(0.95))
        es_99 = abs(self.var_calculator.calculate_expected_shortfall(0.99))
        
        # Drawdown calculation
        if len(self.risk_metrics_history) > 0:
            peak_value = max(rm.portfolio_value for rm in self.risk_metrics_history[-252:])  # 1 year lookback
            current_drawdown = (peak_value - portfolio_value) / peak_value if peak_value > 0 else 0
            max_drawdown = max(rm.current_drawdown for rm in self.risk_metrics_history[-252:]) if self.risk_metrics_history else 0
        else:
            current_drawdown = 0
            max_drawdown = 0
        
        # Volatility calculation
        if len(self.var_calculator.portfolio_returns) > 1:
            volatility = np.std(self.var_calculator.portfolio_returns) * np.sqrt(252)  # Annualized
            mean_return = np.mean(self.var_calculator.portfolio_returns) * 252
            sharpe_ratio = (mean_return - self.risk_limits.max_daily_loss) / volatility if volatility > 0 else 0
        else:
            volatility = 0
            sharpe_ratio = 0
        
        # Concentration risk
        if portfolio_value > 0:
            position_weights = [abs(pos.market_value) / portfolio_value for pos in positions.values()]
            concentration_risk = max(position_weights) if position_weights else 0
        else:
            concentration_risk = 0
        
        # Liquidity risk (simplified)
        liquidity_risk = 1 - (cash_balance / portfolio_value) if portfolio_value > 0 else 0
        
        return RiskMetrics(
            portfolio_value=portfolio_value,
            total_exposure=total_market_value,
            net_exposure=net_exposure,
            gross_exposure=gross_exposure,
            leverage=leverage,
            var_95=var_95,
            var_99=var_99,
            expected_shortfall_95=es_95,
            expected_shortfall_99=es_99,
            max_drawdown=max_drawdown,
            current_drawdown=current_drawdown,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            beta=0.0,  # Would need benchmark
            tracking_error=0.0,  # Would need benchmark
            information_ratio=0.0,  # Would need benchmark
            concentration_risk=concentration_risk,
            liquidity_risk=liquidity_risk,
            model_risk=0.0  # Would need model performance data
        )
    
    def check_risk_limits(self, risk_metrics: RiskMetrics):
        """Check risk limits and generate alerts"""
        alerts = []
        
        # Position size limits
        if risk_metrics.concentration_risk > self.risk_limits.max_position_size:
            alerts.append(RiskAlert(
                alert_type=AlertType.POSITION_LIMIT,
                risk_level=RiskLevel.HIGH,
                message=f"Position concentration exceeds limit: {risk_metrics.concentration_risk:.2%}",
                current_value=risk_metrics.concentration_risk,
                limit_value=self.risk_limits.max_position_size
            ))
        
        # Drawdown limits
        if risk_metrics.current_drawdown > self.risk_limits.max_drawdown:
            risk_level = RiskLevel.CRITICAL if risk_metrics.current_drawdown > self.risk_limits.max_drawdown * 1.5 else RiskLevel.HIGH
            alerts.append(RiskAlert(
                alert_type=AlertType.DRAWDOWN_LIMIT,
                risk_level=risk_level,
                message=f"Drawdown exceeds limit: {risk_metrics.current_drawdown:.2%}",
                current_value=risk_metrics.current_drawdown,
                limit_value=self.risk_limits.max_drawdown
            ))
        
        # VaR limits
        if risk_metrics.var_95 > self.risk_limits.var_limit_95:
            alerts.append(RiskAlert(
                alert_type=AlertType.VAR_BREACH,
                risk_level=RiskLevel.MEDIUM,
                message=f"VaR 95% exceeds limit: {risk_metrics.var_95:.2%}",
                current_value=risk_metrics.var_95,
                limit_value=self.risk_limits.var_limit_95
            ))
        
        if risk_metrics.var_99 > self.risk_limits.var_limit_99:
            alerts.append(RiskAlert(
                alert_type=AlertType.VAR_BREACH,
                risk_level=RiskLevel.HIGH,
                message=f"VaR 99% exceeds limit: {risk_metrics.var_99:.2%}",
                current_value=risk_metrics.var_99,
                limit_value=self.risk_limits.var_limit_99
            ))
        
        # Leverage limits
        if risk_metrics.leverage > self.risk_limits.max_leverage:
            alerts.append(RiskAlert(
                alert_type=AlertType.POSITION_LIMIT,
                risk_level=RiskLevel.MEDIUM,
                message=f"Leverage exceeds limit: {risk_metrics.leverage:.2f}x",
                current_value=risk_metrics.leverage,
                limit_value=self.risk_limits.max_leverage
            ))
        
        # Liquidity limits
        if risk_metrics.liquidity_risk > (1 - self.risk_limits.min_liquidity_ratio):
            alerts.append(RiskAlert(
                alert_type=AlertType.LIQUIDITY_RISK,
                risk_level=RiskLevel.MEDIUM,
                message=f"Liquidity risk too high: {risk_metrics.liquidity_risk:.2%}",
                current_value=risk_metrics.liquidity_risk,
                limit_value=1 - self.risk_limits.min_liquidity_ratio
            ))
        
        # Add alerts to queue
        for alert in alerts:
            self.alerts.append(alert)
            logger.warning(f"Risk Alert [{alert.risk_level.value.upper()}]: {alert.message}")
            
            # Activate circuit breaker for critical alerts
            if alert.risk_level == RiskLevel.CRITICAL:
                self.activate_circuit_breaker(alert)
    
    def activate_circuit_breaker(self, alert: RiskAlert):
        """Activate circuit breaker to halt trading"""
        if not self.circuit_breaker_active:
            self.circuit_breaker_active = True
            logger.critical(f"CIRCUIT BREAKER ACTIVATED: {alert.message}")
            
            # Here you would implement actual trading halt logic
            # For example, send stop signals to trading systems
    
    def deactivate_circuit_breaker(self):
        """Manually deactivate circuit breaker"""
        self.circuit_breaker_active = False
        logger.info("Circuit breaker deactivated")
    
    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive risk dashboard data"""
        current_metrics = self.risk_metrics_history[-1] if self.risk_metrics_history else None
        
        if not current_metrics:
            return {'status': 'no_data'}
        
        # Recent alerts
        recent_alerts = [alert for alert in self.alerts if 
                        (datetime.now() - alert.timestamp).total_seconds() < 3600]  # Last hour
        
        # Position summary
        position_summary = self.position_manager.get_positions_summary()
        
        # Stress test results
        stress_results = {}
        if self.position_manager.positions:
            for scenario in ['market_crash', 'liquidity_crisis']:
                stress_results[scenario] = self.stress_tester.run_stress_test(
                    self.position_manager.positions, scenario
                )
        
        return {
            'status': 'active',
            'circuit_breaker_active': self.circuit_breaker_active,
            'current_metrics': {
                'portfolio_value': current_metrics.portfolio_value,
                'leverage': current_metrics.leverage,
                'var_95': current_metrics.var_95,
                'var_99': current_metrics.var_99,
                'current_drawdown': current_metrics.current_drawdown,
                'concentration_risk': current_metrics.concentration_risk,
                'liquidity_risk': current_metrics.liquidity_risk
            },
            'risk_limits': {
                'max_position_size': self.risk_limits.max_position_size,
                'max_drawdown': self.risk_limits.max_drawdown,
                'var_limit_95': self.risk_limits.var_limit_95,
                'var_limit_99': self.risk_limits.var_limit_99,
                'max_leverage': self.risk_limits.max_leverage
            },
            'recent_alerts': [
                {
                    'type': alert.alert_type.value,
                    'level': alert.risk_level.value,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat()
                }
                for alert in recent_alerts
            ],
            'position_summary': position_summary,
            'stress_test_results': stress_results,
            'timestamp': datetime.now().isoformat()
        }

# Example usage and testing
async def simulate_risk_monitoring():
    """Simulate risk monitoring with sample trades"""
    logger.info("Starting risk monitoring simulation")
    
    # Create risk limits
    risk_limits = RiskLimits(
        max_position_size=0.1,
        max_drawdown=0.05,
        var_limit_95=0.03,
        max_leverage=1.5
    )
    
    # Create risk monitor
    risk_monitor = RiskMonitor(risk_limits)
    risk_monitor.start_monitoring()
    
    # Simulate trades
    symbols = ['BTC/USD', 'ETH/USD', 'AAPL', 'TSLA']
    
    for i in range(50):
        # Generate random trade
        symbol = np.random.choice(symbols)
        action = np.random.choice(['buy', 'sell'])
        price = np.random.uniform(100, 1000)
        quantity = np.random.uniform(1, 10)
        
        trade = Trade(
            timestamp=datetime.now(),
            symbol=symbol,
            action=action,
            quantity=quantity,
            price=price,
            model_name='test_model',
            confidence=np.random.uniform(0.5, 0.9)
        )
        
        # Generate market data
        market_data = {
            sym: np.random.uniform(90, 1100) for sym in symbols
        }
        
        # Process trade
        risk_monitor.process_trade(trade, market_data)
        
        # Print dashboard every 10 trades
        if i % 10 == 0:
            dashboard = risk_monitor.get_risk_dashboard()
            print(f"\n=== Risk Dashboard (Trade {i+1}) ===")
            if dashboard['status'] == 'active':
                metrics = dashboard['current_metrics']
                print(f"Portfolio Value: ${metrics['portfolio_value']:,.2f}")
                print(f"Leverage: {metrics['leverage']:.2f}x")
                print(f"VaR 95%: {metrics['var_95']:.2%}")
                print(f"Current Drawdown: {metrics['current_drawdown']:.2%}")
                print(f"Recent Alerts: {len(dashboard['recent_alerts'])}")
                
                if dashboard['recent_alerts']:
                    print("Latest Alerts:")
                    for alert in dashboard['recent_alerts'][-3:]:
                        print(f"  - [{alert['level'].upper()}] {alert['message']}")
        
        await asyncio.sleep(0.1)  # Small delay
    
    risk_monitor.stop_monitoring()
    
    # Final dashboard
    final_dashboard = risk_monitor.get_risk_dashboard()
    print("\n=== Final Risk Dashboard ===")
    print(json.dumps(final_dashboard, indent=2, default=str))

if __name__ == "__main__":
    asyncio.run(simulate_risk_monitoring())