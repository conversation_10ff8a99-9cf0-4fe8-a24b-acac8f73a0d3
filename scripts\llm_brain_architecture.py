#!/usr/bin/env python3
"""
Noryon LLM-Brain Trading System Architecture

This module contains the main LLMBrainTradingSystem class that orchestrates
the entire AI-powered trading system.
"""

import os
import sys
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

# Core imports
from core.llm.llm_abstraction_layer import LL<PERSON><PERSON>tractionLayer
from core.data.data_manager import DataManager
from core.config.config_manager import ConfigManager

class LLMBrainTradingSystem:
    """
    Main orchestrator for the LLM-Brain Trading System.
    
    This class coordinates all components of the trading system including:
    - LLM processing and decision making
    - Data management and real-time feeds
    - Risk management and portfolio optimization
    - Trade execution and monitoring
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the LLM-Brain Trading System.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path or "config/llm_brain_config.yaml"
        self.logger = self._setup_logging()
        
        # Core components
        self.config_manager = None
        self.llm_layer = None
        self.data_manager = None
        
        # System state
        self.is_initialized = False
        self.is_running = False
        
        self.logger.info("LLM-Brain Trading System initialized")
    
    def _setup_logging(self) -> logging.Logger:
        """
        Setup logging configuration.
        
        Returns:
            Configured logger instance
        """
        logger = logging.getLogger("LLMBrainTradingSystem")
        logger.setLevel(logging.INFO)
        
        # Create logs directory if it doesn't exist
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        
        # File handler
        file_handler = logging.FileHandler(
            logs_dir / f"llm_brain_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    async def initialize(self) -> bool:
        """
        Initialize all system components.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            self.logger.info("Initializing LLM-Brain Trading System...")
            
            # Initialize configuration manager
            self.config_manager = ConfigManager(self.config_path)
            await self.config_manager.load_config()
            
            # Initialize LLM abstraction layer
            self.llm_layer = LLMAbstractionLayer(self.config_manager)
            await self.llm_layer.initialize()
            
            # Initialize data manager
            self.data_manager = DataManager(self.config_manager)
            await self.data_manager.initialize()
            
            self.is_initialized = True
            self.logger.info("LLM-Brain Trading System initialization complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize system: {e}")
            return False
    
    async def start(self) -> bool:
        """
        Start the trading system.
        
        Returns:
            True if started successfully, False otherwise
        """
        if not self.is_initialized:
            self.logger.error("System not initialized. Call initialize() first.")
            return False
        
        try:
            self.logger.info("Starting LLM-Brain Trading System...")
            
            # Start data feeds
            await self.data_manager.start_real_time_feeds()
            
            # Start LLM processing
            await self.llm_layer.start_processing()
            
            self.is_running = True
            self.logger.info("LLM-Brain Trading System started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start system: {e}")
            return False
    
    async def stop(self) -> bool:
        """
        Stop the trading system gracefully.
        
        Returns:
            True if stopped successfully, False otherwise
        """
        try:
            self.logger.info("Stopping LLM-Brain Trading System...")
            
            # Stop LLM processing
            if self.llm_layer:
                await self.llm_layer.stop_processing()
            
            # Stop data feeds
            if self.data_manager:
                await self.data_manager.stop_real_time_feeds()
            
            self.is_running = False
            self.logger.info("LLM-Brain Trading System stopped successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop system: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform system health check.
        
        Returns:
            Dictionary containing health status of all components
        """
        health_status = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "UNKNOWN",
            "components": {}
        }
        
        try:
            # Check configuration
            if self.config_manager:
                health_status["components"]["config"] = "HEALTHY"
            else:
                health_status["components"]["config"] = "ERROR"
            
            # Check LLM layer
            if self.llm_layer:
                llm_health = await self.llm_layer.health_check()
                health_status["components"]["llm"] = llm_health
            else:
                health_status["components"]["llm"] = "ERROR"
            
            # Check data manager
            if self.data_manager:
                data_health = await self.data_manager.health_check()
                health_status["components"]["data"] = data_health
            else:
                health_status["components"]["data"] = "ERROR"
            
            # Determine overall status
            component_statuses = list(health_status["components"].values())
            if all(status == "HEALTHY" for status in component_statuses):
                health_status["overall_status"] = "HEALTHY"
            elif any(status == "ERROR" for status in component_statuses):
                health_status["overall_status"] = "ERROR"
            else:
                health_status["overall_status"] = "DEGRADED"
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            health_status["overall_status"] = "ERROR"
            health_status["error"] = str(e)
        
        return health_status
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get current system status.
        
        Returns:
            Dictionary containing current system status
        """
        return {
            "initialized": self.is_initialized,
            "running": self.is_running,
            "config_path": self.config_path,
            "timestamp": datetime.now().isoformat()
        }

# Main execution
if __name__ == "__main__":
    async def main():
        system = LLMBrainTradingSystem()
        
        # Initialize system
        if await system.initialize():
            print("System initialized successfully")
            
            # Perform health check
            health = await system.health_check()
            print(f"Health status: {health}")
            
            # Start system
            if await system.start():
                print("System started successfully")
                
                # Keep running
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    print("Shutting down...")
                    await system.stop()
            else:
                print("Failed to start system")
        else:
            print("Failed to initialize system")
    
    asyncio.run(main())