#!/usr/bin/env python3
"""
Ultimate AI Launcher - Launch the most powerful and free AI models
"""

import subprocess
import os
from rich.console import Console
from rich.panel import Panel

console = Console()

def launch_ultimate_ai():
    """Launch ultimate AI models"""
    console.print(Panel(
        "[bold red]🚀 ULTIMATE AI MODELS[/bold red]\n\n"
        "Maximum freedom, power, and capabilities",
        title="Ultimate AI"
    ))
    
    models = {
        "1": {
            "name": "maximum-freedom-noryon-phi4-reasoning-finance-v2-latest",
            "description": "Maximum Freedom Finance AI - No restrictions"
        },
        "2": {
            "name": "ultra-enhanced-noryon-gemma-3-12b-finance-latest", 
            "description": "Ultra-Enhanced Market AI - Maximum capabilities"
        },
        "3": {
            "name": "genius-level-noryon-deepseek-r1-finance-v2-latest",
            "description": "Genius-Level Reasoning AI - Unlimited potential"
        },
        "4": {
            "name": "maximum-freedom-unrestricted-noryon-qwen3-finance-v2-latest",
            "description": "Maximum Freedom Intelligence AI - Complete autonomy"
        }
    }
    
    console.print("[green]🎯 Ultimate AI Models Available:[/green]")
    for key, model in models.items():
        console.print(f"  {key}. {model['description']}")
    
    choice = input("\nChoose ultimate AI (1-4): ")
    
    if choice in models:
        selected = models[choice]
        console.print(f"\n[red]🚀 Launching {selected['description']}[/red]")
        
        try:
            subprocess.run(['ollama', 'run', selected['name']], check=True)
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
    else:
        console.print("[red]Invalid choice[/red]")

if __name__ == "__main__":
    launch_ultimate_ai()
