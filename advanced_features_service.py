#!/usr/bin/env python3
"""
Advanced Features Service
Microservice for managing news analysis, sentiment, economic calendar, options flow
"""

import asyncio
import json
import time
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    pika = None

from circuit_breaker import CircuitBreaker
from additional_advanced_features import AdditionalAdvancedFeatures

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('advanced_features.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class FeatureRequest:
    """Data class for advanced feature requests"""
    request_id: str
    feature_type: str  # news, sentiment, economic, options, institutional
    symbol: str
    parameters: Dict[str, Any] = None
    priority: int = 1
    timestamp: datetime = None

@dataclass
class FeatureResponse:
    """Data class for advanced feature responses"""
    request_id: str
    feature_type: str
    symbol: str
    success: bool
    feature_data: Dict[str, Any] = None
    processing_time: float = 0.0
    confidence: float = 0.0
    error: str = ""
    timestamp: datetime = None

class AdvancedFeaturesService:
    """REAL microservice for advanced features"""
    
    def __init__(self, rabbitmq_url: str = "amqp://localhost"):
        self.service_name = "advanced-features"
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        
        # Initialize advanced features engine
        self.features_engine = AdditionalAdvancedFeatures()
        
        # Circuit breakers for external services
        self.circuit_breakers = {
            'news_api': CircuitBreaker(failure_threshold=3, recovery_timeout=60),
            'social_api': CircuitBreaker(failure_threshold=3, recovery_timeout=60),
            'economic_api': CircuitBreaker(failure_threshold=3, recovery_timeout=120),
            'options_api': CircuitBreaker(failure_threshold=3, recovery_timeout=60),
            'institutional_api': CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        }
        
        # Feature processing metrics
        self.feature_metrics = {
            'news_analysis': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'social_sentiment': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'economic_calendar': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'options_flow': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'institutional_flow': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'multi_timeframe': {'requests': 0, 'successes': 0, 'avg_time': 0.0},
            'pattern_recognition': {'requests': 0, 'successes': 0, 'avg_time': 0.0}
        }
        
        # Setup database
        self._setup_database()
        
        logger.info(f"Advanced Features Service initialized", extra={
            "service": self.service_name,
            "features_available": len(self.feature_metrics),
            "circuit_breakers": len(self.circuit_breakers)
        })
    
    def _setup_database(self):
        """Setup microservice database"""
        conn = sqlite3.connect('advanced_features_service.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feature_requests (
                id INTEGER PRIMARY KEY,
                request_id TEXT,
                feature_type TEXT,
                symbol TEXT,
                parameters TEXT,
                status TEXT,
                processing_time REAL,
                confidence REAL,
                error TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feature_cache (
                id INTEGER PRIMARY KEY,
                feature_type TEXT,
                symbol TEXT,
                cache_key TEXT,
                cache_data TEXT,
                confidence REAL,
                created_timestamp DATETIME,
                expiry_timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS feature_metrics (
                id INTEGER PRIMARY KEY,
                feature_type TEXT,
                metric_name TEXT,
                metric_value REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("Advanced Features Service database initialized")
    
    async def setup_message_queue(self):
        """Setup RabbitMQ connection and queues"""
        if not RABBITMQ_AVAILABLE:
            logger.info("RabbitMQ not available, using direct processing mode", extra={
                "service": self.service_name,
                "mode": "direct"
            })
            self.connection = None
            self.channel = None
            return

        try:
            # Connect to RabbitMQ
            self.connection = pika.BlockingConnection(pika.URLParameters(self.rabbitmq_url))
            self.channel = self.connection.channel()

            # Declare queues
            self.channel.queue_declare(queue='feature_requests', durable=True)
            self.channel.queue_declare(queue='feature_responses', durable=True)
            self.channel.queue_declare(queue='feature_updates', durable=True)

            # Setup exchange
            self.channel.exchange_declare(exchange='advanced_features', exchange_type='topic')

            logger.info("Message queue setup completed", extra={
                "service": self.service_name,
                "queues": ["feature_requests", "feature_responses", "feature_updates"]
            })

        except Exception as e:
            logger.error(f"Failed to setup message queue: {e}", extra={
                "service": self.service_name,
                "error": str(e)
            })
            self.connection = None
            self.channel = None
    
    async def process_feature_request(self, request: FeatureRequest) -> FeatureResponse:
        """Process advanced feature request"""
        
        start_time = time.time()
        
        try:
            # Check cache first
            cached_result = self._get_cached_feature(request)
            if cached_result:
                logger.info(f"Returning cached feature result", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "feature_type": request.feature_type,
                    "cache_hit": True
                })
                return FeatureResponse(
                    request_id=request.request_id,
                    feature_type=request.feature_type,
                    symbol=request.symbol,
                    success=True,
                    feature_data=cached_result['data'],
                    processing_time=0.1,
                    confidence=cached_result['confidence'],
                    timestamp=datetime.now()
                )
            
            # Process feature based on type
            result = await self._process_feature_by_type(request)
            
            processing_time = time.time() - start_time
            
            if result.get('error'):
                logger.warn(f"Feature processing failed", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "feature_type": request.feature_type,
                    "error": result['error']
                })
                
                # Update metrics
                self._update_feature_metrics(request.feature_type, False, processing_time)
                
                # Store failed request
                self._store_feature_request(request, False, processing_time, 0.0, result['error'])
                
                return FeatureResponse(
                    request_id=request.request_id,
                    feature_type=request.feature_type,
                    symbol=request.symbol,
                    success=False,
                    error=result['error'],
                    processing_time=processing_time,
                    timestamp=datetime.now()
                )
            
            # Extract confidence from result
            confidence = self._extract_confidence(result, request.feature_type)
            
            # Cache the result
            self._cache_feature_result(request, result, confidence)
            
            # Update metrics
            self._update_feature_metrics(request.feature_type, True, processing_time)
            
            # Store successful request
            self._store_feature_request(request, True, processing_time, confidence)
            
            logger.info(f"Feature processing completed", extra={
                "service": self.service_name,
                "request_id": request.request_id,
                "feature_type": request.feature_type,
                "processing_time": processing_time,
                "confidence": confidence
            })
            
            return FeatureResponse(
                request_id=request.request_id,
                feature_type=request.feature_type,
                symbol=request.symbol,
                success=True,
                feature_data=result,
                processing_time=processing_time,
                confidence=confidence,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            logger.error(f"Feature processing exception", extra={
                "service": self.service_name,
                "request_id": request.request_id,
                "feature_type": request.feature_type,
                "error": str(e)
            })
            
            # Update metrics
            self._update_feature_metrics(request.feature_type, False, processing_time)
            
            # Store failed request
            self._store_feature_request(request, False, processing_time, 0.0, str(e))
            
            return FeatureResponse(
                request_id=request.request_id,
                feature_type=request.feature_type,
                symbol=request.symbol,
                success=False,
                error=str(e),
                processing_time=processing_time,
                timestamp=datetime.now()
            )
    
    async def _process_feature_by_type(self, request: FeatureRequest) -> Dict[str, Any]:
        """Process feature based on type with circuit breaker protection"""
        
        feature_type = request.feature_type
        symbol = request.symbol
        
        try:
            if feature_type == 'news_analysis':
                circuit_breaker = self.circuit_breakers['news_api']
                if circuit_breaker.is_open:
                    return {'error': 'News API circuit breaker open'}
                
                result = circuit_breaker.call(
                    self.features_engine.analyze_real_time_news,
                    symbol
                )
                
            elif feature_type == 'social_sentiment':
                circuit_breaker = self.circuit_breakers['social_api']
                if circuit_breaker.is_open:
                    return {'error': 'Social API circuit breaker open'}
                
                result = circuit_breaker.call(
                    self.features_engine.analyze_social_media_sentiment,
                    symbol
                )
                
            elif feature_type == 'economic_calendar':
                circuit_breaker = self.circuit_breakers['economic_api']
                if circuit_breaker.is_open:
                    return {'error': 'Economic API circuit breaker open'}
                
                currency = request.parameters.get('currency', 'USD') if request.parameters else 'USD'
                result = circuit_breaker.call(
                    self.features_engine.analyze_economic_calendar,
                    currency
                )
                
            elif feature_type == 'options_flow':
                circuit_breaker = self.circuit_breakers['options_api']
                if circuit_breaker.is_open:
                    return {'error': 'Options API circuit breaker open'}
                
                result = circuit_breaker.call(
                    self.features_engine.analyze_options_flow,
                    symbol
                )
                
            elif feature_type == 'institutional_flow':
                circuit_breaker = self.circuit_breakers['institutional_api']
                if circuit_breaker.is_open:
                    return {'error': 'Institutional API circuit breaker open'}
                
                result = circuit_breaker.call(
                    self.features_engine.analyze_institutional_flow,
                    symbol
                )
                
            elif feature_type == 'multi_timeframe':
                result = self.features_engine.multi_timeframe_analysis(symbol)
                
            elif feature_type == 'pattern_recognition':
                result = self.features_engine.advanced_pattern_recognition(symbol)
                
            else:
                return {'error': f'Unknown feature type: {feature_type}'}
            
            return result
            
        except Exception as e:
            logger.error(f"Circuit breaker triggered for {feature_type}", extra={
                "service": self.service_name,
                "feature_type": feature_type,
                "symbol": symbol,
                "error": str(e)
            })
            return {'error': f'{feature_type} processing failed: {str(e)}'}
    
    def _extract_confidence(self, result: Dict[str, Any], feature_type: str) -> float:
        """Extract confidence score from feature result"""
        
        confidence_mappings = {
            'news_analysis': lambda r: r.get('average_relevance', 0.5),
            'social_sentiment': lambda r: abs(r.get('overall_sentiment', 0.5) - 0.5) * 2,
            'economic_calendar': lambda r: 0.8 if r.get('economic_outlook') != 'NEUTRAL' else 0.5,
            'options_flow': lambda r: min(r.get('call_put_ratio', 1.0) / 2.0, 1.0),
            'institutional_flow': lambda r: min(abs(r.get('net_flow', 0)) / 100000, 1.0),
            'multi_timeframe': lambda r: r.get('confluence_strength', 0.5),
            'pattern_recognition': lambda r: r.get('average_confidence', 0.5)
        }
        
        confidence_func = confidence_mappings.get(feature_type, lambda r: 0.5)
        return round(confidence_func(result), 2)
    
    def _get_cached_feature(self, request: FeatureRequest) -> Optional[Dict[str, Any]]:
        """Get cached feature result if available and not expired"""
        
        try:
            conn = sqlite3.connect('advanced_features_service.db')
            cursor = conn.cursor()
            
            cache_key = f"{request.feature_type}_{request.symbol}"
            
            cursor.execute('''
                SELECT cache_data, confidence 
                FROM feature_cache 
                WHERE feature_type = ? AND symbol = ? AND cache_key = ?
                AND datetime(expiry_timestamp) > datetime('now')
            ''', (request.feature_type, request.symbol, cache_key))
            
            cached_result = cursor.fetchone()
            conn.close()
            
            if cached_result:
                cache_data, confidence = cached_result
                return {
                    'data': json.loads(cache_data),
                    'confidence': confidence
                }
            
            return None
            
        except Exception as e:
            logger.warn(f"Cache retrieval failed: {e}")
            return None
    
    def _cache_feature_result(self, request: FeatureRequest, result: Dict[str, Any], confidence: float):
        """Cache feature result with appropriate expiry"""
        
        try:
            conn = sqlite3.connect('advanced_features_service.db')
            cursor = conn.cursor()
            
            cache_key = f"{request.feature_type}_{request.symbol}"
            
            # Set cache expiry based on feature type
            cache_durations = {
                'news_analysis': 15,  # 15 minutes
                'social_sentiment': 10,  # 10 minutes
                'economic_calendar': 60,  # 1 hour
                'options_flow': 5,  # 5 minutes
                'institutional_flow': 5,  # 5 minutes
                'multi_timeframe': 30,  # 30 minutes
                'pattern_recognition': 60  # 1 hour
            }
            
            duration = cache_durations.get(request.feature_type, 30)
            expiry_time = datetime.now() + timedelta(minutes=duration)
            
            # Clear old cache
            cursor.execute('''
                DELETE FROM feature_cache 
                WHERE feature_type = ? AND symbol = ? AND cache_key = ?
            ''', (request.feature_type, request.symbol, cache_key))
            
            # Insert new cache
            cursor.execute('''
                INSERT INTO feature_cache 
                (feature_type, symbol, cache_key, cache_data, confidence, created_timestamp, expiry_timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                request.feature_type, request.symbol, cache_key,
                json.dumps(result, default=str), confidence,
                datetime.now().isoformat(), expiry_time.isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.warn(f"Cache storage failed: {e}")
    
    def _update_feature_metrics(self, feature_type: str, success: bool, processing_time: float):
        """Update feature processing metrics"""
        
        if feature_type in self.feature_metrics:
            metrics = self.feature_metrics[feature_type]
            metrics['requests'] += 1
            
            if success:
                metrics['successes'] += 1
                # Update average processing time
                if metrics['successes'] == 1:
                    metrics['avg_time'] = processing_time
                else:
                    metrics['avg_time'] = (metrics['avg_time'] * (metrics['successes'] - 1) + processing_time) / metrics['successes']
    
    def _store_feature_request(self, request: FeatureRequest, success: bool, 
                             processing_time: float, confidence: float, error: str = ""):
        """Store feature request in database"""
        
        try:
            conn = sqlite3.connect('advanced_features_service.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO feature_requests 
                (request_id, feature_type, symbol, parameters, status, 
                 processing_time, confidence, error, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                request.request_id, request.feature_type, request.symbol,
                json.dumps(request.parameters) if request.parameters else None,
                'SUCCESS' if success else 'FAILED',
                processing_time, confidence, error,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to store feature request: {e}")
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status"""
        
        open_breakers = [name for name, cb in self.circuit_breakers.items() if cb.is_open]
        
        return {
            'service': self.service_name,
            'status': 'healthy' if not open_breakers else 'degraded',
            'circuit_breakers': {name: cb.get_status() for name, cb in self.circuit_breakers.items()},
            'open_breakers': open_breakers,
            'message_queue_connected': self.connection is not None,
            'features_available': list(self.feature_metrics.keys()),
            'timestamp': datetime.now().isoformat()
        }
    
    def get_feature_metrics(self) -> Dict[str, Any]:
        """Get feature processing metrics"""
        
        return {
            'service': self.service_name,
            'feature_metrics': self.feature_metrics,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main service entry point"""
    
    logger.info("Starting Advanced Features Service")
    
    # Initialize service
    service = AdvancedFeaturesService()
    
    # Setup message queue
    await service.setup_message_queue()
    
    # Test service with sample requests
    test_requests = [
        FeatureRequest(request_id="test_news_001", feature_type="news_analysis", symbol="BTC-USD"),
        FeatureRequest(request_id="test_sentiment_001", feature_type="social_sentiment", symbol="BTC-USD"),
        FeatureRequest(request_id="test_patterns_001", feature_type="pattern_recognition", symbol="BTC-USD")
    ]
    
    for test_request in test_requests:
        result = await service.process_feature_request(test_request)
        
        logger.info(f"Feature test completed", extra={
            "feature_type": test_request.feature_type,
            "success": result.success,
            "processing_time": result.processing_time,
            "confidence": result.confidence
        })
    
    # Get service health
    health = service.get_service_health()
    logger.info(f"Service health: {health['status']}")
    
    # Get feature metrics
    metrics = service.get_feature_metrics()
    logger.info(f"Feature metrics available for {len(metrics['feature_metrics'])} features")

if __name__ == "__main__":
    asyncio.run(main())
