#!/usr/bin/env python3
"""
Microservices Orchestrator
REAL orchestrator for coordinating all 4 microservices with fallback to monolithic system
"""

import asyncio
import json
import time
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

# Import microservices
from ai_agent_orchestration_service import AIAgentOrchestrationService, AgentQuery
from technical_analysis_service import TechnicalAnalysisService, AnalysisRequest
from advanced_features_service import AdvancedFeaturesService, FeatureRequest
from data_management_service import DataManagementService, DataRequest

# Import fallback monolithic system
from comprehensive_integration_system import ComprehensiveIntegrationSystem

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('microservices_orchestrator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class OrchestrationRequest:
    """Data class for orchestration requests"""
    request_id: str
    operation: str  # ultimate_analysis, agent_query, technical_analysis, feature_analysis
    symbol: str
    parameters: Dict[str, Any] = None
    priority: int = 1
    timestamp: datetime = None

@dataclass
class OrchestrationResponse:
    """Data class for orchestration responses"""
    request_id: str
    operation: str
    success: bool
    data: Dict[str, Any] = None
    execution_time: float = 0.0
    services_used: List[str] = None
    fallback_used: bool = False
    error: str = ""
    timestamp: datetime = None

class MicroservicesOrchestrator:
    """REAL microservices orchestrator with fallback capability"""
    
    def __init__(self):
        self.orchestrator_name = "microservices-orchestrator"
        
        # Initialize microservices
        self.ai_service = None
        self.ta_service = None
        self.features_service = None
        self.data_service = None
        
        # Fallback monolithic system
        self.fallback_system = None
        
        # Service health tracking
        self.service_health = {
            'ai_agent_orchestration': {'status': 'unknown', 'last_check': None},
            'technical_analysis': {'status': 'unknown', 'last_check': None},
            'advanced_features': {'status': 'unknown', 'last_check': None},
            'data_management': {'status': 'unknown', 'last_check': None}
        }
        
        # Orchestration metrics
        self.orchestration_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'microservices_requests': 0,
            'fallback_requests': 0,
            'avg_execution_time': 0.0,
            'total_execution_time': 0.0
        }
        
        logger.info(f"Microservices Orchestrator initialized", extra={
            "orchestrator": self.orchestrator_name,
            "fallback_available": True
        })
    
    async def initialize_services(self):
        """Initialize all microservices"""
        
        logger.info("Initializing microservices...")
        
        try:
            # Initialize AI Agent Orchestration Service
            self.ai_service = AIAgentOrchestrationService()
            await self.ai_service.setup_message_queue()
            logger.info("AI Agent Orchestration Service initialized")
            
            # Initialize Technical Analysis Service
            self.ta_service = TechnicalAnalysisService()
            await self.ta_service.setup_message_queue()
            logger.info("Technical Analysis Service initialized")
            
            # Initialize Advanced Features Service
            self.features_service = AdvancedFeaturesService()
            await self.features_service.setup_message_queue()
            logger.info("Advanced Features Service initialized")
            
            # Initialize Data Management Service
            self.data_service = DataManagementService()
            await self.data_service.setup_message_queue()
            logger.info("Data Management Service initialized")
            
            # Check service health
            await self.check_all_services_health()
            
            logger.info("All microservices initialized successfully", extra={
                "services_initialized": 4,
                "fallback_available": True
            })
            
        except Exception as e:
            logger.error(f"Failed to initialize microservices: {e}")
            
            # Initialize fallback system
            await self.initialize_fallback_system()
    
    async def initialize_fallback_system(self):
        """Initialize fallback monolithic system"""
        
        logger.warn("Initializing fallback monolithic system")
        
        try:
            self.fallback_system = ComprehensiveIntegrationSystem()
            logger.info("Fallback system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize fallback system: {e}")
            raise Exception("Both microservices and fallback system failed to initialize")
    
    async def process_orchestration_request(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process orchestration request using microservices or fallback"""
        
        start_time = time.time()
        request_id = request.request_id or str(uuid.uuid4())
        
        logger.info(f"Processing orchestration request", extra={
            "request_id": request_id,
            "operation": request.operation,
            "symbol": request.symbol
        })
        
        try:
            # Check if microservices are healthy
            services_healthy = await self.are_microservices_healthy()
            
            if services_healthy and request.operation == 'ultimate_analysis':
                # Use microservices for ultimate analysis
                result = await self._process_with_microservices(request)
                
            elif services_healthy and request.operation == 'agent_query':
                # Use AI service for agent queries
                result = await self._process_agent_query_microservice(request)
                
            elif services_healthy and request.operation == 'technical_analysis':
                # Use technical analysis service
                result = await self._process_technical_analysis_microservice(request)
                
            elif services_healthy and request.operation == 'feature_analysis':
                # Use advanced features service
                result = await self._process_feature_analysis_microservice(request)
                
            else:
                # Use fallback system
                result = await self._process_with_fallback(request)
            
            execution_time = time.time() - start_time
            
            # Update metrics
            self._update_orchestration_metrics(result.success, execution_time, result.fallback_used)
            
            result.execution_time = execution_time
            result.timestamp = datetime.now()
            
            logger.info(f"Orchestration request completed", extra={
                "request_id": request_id,
                "success": result.success,
                "execution_time": execution_time,
                "fallback_used": result.fallback_used,
                "services_used": len(result.services_used or [])
            })
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            logger.error(f"Orchestration request failed", extra={
                "request_id": request_id,
                "error": str(e),
                "execution_time": execution_time
            })
            
            # Update metrics
            self._update_orchestration_metrics(False, execution_time, True)
            
            return OrchestrationResponse(
                request_id=request_id,
                operation=request.operation,
                success=False,
                error=str(e),
                execution_time=execution_time,
                fallback_used=True,
                timestamp=datetime.now()
            )
    
    async def _process_with_microservices(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process ultimate analysis using microservices"""
        
        services_used = []
        analysis_data = {}
        
        try:
            # Step 1: Technical Analysis
            ta_request = AnalysisRequest(
                request_id=f"{request.request_id}_ta",
                symbol=request.symbol,
                timeframe=request.parameters.get('timeframe', '1d') if request.parameters else '1d'
            )
            
            ta_result = await self.ta_service.calculate_technical_analysis(ta_request)
            if ta_result.success:
                analysis_data['technical_analysis'] = ta_result.analysis_data
                services_used.append('technical_analysis')
            
            # Step 2: Advanced Features
            feature_types = ['news_analysis', 'social_sentiment', 'options_flow', 'pattern_recognition']
            
            for feature_type in feature_types:
                feature_request = FeatureRequest(
                    request_id=f"{request.request_id}_{feature_type}",
                    feature_type=feature_type,
                    symbol=request.symbol
                )
                
                feature_result = await self.features_service.process_feature_request(feature_request)
                if feature_result.success:
                    analysis_data[feature_type] = feature_result.feature_data
                    if 'advanced_features' not in services_used:
                        services_used.append('advanced_features')
            
            # Step 3: AI Agent Consensus
            consensus_query = AgentQuery(
                query_id=f"{request.request_id}_consensus",
                agent_name="fathom_r1",  # Use Fathom R1 for strategic analysis
                query_text=f"Provide strategic analysis for {request.symbol} based on available data",
                symbol=request.symbol
            )
            
            ai_result = await self.ai_service.query_agent_async(consensus_query)
            if ai_result.success:
                analysis_data['ai_analysis'] = {
                    'response': ai_result.response_text,
                    'confidence': ai_result.confidence,
                    'decision': ai_result.decision
                }
                services_used.append('ai_agent_orchestration')
            
            # Step 4: Data Storage (store results)
            data_request = DataRequest(
                request_id=f"{request.request_id}_store",
                operation="write",
                database="microservices_results.db",
                table="orchestration_results",
                data={
                    'request_id': request.request_id,
                    'symbol': request.symbol,
                    'analysis_data': json.dumps(analysis_data, default=str),
                    'services_used': json.dumps(services_used),
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            # Create results database if it doesn't exist
            await self._ensure_results_database()
            
            data_result = await self.data_service.process_data_request(data_request)
            if data_result.success:
                services_used.append('data_management')
            
            return OrchestrationResponse(
                request_id=request.request_id,
                operation=request.operation,
                success=True,
                data=analysis_data,
                services_used=services_used,
                fallback_used=False
            )
            
        except Exception as e:
            logger.error(f"Microservices processing failed: {e}")
            
            # Fallback to monolithic system
            return await self._process_with_fallback(request)
    
    async def _process_agent_query_microservice(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process agent query using AI service"""
        
        agent_query = AgentQuery(
            query_id=request.request_id,
            agent_name=request.parameters.get('agent_name', 'fathom_r1') if request.parameters else 'fathom_r1',
            query_text=request.parameters.get('query_text', '') if request.parameters else '',
            symbol=request.symbol
        )
        
        result = await self.ai_service.query_agent_async(agent_query)
        
        return OrchestrationResponse(
            request_id=request.request_id,
            operation=request.operation,
            success=result.success,
            data={
                'agent_response': result.response_text,
                'confidence': result.confidence,
                'decision': result.decision,
                'response_time': result.response_time
            },
            services_used=['ai_agent_orchestration'],
            fallback_used=False,
            error=result.error
        )
    
    async def _process_technical_analysis_microservice(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process technical analysis using TA service"""
        
        ta_request = AnalysisRequest(
            request_id=request.request_id,
            symbol=request.symbol,
            timeframe=request.parameters.get('timeframe', '1d') if request.parameters else '1d'
        )
        
        result = await self.ta_service.calculate_technical_analysis(ta_request)
        
        return OrchestrationResponse(
            request_id=request.request_id,
            operation=request.operation,
            success=result.success,
            data=result.analysis_data,
            services_used=['technical_analysis'],
            fallback_used=False,
            error=result.error
        )
    
    async def _process_feature_analysis_microservice(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process feature analysis using features service"""
        
        feature_request = FeatureRequest(
            request_id=request.request_id,
            feature_type=request.parameters.get('feature_type', 'news_analysis') if request.parameters else 'news_analysis',
            symbol=request.symbol,
            parameters=request.parameters
        )
        
        result = await self.features_service.process_feature_request(feature_request)
        
        return OrchestrationResponse(
            request_id=request.request_id,
            operation=request.operation,
            success=result.success,
            data=result.feature_data,
            services_used=['advanced_features'],
            fallback_used=False,
            error=result.error
        )
    
    async def _process_with_fallback(self, request: OrchestrationRequest) -> OrchestrationResponse:
        """Process request using fallback monolithic system"""
        
        logger.warn(f"Using fallback system for request {request.request_id}")
        
        if not self.fallback_system:
            await self.initialize_fallback_system()
        
        try:
            if request.operation == 'ultimate_analysis':
                result = self.fallback_system.ultimate_market_analysis(request.symbol)
                
                return OrchestrationResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    success=True,
                    data=result,
                    services_used=['fallback_monolithic'],
                    fallback_used=True
                )
            else:
                return OrchestrationResponse(
                    request_id=request.request_id,
                    operation=request.operation,
                    success=False,
                    error=f"Operation {request.operation} not supported in fallback mode",
                    services_used=['fallback_monolithic'],
                    fallback_used=True
                )
                
        except Exception as e:
            return OrchestrationResponse(
                request_id=request.request_id,
                operation=request.operation,
                success=False,
                error=f"Fallback system failed: {str(e)}",
                services_used=['fallback_monolithic'],
                fallback_used=True
            )
    
    async def _ensure_results_database(self):
        """Ensure microservices results database exists"""
        
        try:
            import sqlite3
            conn = sqlite3.connect('microservices_results.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orchestration_results (
                    id INTEGER PRIMARY KEY,
                    request_id TEXT,
                    symbol TEXT,
                    analysis_data TEXT,
                    services_used TEXT,
                    timestamp DATETIME
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to create results database: {e}")
    
    async def check_all_services_health(self) -> Dict[str, Any]:
        """Check health of all microservices"""
        
        health_results = {}
        
        # Check AI service
        if self.ai_service:
            try:
                health = self.ai_service.get_service_health()
                health_results['ai_agent_orchestration'] = health
                self.service_health['ai_agent_orchestration'] = {
                    'status': health['status'],
                    'last_check': datetime.now()
                }
            except Exception as e:
                health_results['ai_agent_orchestration'] = {'status': 'error', 'error': str(e)}
                self.service_health['ai_agent_orchestration'] = {
                    'status': 'error',
                    'last_check': datetime.now()
                }
        
        # Check TA service
        if self.ta_service:
            try:
                health = self.ta_service.get_service_health()
                health_results['technical_analysis'] = health
                self.service_health['technical_analysis'] = {
                    'status': health['status'],
                    'last_check': datetime.now()
                }
            except Exception as e:
                health_results['technical_analysis'] = {'status': 'error', 'error': str(e)}
                self.service_health['technical_analysis'] = {
                    'status': 'error',
                    'last_check': datetime.now()
                }
        
        # Check Features service
        if self.features_service:
            try:
                health = self.features_service.get_service_health()
                health_results['advanced_features'] = health
                self.service_health['advanced_features'] = {
                    'status': health['status'],
                    'last_check': datetime.now()
                }
            except Exception as e:
                health_results['advanced_features'] = {'status': 'error', 'error': str(e)}
                self.service_health['advanced_features'] = {
                    'status': 'error',
                    'last_check': datetime.now()
                }
        
        # Check Data service
        if self.data_service:
            try:
                health = self.data_service.get_service_health()
                health_results['data_management'] = health
                self.service_health['data_management'] = {
                    'status': health['status'],
                    'last_check': datetime.now()
                }
            except Exception as e:
                health_results['data_management'] = {'status': 'error', 'error': str(e)}
                self.service_health['data_management'] = {
                    'status': 'error',
                    'last_check': datetime.now()
                }
        
        return health_results
    
    async def are_microservices_healthy(self) -> bool:
        """Check if microservices are healthy enough to use"""
        
        healthy_services = 0
        total_services = 4
        
        for service_name, health in self.service_health.items():
            if health.get('status') in ['healthy', 'degraded']:
                healthy_services += 1
        
        # Require at least 3 out of 4 services to be healthy
        return healthy_services >= 3
    
    def _update_orchestration_metrics(self, success: bool, execution_time: float, fallback_used: bool):
        """Update orchestration metrics"""
        
        self.orchestration_metrics['total_requests'] += 1
        
        if success:
            self.orchestration_metrics['successful_requests'] += 1
            self.orchestration_metrics['total_execution_time'] += execution_time
            
            # Update average execution time
            self.orchestration_metrics['avg_execution_time'] = (
                self.orchestration_metrics['total_execution_time'] / 
                self.orchestration_metrics['successful_requests']
            )
        
        if fallback_used:
            self.orchestration_metrics['fallback_requests'] += 1
        else:
            self.orchestration_metrics['microservices_requests'] += 1
    
    def get_orchestrator_health(self) -> Dict[str, Any]:
        """Get orchestrator health status"""
        
        return {
            'orchestrator': self.orchestrator_name,
            'status': 'healthy',
            'microservices_health': self.service_health,
            'fallback_available': self.fallback_system is not None,
            'metrics': self.orchestration_metrics,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main orchestrator entry point"""
    
    logger.info("Starting Microservices Orchestrator")
    
    # Initialize orchestrator
    orchestrator = MicroservicesOrchestrator()
    
    # Initialize all services
    await orchestrator.initialize_services()
    
    # Test orchestrator with sample request
    test_request = OrchestrationRequest(
        request_id="test_orchestration_001",
        operation="ultimate_analysis",
        symbol="BTC-USD",
        parameters={'timeframe': '1d'}
    )
    
    result = await orchestrator.process_orchestration_request(test_request)
    
    logger.info(f"Orchestrator test completed", extra={
        "success": result.success,
        "execution_time": result.execution_time,
        "services_used": len(result.services_used or []),
        "fallback_used": result.fallback_used
    })
    
    # Get orchestrator health
    health = orchestrator.get_orchestrator_health()
    logger.info(f"Orchestrator health: {health['status']}")
    
    # Check service health
    service_health = await orchestrator.check_all_services_health()
    healthy_services = len([s for s in service_health.values() if s.get('status') in ['healthy', 'degraded']])
    
    logger.info(f"Microservices health: {healthy_services}/4 services healthy")

if __name__ == "__main__":
    asyncio.run(main())
