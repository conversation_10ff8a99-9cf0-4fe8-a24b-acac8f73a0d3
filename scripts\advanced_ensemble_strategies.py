#!/usr/bin/env python3
"""
Advanced Ensemble Trading Strategies
Sophisticated multi-model decision making for enhanced trading performance
"""

import asyncio
import numpy as np
from typing import Dict, List, Tuple
from dataclasses import dataclass
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

@dataclass
class ModelPrediction:
    model_name: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    price_target: float
    stop_loss: float
    reasoning: str
    specialization: str

@dataclass
class EnsembleDecision:
    final_action: str
    confidence: float
    price_target: float
    stop_loss: float
    position_size: float
    consensus_score: float
    risk_score: float

class AdvancedEnsembleStrategies:
    """Advanced ensemble decision-making strategies"""
    
    def __init__(self):
        # Model weights based on specialization and performance
        self.model_weights = {
            "risk_assessment": 0.15,
            "market_analysis": 0.15,
            "advanced_risk_management": 0.12,
            "enhanced_market_analysis": 0.12,
            "multilingual_analysis": 0.08,
            "cognitive_analysis": 0.08,
            "step_by_step_reasoning": 0.10,
            "efficient_analysis": 0.08,
            "visual_market_analysis": 0.12  # High weight for new visual analysis
        }
        
        # Strategy configurations
        self.strategies = {
            "conservative": {
                "min_consensus": 0.7,
                "max_position_size": 0.02,
                "risk_multiplier": 0.5
            },
            "moderate": {
                "min_consensus": 0.6,
                "max_position_size": 0.03,
                "risk_multiplier": 1.0
            },
            "aggressive": {
                "min_consensus": 0.5,
                "max_position_size": 0.05,
                "risk_multiplier": 1.5
            }
        }
    
    def weighted_voting_strategy(self, predictions: List[ModelPrediction]) -> EnsembleDecision:
        """Weighted voting based on model specialization and confidence"""
        
        # Calculate weighted votes
        buy_weight = 0
        sell_weight = 0
        hold_weight = 0
        
        total_confidence = 0
        weighted_price_targets = []
        weighted_stop_losses = []
        
        for pred in predictions:
            weight = self.model_weights.get(pred.specialization, 0.1)
            confidence_weight = weight * pred.confidence
            
            if pred.action == "BUY":
                buy_weight += confidence_weight
            elif pred.action == "SELL":
                sell_weight += confidence_weight
            else:
                hold_weight += confidence_weight
            
            total_confidence += confidence_weight
            weighted_price_targets.append(pred.price_target * confidence_weight)
            weighted_stop_losses.append(pred.stop_loss * confidence_weight)
        
        # Determine final action
        if buy_weight > sell_weight and buy_weight > hold_weight:
            final_action = "BUY"
            final_confidence = buy_weight / total_confidence
        elif sell_weight > buy_weight and sell_weight > hold_weight:
            final_action = "SELL"
            final_confidence = sell_weight / total_confidence
        else:
            final_action = "HOLD"
            final_confidence = hold_weight / total_confidence
        
        # Calculate consensus score
        max_weight = max(buy_weight, sell_weight, hold_weight)
        consensus_score = max_weight / total_confidence
        
        # Calculate weighted averages
        avg_price_target = sum(weighted_price_targets) / total_confidence
        avg_stop_loss = sum(weighted_stop_losses) / total_confidence
        
        return EnsembleDecision(
            final_action=final_action,
            confidence=final_confidence,
            price_target=avg_price_target,
            stop_loss=avg_stop_loss,
            position_size=self._calculate_position_size(final_confidence, consensus_score),
            consensus_score=consensus_score,
            risk_score=self._calculate_risk_score(predictions)
        )
    
    def confidence_threshold_strategy(self, predictions: List[ModelPrediction], 
                                    strategy_type: str = "moderate") -> EnsembleDecision:
        """Strategy based on confidence thresholds and consensus requirements"""
        
        strategy_config = self.strategies[strategy_type]
        
        # Filter high-confidence predictions
        high_conf_predictions = [p for p in predictions if p.confidence >= 0.7]
        
        if len(high_conf_predictions) < 3:  # Need at least 3 high-confidence models
            return EnsembleDecision(
                final_action="HOLD",
                confidence=0.5,
                price_target=185.0,
                stop_loss=175.0,
                position_size=0.01,
                consensus_score=0.3,
                risk_score=0.8
            )
        
        # Use weighted voting on high-confidence predictions
        decision = self.weighted_voting_strategy(high_conf_predictions)
        
        # Apply strategy-specific adjustments
        if decision.consensus_score < strategy_config["min_consensus"]:
            decision.final_action = "HOLD"
            decision.position_size = 0.01
        else:
            decision.position_size = min(
                decision.position_size,
                strategy_config["max_position_size"]
            )
        
        # Adjust for risk
        decision.risk_score *= strategy_config["risk_multiplier"]
        
        return decision
    
    def visual_enhanced_strategy(self, predictions: List[ModelPrediction]) -> EnsembleDecision:
        """Strategy that gives special weight to visual analysis signals"""
        
        # Find visual analysis prediction
        visual_pred = None
        other_preds = []
        
        for pred in predictions:
            if pred.specialization == "visual_market_analysis":
                visual_pred = pred
            else:
                other_preds.append(pred)
        
        if visual_pred is None:
            # No visual analysis available, use standard weighted voting
            return self.weighted_voting_strategy(predictions)
        
        # Enhanced strategy with visual signals
        base_decision = self.weighted_voting_strategy(other_preds)
        
        # Visual analysis agreement bonus
        if visual_pred.action == base_decision.final_action:
            # Visual analysis agrees - boost confidence
            enhanced_confidence = min(base_decision.confidence * 1.2, 1.0)
            enhanced_consensus = min(base_decision.consensus_score * 1.15, 1.0)
        else:
            # Visual analysis disagrees - reduce confidence
            enhanced_confidence = base_decision.confidence * 0.8
            enhanced_consensus = base_decision.consensus_score * 0.9
        
        # If visual analysis has very high confidence, consider override
        if visual_pred.confidence > 0.85 and base_decision.consensus_score < 0.6:
            return EnsembleDecision(
                final_action=visual_pred.action,
                confidence=visual_pred.confidence * 0.9,  # Slight discount for override
                price_target=visual_pred.price_target,
                stop_loss=visual_pred.stop_loss,
                position_size=self._calculate_position_size(visual_pred.confidence * 0.9, 0.7),
                consensus_score=0.7,
                risk_score=0.6
            )
        
        return EnsembleDecision(
            final_action=base_decision.final_action,
            confidence=enhanced_confidence,
            price_target=base_decision.price_target,
            stop_loss=base_decision.stop_loss,
            position_size=self._calculate_position_size(enhanced_confidence, enhanced_consensus),
            consensus_score=enhanced_consensus,
            risk_score=base_decision.risk_score
        )
    
    def adaptive_strategy(self, predictions: List[ModelPrediction], 
                         market_volatility: float = 0.2) -> EnsembleDecision:
        """Adaptive strategy that adjusts based on market conditions"""
        
        # Adjust strategy based on market volatility
        if market_volatility > 0.3:
            # High volatility - use conservative approach
            return self.confidence_threshold_strategy(predictions, "conservative")
        elif market_volatility < 0.15:
            # Low volatility - can be more aggressive
            return self.confidence_threshold_strategy(predictions, "aggressive")
        else:
            # Normal volatility - use visual enhanced strategy
            return self.visual_enhanced_strategy(predictions)
    
    def _calculate_position_size(self, confidence: float, consensus: float) -> float:
        """Calculate position size based on confidence and consensus"""
        base_size = 0.02
        confidence_multiplier = confidence
        consensus_multiplier = consensus
        
        position_size = base_size * confidence_multiplier * consensus_multiplier
        return min(max(position_size, 0.005), 0.05)  # Cap between 0.5% and 5%
    
    def _calculate_risk_score(self, predictions: List[ModelPrediction]) -> float:
        """Calculate overall risk score from predictions"""
        risk_indicators = []
        
        for pred in predictions:
            # Higher confidence = lower risk
            confidence_risk = 1.0 - pred.confidence
            
            # Risk assessment models have lower risk weight
            if "risk" in pred.specialization:
                confidence_risk *= 0.7
            
            risk_indicators.append(confidence_risk)
        
        return np.mean(risk_indicators) if risk_indicators else 0.5
    
    def compare_strategies(self, predictions: List[ModelPrediction]) -> Dict:
        """Compare all strategies for the same set of predictions"""
        
        strategies_results = {
            "weighted_voting": self.weighted_voting_strategy(predictions),
            "conservative": self.confidence_threshold_strategy(predictions, "conservative"),
            "moderate": self.confidence_threshold_strategy(predictions, "moderate"),
            "aggressive": self.confidence_threshold_strategy(predictions, "aggressive"),
            "visual_enhanced": self.visual_enhanced_strategy(predictions),
            "adaptive": self.adaptive_strategy(predictions)
        }
        
        return strategies_results
    
    def generate_strategy_report(self, strategies_results: Dict) -> Table:
        """Generate comparison report of different strategies"""
        
        table = Table(title="🎯 Ensemble Strategy Comparison")
        table.add_column("Strategy", style="cyan")
        table.add_column("Action", style="green")
        table.add_column("Confidence", style="yellow")
        table.add_column("Position Size", style="blue")
        table.add_column("Consensus", style="magenta")
        table.add_column("Risk Score", style="red")
        
        for strategy_name, decision in strategies_results.items():
            table.add_row(
                strategy_name.replace("_", " ").title(),
                decision.final_action,
                f"{decision.confidence:.2f}",
                f"{decision.position_size:.3f}",
                f"{decision.consensus_score:.2f}",
                f"{decision.risk_score:.2f}"
            )
        
        return table

# Example usage and testing
async def test_ensemble_strategies():
    """Test the ensemble strategies with sample predictions"""
    
    # Sample predictions from 9 models
    sample_predictions = [
        ModelPrediction("model1", "BUY", 0.85, 190.0, 180.0, "Strong uptrend", "risk_assessment"),
        ModelPrediction("model2", "BUY", 0.75, 188.0, 178.0, "Bullish signals", "market_analysis"),
        ModelPrediction("model3", "HOLD", 0.65, 185.0, 175.0, "Mixed signals", "advanced_risk_management"),
        ModelPrediction("model4", "BUY", 0.80, 192.0, 182.0, "Technical breakout", "enhanced_market_analysis"),
        ModelPrediction("model5", "BUY", 0.70, 189.0, 179.0, "Global sentiment", "multilingual_analysis"),
        ModelPrediction("model6", "SELL", 0.60, 180.0, 190.0, "Behavioral concerns", "cognitive_analysis"),
        ModelPrediction("model7", "BUY", 0.85, 191.0, 181.0, "Logical analysis", "step_by_step_reasoning"),
        ModelPrediction("model8", "BUY", 0.75, 187.0, 177.0, "Quick signals", "efficient_analysis"),
        ModelPrediction("model9", "BUY", 0.90, 193.0, 183.0, "Chart patterns", "visual_market_analysis"),
    ]
    
    ensemble = AdvancedEnsembleStrategies()
    
    console.print(Panel(
        "[bold blue]🎯 Testing Advanced Ensemble Strategies[/bold blue]\n\n"
        "Comparing different decision-making approaches\n"
        "with 9-model predictions for AAPL analysis",
        title="Strategy Testing"
    ))
    
    # Compare all strategies
    results = ensemble.compare_strategies(sample_predictions)
    
    # Display results
    console.print(ensemble.generate_strategy_report(results))
    
    # Recommend best strategy
    best_strategy = max(results.items(), key=lambda x: x[1].confidence * x[1].consensus_score)
    
    console.print(Panel(
        f"[bold green]🏆 Recommended Strategy: {best_strategy[0].replace('_', ' ').title()}[/bold green]\n\n"
        f"Action: {best_strategy[1].final_action}\n"
        f"Confidence: {best_strategy[1].confidence:.2f}\n"
        f"Position Size: {best_strategy[1].position_size:.3f}\n"
        f"Price Target: ${best_strategy[1].price_target:.2f}\n"
        f"Stop Loss: ${best_strategy[1].stop_loss:.2f}",
        title="Best Strategy"
    ))

if __name__ == "__main__":
    asyncio.run(test_ensemble_strategies())
