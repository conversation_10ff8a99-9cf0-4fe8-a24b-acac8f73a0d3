#!/usr/bin/env python3
"""
Technical Analysis Service
Microservice for handling professional technical analysis and all 20+ indicators
"""

import asyncio
import json
import time
import logging
import sqlite3
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
try:
    import pika
    RABBITMQ_AVAILABLE = True
except ImportError:
    RABBITMQ_AVAILABLE = False
    pika = None

from circuit_breaker import CircuitBreaker
from professional_technical_analysis import ProfessionalTechnicalAnalysis

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('technical_analysis.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class AnalysisRequest:
    """Data class for technical analysis requests"""
    request_id: str
    symbol: str
    timeframe: str
    indicators: List[str] = None  # Specific indicators to calculate
    priority: int = 1
    timestamp: datetime = None

@dataclass
class AnalysisResponse:
    """Data class for technical analysis responses"""
    request_id: str
    symbol: str
    timeframe: str
    success: bool
    analysis_data: Dict[str, Any] = None
    indicators_calculated: int = 0
    calculation_time: float = 0.0
    error: str = ""
    timestamp: datetime = None

class TechnicalAnalysisService:
    """REAL microservice for technical analysis"""
    
    def __init__(self, rabbitmq_url: str = "amqp://localhost"):
        self.service_name = "technical-analysis"
        self.rabbitmq_url = rabbitmq_url
        self.connection = None
        self.channel = None
        
        # Initialize technical analysis engine
        self.ta_engine = ProfessionalTechnicalAnalysis()
        
        # Circuit breaker for external data sources
        self.data_circuit_breaker = CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=30,
            expected_exception=Exception
        )
        
        # Performance tracking
        self.service_metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'total_calculation_time': 0.0,
            'total_indicators_calculated': 0,
            'avg_calculation_time': 0.0,
            'success_rate': 0.0
        }
        
        # Setup database
        self._setup_database()
        
        logger.info(f"Technical Analysis Service initialized", extra={
            "service": self.service_name,
            "indicators_available": 20,
            "timeframes_supported": ["1m", "5m", "15m", "1h", "4h", "1d", "1w"]
        })
    
    def _setup_database(self):
        """Setup microservice database"""
        conn = sqlite3.connect('technical_analysis_service.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_requests (
                id INTEGER PRIMARY KEY,
                request_id TEXT,
                symbol TEXT,
                timeframe TEXT,
                indicators_requested TEXT,
                status TEXT,
                calculation_time REAL,
                indicators_calculated INTEGER,
                error TEXT,
                timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS indicator_cache (
                id INTEGER PRIMARY KEY,
                symbol TEXT,
                timeframe TEXT,
                indicator_name TEXT,
                indicator_value REAL,
                calculation_timestamp DATETIME,
                expiry_timestamp DATETIME
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS service_performance (
                id INTEGER PRIMARY KEY,
                metric_name TEXT,
                metric_value REAL,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
        
        logger.info("Technical Analysis Service database initialized")
    
    async def setup_message_queue(self):
        """Setup RabbitMQ connection and queues"""
        if not RABBITMQ_AVAILABLE:
            logger.info("RabbitMQ not available, using direct processing mode", extra={
                "service": self.service_name,
                "mode": "direct"
            })
            self.connection = None
            self.channel = None
            return

        try:
            # Connect to RabbitMQ
            self.connection = pika.BlockingConnection(pika.URLParameters(self.rabbitmq_url))
            self.channel = self.connection.channel()

            # Declare queues
            self.channel.queue_declare(queue='technical_analysis_requests', durable=True)
            self.channel.queue_declare(queue='technical_analysis_responses', durable=True)
            self.channel.queue_declare(queue='indicator_updates', durable=True)

            # Setup exchange
            self.channel.exchange_declare(exchange='technical_analysis', exchange_type='topic')

            logger.info("Message queue setup completed", extra={
                "service": self.service_name,
                "queues": ["technical_analysis_requests", "technical_analysis_responses", "indicator_updates"]
            })

        except Exception as e:
            logger.error(f"Failed to setup message queue: {e}", extra={
                "service": self.service_name,
                "error": str(e)
            })
            self.connection = None
            self.channel = None
    
    async def calculate_technical_analysis(self, request: AnalysisRequest) -> AnalysisResponse:
        """Calculate technical analysis with circuit breaker protection"""
        
        start_time = time.time()
        
        try:
            # Check circuit breaker for data source
            if self.data_circuit_breaker.is_open:
                logger.warn(f"Data circuit breaker open", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "symbol": request.symbol
                })
                return AnalysisResponse(
                    request_id=request.request_id,
                    symbol=request.symbol,
                    timeframe=request.timeframe,
                    success=False,
                    error="Data source circuit breaker open",
                    timestamp=datetime.now()
                )
            
            # Check cache first
            cached_analysis = self._get_cached_analysis(request.symbol, request.timeframe)
            if cached_analysis:
                logger.info(f"Returning cached analysis", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "symbol": request.symbol,
                    "cache_hit": True
                })
                return AnalysisResponse(
                    request_id=request.request_id,
                    symbol=request.symbol,
                    timeframe=request.timeframe,
                    success=True,
                    analysis_data=cached_analysis,
                    indicators_calculated=len(cached_analysis),
                    calculation_time=0.1,  # Cache retrieval time
                    timestamp=datetime.now()
                )
            
            # Perform technical analysis
            analysis_result = await self._perform_analysis_with_circuit_breaker(request)
            
            calculation_time = time.time() - start_time
            
            if analysis_result.get('error'):
                logger.warn(f"Technical analysis failed", extra={
                    "service": self.service_name,
                    "request_id": request.request_id,
                    "symbol": request.symbol,
                    "error": analysis_result['error']
                })
                
                return AnalysisResponse(
                    request_id=request.request_id,
                    symbol=request.symbol,
                    timeframe=request.timeframe,
                    success=False,
                    error=analysis_result['error'],
                    calculation_time=calculation_time,
                    timestamp=datetime.now()
                )
            
            # Count indicators calculated
            indicators_calculated = self._count_indicators(analysis_result)
            
            # Cache the results
            self._cache_analysis(request.symbol, request.timeframe, analysis_result)
            
            # Update service metrics
            self._update_service_metrics(True, calculation_time, indicators_calculated)
            
            # Store request
            self._store_analysis_request(request, True, calculation_time, indicators_calculated)
            
            logger.info(f"Technical analysis completed", extra={
                "service": self.service_name,
                "request_id": request.request_id,
                "symbol": request.symbol,
                "calculation_time": calculation_time,
                "indicators_calculated": indicators_calculated
            })
            
            return AnalysisResponse(
                request_id=request.request_id,
                symbol=request.symbol,
                timeframe=request.timeframe,
                success=True,
                analysis_data=analysis_result,
                indicators_calculated=indicators_calculated,
                calculation_time=calculation_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            calculation_time = time.time() - start_time
            
            logger.error(f"Technical analysis exception", extra={
                "service": self.service_name,
                "request_id": request.request_id,
                "symbol": request.symbol,
                "error": str(e)
            })
            
            # Update service metrics
            self._update_service_metrics(False, calculation_time, 0)
            
            # Store failed request
            self._store_analysis_request(request, False, calculation_time, 0, str(e))
            
            return AnalysisResponse(
                request_id=request.request_id,
                symbol=request.symbol,
                timeframe=request.timeframe,
                success=False,
                error=str(e),
                calculation_time=calculation_time,
                timestamp=datetime.now()
            )
    
    async def _perform_analysis_with_circuit_breaker(self, request: AnalysisRequest) -> Dict[str, Any]:
        """Perform analysis with circuit breaker protection"""
        
        try:
            # Use circuit breaker for data retrieval
            analysis = self.data_circuit_breaker.call(
                self.ta_engine.get_complete_analysis,
                request.symbol,
                request.timeframe
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Circuit breaker triggered for data source", extra={
                "service": self.service_name,
                "symbol": request.symbol,
                "error": str(e)
            })
            return {'error': f'Data source failure: {str(e)}'}
    
    def _count_indicators(self, analysis_data: Dict[str, Any]) -> int:
        """Count the number of indicators calculated"""
        
        indicator_keys = [
            'rsi', 'macd', 'bollinger_bands', 'stochastic', 'williams_r',
            'cci', 'atr', 'adx', 'parabolic_sar', 'ichimoku', 'obv',
            'vwap', 'mfi', 'fibonacci', 'patterns'
        ]
        
        count = 0
        for key in indicator_keys:
            if key in analysis_data and analysis_data[key] is not None:
                count += 1
        
        return count
    
    def _get_cached_analysis(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Get cached analysis if available and not expired"""
        
        try:
            conn = sqlite3.connect('technical_analysis_service.db')
            cursor = conn.cursor()
            
            # Check for recent cache (within 5 minutes for intraday, 1 hour for daily)
            cache_expiry_minutes = 5 if timeframe in ['1m', '5m', '15m'] else 60
            
            cursor.execute('''
                SELECT indicator_name, indicator_value 
                FROM indicator_cache 
                WHERE symbol = ? AND timeframe = ? 
                AND datetime(calculation_timestamp) > datetime('now', '-{} minutes')
            '''.format(cache_expiry_minutes), (symbol, timeframe))
            
            cached_indicators = cursor.fetchall()
            conn.close()
            
            if cached_indicators:
                # Reconstruct analysis data from cache
                cached_analysis = {}
                for indicator_name, indicator_value in cached_indicators:
                    cached_analysis[indicator_name] = indicator_value
                
                return cached_analysis
            
            return None
            
        except Exception as e:
            logger.warn(f"Cache retrieval failed: {e}")
            return None
    
    def _cache_analysis(self, symbol: str, timeframe: str, analysis_data: Dict[str, Any]):
        """Cache analysis results"""
        
        try:
            conn = sqlite3.connect('technical_analysis_service.db')
            cursor = conn.cursor()
            
            # Clear old cache for this symbol/timeframe
            cursor.execute('''
                DELETE FROM indicator_cache 
                WHERE symbol = ? AND timeframe = ?
            ''', (symbol, timeframe))
            
            # Cache new indicators
            timestamp = datetime.now()
            for key, value in analysis_data.items():
                if isinstance(value, (int, float)):
                    cursor.execute('''
                        INSERT INTO indicator_cache 
                        (symbol, timeframe, indicator_name, indicator_value, calculation_timestamp)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (symbol, timeframe, key, value, timestamp.isoformat()))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.warn(f"Cache storage failed: {e}")
    
    def _update_service_metrics(self, success: bool, calculation_time: float, indicators_calculated: int):
        """Update service performance metrics"""
        
        self.service_metrics['total_requests'] += 1
        
        if success:
            self.service_metrics['successful_requests'] += 1
            self.service_metrics['total_calculation_time'] += calculation_time
            self.service_metrics['total_indicators_calculated'] += indicators_calculated
        
        # Calculate averages
        if self.service_metrics['successful_requests'] > 0:
            self.service_metrics['avg_calculation_time'] = (
                self.service_metrics['total_calculation_time'] / 
                self.service_metrics['successful_requests']
            )
        
        self.service_metrics['success_rate'] = (
            self.service_metrics['successful_requests'] / 
            self.service_metrics['total_requests']
        )
    
    def _store_analysis_request(self, request: AnalysisRequest, success: bool, 
                              calculation_time: float, indicators_calculated: int, error: str = ""):
        """Store analysis request in database"""
        
        try:
            conn = sqlite3.connect('technical_analysis_service.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO analysis_requests 
                (request_id, symbol, timeframe, indicators_requested, status, 
                 calculation_time, indicators_calculated, error, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                request.request_id, request.symbol, request.timeframe,
                json.dumps(request.indicators) if request.indicators else None,
                'SUCCESS' if success else 'FAILED',
                calculation_time, indicators_calculated, error,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Failed to store analysis request: {e}")
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status"""
        
        return {
            'service': self.service_name,
            'status': 'healthy' if not self.data_circuit_breaker.is_open else 'degraded',
            'data_circuit_breaker': self.data_circuit_breaker.get_status(),
            'message_queue_connected': self.connection is not None,
            'cache_enabled': True,
            'indicators_available': 20,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_service_metrics(self) -> Dict[str, Any]:
        """Get service performance metrics"""
        
        return {
            'service': self.service_name,
            'metrics': self.service_metrics,
            'timestamp': datetime.now().isoformat()
        }

async def main():
    """Main service entry point"""
    
    logger.info("Starting Technical Analysis Service")
    
    # Initialize service
    service = TechnicalAnalysisService()
    
    # Setup message queue
    await service.setup_message_queue()
    
    # Test service with sample request
    test_request = AnalysisRequest(
        request_id="test_ta_001",
        symbol="BTC-USD",
        timeframe="1d"
    )
    
    result = await service.calculate_technical_analysis(test_request)
    
    logger.info(f"Service test completed", extra={
        "success": result.success,
        "calculation_time": result.calculation_time,
        "indicators_calculated": result.indicators_calculated
    })
    
    # Get service health
    health = service.get_service_health()
    logger.info(f"Service health: {health['status']}")
    
    # Get service metrics
    metrics = service.get_service_metrics()
    logger.info(f"Service metrics: {metrics['metrics']['success_rate']:.1%} success rate")

if __name__ == "__main__":
    asyncio.run(main())
