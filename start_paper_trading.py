#!/usr/bin/env python3
"""
Noryon AI Trading System - Paper Trading Startup Script

This script provides a simple way to start paper trading with the Noryon AI system.
It handles configuration, validation, and startup of all necessary components.

Usage:
    python start_paper_trading.py --quick-start
    python start_paper_trading.py --balance 50000
    python start_paper_trading.py --symbols BTC/USDT,ETH/USDT,SPY
"""

import os
import sys
import json
import yaml
import argparse
import asyncio
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import Noryon components
from core.config.config_manager import ConfigManager
from core.registry.broker_registry import BrokerRegistry
from core.portfolio.universal_portfolio_manager import UniversalPortfolioManager
from core.llm.llm_abstraction_layer import LLMAbstractionLayer
from core.data.data_manager import DataManager
from main import TradingAISystem

console = Console()

class PaperTradingManager:
    """Manager for paper trading operations"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.project_root = Path(__file__).parent
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('paper_trading.log')
            ]
        )
        self.logger = logging.getLogger("paper_trading")
        
        # Initialize components
        self.trading_system = None
        self.is_running = False
    
    def create_paper_trading_config(self) -> Dict[str, Any]:
        """Create paper trading configuration"""
        config = {
            "system": {
                "name": "Noryon Paper Trading System",
                "version": "1.0.0",
                "environment": "paper_trading",
                "debug_mode": True,
                "log_level": "INFO"
            },
            "paper_trading": {
                "enabled": True,
                "initial_balance": self.config.get('initial_balance', 100000),
                "currency": "USD",
                "max_position_size": 0.1,  # 10% max per position
                "max_daily_trades": 20,
                "risk_per_trade": 0.02,    # 2% risk per trade
                "max_drawdown": 0.15,      # 15% max drawdown
                "stop_loss_pct": 0.05,     # 5% stop loss
                "take_profit_pct": 0.10,   # 10% take profit
                "commission_rate": 0.001,  # 0.1% commission
                "slippage_rate": 0.0005    # 0.05% slippage
            },
            "trading": {
                "symbols": self.config.get('symbols', [
                    "BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT",
                    "SPY", "QQQ", "AAPL", "MSFT", "GOOGL", "TSLA"
                ]),
                "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"],
                "strategies": {
                    "ai_momentum": {
                        "enabled": True,
                        "confidence_threshold": 0.7,
                        "position_size": 0.05,
                        "timeframe": "15m"
                    },
                    "ai_mean_reversion": {
                        "enabled": True,
                        "confidence_threshold": 0.8,
                        "position_size": 0.03,
                        "timeframe": "1h"
                    },
                    "ai_breakout": {
                        "enabled": True,
                        "confidence_threshold": 0.75,
                        "position_size": 0.04,
                        "timeframe": "4h"
                    }
                }
            },
            "risk_management": {
                "enabled": True,
                "max_portfolio_risk": 0.20,
                "max_correlation": 0.7,
                "var_limit": 0.05,
                "stress_test_enabled": True,
                "position_sizing": "kelly",
                "rebalance_frequency": "daily"
            },
            "ai_config": {
                "primary_llm": "openai",
                "fallback_llm": "deepseek",
                "decision_confidence_threshold": 0.6,
                "sentiment_analysis_enabled": True,
                "news_analysis_enabled": True,
                "technical_analysis_enabled": True,
                "fundamental_analysis_enabled": True
            },
            "monitoring": {
                "enabled": True,
                "dashboard_port": 8080,
                "metrics_interval": 30,
                "alert_thresholds": {
                    "daily_loss": 0.05,
                    "drawdown": 0.10,
                    "win_rate_below": 0.40
                },
                "notifications": {
                    "email_enabled": False,
                    "slack_enabled": False,
                    "console_enabled": True
                }
            },
            "data_sources": {
                "real_time_enabled": True,
                "historical_enabled": True,
                "news_enabled": True,
                "social_sentiment_enabled": False,
                "economic_calendar_enabled": True
            }
        }
        
        return config
    
    def save_config(self, config: Dict[str, Any]):
        """Save paper trading configuration"""
        config_path = self.project_root / "config" / "paper_trading.yaml"
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        self.logger.info(f"Paper trading configuration saved to {config_path}")
    
    def validate_environment(self) -> bool:
        """Validate environment for paper trading"""
        validation_results = []
        
        # Check .env file
        env_path = self.project_root / ".env"
        validation_results.append(("Environment file", env_path.exists()))
        
        # Check required directories
        required_dirs = ["logs", "data", "config", "models"]
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            validation_results.append((f"Directory {dir_name}", dir_path.exists()))
        
        # Check database connection
        try:
            from core.data.data_manager import DataManager
            data_manager = DataManager()
            db_connected = data_manager.test_connection()
            validation_results.append(("Database connection", db_connected))
        except Exception:
            validation_results.append(("Database connection", False))
        
        # Check LLM providers
        try:
            llm_layer = LLMAbstractionLayer()
            llm_available = len(llm_layer.available_providers) > 0
            validation_results.append(("LLM providers", llm_available))
        except Exception:
            validation_results.append(("LLM providers", False))
        
        # Display validation results
        table = Table(title="Environment Validation")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        
        all_valid = True
        for component, status in validation_results:
            status_text = "✅ PASS" if status else "❌ FAIL"
            table.add_row(component, status_text)
            if not status:
                all_valid = False
        
        console.print(table)
        
        if not all_valid:
            console.print("\n[red]Some components failed validation. Please run setup first:[/red]")
            console.print("python setup_complete_system.py --paper-trading-only")
        
        return all_valid
    
    async def start_paper_trading(self):
        """Start the paper trading system"""
        try:
            # Create and save configuration
            config = self.create_paper_trading_config()
            self.save_config(config)
            
            # Initialize trading system
            self.trading_system = TradingAISystem(
                config_path="config",
                environment="paper_trading"
            )
            
            # Start the system
            console.print("[green]Starting Noryon AI Paper Trading System...[/green]")
            
            await self.trading_system.start()
            self.is_running = True
            
            # Display startup information
            self.display_startup_info(config)
            
            # Keep running
            while self.is_running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            console.print("\n[yellow]Shutting down paper trading system...[/yellow]")
            await self.stop_paper_trading()
        except Exception as e:
            console.print(f"[red]Error starting paper trading: {e}[/red]")
            self.logger.error(f"Failed to start paper trading: {e}")
    
    async def stop_paper_trading(self):
        """Stop the paper trading system"""
        self.is_running = False
        if self.trading_system:
            await self.trading_system.stop()
        console.print("[green]Paper trading system stopped successfully[/green]")
    
    def display_startup_info(self, config: Dict[str, Any]):
        """Display startup information"""
        paper_config = config['paper_trading']
        trading_config = config['trading']
        
        startup_info = f"""
[bold green]🚀 Noryon AI Paper Trading System Started![/bold green]

[cyan]📊 Trading Configuration:[/cyan]
• Initial Balance: ${paper_config['initial_balance']:,}
• Max Position Size: {paper_config['max_position_size']*100:.1f}%
• Risk per Trade: {paper_config['risk_per_trade']*100:.1f}%
• Max Daily Trades: {paper_config['max_daily_trades']}
• Stop Loss: {paper_config['stop_loss_pct']*100:.1f}%
• Take Profit: {paper_config['take_profit_pct']*100:.1f}%

[cyan]📈 Trading Symbols:[/cyan]
{', '.join(trading_config['symbols'][:10])}{'...' if len(trading_config['symbols']) > 10 else ''}

[cyan]🤖 AI Strategies:[/cyan]
• AI Momentum (Confidence: {trading_config['strategies']['ai_momentum']['confidence_threshold']*100:.0f}%)
• AI Mean Reversion (Confidence: {trading_config['strategies']['ai_mean_reversion']['confidence_threshold']*100:.0f}%)
• AI Breakout (Confidence: {trading_config['strategies']['ai_breakout']['confidence_threshold']*100:.0f}%)

[cyan]📱 Monitoring:[/cyan]
• Dashboard: http://localhost:{config['monitoring']['dashboard_port']}
• Logs: paper_trading.log
• Metrics Update: Every {config['monitoring']['metrics_interval']}s

[yellow]💡 Commands:[/yellow]
• Ctrl+C: Stop trading
• View dashboard: Open http://localhost:8080
• Check logs: tail -f paper_trading.log
• View positions: python view_positions.py
"""
        
        console.print(Panel(startup_info, title="Paper Trading Active"))
    
    def display_live_stats(self):
        """Display live trading statistics"""
        # This would be implemented to show real-time stats
        # For now, just a placeholder
        pass

class QuickStartWizard:
    """Quick start wizard for paper trading"""
    
    def __init__(self):
        self.config = {}
    
    def run_wizard(self) -> Dict[str, Any]:
        """Run the quick start wizard"""
        console.print(Panel(
            "[bold blue]Noryon AI Paper Trading - Quick Start Wizard[/bold blue]\n\n"
            "This wizard will help you configure paper trading in just a few steps.",
            title="Welcome"
        ))
        
        # Get initial balance
        while True:
            try:
                balance = console.input("\n💰 Initial balance (default: $100,000): ") or "100000"
                self.config['initial_balance'] = float(balance)
                break
            except ValueError:
                console.print("[red]Please enter a valid number[/red]")
        
        # Get trading symbols
        console.print("\n📈 Select trading symbols:")
        console.print("1. Crypto only (BTC, ETH, ADA, SOL)")
        console.print("2. Stocks only (SPY, QQQ, AAPL, MSFT, GOOGL)")
        console.print("3. Mixed (Crypto + Stocks)")
        console.print("4. Custom")
        
        choice = console.input("\nChoice (1-4, default: 3): ") or "3"
        
        if choice == "1":
            self.config['symbols'] = ["BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT"]
        elif choice == "2":
            self.config['symbols'] = ["SPY", "QQQ", "AAPL", "MSFT", "GOOGL"]
        elif choice == "3":
            self.config['symbols'] = [
                "BTC/USDT", "ETH/USDT", "ADA/USDT", "SOL/USDT",
                "SPY", "QQQ", "AAPL", "MSFT", "GOOGL"
            ]
        else:
            symbols_input = console.input("Enter symbols (comma-separated): ")
            self.config['symbols'] = [s.strip() for s in symbols_input.split(',')]
        
        # Risk tolerance
        console.print("\n⚠️ Risk tolerance:")
        console.print("1. Conservative (1% risk per trade)")
        console.print("2. Moderate (2% risk per trade)")
        console.print("3. Aggressive (3% risk per trade)")
        
        risk_choice = console.input("\nChoice (1-3, default: 2): ") or "2"
        risk_levels = {"1": 0.01, "2": 0.02, "3": 0.03}
        self.config['risk_per_trade'] = risk_levels.get(risk_choice, 0.02)
        
        return self.config

def main():
    parser = argparse.ArgumentParser(description="Noryon AI Paper Trading Startup")
    parser.add_argument("--quick-start", action="store_true",
                       help="Run quick start wizard")
    parser.add_argument("--balance", type=float, default=100000,
                       help="Initial balance (default: 100000)")
    parser.add_argument("--symbols", type=str,
                       help="Trading symbols (comma-separated)")
    parser.add_argument("--risk-per-trade", type=float, default=0.02,
                       help="Risk per trade (default: 0.02)")
    parser.add_argument("--validate-only", action="store_true",
                       help="Only validate environment")
    parser.add_argument("--config-only", action="store_true",
                       help="Only create configuration")
    
    args = parser.parse_args()
    
    # Configuration
    config = {
        'initial_balance': args.balance,
        'risk_per_trade': args.risk_per_trade
    }
    
    if args.symbols:
        config['symbols'] = [s.strip() for s in args.symbols.split(',')]
    
    # Run quick start wizard if requested
    if args.quick_start:
        wizard = QuickStartWizard()
        config.update(wizard.run_wizard())
    
    # Initialize paper trading manager
    manager = PaperTradingManager(config)
    
    if args.validate_only:
        # Validate environment only
        manager.validate_environment()
        return
    
    if args.config_only:
        # Create configuration only
        paper_config = manager.create_paper_trading_config()
        manager.save_config(paper_config)
        console.print("[green]Paper trading configuration created successfully![/green]")
        return
    
    # Validate environment first
    if not manager.validate_environment():
        console.print("[red]Environment validation failed. Please run setup first.[/red]")
        return
    
    # Start paper trading
    try:
        asyncio.run(manager.start_paper_trading())
    except KeyboardInterrupt:
        console.print("\n[yellow]Paper trading stopped by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

if __name__ == "__main__":
    main()