#!/usr/bin/env python3
"""
Noryon Advanced Backtesting Framework
Comprehensive backtesting system with multi-strategy support

This module provides:
- Historical data processing and validation
- Multi-strategy backtesting with ensemble support
- Advanced performance metrics and risk analysis
- Walk-forward analysis and out-of-sample testing
- Monte Carlo simulation for robustness testing
- Integration with continuous learning pipeline
- Detailed reporting and visualization
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Callable, Union
import yaml
import pickle
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# Import system components
try:
    from continuous_learning_pipeline import MarketData, ModelPerformance, ContinuousLearningPipeline
    from ensemble_voting_system import EnsembleVotingSystem
    from train_all_models import TrainingOrchestrator
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"System components not available: {e}")
    COMPONENTS_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Trade:
    """Individual trade record"""
    timestamp: datetime
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    quantity: float
    price: float
    model_name: str
    confidence: float
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None
    profit_loss: Optional[float] = None
    duration: Optional[timedelta] = None
    fees: float = 0.0
    slippage: float = 0.0

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    start_date: datetime
    end_date: datetime
    initial_capital: float = 100000.0
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    max_position_size: float = 0.1  # 10% of portfolio
    risk_free_rate: float = 0.02    # 2% annual
    benchmark_symbol: str = "SPY"
    rebalance_frequency: str = "daily"  # daily, weekly, monthly
    walk_forward_window: int = 252  # Trading days
    out_of_sample_ratio: float = 0.2  # 20% for out-of-sample

@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_drawdown: float
    max_drawdown_duration: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_trade_duration: float
    beta: float
    alpha: float
    information_ratio: float
    var_95: float  # Value at Risk 95%
    cvar_95: float  # Conditional Value at Risk 95%
    tail_ratio: float
    skewness: float
    kurtosis: float

class HistoricalDataManager:
    """Manage historical market data"""
    
    def __init__(self, data_path: str = "data/historical"):
        self.data_path = Path(data_path)
        self.data_cache = {}
        self.data_path.mkdir(parents=True, exist_ok=True)
    
    def load_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Load historical data for a symbol"""
        cache_key = f"{symbol}_{start_date.date()}_{end_date.date()}"
        
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        # Try to load from file
        file_path = self.data_path / f"{symbol}.csv"
        
        if file_path.exists():
            df = pd.read_csv(file_path, parse_dates=['timestamp'])
            df = df[(df['timestamp'] >= start_date) & (df['timestamp'] <= end_date)]
        else:
            # Generate synthetic data if file doesn't exist
            logger.warning(f"Historical data file not found for {symbol}, generating synthetic data")
            df = self._generate_synthetic_data(symbol, start_date, end_date)
        
        self.data_cache[cache_key] = df
        return df
    
    def _generate_synthetic_data(self, symbol: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Generate synthetic market data for testing"""
        dates = pd.date_range(start_date, end_date, freq='1H')
        n_points = len(dates)
        
        # Generate realistic price series using geometric Brownian motion
        initial_price = 100.0
        mu = 0.0001  # Drift
        sigma = 0.02  # Volatility
        
        dt = 1/24  # Hourly data
        price_changes = np.random.normal(mu * dt, sigma * np.sqrt(dt), n_points)
        prices = initial_price * np.exp(np.cumsum(price_changes))
        
        # Generate volume with correlation to price changes
        base_volume = 10000
        volume_changes = np.abs(price_changes) * 50000 + np.random.normal(0, 5000, n_points)
        volumes = np.maximum(base_volume + np.cumsum(volume_changes * 0.1), 1000)
        
        # Calculate technical indicators
        df = pd.DataFrame({
            'timestamp': dates,
            'symbol': symbol,
            'open': prices,
            'high': prices * (1 + np.abs(np.random.normal(0, 0.01, n_points))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.01, n_points))),
            'close': prices,
            'volume': volumes
        })
        
        # Add technical indicators
        df['returns'] = df['close'].pct_change()
        df['volatility'] = df['returns'].rolling(24).std() * np.sqrt(24)  # 24-hour volatility
        df['rsi'] = self._calculate_rsi(df['close'])
        df['macd'], df['macd_signal'] = self._calculate_macd(df['close'])
        df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['close'])
        df['sentiment_score'] = np.random.uniform(-1, 1, n_points)
        df['news_impact'] = np.random.normal(0, 0.1, n_points)
        
        return df.fillna(0)
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[pd.Series, pd.Series]:
        """Calculate MACD indicator"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, num_std: float = 2) -> Tuple[pd.Series, pd.Series]:
        """Calculate Bollinger Bands"""
        rolling_mean = prices.rolling(window=window).mean()
        rolling_std = prices.rolling(window=window).std()
        upper_band = rolling_mean + (rolling_std * num_std)
        lower_band = rolling_mean - (rolling_std * num_std)
        return upper_band, lower_band

class TradingStrategy:
    """Base class for trading strategies"""
    
    def __init__(self, name: str, model_name: str = None):
        self.name = name
        self.model_name = model_name or name
        self.position = 0.0
        self.cash = 0.0
        self.portfolio_value = 0.0
    
    def generate_signal(self, data: pd.DataFrame, current_idx: int) -> Dict[str, Any]:
        """Generate trading signal based on current market data"""
        raise NotImplementedError("Subclasses must implement generate_signal")
    
    def update_position(self, trade: Trade):
        """Update strategy position after trade execution"""
        if trade.action == 'buy':
            self.position += trade.quantity
            self.cash -= trade.quantity * trade.price
        elif trade.action == 'sell':
            self.position -= trade.quantity
            self.cash += trade.quantity * trade.price

class SimpleMovingAverageStrategy(TradingStrategy):
    """Simple moving average crossover strategy"""
    
    def __init__(self, fast_window: int = 10, slow_window: int = 30):
        super().__init__("SMA_Strategy")
        self.fast_window = fast_window
        self.slow_window = slow_window
    
    def generate_signal(self, data: pd.DataFrame, current_idx: int) -> Dict[str, Any]:
        if current_idx < self.slow_window:
            return {'action': 'hold', 'confidence': 0.0, 'quantity': 0.0}
        
        # Calculate moving averages
        fast_ma = data['close'].iloc[current_idx-self.fast_window:current_idx].mean()
        slow_ma = data['close'].iloc[current_idx-self.slow_window:current_idx].mean()
        
        # Generate signal
        if fast_ma > slow_ma:
            return {'action': 'buy', 'confidence': 0.7, 'quantity': 0.1}
        elif fast_ma < slow_ma:
            return {'action': 'sell', 'confidence': 0.7, 'quantity': 0.1}
        else:
            return {'action': 'hold', 'confidence': 0.5, 'quantity': 0.0}

class RSIStrategy(TradingStrategy):
    """RSI-based mean reversion strategy"""
    
    def __init__(self, oversold_threshold: float = 30, overbought_threshold: float = 70):
        super().__init__("RSI_Strategy")
        self.oversold_threshold = oversold_threshold
        self.overbought_threshold = overbought_threshold
    
    def generate_signal(self, data: pd.DataFrame, current_idx: int) -> Dict[str, Any]:
        if current_idx < 14:  # Need at least 14 periods for RSI
            return {'action': 'hold', 'confidence': 0.0, 'quantity': 0.0}
        
        current_rsi = data['rsi'].iloc[current_idx]
        
        if current_rsi < self.oversold_threshold:
            return {'action': 'buy', 'confidence': 0.8, 'quantity': 0.05}
        elif current_rsi > self.overbought_threshold:
            return {'action': 'sell', 'confidence': 0.8, 'quantity': 0.05}
        else:
            return {'action': 'hold', 'confidence': 0.3, 'quantity': 0.0}

class BacktestEngine:
    """Main backtesting engine"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.data_manager = HistoricalDataManager()
        self.strategies = []
        self.trades = []
        self.portfolio_history = []
        self.benchmark_data = None
        
    def add_strategy(self, strategy: TradingStrategy):
        """Add a trading strategy to the backtest"""
        self.strategies.append(strategy)
        logger.info(f"Added strategy: {strategy.name}")
    
    def run_backtest(self, symbols: List[str]) -> Dict[str, Any]:
        """Run the complete backtesting process"""
        logger.info(f"Starting backtest from {self.config.start_date} to {self.config.end_date}")
        
        results = {}
        
        for symbol in symbols:
            logger.info(f"Backtesting symbol: {symbol}")
            
            # Load historical data
            data = self.data_manager.load_data(symbol, self.config.start_date, self.config.end_date)
            
            if data.empty:
                logger.warning(f"No data available for {symbol}")
                continue
            
            # Run backtest for each strategy
            for strategy in self.strategies:
                strategy_results = self._run_strategy_backtest(strategy, data, symbol)
                results[f"{symbol}_{strategy.name}"] = strategy_results
        
        # Load benchmark data
        self._load_benchmark_data()
        
        return results
    
    def _run_strategy_backtest(self, strategy: TradingStrategy, data: pd.DataFrame, symbol: str) -> Dict[str, Any]:
        """Run backtest for a single strategy"""
        logger.info(f"Running backtest for {strategy.name} on {symbol}")
        
        # Initialize portfolio
        cash = self.config.initial_capital
        position = 0.0
        portfolio_values = []
        strategy_trades = []
        
        # Iterate through historical data
        for idx in range(len(data)):
            current_data = data.iloc[:idx+1]
            current_price = data.iloc[idx]['close']
            current_time = data.iloc[idx]['timestamp']
            
            # Generate trading signal
            signal = strategy.generate_signal(current_data, idx)
            
            # Execute trade if signal is generated
            if signal['action'] != 'hold' and signal['quantity'] > 0:
                trade = self._execute_trade(
                    signal, current_price, current_time, symbol, strategy.name, cash, position
                )
                
                if trade:
                    strategy_trades.append(trade)
                    
                    # Update portfolio
                    if trade.action == 'buy':
                        position += trade.quantity
                        cash -= trade.quantity * trade.price * (1 + self.config.commission_rate)
                    elif trade.action == 'sell':
                        position -= trade.quantity
                        cash += trade.quantity * trade.price * (1 - self.config.commission_rate)
            
            # Calculate portfolio value
            portfolio_value = cash + position * current_price
            portfolio_values.append({
                'timestamp': current_time,
                'portfolio_value': portfolio_value,
                'cash': cash,
                'position': position,
                'price': current_price
            })
        
        # Calculate performance metrics
        performance = self._calculate_performance_metrics(portfolio_values, strategy_trades)
        
        return {
            'strategy_name': strategy.name,
            'symbol': symbol,
            'trades': strategy_trades,
            'portfolio_history': portfolio_values,
            'performance': performance
        }
    
    def _execute_trade(self, signal: Dict[str, Any], price: float, timestamp: datetime, 
                      symbol: str, strategy_name: str, cash: float, position: float) -> Optional[Trade]:
        """Execute a trade based on the signal"""
        action = signal['action']
        quantity = signal['quantity'] * self.config.initial_capital / price  # Convert percentage to shares
        
        # Apply position size limits
        max_quantity = self.config.max_position_size * self.config.initial_capital / price
        quantity = min(quantity, max_quantity)
        
        # Check if we have enough cash/position
        if action == 'buy' and cash < quantity * price * (1 + self.config.commission_rate):
            return None
        elif action == 'sell' and position < quantity:
            quantity = position  # Sell all available position
        
        if quantity <= 0:
            return None
        
        # Apply slippage
        execution_price = price * (1 + self.config.slippage_rate if action == 'buy' else 1 - self.config.slippage_rate)
        
        trade = Trade(
            timestamp=timestamp,
            symbol=symbol,
            action=action,
            quantity=quantity,
            price=execution_price,
            model_name=strategy_name,
            confidence=signal['confidence'],
            fees=quantity * execution_price * self.config.commission_rate,
            slippage=abs(execution_price - price) * quantity
        )
        
        return trade
    
    def _calculate_performance_metrics(self, portfolio_history: List[Dict], trades: List[Trade]) -> PerformanceMetrics:
        """Calculate comprehensive performance metrics"""
        if not portfolio_history or len(portfolio_history) < 2:
            return self._empty_performance_metrics()
        
        # Convert to DataFrame for easier calculation
        df = pd.DataFrame(portfolio_history)
        df['returns'] = df['portfolio_value'].pct_change().fillna(0)
        
        # Basic metrics
        total_return = (df['portfolio_value'].iloc[-1] / df['portfolio_value'].iloc[0]) - 1
        
        # Annualized return
        days = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).days
        annualized_return = (1 + total_return) ** (365.25 / max(days, 1)) - 1
        
        # Volatility
        volatility = df['returns'].std() * np.sqrt(252)  # Annualized
        
        # Sharpe ratio
        excess_returns = df['returns'].mean() * 252 - self.config.risk_free_rate
        sharpe_ratio = excess_returns / volatility if volatility > 0 else 0
        
        # Sortino ratio
        downside_returns = df['returns'][df['returns'] < 0]
        downside_volatility = downside_returns.std() * np.sqrt(252)
        sortino_ratio = excess_returns / downside_volatility if downside_volatility > 0 else 0
        
        # Maximum drawdown
        cumulative_returns = (1 + df['returns']).cumprod()
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        max_drawdown = drawdown.min()
        
        # Drawdown duration
        drawdown_periods = (drawdown < 0).astype(int)
        max_drawdown_duration = drawdown_periods.groupby((drawdown_periods != drawdown_periods.shift()).cumsum()).sum().max()
        
        # Trade statistics
        if trades:
            profits = [t.profit_loss for t in trades if t.profit_loss is not None]
            winning_trades = len([p for p in profits if p > 0])
            losing_trades = len([p for p in profits if p < 0])
            total_trades = len(profits)
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            avg_win = np.mean([p for p in profits if p > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([p for p in profits if p < 0]) if losing_trades > 0 else 0
            profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0
            
            # Average trade duration
            durations = [t.duration.total_seconds() / 3600 for t in trades if t.duration is not None]
            avg_trade_duration = np.mean(durations) if durations else 0
        else:
            winning_trades = losing_trades = total_trades = 0
            win_rate = avg_win = avg_loss = profit_factor = avg_trade_duration = 0
        
        # Risk metrics
        var_95 = np.percentile(df['returns'], 5) if len(df['returns']) > 0 else 0
        cvar_95 = df['returns'][df['returns'] <= var_95].mean() if len(df['returns']) > 0 else 0
        
        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # Higher moments
        skewness = df['returns'].skew() if len(df['returns']) > 2 else 0
        kurtosis = df['returns'].kurtosis() if len(df['returns']) > 3 else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_drawdown_duration or 0,
            win_rate=win_rate,
            profit_factor=profit_factor,
            avg_win=avg_win,
            avg_loss=avg_loss,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_trade_duration=avg_trade_duration,
            beta=0.0,  # Would need benchmark for calculation
            alpha=0.0,  # Would need benchmark for calculation
            information_ratio=0.0,  # Would need benchmark for calculation
            var_95=var_95,
            cvar_95=cvar_95,
            tail_ratio=abs(var_95 / cvar_95) if cvar_95 != 0 else 0,
            skewness=skewness,
            kurtosis=kurtosis
        )
    
    def _empty_performance_metrics(self) -> PerformanceMetrics:
        """Return empty performance metrics"""
        return PerformanceMetrics(
            total_return=0.0, annualized_return=0.0, volatility=0.0, sharpe_ratio=0.0,
            sortino_ratio=0.0, calmar_ratio=0.0, max_drawdown=0.0, max_drawdown_duration=0,
            win_rate=0.0, profit_factor=0.0, avg_win=0.0, avg_loss=0.0, total_trades=0,
            winning_trades=0, losing_trades=0, avg_trade_duration=0.0, beta=0.0, alpha=0.0,
            information_ratio=0.0, var_95=0.0, cvar_95=0.0, tail_ratio=0.0, skewness=0.0, kurtosis=0.0
        )
    
    def _load_benchmark_data(self):
        """Load benchmark data for comparison"""
        try:
            self.benchmark_data = self.data_manager.load_data(
                self.config.benchmark_symbol,
                self.config.start_date,
                self.config.end_date
            )
        except Exception as e:
            logger.warning(f"Could not load benchmark data: {e}")
    
    def generate_report(self, results: Dict[str, Any], output_path: str = "reports/backtest_report.json"):
        """Generate comprehensive backtest report"""
        logger.info("Generating backtest report")
        
        # Create output directory
        Path(output_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Prepare report data
        report = {
            'backtest_config': {
                'start_date': self.config.start_date.isoformat(),
                'end_date': self.config.end_date.isoformat(),
                'initial_capital': self.config.initial_capital,
                'commission_rate': self.config.commission_rate,
                'slippage_rate': self.config.slippage_rate
            },
            'strategies': {},
            'summary': {},
            'generated_at': datetime.now().isoformat()
        }
        
        # Process results for each strategy
        for strategy_key, strategy_result in results.items():
            performance = strategy_result['performance']
            
            report['strategies'][strategy_key] = {
                'strategy_name': strategy_result['strategy_name'],
                'symbol': strategy_result['symbol'],
                'performance_metrics': {
                    'total_return': performance.total_return,
                    'annualized_return': performance.annualized_return,
                    'volatility': performance.volatility,
                    'sharpe_ratio': performance.sharpe_ratio,
                    'sortino_ratio': performance.sortino_ratio,
                    'max_drawdown': performance.max_drawdown,
                    'win_rate': performance.win_rate,
                    'total_trades': performance.total_trades,
                    'profit_factor': performance.profit_factor
                },
                'trade_count': len(strategy_result['trades']),
                'final_portfolio_value': strategy_result['portfolio_history'][-1]['portfolio_value'] if strategy_result['portfolio_history'] else self.config.initial_capital
            }
        
        # Generate summary statistics
        if results:
            performances = [r['performance'] for r in results.values()]
            report['summary'] = {
                'total_strategies': len(results),
                'avg_return': np.mean([p.total_return for p in performances]),
                'avg_sharpe_ratio': np.mean([p.sharpe_ratio for p in performances]),
                'best_strategy': max(results.keys(), key=lambda k: results[k]['performance'].sharpe_ratio),
                'worst_strategy': min(results.keys(), key=lambda k: results[k]['performance'].sharpe_ratio)
            }
        
        # Save report
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Backtest report saved to {output_path}")
        return report

class WalkForwardAnalysis:
    """Walk-forward analysis for strategy validation"""
    
    def __init__(self, backtest_engine: BacktestEngine, window_size: int = 252, step_size: int = 63):
        self.backtest_engine = backtest_engine
        self.window_size = window_size  # Training window in days
        self.step_size = step_size      # Step size in days
    
    def run_analysis(self, symbols: List[str], strategies: List[TradingStrategy]) -> Dict[str, Any]:
        """Run walk-forward analysis"""
        logger.info("Starting walk-forward analysis")
        
        results = defaultdict(list)
        
        # Calculate date ranges
        start_date = self.backtest_engine.config.start_date
        end_date = self.backtest_engine.config.end_date
        
        current_date = start_date
        while current_date + timedelta(days=self.window_size) < end_date:
            train_start = current_date
            train_end = current_date + timedelta(days=self.window_size)
            test_start = train_end
            test_end = min(train_end + timedelta(days=self.step_size), end_date)
            
            logger.info(f"Walk-forward period: {train_start.date()} to {test_end.date()}")
            
            # Run backtest for this period
            period_config = BacktestConfig(
                start_date=test_start,
                end_date=test_end,
                initial_capital=self.backtest_engine.config.initial_capital,
                commission_rate=self.backtest_engine.config.commission_rate,
                slippage_rate=self.backtest_engine.config.slippage_rate
            )
            
            period_engine = BacktestEngine(period_config)
            for strategy in strategies:
                period_engine.add_strategy(strategy)
            
            period_results = period_engine.run_backtest(symbols)
            
            # Store results
            for strategy_key, strategy_result in period_results.items():
                results[strategy_key].append({
                    'period_start': test_start,
                    'period_end': test_end,
                    'performance': strategy_result['performance']
                })
            
            current_date += timedelta(days=self.step_size)
        
        return dict(results)

# Example usage and testing
async def run_example_backtest():
    """Run an example backtest"""
    logger.info("Running example backtest")
    
    # Create backtest configuration
    config = BacktestConfig(
        start_date=datetime(2023, 1, 1),
        end_date=datetime(2023, 12, 31),
        initial_capital=100000.0,
        commission_rate=0.001,
        slippage_rate=0.0005
    )
    
    # Create backtest engine
    engine = BacktestEngine(config)
    
    # Add strategies
    engine.add_strategy(SimpleMovingAverageStrategy(fast_window=10, slow_window=30))
    engine.add_strategy(RSIStrategy(oversold_threshold=30, overbought_threshold=70))
    
    # Run backtest
    symbols = ['BTC/USD', 'ETH/USD']
    results = engine.run_backtest(symbols)
    
    # Generate report
    report = engine.generate_report(results, "reports/example_backtest_report.json")
    
    # Print summary
    print("\n=== Backtest Results Summary ===")
    for strategy_key, strategy_data in report['strategies'].items():
        print(f"\n{strategy_key}:")
        metrics = strategy_data['performance_metrics']
        print(f"  Total Return: {metrics['total_return']:.2%}")
        print(f"  Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"  Win Rate: {metrics['win_rate']:.2%}")
        print(f"  Total Trades: {metrics['total_trades']}")
    
    return results

if __name__ == "__main__":
    asyncio.run(run_example_backtest())