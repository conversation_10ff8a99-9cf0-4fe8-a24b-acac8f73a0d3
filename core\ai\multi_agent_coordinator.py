#!/usr/bin/env python3
"""
Noryon Multi-Agent Coordination System - Phase 2 Implementation
Advanced Multi-Agent Coordination for Self-Evolving Trading System

This module implements:
- Multi-agent coordination protocols
- Agent communication and knowledge sharing
- Hierarchical agent organization
- Consensus mechanisms
- Performance monitoring and adaptation
- Resource allocation and load balancing
- Emergent behavior management
"""

import asyncio
import json
import logging
import numpy as np
import pandas as pd
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Callable, Set
import uuid
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import weakref

# Import our custom modules
try:
    from .genetic_algorithm_optimizer import GeneticAlgorithmOptimizer, Individual, GeneticConfig
    from .reinforcement_learning_agent import DQNAgent, PPOAgent, AgentConfig, TrainingMetrics
    from .strategy_evolution_engine import StrategyEvolutionEngine, TradingStrategy
except ImportError:
    # Fallback for standalone execution
    import sys
    sys.path.append('.')
    from genetic_algorithm_optimizer import GeneticAlgorithmOptimizer, Individual, GeneticConfig
    from reinforcement_learning_agent import DQNAgent, PPOAgent, AgentConfig, TrainingMetrics
    from strategy_evolution_engine import StrategyEvolutionEngine, TradingStrategy

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentType(Enum):
    """Types of agents in the system"""
    GENETIC_OPTIMIZER = "genetic_optimizer"
    RL_TRADER = "rl_trader"
    ENSEMBLE_VOTER = "ensemble_voter"
    STRATEGY_EVOLVER = "strategy_evolver"
    MARKET_ANALYZER = "market_analyzer"
    RISK_MANAGER = "risk_manager"
    PORTFOLIO_MANAGER = "portfolio_manager"
    COORDINATOR = "coordinator"

class AgentRole(Enum):
    """Roles agents can play in coordination"""
    LEADER = "leader"
    FOLLOWER = "follower"
    SPECIALIST = "specialist"
    GENERALIST = "generalist"
    MEDIATOR = "mediator"
    OBSERVER = "observer"

class MessageType(Enum):
    """Types of messages agents can exchange"""
    STRATEGY_UPDATE = "strategy_update"
    PERFORMANCE_REPORT = "performance_report"
    MARKET_SIGNAL = "market_signal"
    COORDINATION_REQUEST = "coordination_request"
    RESOURCE_REQUEST = "resource_request"
    KNOWLEDGE_SHARE = "knowledge_share"
    CONSENSUS_VOTE = "consensus_vote"
    EMERGENCY_ALERT = "emergency_alert"
    HEARTBEAT = "heartbeat"

class CoordinationProtocol(Enum):
    """Coordination protocols"""
    HIERARCHICAL = "hierarchical"
    DEMOCRATIC = "democratic"
    MARKET_BASED = "market_based"
    SWARM = "swarm"
    HYBRID = "hybrid"

@dataclass
class AgentMessage:
    """Message structure for agent communication"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str = ""
    receiver_id: str = ""  # Empty for broadcast
    message_type: MessageType = MessageType.HEARTBEAT
    content: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    priority: int = 1  # 1=low, 5=high
    requires_response: bool = False
    response_timeout: float = 30.0
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentCapability:
    """Agent capability description"""
    name: str
    description: str
    input_types: List[str]
    output_types: List[str]
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    resource_requirements: Dict[str, float] = field(default_factory=dict)
    specialization_score: float = 0.0

@dataclass
class CoordinationMetrics:
    """Metrics for coordination performance"""
    timestamp: datetime = field(default_factory=datetime.now)
    active_agents: int = 0
    message_throughput: float = 0.0
    consensus_time: float = 0.0
    coordination_efficiency: float = 0.0
    resource_utilization: Dict[str, float] = field(default_factory=dict)
    emergent_behaviors: List[str] = field(default_factory=list)
    system_coherence: float = 0.0

class BaseAgent(ABC):
    """Base class for all agents in the system"""
    
    def __init__(self, agent_id: str, agent_type: AgentType, 
                 capabilities: List[AgentCapability]):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.capabilities = capabilities
        self.role = AgentRole.FOLLOWER
        
        # Communication
        self.message_queue = asyncio.Queue()
        self.coordinator: Optional['MultiAgentCoordinator'] = None
        
        # State
        self.is_active = False
        self.last_heartbeat = datetime.now()
        self.performance_metrics = {}
        self.resource_usage = {}
        
        # Knowledge and learning
        self.knowledge_base = {}
        self.learning_rate = 0.01
        self.adaptation_threshold = 0.1
        
        logger.info(f"Agent {agent_id} ({agent_type.value}) initialized")
    
    @abstractmethod
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process incoming message"""
        pass
    
    @abstractmethod
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute assigned task"""
        pass
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        pass
    
    async def send_message(self, message: AgentMessage):
        """Send message through coordinator"""
        if self.coordinator:
            await self.coordinator.route_message(message)
    
    async def broadcast_message(self, message_type: MessageType, content: Dict[str, Any]):
        """Broadcast message to all agents"""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id="",  # Broadcast
            message_type=message_type,
            content=content
        )
        await self.send_message(message)
    
    def update_performance_metrics(self, metrics: Dict[str, float]):
        """Update agent performance metrics"""
        self.performance_metrics.update(metrics)
        self.last_heartbeat = datetime.now()
    
    def share_knowledge(self, knowledge: Dict[str, Any]):
        """Share knowledge with other agents"""
        self.knowledge_base.update(knowledge)
        
        # Broadcast knowledge
        asyncio.create_task(self.broadcast_message(
            MessageType.KNOWLEDGE_SHARE,
            {'knowledge': knowledge, 'source': self.agent_id}
        ))
    
    def adapt_behavior(self, feedback: Dict[str, Any]):
        """Adapt behavior based on feedback"""
        # Simple adaptation mechanism
        for metric, value in feedback.items():
            if metric in self.performance_metrics:
                current = self.performance_metrics[metric]
                if abs(value - current) > self.adaptation_threshold:
                    # Adjust learning rate or other parameters
                    self.learning_rate *= 1.1 if value > current else 0.9
                    self.learning_rate = np.clip(self.learning_rate, 0.001, 0.1)

class GeneticOptimizerAgent(BaseAgent):
    """Agent wrapper for genetic algorithm optimizer"""
    
    def __init__(self, agent_id: str, config: GeneticConfig, evaluator):
        capabilities = [
            AgentCapability(
                name="strategy_optimization",
                description="Optimize trading strategies using genetic algorithms",
                input_types=["market_data", "strategy_parameters"],
                output_types=["optimized_strategy", "fitness_metrics"]
            )
        ]
        
        super().__init__(agent_id, AgentType.GENETIC_OPTIMIZER, capabilities)
        
        self.optimizer = GeneticAlgorithmOptimizer(config, evaluator)
        self.current_evolution = None
        self.best_strategies = deque(maxlen=10)
        
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process incoming message"""
        if message.message_type == MessageType.STRATEGY_UPDATE:
            # Incorporate new strategy into population
            strategy_data = message.content.get('strategy')
            if strategy_data:
                await self._incorporate_strategy(strategy_data)
        
        elif message.message_type == MessageType.MARKET_SIGNAL:
            # Trigger evolution with new market data
            market_data = message.content.get('market_data')
            if market_data:
                asyncio.create_task(self._evolve_strategies(market_data))
        
        elif message.message_type == MessageType.PERFORMANCE_REPORT:
            # Update fitness evaluation based on real performance
            performance = message.content.get('performance')
            if performance:
                self._update_fitness_evaluation(performance)
        
        return None
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute genetic optimization task"""
        task_type = task.get('type')
        
        if task_type == 'evolve_strategies':
            market_data = task.get('market_data', {})
            genome_length = task.get('genome_length', 10)
            
            best_individual = await self.optimizer.evolve(market_data, genome_length)
            
            return {
                'best_strategy': {
                    'genome': best_individual.genome.tolist(),
                    'fitness': best_individual.fitness,
                    'objectives': best_individual.objectives
                },
                'evolution_summary': self.optimizer.get_evolution_summary()
            }
        
        elif task_type == 'get_best_strategies':
            return {
                'strategies': [strategy for strategy in self.best_strategies]
            }
        
        return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type.value,
            'is_active': self.is_active,
            'current_generation': self.optimizer.generation,
            'population_size': len(self.optimizer.population),
            'best_fitness': self.optimizer.best_individual.fitness if self.optimizer.best_individual else {},
            'performance_metrics': self.performance_metrics,
            'last_heartbeat': self.last_heartbeat.isoformat()
        }
    
    async def _incorporate_strategy(self, strategy_data: Dict[str, Any]):
        """Incorporate external strategy into population"""
        try:
            genome = np.array(strategy_data.get('genome', []))
            if len(genome) > 0:
                individual = Individual(genome=genome)
                
                # Add to population (replace worst if full)
                if len(self.optimizer.population) >= self.optimizer.config.population_size:
                    # Replace worst individual
                    worst_idx = min(range(len(self.optimizer.population)),
                                   key=lambda i: self.optimizer.population[i].fitness.get('composite', 0))
                    self.optimizer.population[worst_idx] = individual
                else:
                    self.optimizer.population.append(individual)
                
                logger.info(f"Incorporated external strategy into population")
        except Exception as e:
            logger.error(f"Error incorporating strategy: {e}")
    
    async def _evolve_strategies(self, market_data: Dict[str, Any]):
        """Evolve strategies with new market data"""
        try:
            self.current_evolution = asyncio.create_task(
                self.optimizer.evolve(market_data)
            )
            
            best_individual = await self.current_evolution
            
            # Store best strategy
            strategy = {
                'genome': best_individual.genome.tolist(),
                'fitness': best_individual.fitness,
                'objectives': best_individual.objectives,
                'timestamp': datetime.now().isoformat()
            }
            self.best_strategies.append(strategy)
            
            # Share best strategy
            await self.broadcast_message(
                MessageType.STRATEGY_UPDATE,
                {'strategy': strategy, 'source': 'genetic_optimizer'}
            )
            
        except Exception as e:
            logger.error(f"Error in strategy evolution: {e}")
    
    def _update_fitness_evaluation(self, performance: Dict[str, Any]):
        """Update fitness evaluation based on real performance"""
        # This could be used to adjust the fitness function
        # based on real-world performance feedback
        pass

class RLTraderAgent(BaseAgent):
    """Agent wrapper for reinforcement learning trader"""
    
    def __init__(self, agent_id: str, config: AgentConfig, algorithm: str = "DQN"):
        capabilities = [
            AgentCapability(
                name="adaptive_trading",
                description="Adaptive trading using reinforcement learning",
                input_types=["market_state", "portfolio_state"],
                output_types=["trading_action", "confidence_score"]
            )
        ]
        
        super().__init__(agent_id, AgentType.RL_TRADER, capabilities)
        
        self.config = config
        self.algorithm = algorithm
        
        # Initialize RL agent
        if algorithm == "DQN":
            self.rl_agent = DQNAgent(config)
        elif algorithm == "PPO":
            self.rl_agent = PPOAgent(config)
        else:
            raise ValueError(f"Unknown algorithm: {algorithm}")
        
        self.training_history = deque(maxlen=1000)
        self.recent_actions = deque(maxlen=100)
        
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process incoming message"""
        if message.message_type == MessageType.MARKET_SIGNAL:
            # Process market signal and potentially take action
            market_state = message.content.get('market_state')
            if market_state:
                action = await self._process_market_state(market_state)
                
                # Share action with other agents
                await self.broadcast_message(
                    MessageType.STRATEGY_UPDATE,
                    {'action': action, 'source': self.agent_id}
                )
        
        elif message.message_type == MessageType.STRATEGY_UPDATE:
            # Learn from other agents' strategies
            strategy = message.content.get('strategy')
            if strategy:
                await self._learn_from_strategy(strategy)
        
        elif message.message_type == MessageType.PERFORMANCE_REPORT:
            # Update learning based on performance feedback
            performance = message.content.get('performance')
            if performance:
                self._update_learning(performance)
        
        return None
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute RL trading task"""
        task_type = task.get('type')
        
        if task_type == 'trade':
            market_state = task.get('market_state')
            portfolio_state = task.get('portfolio_state')
            
            if market_state and portfolio_state:
                action = await self.rl_agent.act(market_state, portfolio_state)
                
                self.recent_actions.append({
                    'action': action,
                    'market_state': market_state,
                    'timestamp': datetime.now().isoformat()
                })
                
                return {'action': action}
        
        elif task_type == 'train':
            training_data = task.get('training_data')
            if training_data:
                metrics = await self.rl_agent.train(training_data)
                self.training_history.append(metrics)
                return {'training_metrics': metrics}
        
        return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get current agent status"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type.value,
            'algorithm': self.algorithm,
            'is_active': self.is_active,
            'training_episodes': len(self.training_history),
            'recent_actions_count': len(self.recent_actions),
            'performance_metrics': self.performance_metrics,
            'last_heartbeat': self.last_heartbeat.isoformat()
        }
    
    async def _process_market_state(self, market_state: Dict[str, Any]) -> Dict[str, Any]:
        """Process market state and determine action"""
        try:
            # Convert market state to format expected by RL agent
            state_vector = self._convert_market_state(market_state)
            
            # Get action from RL agent
            action = await self.rl_agent.act(state_vector)
            
            return {
                'action_type': action.get('type', 'hold'),
                'confidence': action.get('confidence', 0.5),
                'parameters': action.get('parameters', {})
            }
        except Exception as e:
            logger.error(f"Error processing market state: {e}")
            return {'action_type': 'hold', 'confidence': 0.0}
    
    def _convert_market_state(self, market_state: Dict[str, Any]) -> np.ndarray:
        """Convert market state to vector format"""
        # Simple conversion - in practice, this would be more sophisticated
        features = [
            market_state.get('price', 0),
            market_state.get('volume', 0),
            market_state.get('volatility', 0),
            market_state.get('trend', 0),
            market_state.get('momentum', 0)
        ]
        return np.array(features, dtype=np.float32)
    
    async def _learn_from_strategy(self, strategy: Dict[str, Any]):
        """Learn from other agents' strategies"""
        # This could involve updating the RL agent's policy
        # based on successful strategies from other agents
        pass
    
    def _update_learning(self, performance: Dict[str, Any]):
        """Update learning based on performance feedback"""
        # Update RL agent based on performance metrics
        reward = performance.get('reward', 0)
        if hasattr(self.rl_agent, 'update_from_feedback'):
            self.rl_agent.update_from_feedback(reward)

class CoordinatorAgent(BaseAgent):
    """Central coordinator agent"""
    
    def __init__(self, agent_id: str, protocol: CoordinationProtocol):
        capabilities = [
            AgentCapability(
                name="coordination",
                description="Coordinate multi-agent system",
                input_types=["agent_status", "system_metrics"],
                output_types=["coordination_commands", "resource_allocation"]
            )
        ]
        
        super().__init__(agent_id, AgentType.COORDINATOR, capabilities)
        self.role = AgentRole.LEADER
        self.protocol = protocol
        
        # Coordination state
        self.managed_agents: Dict[str, BaseAgent] = {}
        self.coordination_metrics = CoordinationMetrics()
        self.consensus_threshold = 0.7
        self.resource_pool = {
            'cpu': 1.0,
            'memory': 1.0,
            'network': 1.0
        }
        
    async def process_message(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Process coordination messages"""
        if message.message_type == MessageType.COORDINATION_REQUEST:
            return await self._handle_coordination_request(message)
        
        elif message.message_type == MessageType.RESOURCE_REQUEST:
            return await self._handle_resource_request(message)
        
        elif message.message_type == MessageType.CONSENSUS_VOTE:
            return await self._handle_consensus_vote(message)
        
        elif message.message_type == MessageType.EMERGENCY_ALERT:
            await self._handle_emergency(message)
        
        return None
    
    async def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute coordination task"""
        task_type = task.get('type')
        
        if task_type == 'coordinate_agents':
            return await self._coordinate_agents()
        
        elif task_type == 'allocate_resources':
            return await self._allocate_resources()
        
        elif task_type == 'achieve_consensus':
            topic = task.get('topic')
            return await self._achieve_consensus(topic)
        
        return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get coordinator status"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type.value,
            'protocol': self.protocol.value,
            'managed_agents': len(self.managed_agents),
            'coordination_metrics': {
                'active_agents': self.coordination_metrics.active_agents,
                'message_throughput': self.coordination_metrics.message_throughput,
                'consensus_time': self.coordination_metrics.consensus_time,
                'coordination_efficiency': self.coordination_metrics.coordination_efficiency
            },
            'resource_utilization': self.coordination_metrics.resource_utilization,
            'last_heartbeat': self.last_heartbeat.isoformat()
        }
    
    async def _handle_coordination_request(self, message: AgentMessage) -> AgentMessage:
        """Handle coordination request"""
        request_type = message.content.get('request_type')
        
        if request_type == 'join_system':
            # Add agent to managed agents
            agent_info = message.content.get('agent_info')
            if agent_info:
                self.managed_agents[message.sender_id] = agent_info
        
        elif request_type == 'task_assignment':
            # Assign task to appropriate agent
            task = message.content.get('task')
            assigned_agent = await self._assign_task(task)
            
            return AgentMessage(
                sender_id=self.agent_id,
                receiver_id=message.sender_id,
                message_type=MessageType.COORDINATION_REQUEST,
                content={'assigned_agent': assigned_agent}
            )
        
        return AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.COORDINATION_REQUEST,
            content={'status': 'acknowledged'}
        )
    
    async def _handle_resource_request(self, message: AgentMessage) -> AgentMessage:
        """Handle resource allocation request"""
        requested_resources = message.content.get('resources', {})
        
        # Simple resource allocation
        allocated = {}
        for resource, amount in requested_resources.items():
            if resource in self.resource_pool:
                available = self.resource_pool[resource]
                allocated[resource] = min(amount, available)
                self.resource_pool[resource] -= allocated[resource]
        
        return AgentMessage(
            sender_id=self.agent_id,
            receiver_id=message.sender_id,
            message_type=MessageType.RESOURCE_REQUEST,
            content={'allocated_resources': allocated}
        )
    
    async def _handle_consensus_vote(self, message: AgentMessage) -> Optional[AgentMessage]:
        """Handle consensus voting"""
        # Implement consensus mechanism
        vote = message.content.get('vote')
        topic = message.content.get('topic')
        
        # Store vote and check if consensus reached
        # This is a simplified implementation
        return None
    
    async def _handle_emergency(self, message: AgentMessage):
        """Handle emergency situations"""
        emergency_type = message.content.get('emergency_type')
        
        if emergency_type == 'system_overload':
            # Reduce system load
            await self._reduce_system_load()
        
        elif emergency_type == 'agent_failure':
            # Handle agent failure
            failed_agent = message.content.get('failed_agent')
            await self._handle_agent_failure(failed_agent)
    
    async def _coordinate_agents(self) -> Dict[str, Any]:
        """Coordinate all managed agents"""
        coordination_results = {
            'coordinated_agents': len(self.managed_agents),
            'coordination_time': time.time()
        }
        
        # Update coordination metrics
        self.coordination_metrics.active_agents = len(self.managed_agents)
        self.coordination_metrics.timestamp = datetime.now()
        
        return coordination_results
    
    async def _allocate_resources(self) -> Dict[str, Any]:
        """Allocate resources among agents"""
        # Simple equal allocation
        num_agents = len(self.managed_agents)
        if num_agents > 0:
            allocation_per_agent = {}
            for resource, total in self.resource_pool.items():
                allocation_per_agent[resource] = total / num_agents
        
        return {'resource_allocation': allocation_per_agent}
    
    async def _achieve_consensus(self, topic: str) -> Dict[str, Any]:
        """Achieve consensus on a topic"""
        # Simplified consensus mechanism
        start_time = time.time()
        
        # Broadcast consensus request
        await self.broadcast_message(
            MessageType.CONSENSUS_VOTE,
            {'topic': topic, 'request_vote': True}
        )
        
        # Wait for responses (simplified)
        await asyncio.sleep(1.0)
        
        consensus_time = time.time() - start_time
        self.coordination_metrics.consensus_time = consensus_time
        
        return {
            'consensus_achieved': True,
            'consensus_time': consensus_time,
            'topic': topic
        }
    
    async def _assign_task(self, task: Dict[str, Any]) -> str:
        """Assign task to most suitable agent"""
        # Simple task assignment based on agent capabilities
        task_type = task.get('type')
        
        # Find agent with matching capability
        for agent_id, agent in self.managed_agents.items():
            if hasattr(agent, 'capabilities'):
                for capability in agent.capabilities:
                    if task_type in capability.name:
                        return agent_id
        
        # Return first available agent if no specific match
        return list(self.managed_agents.keys())[0] if self.managed_agents else ""
    
    async def _reduce_system_load(self):
        """Reduce system load during emergencies"""
        # Implement load reduction strategies
        pass
    
    async def _handle_agent_failure(self, failed_agent: str):
        """Handle agent failure"""
        if failed_agent in self.managed_agents:
            del self.managed_agents[failed_agent]
            logger.warning(f"Removed failed agent {failed_agent} from system")

class MultiAgentCoordinator:
    """Main multi-agent coordination system"""
    
    def __init__(self, protocol: CoordinationProtocol = CoordinationProtocol.HYBRID):
        self.protocol = protocol
        self.agents: Dict[str, BaseAgent] = {}
        self.message_router = MessageRouter()
        self.coordination_metrics = CoordinationMetrics()
        
        # Create coordinator agent
        self.coordinator = CoordinatorAgent("coordinator", protocol)
        self.add_agent(self.coordinator)
        
        # System state
        self.is_running = False
        self.coordination_loop_task = None
        
        logger.info(f"Multi-Agent Coordinator initialized with {protocol.value} protocol")
    
    def add_agent(self, agent: BaseAgent):
        """Add agent to the system"""
        agent.coordinator = self
        self.agents[agent.agent_id] = agent
        
        # Register with coordinator
        if agent.agent_id != "coordinator":
            self.coordinator.managed_agents[agent.agent_id] = agent
        
        logger.info(f"Added agent {agent.agent_id} to coordination system")
    
    def remove_agent(self, agent_id: str):
        """Remove agent from the system"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            
            if agent_id in self.coordinator.managed_agents:
                del self.coordinator.managed_agents[agent_id]
            
            logger.info(f"Removed agent {agent_id} from coordination system")
    
    async def start(self):
        """Start the coordination system"""
        self.is_running = True
        
        # Start all agents
        for agent in self.agents.values():
            agent.is_active = True
        
        # Start coordination loop
        self.coordination_loop_task = asyncio.create_task(self._coordination_loop())
        
        logger.info("Multi-Agent Coordination System started")
    
    async def stop(self):
        """Stop the coordination system"""
        self.is_running = False
        
        # Stop coordination loop
        if self.coordination_loop_task:
            self.coordination_loop_task.cancel()
            try:
                await self.coordination_loop_task
            except asyncio.CancelledError:
                pass
        
        # Stop all agents
        for agent in self.agents.values():
            agent.is_active = False
        
        logger.info("Multi-Agent Coordination System stopped")
    
    async def route_message(self, message: AgentMessage):
        """Route message to appropriate agent(s)"""
        await self.message_router.route_message(message, self.agents)
    
    async def execute_coordinated_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Execute a task that requires coordination"""
        task_type = task.get('type')
        
        if task_type == 'optimize_and_trade':
            # Coordinate genetic optimization and RL trading
            return await self._optimize_and_trade(task)
        
        elif task_type == 'emergency_response':
            # Handle emergency situations
            return await self._emergency_response(task)
        
        elif task_type == 'system_adaptation':
            # Adapt system based on performance
            return await self._system_adaptation(task)
        
        return {}
    
    async def _coordination_loop(self):
        """Main coordination loop"""
        while self.is_running:
            try:
                # Update coordination metrics
                await self._update_coordination_metrics()
                
                # Check agent health
                await self._check_agent_health()
                
                # Optimize coordination
                await self._optimize_coordination()
                
                # Sleep before next iteration
                await asyncio.sleep(1.0)
                
            except Exception as e:
                logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _update_coordination_metrics(self):
        """Update coordination performance metrics"""
        self.coordination_metrics.active_agents = len([a for a in self.agents.values() if a.is_active])
        self.coordination_metrics.timestamp = datetime.now()
        
        # Calculate message throughput
        self.coordination_metrics.message_throughput = self.message_router.get_throughput()
        
        # Calculate coordination efficiency
        if self.coordination_metrics.active_agents > 0:
            self.coordination_metrics.coordination_efficiency = (
                self.coordination_metrics.message_throughput / 
                self.coordination_metrics.active_agents
            )
    
    async def _check_agent_health(self):
        """Check health of all agents"""
        current_time = datetime.now()
        
        for agent_id, agent in list(self.agents.items()):
            # Check if agent is responsive
            time_since_heartbeat = (current_time - agent.last_heartbeat).total_seconds()
            
            if time_since_heartbeat > 60:  # 1 minute timeout
                logger.warning(f"Agent {agent_id} appears unresponsive")
                agent.is_active = False
                
                # Notify coordinator
                await self.coordinator.process_message(AgentMessage(
                    sender_id="system",
                    receiver_id="coordinator",
                    message_type=MessageType.EMERGENCY_ALERT,
                    content={
                        'emergency_type': 'agent_failure',
                        'failed_agent': agent_id
                    }
                ))
    
    async def _optimize_coordination(self):
        """Optimize coordination based on current metrics"""
        # Simple optimization: adjust message routing based on load
        if self.coordination_metrics.message_throughput > 100:  # High load
            # Implement load balancing
            pass
    
    async def _optimize_and_trade(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate genetic optimization and RL trading"""
        market_data = task.get('market_data', {})
        
        results = {}
        
        # Find genetic optimizer and RL trader agents
        genetic_agents = [a for a in self.agents.values() 
                         if a.agent_type == AgentType.GENETIC_OPTIMIZER]
        rl_agents = [a for a in self.agents.values() 
                    if a.agent_type == AgentType.RL_TRADER]
        
        # Execute genetic optimization
        if genetic_agents:
            genetic_agent = genetic_agents[0]
            genetic_result = await genetic_agent.execute_task({
                'type': 'evolve_strategies',
                'market_data': market_data
            })
            results['genetic_optimization'] = genetic_result
        
        # Execute RL trading
        if rl_agents:
            rl_agent = rl_agents[0]
            rl_result = await rl_agent.execute_task({
                'type': 'trade',
                'market_state': market_data,
                'portfolio_state': task.get('portfolio_state', {})
            })
            results['rl_trading'] = rl_result
        
        return results
    
    async def _emergency_response(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Handle emergency response coordination"""
        emergency_type = task.get('emergency_type')
        
        # Broadcast emergency alert
        await self.coordinator.broadcast_message(
            MessageType.EMERGENCY_ALERT,
            {'emergency_type': emergency_type, 'details': task.get('details', {})}
        )
        
        return {'emergency_handled': True, 'response_time': time.time()}
    
    async def _system_adaptation(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate system adaptation"""
        performance_data = task.get('performance_data', {})
        
        # Share performance data with all agents
        for agent in self.agents.values():
            await agent.process_message(AgentMessage(
                sender_id="coordinator",
                receiver_id=agent.agent_id,
                message_type=MessageType.PERFORMANCE_REPORT,
                content={'performance': performance_data}
            ))
        
        return {'adaptation_completed': True}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        agent_statuses = {}
        for agent_id, agent in self.agents.items():
            agent_statuses[agent_id] = agent.get_status()
        
        return {
            'coordination_protocol': self.protocol.value,
            'is_running': self.is_running,
            'total_agents': len(self.agents),
            'active_agents': len([a for a in self.agents.values() if a.is_active]),
            'coordination_metrics': {
                'active_agents': self.coordination_metrics.active_agents,
                'message_throughput': self.coordination_metrics.message_throughput,
                'consensus_time': self.coordination_metrics.consensus_time,
                'coordination_efficiency': self.coordination_metrics.coordination_efficiency,
                'system_coherence': self.coordination_metrics.system_coherence
            },
            'agents': agent_statuses
        }

class MessageRouter:
    """Routes messages between agents"""
    
    def __init__(self):
        self.message_count = 0
        self.start_time = time.time()
        self.message_history = deque(maxlen=1000)
    
    async def route_message(self, message: AgentMessage, agents: Dict[str, BaseAgent]):
        """Route message to appropriate agent(s)"""
        self.message_count += 1
        self.message_history.append(message)
        
        if message.receiver_id == "" or message.receiver_id == "broadcast":
            # Broadcast to all agents except sender
            for agent_id, agent in agents.items():
                if agent_id != message.sender_id:
                    await agent.message_queue.put(message)
                    asyncio.create_task(agent.process_message(message))
        else:
            # Send to specific agent
            if message.receiver_id in agents:
                agent = agents[message.receiver_id]
                await agent.message_queue.put(message)
                response = await agent.process_message(message)
                
                # Handle response if required
                if message.requires_response and response:
                    await self.route_message(response, agents)
    
    def get_throughput(self) -> float:
        """Get message throughput (messages per second)"""
        elapsed_time = time.time() - self.start_time
        return self.message_count / elapsed_time if elapsed_time > 0 else 0

# Example usage and integration
if __name__ == "__main__":
    async def main():
        # Create multi-agent coordinator
        coordinator = MultiAgentCoordinator(CoordinationProtocol.HYBRID)
        
        # Create and add genetic optimizer agent
        genetic_config = GeneticConfig(population_size=20, generations=50)
        from genetic_algorithm_optimizer import TradingStrategyEvaluator
        evaluator = TradingStrategyEvaluator({'initial_balance': 100000})
        genetic_agent = GeneticOptimizerAgent("genetic_optimizer_1", genetic_config, evaluator)
        coordinator.add_agent(genetic_agent)
        
        # Create and add RL trader agent
        rl_config = AgentConfig(state_size=10, action_size=3)
        rl_agent = RLTraderAgent("rl_trader_1", rl_config, "DQN")
        coordinator.add_agent(rl_agent)
        
        # Start coordination system
        await coordinator.start()
        
        # Execute coordinated task
        market_data = {
            'price': 100.0,
            'volume': 1000000,
            'volatility': 0.02,
            'trend': 0.01
        }
        
        task = {
            'type': 'optimize_and_trade',
            'market_data': market_data,
            'portfolio_state': {'balance': 100000, 'positions': {}}
        }
        
        result = await coordinator.execute_coordinated_task(task)
        print(f"Coordinated task result: {result}")
        
        # Get system status
        status = coordinator.get_system_status()
        print(f"System status: {status}")
        
        # Run for a short time
        await asyncio.sleep(5)
        
        # Stop coordination system
        await coordinator.stop()
        
        print("Multi-Agent Coordination System demonstration completed!")
    
    # Run the example
    asyncio.run(main())