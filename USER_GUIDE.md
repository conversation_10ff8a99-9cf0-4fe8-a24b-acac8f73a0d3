# 📖 Noryon AI Trading System - User Guide

**Complete Guide to Using the Noryon AI Trading Platform**

---

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [System Overview](#system-overview)
3. [Installation & Setup](#installation--setup)
4. [Configuration](#configuration)
5. [Training AI Models](#training-ai-models)
6. [Backtesting Strategies](#backtesting-strategies)
7. [Paper Trading](#paper-trading)
8. [Live Trading](#live-trading)
9. [Monitoring & Analytics](#monitoring--analytics)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)
12. [FAQ](#faq)

---

## 🚀 Getting Started

### What is Noryon?

Noryon is an advanced AI-powered trading system that combines multiple machine learning models, sophisticated risk management, and real-time market analysis to execute intelligent trading strategies.

### Key Benefits

- **AI-Driven Decisions**: Multiple AI models analyze market conditions
- **Risk Management**: Advanced position sizing and portfolio protection
- **Automated Trading**: Hands-off trading with intelligent automation
- **Comprehensive Analytics**: Detailed performance tracking and reporting
- **Paper Trading**: Risk-free testing with virtual money

### Prerequisites

- **Computer**: Windows, macOS, or Linux
- **Python**: Version 3.8 or higher
- **Memory**: 8GB RAM minimum (16GB recommended)
- **Storage**: 5GB free space
- **Internet**: Stable connection for market data and AI APIs

---

## 🏗️ System Overview

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    NORYON AI TRADING SYSTEM                 │
├─────────────────────────────────────────────────────────────┤
│  🎯 System Integrator                                      │
│     ├── Orchestrates all components                        │
│     ├── Manages trading workflow                           │
│     └── Handles system coordination                        │
├─────────────────────────────────────────────────────────────┤
│  🤖 AI Engine                                              │
│     ├── Dynamic Model Loader                               │
│     ├── Ensemble Voting System                             │
│     ├── LLM Brain Architecture                             │
│     └── Multi-Agent Coordinator                            │
├─────────────────────────────────────────────────────────────┤
│  📈 Trading Engine                                         │
│     ├── Enhanced Momentum Strategy                         │
│     ├── Market Regime Detector                             │
│     ├── Strategy Evolution Engine                          │
│     └── Signal Generation                                  │
├─────────────────────────────────────────────────────────────┤
│  🛡️ Risk Management                                        │
│     ├── Position Sizing Calculator                         │
│     ├── Portfolio Risk Monitor                             │
│     ├── Stop Loss Optimizer                                │
│     └── Exposure Limits                                    │
├─────────────────────────────────────────────────────────────┤
│  📊 Analytics & Monitoring                                 │
│     ├── Performance Dashboard                              │
│     ├── Backtesting Engine                                 │
│     ├── Model Evaluator                                    │
│     └── Training Pipeline                                  │
└─────────────────────────────────────────────────────────────┘
```

### Trading Workflow

1. **Market Data Collection**: Real-time price, volume, and technical indicators
2. **AI Analysis**: Multiple models analyze market conditions
3. **Signal Generation**: Ensemble voting produces trading signals
4. **Risk Assessment**: Position sizing and risk validation
5. **Trade Execution**: Automated order placement and management
6. **Monitoring**: Real-time performance tracking and alerts

---

## 🔧 Installation & Setup

### Quick Setup (Recommended)

1. **Download Noryon**
   ```bash
   git clone https://github.com/your-username/noryon-ai-trading.git
   cd noryon-ai-trading
   ```

2. **Run Automated Setup**
   ```bash
   python setup.py
   ```
   This will:
   - Check system requirements
   - Install dependencies
   - Create directory structure
   - Setup configuration files

3. **Launch Interactive Setup**
   ```bash
   python start_noryon.py --mode setup
   ```

### Manual Setup

If you prefer manual installation:

1. **Create Virtual Environment**
   ```bash
   python -m venv noryon_env
   
   # Windows
   noryon_env\Scripts\activate
   
   # macOS/Linux
   source noryon_env/bin/activate
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your API keys
   ```

### API Key Setup

You'll need API keys for AI models:

1. **Anthropic Claude**
   - Visit: https://console.anthropic.com/
   - Create account and generate API key
   - Add to `.env`: `ANTHROPIC_API_KEY=your_key_here`

2. **DeepSeek**
   - Visit: https://platform.deepseek.com/
   - Create account and generate API key
   - Add to `.env`: `DEEPSEEK_API_KEY=your_key_here`

3. **OpenAI (Optional)**
   - Visit: https://platform.openai.com/
   - Create account and generate API key
   - Add to `.env`: `OPENAI_API_KEY=your_key_here`

---

## ⚙️ Configuration

### Trading Configuration

Edit `config/trading_config.yaml`:

```yaml
# Basic Trading Settings
symbols: ["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA"]
max_positions: 5
risk_per_trade: 0.01  # 1% risk per trade
max_portfolio_risk: 0.05  # 5% max portfolio risk
paper_trading: true  # Start with paper trading

# Strategy Parameters
momentum_strategy:
  fast_ma_period: 12
  slow_ma_period: 26
  rsi_period: 14
  volume_threshold: 1.5
  confidence_threshold: 0.7

# Risk Management
risk_management:
  max_position_size: 0.05  # 5% max position size
  stop_loss_pct: 0.02      # 2% stop loss
  take_profit_pct: 0.06    # 6% take profit
  max_drawdown: 0.10       # 10% max drawdown

# Market Hours
market_hours:
  start_time: "09:30"
  end_time: "16:00"
  timezone: "US/Eastern"
```

### AI Model Configuration

Edit `config/ai/models.yaml`:

```yaml
# Model Providers
anthropic:
  api_key: "${ANTHROPIC_API_KEY}"
  model: "claude-3-sonnet-20240229"
  max_tokens: 4000
  temperature: 0.1

deepseek:
  api_key: "${DEEPSEEK_API_KEY}"
  model: "deepseek-chat"
  max_tokens: 4000
  temperature: 0.1

# Model Specializations
model_specializations:
  momentum_analysis: ["anthropic", "deepseek"]
  risk_assessment: ["anthropic"]
  market_regime: ["deepseek"]
  portfolio_optimization: ["anthropic"]

# Ensemble Voting Weights
ensemble_weights:
  anthropic: 0.6
  deepseek: 0.4
```

### Risk Configuration

Edit `config/risk/trading.yaml`:

```yaml
# Position Sizing
position_sizing:
  method: "kelly_criterion"  # or "fixed_percentage"
  base_risk: 0.01
  max_position: 0.05
  volatility_adjustment: true

# Portfolio Limits
portfolio_limits:
  max_positions: 10
  max_sector_exposure: 0.30
  max_single_position: 0.10
  correlation_limit: 0.70

# Stop Loss Rules
stop_loss:
  method: "atr_based"  # or "percentage"
  atr_multiplier: 2.0
  min_stop_loss: 0.01
  max_stop_loss: 0.05
  trailing_stop: true
```

---

## 🎓 Training AI Models

### Why Train Models?

Before trading, you need to train AI models on historical data to:
- Learn market patterns
- Optimize strategy parameters
- Validate model performance
- Ensure models are ready for live trading

### Training Process

1. **Start Training**
   ```bash
   python start_noryon.py --mode training
   ```

2. **Training Steps**
   - **Data Collection**: Downloads historical market data
   - **Feature Engineering**: Creates technical indicators and features
   - **Model Training**: Trains multiple AI models
   - **Validation**: Tests models on out-of-sample data
   - **Model Selection**: Chooses best performing models
   - **Model Saving**: Saves trained models for trading

3. **Monitor Training Progress**
   ```bash
   # Check training logs
   tail -f logs/training/training_*.log
   
   # View MLflow dashboard (if configured)
   mlflow ui
   ```

### Training Configuration

Edit `core/training/training_config.py` for advanced settings:

```python
# Training Parameters
TRAINING_CONFIG = {
    'data_period': '2y',  # 2 years of historical data
    'validation_split': 0.2,  # 20% for validation
    'test_split': 0.1,  # 10% for testing
    'batch_size': 32,
    'epochs': 100,
    'early_stopping_patience': 10,
    'learning_rate': 0.001
}
```

### Training Results

After training, check:
- `models/trained/` - Saved model files
- `logs/training/` - Training logs
- `reports/training/` - Training reports

---

## 📊 Backtesting Strategies

### What is Backtesting?

Backtesting tests your trading strategies on historical data to:
- Evaluate strategy performance
- Identify potential issues
- Optimize parameters
- Build confidence before live trading

### Running Backtests

1. **Basic Backtesting**
   ```bash
   python start_noryon.py --mode backtesting
   ```

2. **Backtest Specific Symbols**
   ```bash
   python start_noryon.py --mode backtesting --symbols AAPL GOOGL MSFT
   ```

3. **Advanced Backtesting**
   ```bash
   # Walk-forward analysis
   python -m core.backtesting.backtesting_engine --walk-forward
   
   # Monte Carlo simulation
   python -m core.backtesting.backtesting_engine --monte-carlo
   ```

### Backtesting Results

Backtesting generates comprehensive reports:

#### Performance Metrics
- **Total Return**: Overall strategy performance
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Largest peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Ratio of gross profit to gross loss

#### Risk Metrics
- **Value at Risk (VaR)**: Potential loss at confidence level
- **Conditional VaR**: Expected loss beyond VaR
- **Beta**: Correlation with market
- **Volatility**: Standard deviation of returns

#### Trade Analysis
- **Number of Trades**: Total trades executed
- **Average Trade**: Mean profit/loss per trade
- **Best/Worst Trade**: Largest gains and losses
- **Trade Duration**: Average holding period

### Interpreting Results

**Good Performance Indicators:**
- Sharpe Ratio > 1.0
- Maximum Drawdown < 10%
- Win Rate > 50%
- Consistent returns across different market conditions

**Warning Signs:**
- High volatility
- Large drawdowns
- Poor performance in recent periods
- Over-optimization (too good to be true)

---

## 📈 Paper Trading

### What is Paper Trading?

Paper trading simulates real trading with virtual money:
- Test strategies in live market conditions
- No financial risk
- Real-time market data
- Identical to live trading except no real money

### Starting Paper Trading

1. **Ensure Paper Trading is Enabled**
   ```yaml
   # In config/trading_config.yaml
   paper_trading: true
   ```

2. **Start Paper Trading**
   ```bash
   python start_noryon.py --mode paper
   ```

3. **Monitor Performance**
   - Dashboard: http://localhost:8050
   - Logs: `logs/trading/`
   - Reports: `reports/performance/`

### Paper Trading Features

- **Real Market Data**: Live prices and market conditions
- **Realistic Execution**: Simulated order fills and slippage
- **Portfolio Tracking**: Real-time P&L and positions
- **Risk Management**: All risk controls active
- **Performance Analytics**: Same metrics as live trading

### Paper Trading Best Practices

1. **Run for Extended Period**: At least 1-3 months
2. **Monitor Daily**: Check performance and system health
3. **Analyze Results**: Review trades and strategy performance
4. **Adjust Parameters**: Fine-tune based on results
5. **Validate Risk Management**: Ensure risk controls work properly

---

## 💰 Live Trading

### ⚠️ Important Warning

**Live trading involves real money and substantial risk of loss. Only proceed if you:**
- Have thoroughly tested with paper trading
- Understand the risks involved
- Can afford to lose the money you're trading
- Have reviewed and understand all system settings

### Prerequisites for Live Trading

1. **Successful Paper Trading**: At least 1-3 months of profitable paper trading
2. **Broker Account**: Funded account with supported broker
3. **API Access**: Broker API keys configured
4. **Risk Management**: Proper position sizing and stop losses
5. **Monitoring Setup**: Ability to monitor trades regularly

### Broker Setup

Currently supported brokers:
- **Alpaca**: Commission-free stock trading
- **Interactive Brokers**: Professional trading platform
- **TD Ameritrade**: Retail trading platform

#### Alpaca Setup Example

1. **Create Account**: https://alpaca.markets/
2. **Get API Keys**: From Alpaca dashboard
3. **Configure Environment**:
   ```bash
   # In .env file
   BROKER_API_KEY=your_alpaca_key
   BROKER_API_SECRET=your_alpaca_secret
   BROKER_BASE_URL=https://api.alpaca.markets  # Live trading
   # BROKER_BASE_URL=https://paper-api.alpaca.markets  # Paper trading
   ```

### Starting Live Trading

1. **Final Configuration Check**
   ```bash
   python start_noryon.py --mode status
   ```

2. **Enable Live Trading**
   ```yaml
   # In config/trading_config.yaml
   paper_trading: false
   ```

3. **Start Live Trading**
   ```bash
   python start_noryon.py --mode live
   ```
   
   **Confirmation Required**: System will ask for explicit confirmation

### Live Trading Monitoring

**Essential Monitoring:**
- **Daily P&L**: Check profits and losses
- **Position Sizes**: Ensure proper risk management
- **System Health**: Monitor for errors or issues
- **Market Conditions**: Be aware of unusual market events

**Monitoring Tools:**
- **Dashboard**: Real-time performance at http://localhost:8050
- **Logs**: System logs in `logs/trading/`
- **Alerts**: Email/SMS notifications for important events
- **Mobile App**: Monitor on-the-go (if available)

### Emergency Procedures

**If Something Goes Wrong:**

1. **Stop Trading Immediately**
   ```bash
   # Press Ctrl+C in terminal
   # Or run:
   python start_noryon.py --stop
   ```

2. **Close All Positions** (if necessary)
   ```bash
   python -m core.integration.emergency_stop
   ```

3. **Review Logs**
   ```bash
   tail -f logs/trading/trading_*.log
   ```

4. **Contact Support** (if available)

---

## 📊 Monitoring & Analytics

### Performance Dashboard

Access the web dashboard at: http://localhost:8050

#### Dashboard Sections

1. **Portfolio Overview**
   - Current portfolio value
   - Daily P&L
   - Open positions
   - Cash balance

2. **Performance Metrics**
   - Total return
   - Sharpe ratio
   - Maximum drawdown
   - Win rate

3. **Risk Analysis**
   - Portfolio risk
   - Position sizes
   - Sector exposure
   - Correlation analysis

4. **Trade History**
   - Recent trades
   - Trade performance
   - Entry/exit analysis

5. **Model Performance**
   - AI model accuracy
   - Prediction confidence
   - Model voting results

### System Status

Check system health:

```bash
python start_noryon.py --mode status
```

This shows:
- System configuration
- Model status
- Recent performance
- Log file information

### Performance Reports

Generate detailed reports:

```bash
# Daily performance report
python -m core.monitoring.performance_dashboard --report daily

# Weekly performance report
python -m core.monitoring.performance_dashboard --report weekly

# Monthly performance report
python -m core.monitoring.performance_dashboard --report monthly
```

### Alerts and Notifications

Configure alerts for:
- Large losses
- System errors
- Model performance issues
- Risk limit breaches

---

## 🔧 Troubleshooting

### Common Issues

#### 1. Installation Problems

**Issue**: Dependencies fail to install
```bash
ERROR: Could not install packages due to an EnvironmentError
```

**Solutions**:
```bash
# Update pip
pip install --upgrade pip

# Install with user flag
pip install --user -r requirements.txt

# Use conda instead
conda install --file requirements.txt
```

#### 2. API Key Issues

**Issue**: AI models not working
```
Error: Invalid API key
```

**Solutions**:
1. Check `.env` file has correct API keys
2. Verify API keys are active on provider websites
3. Check for extra spaces or quotes in `.env` file
4. Restart system after updating `.env`

#### 3. Market Data Issues

**Issue**: No market data received
```
Error: Failed to fetch market data
```

**Solutions**:
1. Check internet connection
2. Verify market hours (markets closed?)
3. Check data provider API limits
4. Review broker API configuration

#### 4. Training Failures

**Issue**: Model training fails
```
Error: Training failed with insufficient data
```

**Solutions**:
1. Check historical data availability
2. Reduce training period if data is limited
3. Verify symbols are valid
4. Check system memory (training requires RAM)

#### 5. Performance Issues

**Issue**: System running slowly

**Solutions**:
1. Reduce number of symbols
2. Increase system memory
3. Use faster storage (SSD)
4. Reduce model complexity
5. Check for background processes

### Log Analysis

**Check System Logs**:
```bash
# Trading logs
tail -f logs/trading/trading_*.log

# Training logs
tail -f logs/training/training_*.log

# System logs
tail -f logs/system/system_*.log

# Error logs
grep -i error logs/**/*.log
```

**Common Log Messages**:
- `INFO: Trade executed` - Normal trading activity
- `WARNING: High risk detected` - Risk management alert
- `ERROR: API rate limit` - Too many API calls
- `CRITICAL: System shutdown` - System stopped

### Getting Help

1. **Check Documentation**: README.md and this guide
2. **Review Configuration**: Ensure all settings are correct
3. **Check Logs**: Look for error messages
4. **Search Issues**: GitHub issues page
5. **Community Forum**: Ask questions in discussions
6. **Contact Support**: Email support if available

---

## 💡 Best Practices

### Trading Best Practices

1. **Start Small**
   - Begin with paper trading
   - Use small position sizes initially
   - Gradually increase as confidence grows

2. **Risk Management**
   - Never risk more than 1-2% per trade
   - Set maximum portfolio risk limits
   - Use stop losses on all positions
   - Diversify across multiple symbols

3. **Monitoring**
   - Check system daily
   - Review performance weekly
   - Analyze trades monthly
   - Stay informed about market conditions

4. **Continuous Improvement**
   - Regularly retrain models
   - Backtest new strategies
   - Optimize parameters based on performance
   - Stay updated with system improvements

### System Maintenance

1. **Regular Updates**
   ```bash
   # Update dependencies
   pip install --upgrade -r requirements.txt
   
   # Update system code
   git pull origin main
   ```

2. **Data Management**
   - Clean old log files regularly
   - Archive historical data
   - Monitor disk space usage
   - Backup important configurations

3. **Model Maintenance**
   - Retrain models monthly
   - Monitor model performance
   - Update model parameters
   - Test new model versions

4. **Security**
   - Keep API keys secure
   - Use strong passwords
   - Enable two-factor authentication
   - Regular security updates

### Performance Optimization

1. **System Resources**
   - Use SSD storage for better performance
   - Ensure adequate RAM (16GB+ recommended)
   - Use dedicated trading computer
   - Stable internet connection

2. **Configuration Tuning**
   - Optimize symbol selection
   - Adjust model parameters
   - Fine-tune risk settings
   - Balance performance vs. risk

3. **Monitoring Efficiency**
   - Set up automated alerts
   - Use dashboard for quick overview
   - Regular performance reviews
   - Efficient log management

---

## ❓ FAQ

### General Questions

**Q: Is Noryon suitable for beginners?**
A: Noryon is designed for users with some trading knowledge. While the system automates many aspects, understanding basic trading concepts and risk management is essential.

**Q: How much money do I need to start?**
A: You can start paper trading with $0. For live trading, most brokers require a minimum of $2,000-$25,000 depending on account type and trading style.

**Q: What markets does Noryon support?**
A: Currently supports US stocks. Crypto and forex support may be added in future versions.

**Q: How often should I retrain models?**
A: Recommended monthly retraining, or when performance degrades significantly.

### Technical Questions

**Q: Can I run Noryon on a VPS?**
A: Yes, Noryon can run on cloud servers. Ensure adequate resources and stable internet.

**Q: Does Noryon work with my broker?**
A: Check the supported brokers list. API integration is required for live trading.

**Q: Can I customize the trading strategies?**
A: Yes, strategies are modular and can be customized. Programming knowledge helpful.

**Q: How much data does Noryon use?**
A: Moderate data usage for market data and AI API calls. Estimate 1-5GB per month.

### Performance Questions

**Q: What returns can I expect?**
A: Returns vary greatly based on market conditions, strategy, and risk settings. No guarantees.

**Q: How does Noryon handle market crashes?**
A: Built-in risk management includes stop losses, position limits, and drawdown controls.

**Q: Can I trade multiple accounts?**
A: Currently designed for single account. Multi-account support may be added later.

**Q: Does Noryon work in all market conditions?**
A: Market regime detection adapts to different conditions, but no system works in all scenarios.

### Support Questions

**Q: Where can I get help?**
A: Check documentation, GitHub issues, community forums, or contact support.

**Q: Is there a mobile app?**
A: Currently web-based dashboard only. Mobile app may be developed in future.

**Q: Can I contribute to development?**
A: Yes! Check the contributing guidelines and submit pull requests.

**Q: Is Noryon open source?**
A: Check the license file for current licensing terms.

---

## 📞 Support & Resources

### Documentation
- **README.md**: System overview and quick start
- **USER_GUIDE.md**: This comprehensive guide
- **API_DOCS.md**: Technical API documentation
- **CONTRIBUTING.md**: Development contribution guide

### Community
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: Community Q&A and discussions
- **Discord/Slack**: Real-time community chat (if available)

### Professional Support
- **Email**: <EMAIL> (if available)
- **Consulting**: Custom development and optimization
- **Training**: Professional training sessions

### Learning Resources
- **Trading Education**: Basic trading and risk management
- **AI/ML Resources**: Understanding the AI components
- **Python Programming**: For customization and development

---

**Remember: Trading involves substantial risk. Never trade with money you cannot afford to lose. This software is for educational and research purposes. Past performance does not guarantee future results.**

---

*Happy Trading! 📈*

**Noryon AI Trading System Team**