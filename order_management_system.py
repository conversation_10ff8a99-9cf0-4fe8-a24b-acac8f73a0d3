#!/usr/bin/env python3
"""
Order Management System
Complete order lifecycle management, status tracking, and execution monitoring
"""

import sqlite3
import time
import json
import threading
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum

class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LIMIT = "STOP_LIMIT"
    TAKE_PROFIT = "TAKE_PROFIT"
    TRAILING_STOP = "TRAILING_STOP"

class OrderSide(Enum):
    BUY = "BUY"
    SELL = "SELL"

class OrderStatus(Enum):
    PENDING = "PENDING"
    SUBMITTED = "SUBMITTED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"

class TimeInForce(Enum):
    GTC = "GTC"  # Good Till Cancelled
    DAY = "DAY"  # Day Order
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill

@dataclass
class Order:
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.GTC
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    remaining_quantity: float = 0.0
    average_fill_price: float = 0.0
    commission: float = 0.0
    created_time: datetime = None
    submitted_time: Optional[datetime] = None
    filled_time: Optional[datetime] = None
    last_update: datetime = None
    broker: str = "paper"
    parent_order_id: Optional[str] = None
    child_orders: List[str] = None
    error_message: Optional[str] = None

@dataclass
class Fill:
    fill_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    commission: float
    timestamp: datetime
    execution_id: str

class OrderManagementSystem:
    """Complete order management and tracking system"""
    
    def __init__(self):
        self.orders = {}
        self.fills = {}
        self.order_callbacks = {}
        self.monitoring_active = False
        
        # Setup database
        self._setup_database()
        
        # Load existing orders
        self._load_orders()
        
        print("📋 ORDER MANAGEMENT SYSTEM INITIALIZED")
        print(f"   📊 Active orders: {len([o for o in self.orders.values() if o.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]])}")
        print(f"   ✅ Completed orders: {len([o for o in self.orders.values() if o.status in [OrderStatus.FILLED, OrderStatus.CANCELLED]])}")
    
    def _setup_database(self):
        """Setup order management database"""
        conn = sqlite3.connect('order_management.db')
        cursor = conn.cursor()
        
        # Orders table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                order_id TEXT PRIMARY KEY,
                symbol TEXT,
                side TEXT,
                order_type TEXT,
                quantity REAL,
                price REAL,
                stop_price REAL,
                time_in_force TEXT,
                status TEXT,
                filled_quantity REAL,
                remaining_quantity REAL,
                average_fill_price REAL,
                commission REAL,
                created_time DATETIME,
                submitted_time DATETIME,
                filled_time DATETIME,
                last_update DATETIME,
                broker TEXT,
                parent_order_id TEXT,
                error_message TEXT
            )
        ''')
        
        # Fills table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fills (
                fill_id TEXT PRIMARY KEY,
                order_id TEXT,
                symbol TEXT,
                side TEXT,
                quantity REAL,
                price REAL,
                commission REAL,
                timestamp DATETIME,
                execution_id TEXT
            )
        ''')
        
        # Order events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS order_events (
                id INTEGER PRIMARY KEY,
                order_id TEXT,
                event_type TEXT,
                event_data TEXT,
                timestamp DATETIME
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _load_orders(self):
        """Load existing orders from database"""
        conn = sqlite3.connect('order_management.db')
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM orders')
        
        for row in cursor.fetchall():
            order_data = dict(zip([col[0] for col in cursor.description], row))
            
            # Convert datetime strings back to datetime objects
            for field in ['created_time', 'submitted_time', 'filled_time', 'last_update']:
                if order_data[field]:
                    order_data[field] = datetime.fromisoformat(order_data[field])
            
            # Convert enums
            order_data['side'] = OrderSide(order_data['side'])
            order_data['order_type'] = OrderType(order_data['order_type'])
            order_data['status'] = OrderStatus(order_data['status'])
            order_data['time_in_force'] = TimeInForce(order_data['time_in_force'])
            
            # Create order object
            order = Order(**order_data)
            self.orders[order.order_id] = order
        
        conn.close()
    
    def create_order(self, symbol: str, side: str, quantity: float, 
                    order_type: str = 'MARKET', price: Optional[float] = None,
                    stop_price: Optional[float] = None, 
                    time_in_force: str = 'GTC') -> str:
        """Create new order"""
        
        order_id = str(uuid.uuid4())
        
        order = Order(
            order_id=order_id,
            symbol=symbol,
            side=OrderSide(side.upper()),
            order_type=OrderType(order_type.upper()),
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            time_in_force=TimeInForce(time_in_force.upper()),
            remaining_quantity=quantity,
            created_time=datetime.now(),
            last_update=datetime.now(),
            child_orders=[]
        )
        
        # Validate order
        validation_result = self._validate_order(order)
        if not validation_result['valid']:
            order.status = OrderStatus.REJECTED
            order.error_message = validation_result['error']
            print(f"❌ Order rejected: {validation_result['error']}")
            return order_id
        
        # Store order
        self.orders[order_id] = order
        self._save_order(order)
        self._log_order_event(order_id, 'CREATED', asdict(order))
        
        print(f"📋 Order created: {order_id}")
        print(f"   {symbol} {side} {quantity} @ {price or 'market'}")
        
        return order_id
    
    def submit_order(self, order_id: str) -> bool:
        """Submit order for execution"""
        
        if order_id not in self.orders:
            print(f"❌ Order not found: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        if order.status != OrderStatus.PENDING:
            print(f"❌ Order cannot be submitted: {order.status}")
            return False
        
        # Simulate order submission (in real system, this would call broker API)
        order.status = OrderStatus.SUBMITTED
        order.submitted_time = datetime.now()
        order.last_update = datetime.now()
        
        self._save_order(order)
        self._log_order_event(order_id, 'SUBMITTED', {'submitted_time': order.submitted_time.isoformat()})
        
        print(f"📤 Order submitted: {order_id}")
        
        # Start monitoring this order
        self._start_order_monitoring(order_id)
        
        # Call callbacks
        self._call_order_callbacks(order, 'SUBMITTED')
        
        return True
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel order"""
        
        if order_id not in self.orders:
            print(f"❌ Order not found: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        if order.status not in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
            print(f"❌ Order cannot be cancelled: {order.status}")
            return False
        
        # Cancel order
        order.status = OrderStatus.CANCELLED
        order.last_update = datetime.now()
        
        self._save_order(order)
        self._log_order_event(order_id, 'CANCELLED', {'cancelled_time': order.last_update.isoformat()})
        
        print(f"🚫 Order cancelled: {order_id}")
        
        # Call callbacks
        self._call_order_callbacks(order, 'CANCELLED')
        
        return True
    
    def modify_order(self, order_id: str, new_price: Optional[float] = None,
                    new_quantity: Optional[float] = None) -> bool:
        """Modify existing order"""
        
        if order_id not in self.orders:
            print(f"❌ Order not found: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        if order.status not in [OrderStatus.PENDING, OrderStatus.SUBMITTED]:
            print(f"❌ Order cannot be modified: {order.status}")
            return False
        
        # Store original values
        original_price = order.price
        original_quantity = order.quantity
        
        # Update order
        if new_price is not None:
            order.price = new_price
        if new_quantity is not None:
            order.quantity = new_quantity
            order.remaining_quantity = new_quantity - order.filled_quantity
        
        order.last_update = datetime.now()
        
        self._save_order(order)
        self._log_order_event(order_id, 'MODIFIED', {
            'original_price': original_price,
            'new_price': order.price,
            'original_quantity': original_quantity,
            'new_quantity': order.quantity
        })
        
        print(f"✏️ Order modified: {order_id}")
        
        # Call callbacks
        self._call_order_callbacks(order, 'MODIFIED')
        
        return True
    
    def simulate_fill(self, order_id: str, fill_quantity: float, fill_price: float) -> bool:
        """Simulate order fill (for paper trading)"""
        
        if order_id not in self.orders:
            return False
        
        order = self.orders[order_id]
        
        if order.status not in [OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]:
            return False
        
        # Create fill
        fill_id = str(uuid.uuid4())
        fill = Fill(
            fill_id=fill_id,
            order_id=order_id,
            symbol=order.symbol,
            side=order.side,
            quantity=fill_quantity,
            price=fill_price,
            commission=fill_quantity * fill_price * 0.001,  # 0.1% commission
            timestamp=datetime.now(),
            execution_id=f"exec_{int(time.time())}"
        )
        
        # Update order
        order.filled_quantity += fill_quantity
        order.remaining_quantity -= fill_quantity
        
        # Calculate average fill price
        total_filled_value = (order.average_fill_price * (order.filled_quantity - fill_quantity)) + (fill_price * fill_quantity)
        order.average_fill_price = total_filled_value / order.filled_quantity
        
        order.commission += fill.commission
        order.last_update = datetime.now()
        
        # Update status
        if order.remaining_quantity <= 0:
            order.status = OrderStatus.FILLED
            order.filled_time = datetime.now()
        else:
            order.status = OrderStatus.PARTIALLY_FILLED
        
        # Store fill and order
        self.fills[fill_id] = fill
        self._save_fill(fill)
        self._save_order(order)
        self._log_order_event(order_id, 'FILLED', asdict(fill))
        
        print(f"✅ Order fill: {order_id}")
        print(f"   Filled: {fill_quantity} @ ${fill_price:.2f}")
        print(f"   Status: {order.status.value}")
        
        # Call callbacks
        self._call_order_callbacks(order, 'FILLED')
        
        return True
    
    def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get order status and details"""
        
        if order_id not in self.orders:
            return None
        
        order = self.orders[order_id]
        
        # Get fills for this order
        order_fills = [fill for fill in self.fills.values() if fill.order_id == order_id]
        
        return {
            'order': asdict(order),
            'fills': [asdict(fill) for fill in order_fills],
            'fill_count': len(order_fills),
            'total_commission': sum(fill.commission for fill in order_fills)
        }
    
    def get_orders_by_status(self, status: OrderStatus) -> List[Order]:
        """Get orders by status"""
        return [order for order in self.orders.values() if order.status == status]
    
    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """Get orders by symbol"""
        return [order for order in self.orders.values() if order.symbol == symbol]
    
    def add_order_callback(self, callback: Callable, event_types: List[str] = None):
        """Add callback for order events"""
        callback_id = str(uuid.uuid4())
        self.order_callbacks[callback_id] = {
            'callback': callback,
            'event_types': event_types or ['SUBMITTED', 'FILLED', 'CANCELLED', 'REJECTED']
        }
        return callback_id
    
    def remove_order_callback(self, callback_id: str):
        """Remove order callback"""
        if callback_id in self.order_callbacks:
            del self.order_callbacks[callback_id]
    
    def _validate_order(self, order: Order) -> Dict[str, Any]:
        """Validate order parameters"""
        
        # Basic validation
        if order.quantity <= 0:
            return {'valid': False, 'error': 'Quantity must be positive'}
        
        if order.order_type == OrderType.LIMIT and order.price is None:
            return {'valid': False, 'error': 'Limit orders require price'}
        
        if order.order_type in [OrderType.STOP_LOSS, OrderType.STOP_LIMIT] and order.stop_price is None:
            return {'valid': False, 'error': 'Stop orders require stop price'}
        
        return {'valid': True}
    
    def _start_order_monitoring(self, order_id: str):
        """Start monitoring order for fills (paper trading simulation)"""
        
        def monitor_order():
            order = self.orders[order_id]
            
            # Simulate market order immediate fill
            if order.order_type == OrderType.MARKET:
                time.sleep(1)  # Simulate network delay
                
                # Simulate market price (would get from real data feed)
                market_price = order.price or 100.0  # Default price for simulation
                
                self.simulate_fill(order_id, order.quantity, market_price)
            
            # Simulate limit order conditional fill
            elif order.order_type == OrderType.LIMIT:
                # In real system, this would monitor market price and fill when price is reached
                # For simulation, randomly fill after some time
                time.sleep(5)
                
                # 70% chance of fill for simulation
                import random
                if random.random() < 0.7:
                    self.simulate_fill(order_id, order.quantity, order.price)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_order, daemon=True)
        monitor_thread.start()
    
    def _call_order_callbacks(self, order: Order, event_type: str):
        """Call registered callbacks for order events"""
        
        for callback_info in self.order_callbacks.values():
            if event_type in callback_info['event_types']:
                try:
                    callback_info['callback'](order, event_type)
                except Exception as e:
                    print(f"❌ Callback error: {e}")
    
    def _save_order(self, order: Order):
        """Save order to database"""
        conn = sqlite3.connect('order_management.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO orders 
            (order_id, symbol, side, order_type, quantity, price, stop_price, time_in_force,
             status, filled_quantity, remaining_quantity, average_fill_price, commission,
             created_time, submitted_time, filled_time, last_update, broker, parent_order_id, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            order.order_id, order.symbol, order.side.value, order.order_type.value,
            order.quantity, order.price, order.stop_price, order.time_in_force.value,
            order.status.value, order.filled_quantity, order.remaining_quantity,
            order.average_fill_price, order.commission,
            order.created_time.isoformat() if order.created_time else None,
            order.submitted_time.isoformat() if order.submitted_time else None,
            order.filled_time.isoformat() if order.filled_time else None,
            order.last_update.isoformat() if order.last_update else None,
            order.broker, order.parent_order_id, order.error_message
        ))
        
        conn.commit()
        conn.close()
    
    def _save_fill(self, fill: Fill):
        """Save fill to database"""
        conn = sqlite3.connect('order_management.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO fills 
            (fill_id, order_id, symbol, side, quantity, price, commission, timestamp, execution_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            fill.fill_id, fill.order_id, fill.symbol, fill.side.value,
            fill.quantity, fill.price, fill.commission,
            fill.timestamp.isoformat(), fill.execution_id
        ))
        
        conn.commit()
        conn.close()
    
    def _log_order_event(self, order_id: str, event_type: str, event_data: Any):
        """Log order event"""
        conn = sqlite3.connect('order_management.db')
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO order_events (order_id, event_type, event_data, timestamp)
            VALUES (?, ?, ?, ?)
        ''', (order_id, event_type, json.dumps(event_data, default=str), datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def get_order_summary(self) -> Dict[str, Any]:
        """Get order management summary"""
        
        status_counts = {}
        for status in OrderStatus:
            status_counts[status.value] = len(self.get_orders_by_status(status))
        
        return {
            'timestamp': datetime.now().isoformat(),
            'total_orders': len(self.orders),
            'total_fills': len(self.fills),
            'status_breakdown': status_counts,
            'active_orders': status_counts['PENDING'] + status_counts['SUBMITTED'] + status_counts['PARTIALLY_FILLED'],
            'completed_orders': status_counts['FILLED'] + status_counts['CANCELLED'],
            'monitoring_active': self.monitoring_active
        }

def main():
    """Test order management system"""
    print("📋 ORDER MANAGEMENT SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize OMS
    oms = OrderManagementSystem()
    
    # Add callback to track order events
    def order_callback(order: Order, event_type: str):
        print(f"🔔 Order Event: {event_type} - {order.symbol} {order.side.value} {order.quantity}")
    
    oms.add_order_callback(order_callback)
    
    # Create test orders
    print("\n📋 Creating test orders...")
    
    # Market order
    order1 = oms.create_order('BTC-USD', 'BUY', 0.1, 'MARKET')
    oms.submit_order(order1)
    
    # Limit order
    order2 = oms.create_order('AAPL', 'BUY', 100, 'LIMIT', price=150.0)
    oms.submit_order(order2)
    
    # Stop loss order
    order3 = oms.create_order('TSLA', 'SELL', 50, 'STOP_LOSS', stop_price=180.0)
    oms.submit_order(order3)
    
    # Wait for some fills
    print("\n⏱️ Waiting for order fills...")
    time.sleep(8)
    
    # Check order statuses
    print(f"\n📊 ORDER STATUS SUMMARY:")
    summary = oms.get_order_summary()
    
    print(f"   Total orders: {summary['total_orders']}")
    print(f"   Active orders: {summary['active_orders']}")
    print(f"   Completed orders: {summary['completed_orders']}")
    print(f"   Total fills: {summary['total_fills']}")
    
    print(f"\n📋 ORDER DETAILS:")
    for order_id in [order1, order2, order3]:
        status = oms.get_order_status(order_id)
        if status:
            order = status['order']
            print(f"   {order['symbol']}: {order['status']} - Filled: {order['filled_quantity']}/{order['quantity']}")
    
    # Test order modification
    print(f"\n✏️ Testing order modification...")
    if order2 in oms.orders and oms.orders[order2].status == OrderStatus.SUBMITTED:
        oms.modify_order(order2, new_price=155.0)
    
    # Test order cancellation
    print(f"\n🚫 Testing order cancellation...")
    if order3 in oms.orders and oms.orders[order3].status in [OrderStatus.SUBMITTED, OrderStatus.PENDING]:
        oms.cancel_order(order3)
    
    print(f"\n✅ ORDER MANAGEMENT SYSTEM TEST COMPLETE")

if __name__ == "__main__":
    main()
