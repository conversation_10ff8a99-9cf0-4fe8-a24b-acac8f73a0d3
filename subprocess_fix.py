
import subprocess
import sys

def run_ollama_safe(cmd, timeout=60):
    """Safe ollama command runner"""
    try:
        if isinstance(cmd, str):
            cmd = cmd.split()
        
        # Set encoding explicitly
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',  # Replace problematic characters
            timeout=timeout
        )
        return result
    except subprocess.TimeoutExpired:
        return subprocess.CompletedProcess(cmd, 1, "", "Timeout")
    except Exception as e:
        return subprocess.CompletedProcess(cmd, 1, "", str(e))

def run_ollama_interactive(model_name):
    """Start interactive ollama session"""
    try:
        cmd = ['ollama', 'run', model_name]
        process = subprocess.Popen(
            cmd,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        return process
    except Exception as e:
        print(f"Error starting {model_name}: {e}")
        return None
