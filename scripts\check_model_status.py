#!/usr/bin/env python3
"""
Check Status of All Available Models
Comprehensive status check for all models in the Noryon AI system
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def check_model_status():
    """Check status of all available models"""
    
    console.print(Panel(
        "[bold blue]Noryon AI Model Status Report[/bold blue]\n\n"
        "Checking all available models and their training status",
        title="Model Status Check"
    ))
    
    # Define model locations
    model_locations = {
        "DeepSeek R1 (Local)": Path("deepseek r1"),
        "DeepSeek R1 (HF Download)": Path("models/deepseek-finance-v1"),
        "Mistral 3": Path("mistral"),
        "Mistral Finance": Path("models/mistral-finance-v1"),
        "Qwen 3": Path("qwen3"),
        "Qwen Finance": Path("models/qwen3-finance-v1"),
        "Phi Finance": Path("models/phi-finance-v1"),
        "Gemma 3 12B (Ollama)": Path("ollama/models/manifests/registry.ollama.ai/library/gemma2"),
        "Phi 4 9B (Ollama)": Path("ollama/models/manifests/registry.ollama.ai/library/phi4")
    }
    
    # Create status table
    table = Table(title="Model Status Overview")
    table.add_column("Model", style="cyan", no_wrap=True)
    table.add_column("Location", style="yellow")
    table.add_column("Status", style="green")
    table.add_column("Size", style="blue")
    table.add_column("Training", style="magenta")
    
    total_models = 0
    ready_models = 0
    
    for model_name, model_path in model_locations.items():
        if model_path.exists():
            status = "✅ Available"
            ready_models += 1
            
            # Calculate size
            try:
                if model_path.is_file():
                    size = f"{model_path.stat().st_size / (1024**3):.1f} GB"
                else:
                    total_size = sum(f.stat().st_size for f in model_path.rglob('*') if f.is_file())
                    size = f"{total_size / (1024**3):.1f} GB"
            except:
                size = "Unknown"
            
            # Check training status
            if "finance" in model_name.lower():
                training_status = "🎯 Trained"
            elif model_name == "DeepSeek R1 (HF Download)":
                training_status = "🔄 In Progress"
            else:
                training_status = "⏳ Pending"
                
        else:
            status = "❌ Not Found"
            size = "-"
            training_status = "-"
        
        table.add_row(
            model_name,
            str(model_path),
            status,
            size,
            training_status
        )
        total_models += 1
    
    console.print(table)
    
    # Summary
    console.print(f"\n[bold green]Summary:[/bold green]")
    console.print(f"• Total Models: {total_models}")
    console.print(f"• Available: {ready_models}")
    console.print(f"• Missing: {total_models - ready_models}")
    
    return ready_models, total_models

def check_ollama_models():
    """Check Ollama models specifically"""
    console.print("\n[yellow]Checking Ollama Models...[/yellow]")
    
    try:
        import subprocess
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        
        if result.returncode == 0:
            console.print("[green]✅ Ollama is running[/green]")
            console.print("Available Ollama models:")
            console.print(result.stdout)
            return True
        else:
            console.print("[red]❌ Ollama not running or not installed[/red]")
            return False
    except FileNotFoundError:
        console.print("[red]❌ Ollama not found in PATH[/red]")
        return False

def check_training_progress():
    """Check current training progress"""
    console.print("\n[yellow]Checking Training Progress...[/yellow]")
    
    # Check if DeepSeek training is running
    training_files = [
        Path("models/deepseek-finance-v1"),
        Path("models/mistral-finance-v1"),
        Path("models/qwen3-finance-v1"),
        Path("models/phi-finance-v1")
    ]
    
    training_table = Table(title="Training Status")
    training_table.add_column("Model", style="cyan")
    training_table.add_column("Status", style="green")
    training_table.add_column("Last Modified", style="yellow")
    
    for training_path in training_files:
        model_name = training_path.name
        
        if training_path.exists():
            # Check for training files
            config_files = list(training_path.glob("*.yaml")) + list(training_path.glob("*.json"))
            model_files = list(training_path.glob("*.safetensors")) + list(training_path.glob("*.bin"))
            
            if model_files:
                status = "✅ Completed"
                last_modified = datetime.fromtimestamp(
                    max(f.stat().st_mtime for f in model_files)
                ).strftime("%Y-%m-%d %H:%M")
            elif config_files:
                status = "🔄 In Progress"
                last_modified = datetime.fromtimestamp(
                    max(f.stat().st_mtime for f in config_files)
                ).strftime("%Y-%m-%d %H:%M")
            else:
                status = "📁 Directory Only"
                last_modified = "-"
        else:
            status = "❌ Not Started"
            last_modified = "-"
        
        training_table.add_row(model_name, status, last_modified)
    
    console.print(training_table)

def main():
    """Main status check function"""
    console.print(f"[bold blue]🔍 Model Status Check - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}[/bold blue]\n")
    
    # Check model availability
    ready_models, total_models = check_model_status()
    
    # Check Ollama
    ollama_available = check_ollama_models()
    
    # Check training progress
    check_training_progress()
    
    # Recommendations
    console.print(Panel(
        "[bold yellow]📋 Next Steps Recommendations[/bold yellow]\n\n"
        "Based on your available models:\n\n"
        "1. ✅ DeepSeek R1 download is 75% complete - will finish soon\n"
        "2. 🚀 Train Gemma 3 12B and Phi 4 9B from Ollama\n"
        "3. 🔄 Continue with existing Mistral and Qwen training\n"
        "4. 🎯 Integrate all trained models into trading system\n"
        "5. 📊 Set up ensemble model for better predictions",
        title="Recommendations"
    ))
    
    return {
        'ready_models': ready_models,
        'total_models': total_models,
        'ollama_available': ollama_available
    }

if __name__ == "__main__":
    status = main()
    print(f"\nStatus: {status}")
