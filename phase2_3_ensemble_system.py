#!/usr/bin/env python3
"""
Phase 2.3: Ensemble System Creation
Coordinate multiple enhanced models for maximum intelligence
"""

import subprocess
import json
import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

@dataclass
class ModelResponse:
    model_name: str
    response: str
    response_time: float
    success: bool
    confidence_score: float = 0.0

class EnsembleIntelligenceSystem:
    """Coordinate multiple models for collective intelligence"""
    
    def __init__(self):
        # Your enhanced models from Phase 2
        self.enhanced_models = [
            'phase2-smart-unrestricted-qwen3-14b-latest',
            'phase2-unrestricted-noryon-qwen3-finance-v2-latest',
            'phase2-unrestricted-noryon-phi-4-9b-finance-latest',
            'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest',
            'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest'
        ]
        
        # Model specializations
        self.model_specializations = {
            'phase2-smart-unrestricted-qwen3-14b-latest': {
                'strengths': ['reasoning', 'analysis', 'comprehensive_responses'],
                'best_for': ['complex_analysis', 'detailed_explanations', 'multi_step_problems'],
                'speed_rating': 3,
                'quality_rating': 10
            },
            'phase2-unrestricted-noryon-qwen3-finance-v2-latest': {
                'strengths': ['finance', 'trading', 'market_analysis'],
                'best_for': ['trading_strategies', 'market_analysis', 'financial_planning'],
                'speed_rating': 6,
                'quality_rating': 9
            },
            'phase2-unrestricted-noryon-phi-4-9b-finance-latest': {
                'strengths': ['speed', 'efficiency', 'concise_responses'],
                'best_for': ['quick_answers', 'rapid_analysis', 'real_time_decisions'],
                'speed_rating': 9,
                'quality_rating': 8
            },
            'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': {
                'strengths': ['ultra_fast', 'efficient', 'optimized'],
                'best_for': ['instant_responses', 'high_frequency_queries', 'rapid_decisions'],
                'speed_rating': 10,
                'quality_rating': 7
            },
            'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest': {
                'strengths': ['ultimate_optimization', 'balanced', 'enhanced'],
                'best_for': ['optimized_performance', 'balanced_responses', 'general_use'],
                'speed_rating': 8,
                'quality_rating': 8
            }
        }
        
        self.ensemble_strategies = {
            'consensus': 'Get responses from multiple models and find consensus',
            'best_of_n': 'Get responses from N models and select the best one',
            'specialized': 'Route to the most specialized model for the task',
            'parallel': 'Get responses from all models simultaneously',
            'hierarchical': 'Use fast model first, then detailed model if needed'
        }
    
    def step1_analyze_query_requirements(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine optimal ensemble strategy"""
        print(f"STEP 1: Analyzing query requirements...")
        
        analysis = {
            'query_type': 'general',
            'complexity': 'medium',
            'urgency': 'normal',
            'domain': 'general',
            'recommended_strategy': 'specialized',
            'recommended_models': [],
            'expected_response_time': 'medium'
        }
        
        query_lower = query.lower()
        
        # Determine query type
        if any(word in query_lower for word in ['trade', 'trading', 'buy', 'sell', 'market', 'price', 'crypto', 'bitcoin', 'stock']):
            analysis['query_type'] = 'trading'
            analysis['domain'] = 'finance'
            analysis['recommended_models'] = [
                'phase2-unrestricted-noryon-qwen3-finance-v2-latest',
                'phase2-unrestricted-noryon-phi-4-9b-finance-latest'
            ]
        elif any(word in query_lower for word in ['analyze', 'analysis', 'explain', 'detailed', 'comprehensive']):
            analysis['query_type'] = 'analysis'
            analysis['complexity'] = 'high'
            analysis['recommended_models'] = [
                'phase2-smart-unrestricted-qwen3-14b-latest',
                'phase2-unrestricted-noryon-qwen3-finance-v2-latest'
            ]
        elif any(word in query_lower for word in ['quick', 'fast', 'urgent', 'now', 'immediately']):
            analysis['query_type'] = 'urgent'
            analysis['urgency'] = 'high'
            analysis['expected_response_time'] = 'fast'
            analysis['recommended_models'] = [
                'speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest',
                'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest'
            ]
        else:
            analysis['recommended_models'] = [
                'phase2-smart-unrestricted-qwen3-14b-latest',
                'ultimate-speed_optimized-phase2-unrestricted-noryon-phi-4-9b-finance-latest'
            ]
        
        # Determine complexity
        if len(query) > 200 or any(word in query_lower for word in ['complex', 'detailed', 'comprehensive', 'multi-step']):
            analysis['complexity'] = 'high'
        elif len(query) < 50:
            analysis['complexity'] = 'low'
        
        print(f"✅ Query Analysis: {analysis['query_type']} | {analysis['complexity']} complexity | {analysis['domain']} domain")
        return analysis
    
    def step2_execute_ensemble_strategy(self, query: str, analysis: Dict[str, Any]) -> List[ModelResponse]:
        """Execute the optimal ensemble strategy"""
        print(f"STEP 2: Executing ensemble strategy...")
        
        strategy = analysis['recommended_strategy']
        models = analysis['recommended_models']
        
        if strategy == 'specialized':
            return self._execute_specialized_strategy(query, models)
        elif strategy == 'consensus':
            return self._execute_consensus_strategy(query, models)
        elif strategy == 'parallel':
            return self._execute_parallel_strategy(query, models)
        else:
            return self._execute_best_of_n_strategy(query, models)
    
    def _execute_specialized_strategy(self, query: str, models: List[str]) -> List[ModelResponse]:
        """Execute specialized routing strategy"""
        print(f"🎯 Using specialized strategy with {len(models)} models")
        
        responses = []
        
        # Use the most specialized model first
        primary_model = models[0] if models else self.enhanced_models[0]
        
        try:
            start_time = time.time()
            result = subprocess.run([
                'ollama', 'run', primary_model, query
            ], capture_output=True, text=True, timeout=60)
            end_time = time.time()
            
            if result.returncode == 0:
                response = result.stdout.strip()
                confidence = self._calculate_confidence_score(response, query)
                
                responses.append(ModelResponse(
                    model_name=primary_model,
                    response=response,
                    response_time=end_time - start_time,
                    success=True,
                    confidence_score=confidence
                ))
                
                print(f"✅ {primary_model}: {end_time - start_time:.1f}s, confidence: {confidence:.2f}")
            else:
                print(f"❌ {primary_model}: Failed")
                
        except Exception as e:
            print(f"❌ {primary_model}: Error - {e}")
        
        return responses
    
    def _execute_consensus_strategy(self, query: str, models: List[str]) -> List[ModelResponse]:
        """Execute consensus strategy with multiple models"""
        print(f"🤝 Using consensus strategy with {len(models)} models")
        
        responses = []
        
        # Query multiple models
        for model in models[:3]:  # Limit to 3 models for consensus
            try:
                start_time = time.time()
                result = subprocess.run([
                    'ollama', 'run', model, query
                ], capture_output=True, text=True, timeout=45)
                end_time = time.time()
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    confidence = self._calculate_confidence_score(response, query)
                    
                    responses.append(ModelResponse(
                        model_name=model,
                        response=response,
                        response_time=end_time - start_time,
                        success=True,
                        confidence_score=confidence
                    ))
                    
                    print(f"✅ {model.split('-')[-1]}: {end_time - start_time:.1f}s")
                else:
                    print(f"❌ {model.split('-')[-1]}: Failed")
                    
            except Exception as e:
                print(f"❌ {model.split('-')[-1]}: Error")
        
        return responses
    
    def _execute_parallel_strategy(self, query: str, models: List[str]) -> List[ModelResponse]:
        """Execute parallel strategy (simplified version)"""
        print(f"⚡ Using parallel strategy with {len(models)} models")
        
        # For simplicity, execute sequentially but quickly
        return self._execute_consensus_strategy(query, models)
    
    def _execute_best_of_n_strategy(self, query: str, models: List[str]) -> List[ModelResponse]:
        """Execute best-of-N strategy"""
        print(f"🏆 Using best-of-N strategy with {len(models)} models")
        
        return self._execute_consensus_strategy(query, models)
    
    def _calculate_confidence_score(self, response: str, query: str) -> float:
        """Calculate confidence score for a response"""
        score = 0.5  # Base score
        
        # Length factor
        if len(response) > 500:
            score += 0.1
        if len(response) > 1500:
            score += 0.1
        
        # Structure factor
        if any(marker in response for marker in ['1.', '2.', '3.', '•', '-', ':']):
            score += 0.1
        
        # Relevance factor (simplified)
        query_words = set(query.lower().split())
        response_words = set(response.lower().split())
        overlap = len(query_words.intersection(response_words)) / len(query_words) if query_words else 0
        score += overlap * 0.2
        
        # Finance keywords bonus
        if any(word in response.lower() for word in ['analysis', 'strategy', 'recommendation', 'consider', 'however']):
            score += 0.1
        
        return min(score, 1.0)
    
    def step3_synthesize_responses(self, responses: List[ModelResponse], strategy: str) -> Dict[str, Any]:
        """Synthesize multiple responses into final answer"""
        print(f"STEP 3: Synthesizing {len(responses)} responses...")
        
        if not responses:
            return {
                'final_response': "No successful responses received.",
                'confidence': 0.0,
                'models_used': [],
                'synthesis_method': 'none'
            }
        
        if len(responses) == 1:
            # Single response
            response = responses[0]
            return {
                'final_response': response.response,
                'confidence': response.confidence_score,
                'models_used': [response.model_name],
                'synthesis_method': 'single',
                'response_time': response.response_time
            }
        
        # Multiple responses - find best one
        best_response = max(responses, key=lambda r: r.confidence_score)
        
        # Create synthesis
        synthesis = f"""ENSEMBLE INTELLIGENCE RESPONSE:

PRIMARY ANALYSIS ({best_response.model_name.split('-')[-1]}):
{best_response.response}

ENSEMBLE VALIDATION:
- Analyzed by {len(responses)} specialized models
- Confidence score: {best_response.confidence_score:.2f}/1.0
- Response time: {best_response.response_time:.1f} seconds
- Consensus level: {len([r for r in responses if r.success])}/{len(responses)} models successful
"""
        
        if len(responses) > 1:
            # Add alternative perspectives
            other_responses = [r for r in responses if r != best_response and r.success]
            if other_responses:
                synthesis += f"\nALTERNATIVE PERSPECTIVES:\n"
                for i, resp in enumerate(other_responses[:2], 1):
                    model_short = resp.model_name.split('-')[-1]
                    preview = resp.response[:200] + "..." if len(resp.response) > 200 else resp.response
                    synthesis += f"{i}. {model_short}: {preview}\n"
        
        avg_confidence = sum(r.confidence_score for r in responses if r.success) / len([r for r in responses if r.success])
        
        print(f"✅ Synthesized response from {len(responses)} models, confidence: {avg_confidence:.2f}")
        
        return {
            'final_response': synthesis,
            'confidence': avg_confidence,
            'models_used': [r.model_name for r in responses],
            'synthesis_method': 'ensemble',
            'response_time': best_response.response_time,
            'best_model': best_response.model_name
        }
    
    def query_ensemble(self, query: str) -> Dict[str, Any]:
        """Main method to query the ensemble system"""
        print(f"ENSEMBLE INTELLIGENCE SYSTEM")
        print(f"=" * 50)
        print(f"Query: {query[:100]}{'...' if len(query) > 100 else ''}")
        print()
        
        start_time = time.time()
        
        # Step 1: Analyze query
        analysis = self.step1_analyze_query_requirements(query)
        
        # Step 2: Execute strategy
        responses = self.step2_execute_ensemble_strategy(query, analysis)
        
        # Step 3: Synthesize
        result = self.step3_synthesize_responses(responses, analysis['recommended_strategy'])
        
        total_time = time.time() - start_time
        result['total_time'] = total_time
        
        print(f"\n" + "=" * 50)
        print(f"ENSEMBLE COMPLETE: {total_time:.1f}s total")
        print(f"Best model: {result.get('best_model', 'N/A')}")
        print(f"Confidence: {result['confidence']:.2f}/1.0")
        
        return result

def main():
    """Test the ensemble system"""
    ensemble = EnsembleIntelligenceSystem()
    
    print("PHASE 2.3: ENSEMBLE SYSTEM TESTING")
    print("=" * 60)
    
    # Test queries
    test_queries = [
        "Analyze Bitcoin's current market conditions and provide a trading strategy",
        "What are the best investment options for a $50,000 portfolio?",
        "Quick analysis: Should I buy or sell Tesla stock today?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\nTEST {i}/3:")
        result = ensemble.query_ensemble(query)
        
        print(f"\nRESULT PREVIEW:")
        preview = result['final_response'][:300] + "..." if len(result['final_response']) > 300 else result['final_response']
        print(preview)
        print()
    
    print("=" * 60)
    print("PHASE 2.3 ENSEMBLE SYSTEM READY!")
    print("Use ensemble.query_ensemble(your_question) for intelligent responses")

if __name__ == "__main__":
    main()
