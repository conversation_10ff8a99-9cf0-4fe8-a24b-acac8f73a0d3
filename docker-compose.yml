# Noryon AI Trading System - Docker Compose Configuration
# This file defines the complete system architecture with all services

version: '3.8'

services:
  # Main Noryon AI Trading System
  noryon-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: noryon-trading-system
    restart: unless-stopped
    ports:
      - "8000:8000"  # API server
      - "9090:9090"  # Prometheus metrics
    volumes:
      - ./data:/app/data
      - ./models:/app/models
      - ./logs:/app/logs
      - ./reports:/app/reports
      - ./config:/app/config
      - ./mlruns:/app/mlruns
    environment:
      - NORYON_ENV=production
      - PYTHONPATH=/app
      - DATABASE_URL=******************************************/noryon
      - REDIS_URL=redis://redis:6379/0
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    depends_on:
      - postgres
      - redis
      - mlflow
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # API-only service (lightweight)
  noryon-api:
    build:
      context: .
      dockerfile: Dockerfile
      target: api-only
    container_name: noryon-api-server
    restart: unless-stopped
    ports:
      - "8001:8000"
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - NORYON_ENV=production
      - PYTHONPATH=/app
      - DATABASE_URL=******************************************/noryon
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    networks:
      - noryon-network
    profiles:
      - api-only

  # Training service
  noryon-training:
    build:
      context: .
      dockerfile: Dockerfile
      target: training
    container_name: noryon-training
    restart: "no"  # Training jobs should not auto-restart
    volumes:
      - ./data:/app/data
      - ./models:/app/models
      - ./logs:/app/logs
      - ./config:/app/config
      - ./mlruns:/app/mlruns
    environment:
      - NORYON_ENV=production
      - PYTHONPATH=/app
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    depends_on:
      - mlflow
    networks:
      - noryon-network
    profiles:
      - training

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: noryon-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=noryon
      - POSTGRES_USER=noryon
      - POSTGRES_PASSWORD=password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U noryon -d noryon"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: noryon-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redispassword
    volumes:
      - redis_data:/data
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MLflow Tracking Server
  mlflow:
    image: python:3.11-slim
    container_name: noryon-mlflow
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - MLFLOW_BACKEND_STORE_URI=******************************************/noryon
      - MLFLOW_DEFAULT_ARTIFACT_ROOT=/mlflow/artifacts
    volumes:
      - mlflow_artifacts:/mlflow/artifacts
    command: >
      bash -c "pip install mlflow psycopg2-binary &&
               mlflow server 
               --backend-store-uri ******************************************/noryon 
               --default-artifact-root /mlflow/artifacts 
               --host 0.0.0.0 
               --port 5000"
    depends_on:
      - postgres
    networks:
      - noryon-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: noryon-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - noryon-network
    profiles:
      - monitoring

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: noryon-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - noryon-network
    profiles:
      - monitoring

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: noryon-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./static:/var/www/static
    depends_on:
      - noryon-app
    networks:
      - noryon-network
    profiles:
      - production

  # Development environment
  noryon-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: noryon-dev
    restart: unless-stopped
    ports:
      - "8002:8000"
      - "8888:8888"  # Jupyter
    volumes:
      - .:/app
      - /app/__pycache__
      - /app/.git
    environment:
      - NORYON_ENV=development
      - PYTHONPATH=/app
      - DATABASE_URL=******************************************/noryon
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    networks:
      - noryon-network
    profiles:
      - development
    command: >
      bash -c "pip install jupyter &&
               jupyter notebook --ip=0.0.0.0 --port=8888 --no-browser --allow-root &
               python main.py --mode api --log-level DEBUG"

networks:
  noryon-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mlflow_artifacts:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Health check for the entire stack
x-healthcheck-config: &healthcheck
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 40s