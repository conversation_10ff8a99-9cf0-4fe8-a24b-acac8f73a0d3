#!/usr/bin/env python3
"""
Noryon AI Trading System - Comprehensive Test Suite

This module contains comprehensive tests for all system components to ensure
reliability, performance, and correctness of the trading system.

Usage:
    python test_system.py                    # Run all tests
    python test_system.py --component api   # Test specific component
    python test_system.py --integration     # Run integration tests only
    python test_system.py --performance     # Run performance tests only
"""

import asyncio
import pytest
import unittest
import logging
import time
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# Test framework imports
import httpx
import pandas as pd
import numpy as np
from fastapi.testclient import TestClient

# System imports
from system_integration import SystemOrchestrator
from api_server import create_app
from training_pipeline import TrainingPipeline
from continuous_learning_pipeline import ContinuousLearningPipeline
from risk_management_system import RiskMonitor, Position, RiskLimits
from performance_analytics import PerformanceAnalytics
from backtesting_framework import BacktestEngine, BacktestConfig
from automated_deployment_pipeline import DeploymentPipeline

# Configure test logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestConfig:
    """
    Test configuration and utilities.
    """
    
    @staticmethod
    def create_test_config() -> Dict[str, Any]:
        """Create a test configuration."""
        return {
            "system": {
                "name": "Test Noryon System",
                "environment": "test",
                "debug": True,
                "log_level": "DEBUG"
            },
            "api": {
                "host": "127.0.0.1",
                "port": 8001,
                "cors_origins": ["*"]
            },
            "trading": {
                "models": {
                    "enabled": ["test_model"],
                    "ensemble_weights": {"test_model": 1.0}
                },
                "parameters": {
                    "initial_capital": 100000.0,
                    "max_positions": 5,
                    "transaction_cost": 0.001
                }
            },
            "risk": {
                "limits": {
                    "max_position_size": 0.20,
                    "max_portfolio_risk": 0.10,
                    "max_daily_loss": 0.05
                }
            },
            "data": {
                "sources": {
                    "yahoo": {
                        "enabled": True,
                        "symbols": ["AAPL", "GOOGL", "MSFT"]
                    }
                }
            }
        }
    
    @staticmethod
    def create_test_data() -> pd.DataFrame:
        """Create synthetic test data."""
        dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
        np.random.seed(42)
        
        data = []
        for symbol in ['AAPL', 'GOOGL', 'MSFT']:
            prices = 100 + np.cumsum(np.random.randn(len(dates)) * 0.02)
            for i, date in enumerate(dates):
                data.append({
                    'symbol': symbol,
                    'date': date,
                    'open': prices[i] * (1 + np.random.randn() * 0.01),
                    'high': prices[i] * (1 + abs(np.random.randn()) * 0.02),
                    'low': prices[i] * (1 - abs(np.random.randn()) * 0.02),
                    'close': prices[i],
                    'volume': np.random.randint(1000000, 10000000)
                })
        
        return pd.DataFrame(data)


class TestSystemIntegration(unittest.TestCase):
    """
    Test system integration and orchestration.
    """
    
    def setUp(self):
        """Set up test environment."""
        self.test_config = TestConfig.create_test_config()
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test environment."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @pytest.mark.asyncio
    async def test_system_orchestrator_initialization(self):
        """Test system orchestrator initialization."""
        with patch('system_integration.yaml.safe_load', return_value=self.test_config):
            orchestrator = SystemOrchestrator("test_config.yaml")
            await orchestrator.initialize()
            
            self.assertIsNotNone(orchestrator.config)
            self.assertEqual(orchestrator.state.value, "initialized")
    
    @pytest.mark.asyncio
    async def test_component_lifecycle(self):
        """Test component start/stop lifecycle."""
        with patch('system_integration.yaml.safe_load', return_value=self.test_config):
            orchestrator = SystemOrchestrator("test_config.yaml")
            await orchestrator.initialize()
            
            # Test starting a component
            result = await orchestrator.start_component("risk_monitor")
            self.assertTrue(result)
            
            # Test stopping a component
            result = await orchestrator.stop_component("risk_monitor")
            self.assertTrue(result)
    
    @pytest.mark.asyncio
    async def test_event_bus(self):
        """Test event bus functionality."""
        from system_integration import EventBus, SystemEvent, EventType
        
        event_bus = EventBus()
        received_events = []
        
        def event_handler(event: SystemEvent):
            received_events.append(event)
        
        # Subscribe to events
        event_bus.subscribe(EventType.SYSTEM_STARTED, event_handler)
        
        # Publish event
        test_event = SystemEvent(
            type=EventType.SYSTEM_STARTED,
            component="test",
            message="Test event",
            data={"test": True}
        )
        
        await event_bus.publish(test_event)
        
        # Verify event was received
        self.assertEqual(len(received_events), 1)
        self.assertEqual(received_events[0].type, EventType.SYSTEM_STARTED)


class TestAPIServer(unittest.TestCase):
    """
    Test API server functionality.
    """
    
    def setUp(self):
        """Set up test client."""
        self.test_config = TestConfig.create_test_config()
        
        with patch('api_server.SystemOrchestrator') as mock_orchestrator:
            mock_instance = AsyncMock()
            mock_orchestrator.return_value = mock_instance
            
            self.app = create_app()
            self.client = TestClient(self.app)
    
    def test_health_endpoint(self):
        """Test health check endpoint."""
        response = self.client.get("/health")
        self.assertEqual(response.status_code, 200)
        
        data = response.json()
        self.assertIn("status", data)
        self.assertIn("timestamp", data)
    
    def test_system_status_endpoint(self):
        """Test system status endpoint."""
        with patch('api_server.orchestrator') as mock_orchestrator:
            mock_orchestrator.get_status.return_value = {
                "status": "running",
                "components": {},
                "metrics": {}
            }
            
            response = self.client.get("/api/v1/system/status")
            self.assertEqual(response.status_code, 200)
    
    def test_authentication(self):
        """Test API authentication."""
        # Test without API key
        response = self.client.post("/api/v1/training/start")
        self.assertEqual(response.status_code, 401)
        
        # Test with invalid API key
        headers = {"X-API-Key": "invalid_key"}
        response = self.client.post("/api/v1/training/start", headers=headers)
        self.assertEqual(response.status_code, 401)
        
        # Test with valid API key
        headers = {"X-API-Key": "demo_key_123"}
        response = self.client.post("/api/v1/training/start", headers=headers)
        # Should not be 401 (may be other error due to missing data)
        self.assertNotEqual(response.status_code, 401)


class TestRiskManagement(unittest.TestCase):
    """
    Test risk management system.
    """
    
    def setUp(self):
        """Set up risk management test environment."""
        self.risk_limits = RiskLimits(
            max_position_size=0.10,
            max_portfolio_risk=0.05,
            max_daily_loss=0.02,
            max_drawdown=0.15,
            max_leverage=1.0
        )
        
        self.risk_monitor = RiskMonitor(self.risk_limits)
    
    @pytest.mark.asyncio
    async def test_position_risk_calculation(self):
        """Test position risk calculation."""
        # Add test position
        position = Position(
            symbol="AAPL",
            quantity=100,
            entry_price=150.0,
            current_price=155.0,
            timestamp=datetime.now()
        )
        
        await self.risk_monitor.add_position(position)
        
        # Calculate risk metrics
        metrics = await self.risk_monitor.calculate_risk_metrics()
        
        self.assertIsNotNone(metrics)
        self.assertGreaterEqual(metrics.portfolio_value, 0)
    
    @pytest.mark.asyncio
    async def test_risk_limit_violations(self):
        """Test risk limit violation detection."""
        # Create position that violates limits
        large_position = Position(
            symbol="AAPL",
            quantity=1000,  # Large position
            entry_price=150.0,
            current_price=155.0,
            timestamp=datetime.now()
        )
        
        await self.risk_monitor.add_position(large_position)
        
        # Check for violations
        violations = await self.risk_monitor.check_risk_limits()
        
        # Should detect position size violation
        self.assertTrue(len(violations) > 0)
    
    @pytest.mark.asyncio
    async def test_var_calculation(self):
        """Test VaR calculation."""
        # Create sample returns data
        returns = np.random.normal(0.001, 0.02, 252)  # Daily returns for 1 year
        
        var_95 = self.risk_monitor.var_calculator.calculate_var(
            returns, confidence_level=0.95
        )
        
        self.assertIsInstance(var_95, float)
        self.assertLess(var_95, 0)  # VaR should be negative


class TestPerformanceAnalytics(unittest.TestCase):
    """
    Test performance analytics system.
    """
    
    def setUp(self):
        """Set up performance analytics test environment."""
        self.analytics = PerformanceAnalytics()
        
        # Add sample data
        self.test_data = TestConfig.create_test_data()
    
    @pytest.mark.asyncio
    async def test_performance_calculation(self):
        """Test performance metrics calculation."""
        # Simulate portfolio updates
        for i in range(10):
            portfolio_value = 100000 + i * 1000
            await self.analytics.update_portfolio_value(portfolio_value)
            await asyncio.sleep(0.01)  # Small delay
        
        # Calculate performance
        snapshot = await self.analytics.get_current_performance()
        
        self.assertIsNotNone(snapshot)
        self.assertGreater(snapshot.total_return, 0)
    
    @pytest.mark.asyncio
    async def test_trade_analysis(self):
        """Test trade analysis functionality."""
        from performance_analytics import Trade
        
        # Add sample trades
        trades = [
            Trade(
                symbol="AAPL",
                quantity=100,
                entry_price=150.0,
                exit_price=155.0,
                entry_time=datetime.now() - timedelta(days=1),
                exit_time=datetime.now(),
                model="test_model"
            ),
            Trade(
                symbol="GOOGL",
                quantity=50,
                entry_price=2500.0,
                exit_price=2450.0,
                entry_time=datetime.now() - timedelta(days=2),
                exit_time=datetime.now() - timedelta(days=1),
                model="test_model"
            )
        ]
        
        for trade in trades:
            await self.analytics.add_trade(trade)
        
        # Analyze trades
        analysis = await self.analytics.analyze_trades()
        
        self.assertEqual(analysis["total_trades"], 2)
        self.assertEqual(analysis["winning_trades"], 1)
        self.assertEqual(analysis["losing_trades"], 1)


class TestBacktesting(unittest.TestCase):
    """
    Test backtesting framework.
    """
    
    def setUp(self):
        """Set up backtesting test environment."""
        self.test_data = TestConfig.create_test_data()
        self.backtest_engine = BacktestEngine()
    
    @pytest.mark.asyncio
    async def test_backtest_execution(self):
        """Test backtest execution."""
        from backtesting_framework import SimpleMovingAverageStrategy
        
        # Create test strategy
        strategy = SimpleMovingAverageStrategy(short_window=5, long_window=20)
        
        # Create backtest config
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 6, 30),
            initial_capital=100000.0,
            symbols=["AAPL"]
        )
        
        # Run backtest
        with patch.object(self.backtest_engine.data_manager, 'get_historical_data', 
                         return_value=self.test_data[self.test_data['symbol'] == 'AAPL']):
            result = await self.backtest_engine.run_backtest(strategy, config)
        
        self.assertIsNotNone(result)
        self.assertGreaterEqual(result.total_return, -1.0)  # Not completely lost
    
    @pytest.mark.asyncio
    async def test_walk_forward_analysis(self):
        """Test walk-forward analysis."""
        from backtesting_framework import WalkForwardAnalysis, SimpleMovingAverageStrategy
        
        strategy = SimpleMovingAverageStrategy(short_window=5, long_window=20)
        wfa = WalkForwardAnalysis()
        
        config = BacktestConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 6, 30),
            initial_capital=100000.0,
            symbols=["AAPL"]
        )
        
        # Mock data for walk-forward
        with patch.object(wfa, '_get_data_for_period', return_value=self.test_data):
            results = await wfa.run_walk_forward_analysis(
                strategy, config, training_period=60, testing_period=30, step_size=15
            )
        
        self.assertIsInstance(results, list)
        self.assertGreater(len(results), 0)


class TestTrainingPipeline(unittest.TestCase):
    """
    Test training pipeline functionality.
    """
    
    def setUp(self):
        """Set up training pipeline test environment."""
        self.test_config = TestConfig.create_test_config()
        
    @pytest.mark.asyncio
    async def test_training_pipeline_initialization(self):
        """Test training pipeline initialization."""
        with patch('training_pipeline.yaml.safe_load', return_value=self.test_config):
            pipeline = TrainingPipeline("test_config.yaml")
            
            self.assertIsNotNone(pipeline.config)
            self.assertEqual(len(pipeline.models), 1)  # test_model
    
    @pytest.mark.asyncio
    async def test_data_preparation(self):
        """Test data preparation functionality."""
        with patch('training_pipeline.yaml.safe_load', return_value=self.test_config):
            pipeline = TrainingPipeline("test_config.yaml")
            
            # Mock data preparation
            with patch.object(pipeline, '_prepare_training_data') as mock_prep:
                mock_prep.return_value = ("train_data", "val_data")
                
                train_data, val_data = await pipeline._prepare_training_data()
                
                self.assertEqual(train_data, "train_data")
                self.assertEqual(val_data, "val_data")


class TestDeploymentPipeline(unittest.TestCase):
    """
    Test deployment pipeline functionality.
    """
    
    def setUp(self):
        """Set up deployment pipeline test environment."""
        self.test_config = TestConfig.create_test_config()
        
    @pytest.mark.asyncio
    async def test_deployment_pipeline_initialization(self):
        """Test deployment pipeline initialization."""
        with patch('automated_deployment_pipeline.yaml.safe_load', return_value=self.test_config):
            pipeline = DeploymentPipeline("test_config.yaml")
            
            self.assertIsNotNone(pipeline.config)
    
    @pytest.mark.asyncio
    async def test_health_checks(self):
        """Test deployment health checks."""
        with patch('automated_deployment_pipeline.yaml.safe_load', return_value=self.test_config):
            pipeline = DeploymentPipeline("test_config.yaml")
            
            # Mock health check
            with patch.object(pipeline.health_monitor, 'check_system_health') as mock_health:
                mock_health.return_value = True
                
                health_status = await pipeline.health_monitor.check_system_health()
                
                self.assertTrue(health_status)


class TestPerformance(unittest.TestCase):
    """
    Performance and load testing.
    """
    
    def setUp(self):
        """Set up performance test environment."""
        self.test_config = TestConfig.create_test_config()
    
    @pytest.mark.asyncio
    async def test_api_response_time(self):
        """Test API response time under load."""
        with patch('api_server.SystemOrchestrator') as mock_orchestrator:
            mock_instance = AsyncMock()
            mock_orchestrator.return_value = mock_instance
            
            app = create_app()
            client = TestClient(app)
            
            # Measure response time
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            
            response_time = end_time - start_time
            
            self.assertEqual(response.status_code, 200)
            self.assertLess(response_time, 1.0)  # Should respond within 1 second
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self):
        """Test handling of concurrent requests."""
        with patch('api_server.SystemOrchestrator') as mock_orchestrator:
            mock_instance = AsyncMock()
            mock_orchestrator.return_value = mock_instance
            
            app = create_app()
            
            async def make_request():
                async with httpx.AsyncClient(app=app, base_url="http://test") as client:
                    response = await client.get("/health")
                    return response.status_code
            
            # Make 10 concurrent requests
            tasks = [make_request() for _ in range(10)]
            results = await asyncio.gather(*tasks)
            
            # All requests should succeed
            self.assertTrue(all(status == 200 for status in results))
    
    @pytest.mark.asyncio
    async def test_memory_usage(self):
        """Test memory usage during operations."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform memory-intensive operations
        analytics = PerformanceAnalytics()
        
        for i in range(1000):
            await analytics.update_portfolio_value(100000 + i)
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB)
        self.assertLess(memory_increase, 100 * 1024 * 1024)


def run_tests(component: Optional[str] = None, test_type: Optional[str] = None):
    """
    Run the test suite.
    
    Args:
        component: Specific component to test
        test_type: Type of tests to run (integration, performance, etc.)
    """
    # Configure test discovery
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    if component:
        # Run tests for specific component
        if component == "api":
            suite.addTests(loader.loadTestsFromTestCase(TestAPIServer))
        elif component == "risk":
            suite.addTests(loader.loadTestsFromTestCase(TestRiskManagement))
        elif component == "performance":
            suite.addTests(loader.loadTestsFromTestCase(TestPerformanceAnalytics))
        elif component == "backtesting":
            suite.addTests(loader.loadTestsFromTestCase(TestBacktesting))
        elif component == "training":
            suite.addTests(loader.loadTestsFromTestCase(TestTrainingPipeline))
        elif component == "deployment":
            suite.addTests(loader.loadTestsFromTestCase(TestDeploymentPipeline))
        else:
            print(f"Unknown component: {component}")
            return
    elif test_type:
        # Run specific type of tests
        if test_type == "integration":
            suite.addTests(loader.loadTestsFromTestCase(TestSystemIntegration))
        elif test_type == "performance":
            suite.addTests(loader.loadTestsFromTestCase(TestPerformance))
        else:
            print(f"Unknown test type: {test_type}")
            return
    else:
        # Run all tests
        test_classes = [
            TestSystemIntegration,
            TestAPIServer,
            TestRiskManagement,
            TestPerformanceAnalytics,
            TestBacktesting,
            TestTrainingPipeline,
            TestDeploymentPipeline,
            TestPerformance
        ]
        
        for test_class in test_classes:
            suite.addTests(loader.loadTestsFromTestCase(test_class))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\nTest Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Noryon AI Trading System Test Suite")
    parser.add_argument("--component", help="Test specific component")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--performance", action="store_true", help="Run performance tests only")
    
    args = parser.parse_args()
    
    test_type = None
    if args.integration:
        test_type = "integration"
    elif args.performance:
        test_type = "performance"
    
    success = run_tests(component=args.component, test_type=test_type)
    
    if not success:
        exit(1)