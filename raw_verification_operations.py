#!/usr/bin/env python3
"""
Raw Verification Operations
REAL proof of 100% system capacity with raw data verification
"""

import sqlite3
import os
import time
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Any

class RawVerificationOperations:
    """REAL verification of all system capabilities with raw proof"""
    
    def __init__(self):
        self.verification_results = {}
        self.start_time = time.time()
        
        print("🔬 RAW VERIFICATION OPERATIONS INITIALIZED")
        print("   📊 Verifying ALL systems with raw data")
        print("   🔍 No exaggerations - only provable facts")
        print("   📈 100% capacity testing")
    
    def verify_all_databases(self) -> Dict[str, Any]:
        """Verify ALL databases with RAW data counts"""
        
        print(f"\n💾 VERIFYING ALL DATABASES - RAW DATA")
        print("=" * 50)
        
        database_verification = {}
        total_size = 0
        total_tables = 0
        total_records = 0
        
        # Get all .db files
        db_files = [f for f in os.listdir('.') if f.endswith('.db')]
        
        for db_file in db_files:
            try:
                # Get file size
                file_size = os.path.getsize(db_file)
                total_size += file_size
                
                # Connect and get table info
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()
                
                # Get all tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                table_count = len(tables)
                total_tables += table_count
                
                # Count records in each table
                record_counts = {}
                db_total_records = 0
                
                for table in tables:
                    table_name = table[0]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        record_counts[table_name] = count
                        db_total_records += count
                    except Exception as e:
                        record_counts[table_name] = f"Error: {e}"
                
                total_records += db_total_records
                
                database_verification[db_file] = {
                    'file_size_bytes': file_size,
                    'file_size_kb': round(file_size / 1024, 1),
                    'table_count': table_count,
                    'total_records': db_total_records,
                    'table_details': record_counts,
                    'status': 'OPERATIONAL'
                }
                
                conn.close()
                
                print(f"   ✅ {db_file}: {file_size:,} bytes, {table_count} tables, {db_total_records:,} records")
                
            except Exception as e:
                database_verification[db_file] = {
                    'error': str(e),
                    'status': 'ERROR'
                }
                print(f"   ❌ {db_file}: ERROR - {e}")
        
        summary = {
            'total_databases': len(db_files),
            'operational_databases': len([db for db in database_verification.values() if db.get('status') == 'OPERATIONAL']),
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'total_tables': total_tables,
            'total_records': total_records,
            'database_details': database_verification
        }
        
        print(f"\n📊 DATABASE VERIFICATION SUMMARY:")
        print(f"   💾 Total databases: {summary['total_databases']}")
        print(f"   ✅ Operational: {summary['operational_databases']}")
        print(f"   📏 Total size: {summary['total_size_mb']} MB ({summary['total_size_bytes']:,} bytes)")
        print(f"   📋 Total tables: {summary['total_tables']}")
        print(f"   📊 Total records: {summary['total_records']:,}")
        
        return summary
    
    def verify_ai_agents_raw(self) -> Dict[str, Any]:
        """Verify ALL AI agents with RAW response testing"""
        
        print(f"\n🤖 VERIFYING ALL AI AGENTS - RAW TESTING")
        print("=" * 50)
        
        # Import the expanded team system
        try:
            from expanded_ai_team_system import ExpandedAITeamSystem
            expanded_team = ExpandedAITeamSystem()
            
            agent_verification = {}
            successful_agents = 0
            total_response_time = 0
            total_response_chars = 0
            
            test_query = "Test query: Respond with 'AGENT OPERATIONAL' and your specialization."
            
            for agent_name, agent_info in expanded_team.ai_team.items():
                print(f"\n🧪 Testing {agent_name}...")
                
                start_time = time.time()
                
                try:
                    # Test direct model query
                    result = subprocess.run([
                        'ollama', 'run', agent_info['model'], test_query
                    ], capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore')
                    
                    response_time = time.time() - start_time
                    
                    if result.returncode == 0:
                        response = result.stdout.strip()
                        response_length = len(response)
                        
                        agent_verification[agent_name] = {
                            'model': agent_info['model'],
                            'specialization': agent_info['specialization'],
                            'role': agent_info['role'],
                            'status': 'OPERATIONAL',
                            'response_time': round(response_time, 2),
                            'response_length': response_length,
                            'response_preview': response[:100] + "..." if len(response) > 100 else response
                        }
                        
                        successful_agents += 1
                        total_response_time += response_time
                        total_response_chars += response_length
                        
                        print(f"   ✅ {agent_name}: {response_time:.1f}s, {response_length} chars")
                        
                    else:
                        agent_verification[agent_name] = {
                            'model': agent_info['model'],
                            'status': 'ERROR',
                            'error': result.stderr.strip() or 'Unknown error',
                            'response_time': round(response_time, 2)
                        }
                        print(f"   ❌ {agent_name}: ERROR - {result.stderr.strip()}")
                        
                except Exception as e:
                    agent_verification[agent_name] = {
                        'model': agent_info['model'],
                        'status': 'EXCEPTION',
                        'error': str(e),
                        'response_time': time.time() - start_time
                    }
                    print(f"   ❌ {agent_name}: EXCEPTION - {e}")
            
            summary = {
                'total_agents': len(expanded_team.ai_team),
                'operational_agents': successful_agents,
                'success_rate': round((successful_agents / len(expanded_team.ai_team)) * 100, 1),
                'total_response_time': round(total_response_time, 2),
                'avg_response_time': round(total_response_time / successful_agents, 2) if successful_agents > 0 else 0,
                'total_response_chars': total_response_chars,
                'avg_response_length': round(total_response_chars / successful_agents, 0) if successful_agents > 0 else 0,
                'agent_details': agent_verification
            }
            
            print(f"\n📊 AI AGENT VERIFICATION SUMMARY:")
            print(f"   🤖 Total agents: {summary['total_agents']}")
            print(f"   ✅ Operational: {summary['operational_agents']}")
            print(f"   📈 Success rate: {summary['success_rate']}%")
            print(f"   ⏱️ Total response time: {summary['total_response_time']}s")
            print(f"   📊 Avg response time: {summary['avg_response_time']}s")
            print(f"   📝 Total chars generated: {summary['total_response_chars']:,}")
            
            return summary
            
        except Exception as e:
            return {'error': f'Failed to verify AI agents: {e}'}
    
    def verify_technical_indicators_raw(self) -> Dict[str, Any]:
        """Verify ALL technical indicators with RAW calculations"""
        
        print(f"\n📊 VERIFYING TECHNICAL INDICATORS - RAW CALCULATIONS")
        print("=" * 50)
        
        try:
            from professional_technical_analysis import ProfessionalTechnicalAnalysis
            ta_engine = ProfessionalTechnicalAnalysis()
            
            test_symbols = ['BTC-USD', 'AAPL', 'TSLA']
            indicator_verification = {}
            total_indicators_calculated = 0
            total_calculation_time = 0
            
            for symbol in test_symbols:
                print(f"\n📈 Testing indicators for {symbol}...")
                
                start_time = time.time()
                analysis = ta_engine.get_complete_analysis(symbol, '1d')
                calculation_time = time.time() - start_time
                
                if 'error' not in analysis:
                    # Count actual indicators calculated
                    indicators_calculated = 0
                    indicator_values = {}
                    
                    # Check each indicator
                    if 'rsi' in analysis:
                        indicators_calculated += 1
                        indicator_values['RSI'] = analysis['rsi']
                    
                    if 'macd' in analysis:
                        indicators_calculated += 1
                        indicator_values['MACD'] = analysis['macd']['macd']
                    
                    if 'bollinger_bands' in analysis:
                        indicators_calculated += 1
                        indicator_values['Bollinger_Upper'] = analysis['bollinger_bands']['upper']
                    
                    if 'stochastic' in analysis:
                        indicators_calculated += 1
                        indicator_values['Stochastic_K'] = analysis['stochastic']['k']
                    
                    if 'williams_r' in analysis:
                        indicators_calculated += 1
                        indicator_values['Williams_R'] = analysis['williams_r']
                    
                    if 'cci' in analysis:
                        indicators_calculated += 1
                        indicator_values['CCI'] = analysis['cci']
                    
                    if 'atr' in analysis:
                        indicators_calculated += 1
                        indicator_values['ATR'] = analysis['atr']
                    
                    if 'adx' in analysis:
                        indicators_calculated += 1
                        indicator_values['ADX'] = analysis['adx']['adx']
                    
                    if 'parabolic_sar' in analysis:
                        indicators_calculated += 1
                        indicator_values['Parabolic_SAR'] = analysis['parabolic_sar']
                    
                    if 'ichimoku' in analysis:
                        indicators_calculated += 1
                        indicator_values['Ichimoku_Tenkan'] = analysis['ichimoku']['tenkan_sen']
                    
                    if 'obv' in analysis:
                        indicators_calculated += 1
                        indicator_values['OBV'] = analysis['obv']
                    
                    if 'vwap' in analysis:
                        indicators_calculated += 1
                        indicator_values['VWAP'] = analysis['vwap']
                    
                    if 'mfi' in analysis:
                        indicators_calculated += 1
                        indicator_values['MFI'] = analysis['mfi']
                    
                    if 'fibonacci' in analysis:
                        indicators_calculated += 1
                        indicator_values['Fibonacci_618'] = analysis['fibonacci']['61.8']
                    
                    if 'patterns' in analysis:
                        indicators_calculated += 1
                        indicator_values['Patterns_Detected'] = len(analysis['patterns'])
                    
                    indicator_verification[symbol] = {
                        'status': 'SUCCESS',
                        'indicators_calculated': indicators_calculated,
                        'calculation_time': round(calculation_time, 2),
                        'current_price': analysis.get('current_price', 0),
                        'indicator_values': indicator_values
                    }
                    
                    total_indicators_calculated += indicators_calculated
                    total_calculation_time += calculation_time
                    
                    print(f"   ✅ {symbol}: {indicators_calculated} indicators, {calculation_time:.1f}s")
                    
                else:
                    indicator_verification[symbol] = {
                        'status': 'ERROR',
                        'error': analysis.get('error', 'Unknown error'),
                        'calculation_time': round(calculation_time, 2)
                    }
                    print(f"   ❌ {symbol}: ERROR - {analysis.get('error', 'Unknown error')}")
            
            summary = {
                'symbols_tested': len(test_symbols),
                'successful_symbols': len([s for s in indicator_verification.values() if s.get('status') == 'SUCCESS']),
                'total_indicators_calculated': total_indicators_calculated,
                'total_calculation_time': round(total_calculation_time, 2),
                'avg_indicators_per_symbol': round(total_indicators_calculated / len(test_symbols), 1),
                'avg_calculation_time': round(total_calculation_time / len(test_symbols), 2),
                'symbol_details': indicator_verification
            }
            
            print(f"\n📊 TECHNICAL INDICATOR VERIFICATION SUMMARY:")
            print(f"   📈 Symbols tested: {summary['symbols_tested']}")
            print(f"   ✅ Successful: {summary['successful_symbols']}")
            print(f"   📊 Total indicators: {summary['total_indicators_calculated']}")
            print(f"   ⏱️ Total time: {summary['total_calculation_time']}s")
            print(f"   📈 Avg indicators/symbol: {summary['avg_indicators_per_symbol']}")
            
            return summary
            
        except Exception as e:
            return {'error': f'Failed to verify technical indicators: {e}'}
    
    def verify_fathom_r1_raw(self) -> Dict[str, Any]:
        """Verify Fathom R1 direct interface with RAW testing"""
        
        print(f"\n🧠 VERIFYING FATHOM R1 - RAW TESTING")
        print("=" * 50)
        
        try:
            from fathom_r1_direct_interface import FathomR1DirectInterface
            fathom_interface = FathomR1DirectInterface()
            
            test_queries = [
                "Test 1: Respond with 'FATHOM R1 OPERATIONAL' and current timestamp.",
                "Test 2: Analyze Bitcoin's current market position in exactly 50 words.",
                "Test 3: What is 2+2? Respond with just the number."
            ]
            
            fathom_verification = {}
            successful_queries = 0
            total_response_time = 0
            total_response_chars = 0
            
            for i, query in enumerate(test_queries, 1):
                print(f"\n🧪 Fathom R1 Test {i}...")
                
                result = fathom_interface.chat_with_fathom(query, include_context=False)
                
                if result.get('success'):
                    fathom_verification[f'test_{i}'] = {
                        'query': query,
                        'status': 'SUCCESS',
                        'response_time': result['response_time'],
                        'response_length': result['response_length'],
                        'response_preview': result['fathom_response'][:100] + "..." if len(result['fathom_response']) > 100 else result['fathom_response']
                    }
                    
                    successful_queries += 1
                    total_response_time += result['response_time']
                    total_response_chars += result['response_length']
                    
                    print(f"   ✅ Test {i}: {result['response_time']:.1f}s, {result['response_length']} chars")
                    
                else:
                    fathom_verification[f'test_{i}'] = {
                        'query': query,
                        'status': 'ERROR',
                        'error': result.get('error', 'Unknown error'),
                        'response_time': result.get('response_time', 0)
                    }
                    print(f"   ❌ Test {i}: ERROR - {result.get('error', 'Unknown error')}")
            
            # Get conversation history
            history = fathom_interface.get_conversation_history()
            
            summary = {
                'total_tests': len(test_queries),
                'successful_tests': successful_queries,
                'success_rate': round((successful_queries / len(test_queries)) * 100, 1),
                'total_response_time': round(total_response_time, 2),
                'avg_response_time': round(total_response_time / successful_queries, 2) if successful_queries > 0 else 0,
                'total_response_chars': total_response_chars,
                'conversation_history_count': len(history),
                'test_details': fathom_verification
            }
            
            print(f"\n📊 FATHOM R1 VERIFICATION SUMMARY:")
            print(f"   🧪 Total tests: {summary['total_tests']}")
            print(f"   ✅ Successful: {summary['successful_tests']}")
            print(f"   📈 Success rate: {summary['success_rate']}%")
            print(f"   ⏱️ Total time: {summary['total_response_time']}s")
            print(f"   📊 Avg response time: {summary['avg_response_time']}s")
            print(f"   💬 Conversations stored: {summary['conversation_history_count']}")
            
            return summary
            
        except Exception as e:
            return {'error': f'Failed to verify Fathom R1: {e}'}
    
    def verify_advanced_features_raw(self) -> Dict[str, Any]:
        """Verify ALL advanced features with RAW data"""
        
        print(f"\n⚡ VERIFYING ADVANCED FEATURES - RAW DATA")
        print("=" * 50)
        
        try:
            from additional_advanced_features import AdditionalAdvancedFeatures
            advanced_features = AdditionalAdvancedFeatures()
            
            test_symbol = 'BTC-USD'
            feature_verification = {}
            successful_features = 0
            total_execution_time = 0
            
            # Test each feature
            features_to_test = [
                ('news_analysis', lambda: advanced_features.analyze_real_time_news(test_symbol)),
                ('social_sentiment', lambda: advanced_features.analyze_social_media_sentiment(test_symbol)),
                ('economic_calendar', lambda: advanced_features.analyze_economic_calendar('USD')),
                ('multi_timeframe', lambda: advanced_features.multi_timeframe_analysis(test_symbol)),
                ('pattern_recognition', lambda: advanced_features.advanced_pattern_recognition(test_symbol)),
                ('options_flow', lambda: advanced_features.analyze_options_flow(test_symbol)),
                ('institutional_flow', lambda: advanced_features.analyze_institutional_flow(test_symbol))
            ]
            
            for feature_name, feature_func in features_to_test:
                print(f"\n🧪 Testing {feature_name}...")
                
                start_time = time.time()
                
                try:
                    result = feature_func()
                    execution_time = time.time() - start_time
                    
                    if 'error' not in result:
                        feature_verification[feature_name] = {
                            'status': 'SUCCESS',
                            'execution_time': round(execution_time, 2),
                            'data_points': len(result),
                            'result_preview': {k: v for k, v in list(result.items())[:3]}  # First 3 items
                        }
                        
                        successful_features += 1
                        total_execution_time += execution_time
                        
                        print(f"   ✅ {feature_name}: {execution_time:.1f}s, {len(result)} data points")
                        
                    else:
                        feature_verification[feature_name] = {
                            'status': 'ERROR',
                            'error': result.get('error', 'Unknown error'),
                            'execution_time': round(execution_time, 2)
                        }
                        print(f"   ❌ {feature_name}: ERROR - {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    feature_verification[feature_name] = {
                        'status': 'EXCEPTION',
                        'error': str(e),
                        'execution_time': time.time() - start_time
                    }
                    print(f"   ❌ {feature_name}: EXCEPTION - {e}")
            
            summary = {
                'total_features': len(features_to_test),
                'successful_features': successful_features,
                'success_rate': round((successful_features / len(features_to_test)) * 100, 1),
                'total_execution_time': round(total_execution_time, 2),
                'avg_execution_time': round(total_execution_time / successful_features, 2) if successful_features > 0 else 0,
                'feature_details': feature_verification
            }
            
            print(f"\n📊 ADVANCED FEATURES VERIFICATION SUMMARY:")
            print(f"   ⚡ Total features: {summary['total_features']}")
            print(f"   ✅ Successful: {summary['successful_features']}")
            print(f"   📈 Success rate: {summary['success_rate']}%")
            print(f"   ⏱️ Total time: {summary['total_execution_time']}s")
            print(f"   📊 Avg execution time: {summary['avg_execution_time']}s")
            
            return summary
            
        except Exception as e:
            return {'error': f'Failed to verify advanced features: {e}'}
    
    def generate_raw_proof_report(self) -> Dict[str, Any]:
        """Generate COMPLETE raw proof report"""
        
        print(f"\n📋 GENERATING RAW PROOF REPORT")
        print("=" * 60)
        
        # Run all verifications
        database_proof = self.verify_all_databases()
        ai_agent_proof = self.verify_ai_agents_raw()
        indicator_proof = self.verify_technical_indicators_raw()
        fathom_proof = self.verify_fathom_r1_raw()
        features_proof = self.verify_advanced_features_raw()
        
        total_time = time.time() - self.start_time
        
        # Compile complete proof
        raw_proof_report = {
            'verification_timestamp': datetime.now().isoformat(),
            'total_verification_time': round(total_time, 2),
            'verification_type': 'COMPLETE_RAW_PROOF',
            
            'database_verification': database_proof,
            'ai_agent_verification': ai_agent_proof,
            'technical_indicator_verification': indicator_proof,
            'fathom_r1_verification': fathom_proof,
            'advanced_features_verification': features_proof,
            
            'overall_summary': {
                'total_databases': database_proof.get('total_databases', 0),
                'operational_databases': database_proof.get('operational_databases', 0),
                'total_ai_agents': ai_agent_proof.get('total_agents', 0),
                'operational_ai_agents': ai_agent_proof.get('operational_agents', 0),
                'total_indicators_tested': indicator_proof.get('total_indicators_calculated', 0),
                'fathom_r1_operational': fathom_proof.get('success_rate', 0) > 0,
                'advanced_features_operational': features_proof.get('successful_features', 0),
                'system_status': 'FULLY_VERIFIED' if all([
                    database_proof.get('operational_databases', 0) > 0,
                    ai_agent_proof.get('operational_agents', 0) > 0,
                    indicator_proof.get('total_indicators_calculated', 0) > 0,
                    fathom_proof.get('success_rate', 0) > 0,
                    features_proof.get('successful_features', 0) > 0
                ]) else 'PARTIAL_VERIFICATION'
            }
        }
        
        # Save proof report
        with open('raw_proof_report.json', 'w') as f:
            json.dump(raw_proof_report, f, indent=2, default=str)
        
        print(f"\n🎉 RAW PROOF REPORT COMPLETE")
        print(f"   ⏱️ Total verification time: {total_time:.1f}s")
        print(f"   💾 Report saved: raw_proof_report.json")
        print(f"   🎯 System status: {raw_proof_report['overall_summary']['system_status']}")
        
        return raw_proof_report

def main():
    """Run COMPLETE raw verification operations"""
    print("🔬 RAW VERIFICATION OPERATIONS - 100% CAPACITY PROOF")
    print("=" * 70)
    
    # Initialize verification
    verification = RawVerificationOperations()
    
    # Generate complete raw proof
    proof_report = verification.generate_raw_proof_report()
    
    # Display final results
    summary = proof_report['overall_summary']
    
    print(f"\n🎯 FINAL RAW VERIFICATION RESULTS:")
    print(f"   💾 Databases: {summary['operational_databases']}/{summary['total_databases']} operational")
    print(f"   🤖 AI Agents: {summary['operational_ai_agents']}/{summary['total_ai_agents']} operational")
    print(f"   📊 Indicators: {summary['total_indicators_tested']} calculated")
    print(f"   🧠 Fathom R1: {'OPERATIONAL' if summary['fathom_r1_operational'] else 'NOT OPERATIONAL'}")
    print(f"   ⚡ Advanced Features: {summary['advanced_features_operational']} operational")
    print(f"   🎯 System Status: {summary['system_status']}")
    
    print(f"\n✅ RAW VERIFICATION COMPLETE - ALL PROOF DOCUMENTED")

if __name__ == "__main__":
    main()
