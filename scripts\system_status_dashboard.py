#!/usr/bin/env python3
"""
Noryon AI System Status Dashboard
Real-time monitoring of all system components
"""

import os
import sys
import time
import json
import psutil
import requests
from pathlib import Path
from datetime import datetime
import subprocess

class SystemStatusDashboard:
    """Real-time system status monitoring"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.status_data = {}
        
    def check_training_progress(self):
        """Check training progress for all models"""
        training_status = {}
        
        # Check for model directories
        models_dir = self.project_root / "models"
        if models_dir.exists():
            for model_dir in models_dir.iterdir():
                if model_dir.is_dir():
                    model_name = model_dir.name
                    
                    # Check for training info
                    training_info_file = model_dir / "training_info.yaml"
                    if training_info_file.exists():
                        training_status[model_name] = {
                            "status": "completed",
                            "path": str(model_dir),
                            "completed_at": training_info_file.stat().st_mtime
                        }
                    else:
                        training_status[model_name] = {
                            "status": "in_progress" if any(model_dir.glob("*.safetensors")) else "not_started",
                            "path": str(model_dir)
                        }
        
        return training_status
    
    def check_llm_server_status(self):
        """Check LLM server status"""
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    "status": "running",
                    "model_loaded": data.get("model_loaded", False),
                    "url": "http://localhost:8000"
                }
        except:
            pass
        
        return {"status": "not_running"}
    
    def check_system_resources(self):
        """Check system resource usage"""
        return {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "gpu_available": self.check_gpu_status()
        }
    
    def check_gpu_status(self):
        """Check GPU availability and usage"""
        try:
            import torch
            if torch.cuda.is_available():
                return {
                    "available": True,
                    "device_count": torch.cuda.device_count(),
                    "current_device": torch.cuda.current_device(),
                    "device_name": torch.cuda.get_device_name()
                }
        except:
            pass
        
        return {"available": False}
    
    def check_running_processes(self):
        """Check for running training processes"""
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'train_models' in cmdline or 'python' in proc.info['name'] and 'train' in cmdline:
                    processes.append({
                        "pid": proc.info['pid'],
                        "name": proc.info['name'],
                        "command": cmdline[:100] + "..." if len(cmdline) > 100 else cmdline
                    })
            except:
                continue
        
        return processes
    
    def check_integration_status(self):
        """Check integration test status"""
        try:
            # Run quick integration test
            result = subprocess.run([
                sys.executable, "test_system_integration.py"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                return {"status": "passing", "last_check": datetime.now().isoformat()}
            else:
                return {"status": "failing", "error": result.stderr[:200]}
        except subprocess.TimeoutExpired:
            return {"status": "timeout"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_comprehensive_status(self):
        """Get comprehensive system status"""
        print("🔍 Gathering system status...")
        
        status = {
            "timestamp": datetime.now().isoformat(),
            "training": self.check_training_progress(),
            "llm_server": self.check_llm_server_status(),
            "system_resources": self.check_system_resources(),
            "running_processes": self.check_running_processes(),
            "integration_tests": self.check_integration_status()
        }
        
        return status
    
    def display_status(self, status):
        """Display formatted status"""
        print("\n" + "="*80)
        print("🚀 NORYON AI SYSTEM STATUS DASHBOARD")
        print("="*80)
        print(f"📅 Last Updated: {status['timestamp']}")
        
        # Training Status
        print("\n📚 MODEL TRAINING STATUS:")
        print("-" * 40)
        training = status['training']
        if training:
            for model_name, info in training.items():
                status_icon = {
                    "completed": "✅",
                    "in_progress": "🔄", 
                    "not_started": "⏳"
                }.get(info['status'], "❓")
                print(f"{status_icon} {model_name}: {info['status'].upper()}")
        else:
            print("⏳ No training data found")
        
        # LLM Server Status
        print("\n🧠 LLM SERVER STATUS:")
        print("-" * 40)
        llm = status['llm_server']
        if llm['status'] == 'running':
            model_status = "✅ Loaded" if llm.get('model_loaded') else "🔄 Loading"
            print(f"✅ Server: Running ({llm['url']})")
            print(f"{model_status.split()[0]} Model: {model_status.split()[1]}")
        else:
            print("❌ Server: Not Running")
        
        # System Resources
        print("\n💻 SYSTEM RESOURCES:")
        print("-" * 40)
        resources = status['system_resources']
        cpu_icon = "🔥" if resources['cpu_percent'] > 80 else "✅"
        mem_icon = "🔥" if resources['memory_percent'] > 80 else "✅"
        disk_icon = "🔥" if resources['disk_usage'] > 80 else "✅"
        
        print(f"{cpu_icon} CPU: {resources['cpu_percent']:.1f}%")
        print(f"{mem_icon} Memory: {resources['memory_percent']:.1f}%")
        print(f"{disk_icon} Disk: {resources['disk_usage']:.1f}%")
        
        gpu = resources['gpu_available']
        if gpu['available']:
            print(f"🎮 GPU: {gpu['device_name']} (Available)")
        else:
            print("💻 GPU: Not Available (Using CPU)")
        
        # Running Processes
        print("\n⚙️ ACTIVE TRAINING PROCESSES:")
        print("-" * 40)
        processes = status['running_processes']
        if processes:
            for proc in processes:
                print(f"🔄 PID {proc['pid']}: {proc['command']}")
        else:
            print("⏸️ No active training processes")
        
        # Integration Tests
        print("\n🧪 INTEGRATION TESTS:")
        print("-" * 40)
        integration = status['integration_tests']
        test_icon = {
            "passing": "✅",
            "failing": "❌",
            "timeout": "⏱️",
            "error": "🚫"
        }.get(integration['status'], "❓")
        print(f"{test_icon} Status: {integration['status'].upper()}")
        
        if integration['status'] in ['failing', 'error'] and 'error' in integration:
            print(f"   Error: {integration['error']}")
    
    def run_continuous_monitoring(self, interval=30):
        """Run continuous monitoring"""
        print("🔄 Starting continuous monitoring (Ctrl+C to stop)")
        
        try:
            while True:
                os.system('cls' if os.name == 'nt' else 'clear')
                status = self.get_comprehensive_status()
                self.display_status(status)
                
                print(f"\n⏱️ Next update in {interval} seconds...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")

def main():
    """Main function"""
    dashboard = SystemStatusDashboard()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        dashboard.run_continuous_monitoring()
    else:
        status = dashboard.get_comprehensive_status()
        dashboard.display_status(status)
        
        print("\n💡 TIP: Run with --continuous for real-time monitoring")
        print("   python system_status_dashboard.py --continuous")

if __name__ == "__main__":
    main()
