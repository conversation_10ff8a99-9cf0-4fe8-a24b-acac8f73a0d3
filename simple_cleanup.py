#!/usr/bin/env python3
"""
Simple System Cleanup - Remove junk and organize
"""

import os
import shutil
import glob
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

console = Console()

def remove_junk_files():
    """Remove junk files"""
    junk_patterns = [
        "*.tmp", "*.temp", "*~", "*.bak", "*.backup",
        "temp_*.py", "test_*.py.bak", "old_*.py", 
        "backup_*.py", "duplicate_*.py"
    ]
    
    removed = []
    for pattern in junk_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                removed.append(file)
            except:
                pass
    
    return removed

def remove_empty_dirs():
    """Remove empty directories"""
    removed = []
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    removed.append(dir_path)
            except:
                pass
    return removed

def create_quick_start():
    """Create quick start file"""
    content = """# NORYON AI TRADING SYSTEM - QUICK START

## READY TO USE COMMANDS:

1. Start Paper Trading:
   python start_paper_trading.py --quick-start

2. Monitor Performance:
   python live_dashboard.py

3. Test AI Models:
   python ensemble_voting_system.py --test-all

4. Check System Status:
   python final_system_status.py

## YOUR SYSTEM STATUS:
- 26 AI Models Ready
- Ensemble Voting System Operational
- Live Dashboard Running
- Enterprise Architecture Complete
- Advanced Risk Management Active

## READY TO TRADE!
Your Noryon AI Trading System is fully operational!
"""
    
    with open("QUICK_START.md", 'w', encoding='utf-8') as f:
        f.write(content)

def organize_main_files():
    """Keep main files in root for easy access"""
    main_files = [
        "start_paper_trading.py",
        "live_dashboard.py", 
        "ensemble_voting_system.py",
        "final_system_status.py",
        "SYSTEM_READY_SUMMARY.md",
        "QUICK_START.md"
    ]
    
    console.print("[green]Main operational files kept in root directory:[/green]")
    for file in main_files:
        if Path(file).exists():
            console.print(f"  ✅ {file}")
        else:
            console.print(f"  ❌ {file} (missing)")

def main():
    """Main cleanup"""
    console.print(Panel(
        "[bold blue]SYSTEM CLEANUP & ORGANIZATION[/bold blue]\n\n"
        "Cleaning junk files and organizing your AI trading system",
        title="Cleanup"
    ))
    
    # Remove junk files
    removed_files = remove_junk_files()
    console.print(f"[green]✅ Removed {len(removed_files)} junk files[/green]")
    
    # Remove empty directories
    removed_dirs = remove_empty_dirs()
    console.print(f"[green]✅ Removed {len(removed_dirs)} empty directories[/green]")
    
    # Create quick start guide
    create_quick_start()
    console.print("[green]✅ Created QUICK_START.md[/green]")
    
    # Show main files
    organize_main_files()
    
    # Final summary
    console.print(Panel(
        "[bold green]CLEANUP COMPLETE![/bold green]\n\n"
        f"• Removed {len(removed_files)} junk files\n"
        f"• Removed {len(removed_dirs)} empty directories\n"
        "• Created quick start guide\n"
        "• Organized main operational files\n\n"
        "[yellow]Your system is clean and ready to trade![/yellow]",
        title="Success"
    ))
    
    # Show current important files
    console.print("\n[blue]📂 Main Files Ready to Use:[/blue]")
    important_files = [
        "start_paper_trading.py - Start trading",
        "live_dashboard.py - Monitor performance", 
        "ensemble_voting_system.py - Test AI models",
        "final_system_status.py - Check system status",
        "SYSTEM_READY_SUMMARY.md - Complete overview",
        "QUICK_START.md - Quick start guide"
    ]
    
    for file_desc in important_files:
        console.print(f"  📄 {file_desc}")
    
    console.print(Panel(
        "[bold blue]NEXT STEPS:[/bold blue]\n\n"
        "1. Read QUICK_START.md\n"
        "2. Run: python start_paper_trading.py --quick-start\n"
        "3. Monitor: python live_dashboard.py\n"
        "4. Test: python ensemble_voting_system.py --test-all\n\n"
        "[green]Your AI trading system is ready![/green]",
        title="Ready to Trade"
    ))

if __name__ == "__main__":
    main()
