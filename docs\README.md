# Noryon Trading AI System

🚀 **Universal, Flexible, Broker-Agnostic Trading AI Platform**

Noryon is a sophisticated trading AI system designed to work with any broker, exchange, or trading platform through a universal adapter pattern. Built for flexibility, scalability, and ease of use.

## ✨ Key Features

### 🌐 Universal Broker Support
- **Broker-Agnostic Architecture**: Works with any broker through standardized adapters
- **Pre-built Adapters**: Binance, Coinbase, Interactive Brokers, Alpaca, OANDA, and more
- **Easy Integration**: Add new brokers with minimal code changes
- **Multi-Broker Trading**: Trade across multiple brokers simultaneously

### 🤖 Advanced AI Engine
- **Hybrid AI System**: Combines local models (Qwen-2.5-7B) with API models (GPT-4, Claude)
- **Multi-Modal Analysis**: Technical, fundamental, and sentiment analysis
- **Ensemble Decision Making**: Multiple AI models vote on trading decisions
- **Adaptive Learning**: Continuously improves based on market performance

### 📊 Comprehensive Portfolio Management
- **Cross-Broker Position Tracking**: Unified view of all positions
- **Smart Order Routing**: Automatically routes orders to optimal brokers
- **Advanced Risk Management**: Position sizing, stop-loss, drawdown protection
- **Real-Time Monitoring**: Live portfolio metrics and performance tracking

### 🛡️ Enterprise-Grade Security
- **Encrypted Credentials**: All API keys and secrets are encrypted at rest
- **Secure Configuration**: Environment-based configuration management
- **Audit Logging**: Complete audit trail of all trading activities
- **Risk Controls**: Multiple layers of risk management and validation

### 🎯 Easy Setup & Configuration
- **Interactive Setup Wizard**: Guided configuration for all components
- **Template-Based Broker Setup**: Pre-configured templates for popular brokers
- **Environment Management**: Development, staging, and production environments
- **One-Command Deployment**: Get started with a single command

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Noryon Trading AI System                 │
├─────────────────────────────────────────────────────────────┤
│  📊 Portfolio Manager  │  🤖 AI Engine  │  ⚙️ Config Mgr   │
├─────────────────────────────────────────────────────────────┤
│                 🔌 Universal Broker Interface               │
├─────────────────────────────────────────────────────────────┤
│  📈 Binance  │  🏦 IB  │  💰 Alpaca  │  🌍 OANDA  │  ➕ More │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

1. **Universal Broker Interface**: Standardized API for all broker interactions
2. **Broker Registry**: Dynamic discovery and management of broker adapters
3. **Portfolio Manager**: Cross-broker position tracking and order routing
4. **Configuration Manager**: Secure, environment-based configuration
5. **Setup Wizard**: Interactive system configuration and broker setup

## 🚀 Quick Start

### Prerequisites

- Python 3.9 or higher
- Git
- At least 8GB RAM (16GB recommended for AI models)
- Internet connection for API access

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/noryon-trading-ai.git
   cd noryon-trading-ai
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run setup wizard**:
   ```bash
   python main.py --setup
   ```

5. **Start the system**:
   ```bash
   python main.py
   ```

### Setup Wizard

The interactive setup wizard will guide you through:

1. **Environment Configuration**: Choose development, staging, or production
2. **Trading Parameters**: Risk limits, position sizing, stop-loss settings
3. **AI Configuration**: Local models, API models, confidence thresholds
4. **Broker Setup**: Configure brokers and enter API credentials
5. **Validation**: Verify configuration and test connections

## 📋 Supported Brokers

### Cryptocurrency Exchanges
- ✅ **Binance**: World's largest crypto exchange
- ✅ **Coinbase Pro**: Professional crypto trading
- 🔄 **Kraken**: European crypto exchange (coming soon)
- 🔄 **FTX**: Derivatives and spot trading (coming soon)

### Traditional Brokers
- ✅ **Interactive Brokers**: Stocks, forex, options, futures
- ✅ **Alpaca**: Commission-free stock trading
- ✅ **OANDA**: Forex and CFD trading
- 🔄 **TD Ameritrade**: Full-service brokerage (coming soon)

### Adding Custom Brokers

Easily add support for any broker by implementing the `UniversalBrokerInterface`:

```python
from core.interfaces.broker_interface import UniversalBrokerInterface

class MyBrokerAdapter(UniversalBrokerInterface):
    async def connect(self) -> bool:
        # Implement connection logic
        pass
    
    async def place_order(self, order: UniversalOrder) -> OrderResult:
        # Implement order placement
        pass
    
    # ... implement other required methods
```

## ⚙️ Configuration

### Environment Structure

```
config/
├── environments/
│   ├── development.yaml    # Development settings
│   ├── staging.yaml       # Staging settings
│   └── production.yaml    # Production settings
├── brokers/
│   ├── binance.yaml       # Binance configuration
│   ├── interactive_brokers.yaml
│   └── custom_broker.yaml
├── risk/
│   └── trading.yaml       # Risk management settings
├── ai/
│   └── models.yaml        # AI model configuration
└── credentials/
    ├── brokers.json       # Encrypted API credentials
    └── .key              # Encryption key
```

### Key Configuration Files

#### Trading Configuration (`config/risk/trading.yaml`)
```yaml
max_position_size: 0.02      # 2% of account per position
max_daily_loss: 0.05         # 5% daily loss limit
max_drawdown: 0.10           # 10% maximum drawdown
risk_per_trade: 0.01         # 1% risk per trade
max_open_positions: 5        # Maximum concurrent positions
enable_stop_loss: true
enable_take_profit: true
default_stop_loss_pips: 50
default_take_profit_pips: 100
```

#### AI Configuration (`config/ai/models.yaml`)
```yaml
local_model_name: "qwen-2.5-7b"
local_model_path: "models/qwen-2.5-7b"
api_models:
  - "gpt-4"
  - "claude-3"
confidence_threshold: 0.7
ensemble_voting: true
max_api_calls_per_hour: 100
enable_fine_tuning: true
```

## 🤖 AI Engine

### Hybrid AI Architecture

Noryon uses a sophisticated hybrid AI system that combines:

1. **Local Models**: Fast, private inference using Qwen-2.5-7B
2. **API Models**: Advanced reasoning with GPT-4, Claude-3
3. **Ensemble Voting**: Multiple models vote on trading decisions
4. **Confidence Scoring**: Only execute trades above confidence threshold

### Analysis Types

- **Technical Analysis**: 50+ technical indicators, pattern recognition
- **Fundamental Analysis**: Economic data, earnings, news sentiment
- **Market Microstructure**: Order book analysis, volume profiling
- **Cross-Asset Analysis**: Correlation analysis, regime detection

### Decision Process

```
📊 Market Data → 🔍 Technical Analysis → 🤖 Local AI Model
                                                    ↓
📰 News Data → 💭 Sentiment Analysis → 🌐 API AI Models → 🗳️ Ensemble Vote → 📈 Trading Signal
                                                    ↑
💰 Economic Data → 📊 Fundamental Analysis → 🧠 Risk Assessment
```

## 📊 Portfolio Management

### Features

- **Multi-Broker Positions**: Track positions across all connected brokers
- **Smart Order Routing**: Automatically route orders to optimal brokers
- **Risk Management**: Real-time risk monitoring and position sizing
- **Performance Analytics**: Comprehensive performance metrics and reporting

### Risk Management

- **Position Sizing**: Kelly Criterion, fixed fractional, volatility-based
- **Stop Loss**: Dynamic stops, trailing stops, time-based exits
- **Drawdown Protection**: Maximum drawdown limits with automatic shutdown
- **Correlation Limits**: Prevent over-concentration in correlated assets

### Performance Metrics

- **Returns**: Total return, annualized return, risk-adjusted returns
- **Risk Metrics**: Sharpe ratio, maximum drawdown, volatility
- **Trade Analytics**: Win rate, profit factor, average win/loss
- **Benchmark Comparison**: Compare against market indices

## 🛠️ Development

### Project Structure

```
noryon/
├── main.py                 # Main application entry point
├── requirements.txt        # Python dependencies
├── README.md              # This file
├── core/                  # Core system components
│   ├── interfaces/        # Universal broker interface
│   ├── registry/          # Broker discovery and management
│   ├── portfolio/         # Portfolio management
│   ├── config/           # Configuration management
│   └── setup/            # Setup wizard
├── adapters/             # Broker-specific adapters
│   ├── crypto/           # Cryptocurrency exchanges
│   ├── traditional/      # Traditional brokers
│   └── forex/           # Forex brokers
├── ai/                   # AI engine components
│   ├── models/          # AI model implementations
│   ├── analysis/        # Analysis modules
│   └── signals/         # Signal generation
├── data/                # Data storage
├── logs/                # Application logs
├── config/              # Configuration files
└── tests/               # Test suite
```

### Adding New Brokers

1. **Create Adapter**: Implement `UniversalBrokerInterface`
2. **Add Configuration**: Create broker YAML configuration
3. **Register Broker**: Add to broker registry
4. **Test Integration**: Verify all functionality works

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=core --cov=adapters

# Run specific test file
pytest tests/test_portfolio_manager.py
```

### Code Quality

```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8 .

# Type checking
mypy core/ adapters/
```

## 🚀 Deployment

### Docker Deployment

```bash
# Build image
docker build -t noryon-trading-ai .

# Run container
docker run -d \
  --name noryon-ai \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  noryon-trading-ai
```

### Docker Compose

```yaml
version: '3.8'
services:
  noryon-ai:
    build: .
    volumes:
      - ./config:/app/config
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - ENVIRONMENT=production
    restart: unless-stopped
  
  redis:
    image: redis:alpine
    restart: unless-stopped
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: noryon
      POSTGRES_USER: noryon
      POSTGRES_PASSWORD: your_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### Production Considerations

- **Security**: Use environment variables for sensitive data
- **Monitoring**: Set up Prometheus/Grafana for system monitoring
- **Backup**: Regular backups of configuration and trading data
- **Scaling**: Use Kubernetes for horizontal scaling
- **Logging**: Centralized logging with ELK stack

## 📈 Usage Examples

### Basic Trading Setup

```python
from core.config.config_manager import ConfigManager
from core.portfolio.universal_portfolio_manager import UniversalPortfolioManager
from adapters.crypto.binance import BinanceAdapter

# Initialize system
config = ConfigManager(environment="development")
portfolio = UniversalPortfolioManager(config)

# Add broker
binance = BinanceAdapter(config.get_broker_credentials("binance"))
portfolio.register_broker("binance", binance)

# Place order
result = await portfolio.place_order(
    symbol="BTCUSDT",
    side=OrderSide.BUY,
    quantity=0.001,
    order_type=OrderType.MARKET
)
```

### Custom Strategy Implementation

```python
from core.interfaces.broker_interface import OrderSide, OrderType

class MyTradingStrategy:
    def __init__(self, portfolio_manager):
        self.portfolio = portfolio_manager
    
    async def analyze_market(self, symbol: str):
        # Implement your analysis logic
        market_data = await self.get_market_data(symbol)
        signal = self.generate_signal(market_data)
        return signal
    
    async def execute_trade(self, symbol: str, signal: dict):
        if signal['action'] == 'BUY':
            await self.portfolio.place_order(
                symbol=symbol,
                side=OrderSide.BUY,
                quantity=signal['quantity'],
                order_type=OrderType.LIMIT,
                price=signal['price']
            )
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Errors**
   - Check API credentials
   - Verify network connectivity
   - Check broker API status

2. **Configuration Issues**
   - Run setup wizard again
   - Validate YAML syntax
   - Check file permissions

3. **Performance Issues**
   - Increase system resources
   - Optimize AI model settings
   - Check database performance

### Debug Mode

```bash
# Run with debug logging
python main.py --log-level DEBUG

# Check system status
python -c "from core.config.config_manager import ConfigManager; print(ConfigManager().get_config_summary())"
```

### Log Files

- **Application Logs**: `logs/trading_ai_YYYYMMDD.log`
- **Error Logs**: `logs/errors.log`
- **Trade Logs**: `logs/trades.log`
- **Performance Logs**: `logs/performance.log`

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

### Code Standards

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive tests
- Update documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.noryon.ai](https://docs.noryon.ai)
- **Discord**: [Join our community](https://discord.gg/noryon)
- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/your-org/noryon-trading-ai/issues)

## 🗺️ Roadmap

### Version 1.1 (Q1 2024)
- [ ] Advanced backtesting engine
- [ ] Strategy marketplace
- [ ] Mobile app for monitoring
- [ ] Advanced charting and analytics

### Version 1.2 (Q2 2024)
- [ ] Machine learning model marketplace
- [ ] Social trading features
- [ ] Advanced risk analytics
- [ ] Multi-language support

### Version 2.0 (Q3 2024)
- [ ] Decentralized trading protocols
- [ ] Advanced derivatives support
- [ ] Institutional features
- [ ] White-label solutions

## 🙏 Acknowledgments

- **OpenAI** for GPT models
- **Anthropic** for Claude models
- **Alibaba** for Qwen models
- **CCXT** for cryptocurrency exchange integration
- **Rich** for beautiful terminal interfaces
- **FastAPI** for high-performance APIs

---

**Built with ❤️ by the Noryon Team**

*Empowering traders with AI-driven insights and universal broker connectivity.*