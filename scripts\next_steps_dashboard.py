#!/usr/bin/env python3
"""
Next Steps Dashboard - Shows current system status and recommended actions
"""

import subprocess
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, BarColumn, TextColumn
console = Console()

def check_ollama_models():
    """Check available Ollama models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            models = [line.split()[0] for line in lines if line.strip()]
            return len(models), models
    except:
        pass
    return 0, []

def check_trained_models():
    """Check for trained HuggingFace models"""
    models_dir = Path("models")
    if not models_dir.exists():
        return 0, []
    
    trained_models = []
    for model_dir in models_dir.iterdir():
        if model_dir.is_dir() and any(model_dir.glob("*.bin")) or any(model_dir.glob("*.safetensors")):
            trained_models.append(model_dir.name)
    
    return len(trained_models), trained_models

def check_system_health():
    """Check overall system health"""
    health_checks = {
        "Python Environment": sys.version_info >= (3, 8),
        "Models Directory": Path("models").exists(),
        "Config Directory": Path("config").exists(),
        "Data Directory": Path("data").exists(),
        "Core Module": Path("core").exists(),
    }
    
    return health_checks

def main():
    """Display comprehensive system status and next steps"""
    
    console.print(Panel(
        "[bold blue]🤖 Noryon AI Trading System - Status Dashboard[/bold blue]",
        title="System Overview"
    ))
    
    # System Health Check
    health_checks = check_system_health()
    health_table = Table(title="System Health")
    health_table.add_column("Component", style="cyan")
    health_table.add_column("Status", style="green")
    
    for component, status in health_checks.items():
        status_icon = "✅" if status else "❌"
        health_table.add_row(component, f"{status_icon} {'OK' if status else 'ISSUE'}")
    
    console.print(health_table)
    
    # Model Status
    ollama_count, ollama_models = check_ollama_models()
    trained_count, trained_models = check_trained_models()
    
    model_table = Table(title="AI Models Status")
    model_table.add_column("Model Type", style="cyan")
    model_table.add_column("Count", style="green")
    model_table.add_column("Status", style="yellow")
    
    model_table.add_row("Ollama Models", str(ollama_count), "✅ Ready" if ollama_count > 0 else "⚠️ None found")
    model_table.add_row("Trained Models", str(trained_count), "✅ Ready" if trained_count > 0 else "⚠️ Need training")
    
    console.print(model_table)
    
    # Available Ollama Models (first 10)
    if ollama_models:
        console.print(f"\n[green]Available Ollama Models ({len(ollama_models)} total):[/green]")
        for i, model in enumerate(ollama_models[:10]):
            console.print(f"  {i+1}. {model}")
        if len(ollama_models) > 10:
            console.print(f"  ... and {len(ollama_models) - 10} more")
    
    # Next Steps Recommendations
    next_steps = []
    
    if trained_count == 0:
        next_steps.append("🔥 PRIORITY: Train AI models - `python start_comprehensive_training.py`")
    
    if ollama_count < 5:
        next_steps.append("📥 Download more Ollama models for ensemble trading")
    
    next_steps.extend([
        "🧪 Test system integration - `python test_system_integration.py`",
        "📊 Validate model performance - `python comprehensive_model_testing.py`",
        "🚀 Start paper trading - `python start_paper_trading.py`",
        "📈 Monitor performance - `python live_dashboard.py`"
    ])
    
    console.print(Panel(
        "\n".join(f"{i+1}. {step}" for i, step in enumerate(next_steps)),
        title="🎯 Recommended Next Steps",
        border_style="green"
    ))
    
    # Quick Actions
    console.print(Panel(
        "[bold yellow]Quick Start Commands:[/bold yellow]\n\n"
        "• Full system setup: `python setup_noryon.py --full-setup`\n"
        "• Train all models: `python start_comprehensive_training.py`\n"
        "• Test everything: `python run_end_to_end_test.py`\n"
        "• Start trading: `python start_paper_trading.py --auto-start`\n"
        "• Emergency stop: `python emergency_stop.py`",
        title="⚡ Quick Actions"
    ))
    
    # System Capabilities
    console.print(Panel(
        "[bold green]Current System Capabilities:[/bold green]\n\n"
        f"• {ollama_count} Ollama models ready for inference\n"
        f"• {trained_count} custom-trained financial models\n"
        "• Multi-model ensemble voting system\n"
        "• GPU-accelerated training pipeline\n"
        "• Real-time paper trading system\n"
        "• Advanced risk management\n"
        "• Performance monitoring dashboard",
        title="🚀 System Features"
    ))

if __name__ == "__main__":
    main()
