#!/usr/bin/env python3
"""
Optimized Model Caller - Handles timeouts and encoding properly
"""

import subprocess
import time
import os
from typing import Optional, Tuple

class OptimizedModelCaller:
    def __init__(self):
        # Set proper encoding
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
    
    def call_model_safe(self, model_name: str, prompt: str, timeout: int = 45) -> Tuple[bool, str, float]:
        """Safely call a model with proper error handling"""
        start_time = time.time()
        
        try:
            result = subprocess.run([
                'ollama', 'run', model_name, prompt
            ], 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            encoding='utf-8',
            errors='replace'  # Replace problematic characters instead of failing
            )
            
            response_time = time.time() - start_time
            
            if result.returncode == 0:
                response = result.stdout.strip()
                return True, response, response_time
            else:
                error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                return False, f"Model error: {error_msg}", response_time
                
        except subprocess.TimeoutExpired:
            response_time = time.time() - start_time
            return False, f"Timeout after {timeout}s", response_time
        except Exception as e:
            response_time = time.time() - start_time
            return False, f"Exception: {str(e)}", response_time
    
    def call_model_with_retries(self, model_name: str, prompt: str, max_retries: int = 2) -> Tuple[bool, str, float]:
        """Call model with automatic retries"""
        for attempt in range(max_retries + 1):
            success, response, response_time = self.call_model_safe(model_name, prompt)
            
            if success:
                return True, response, response_time
            
            if attempt < max_retries:
                print(f"  Retry {attempt + 1}/{max_retries} for {model_name}")
                time.sleep(2)  # Brief pause between retries
        
        return False, response, response_time

# Global instance
model_caller = OptimizedModelCaller()
