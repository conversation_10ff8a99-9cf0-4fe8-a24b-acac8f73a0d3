#!/usr/bin/env python3
"""
Finance-Focused AI Model Training System
Train AI models specifically on financial and economic data
"""

import os
import sys
import json
import asyncio
import subprocess
from pathlib import Path
from typing import Dict, List, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn
import yaml

console = Console()

class FinanceDatasetManager:
    """Manage and prepare financial datasets for training"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.datasets = self._discover_datasets()
    
    def _discover_datasets(self) -> Dict[str, Dict]:
        """Discover all available financial datasets"""
        datasets = {}
        
        # Define dataset categories and their purposes
        dataset_info = {
            "JosephgflowersFinance-Instruct-500k": {
                "type": "instruction",
                "description": "500k financial instruction dataset",
                "priority": 1,
                "specialization": "general_finance"
            },
            "BAAIIndustryInstruction_Finance-Economics": {
                "type": "instruction", 
                "description": "Finance & Economics instructions",
                "priority": 2,
                "specialization": "economics"
            },
            "paperswithbacktestStocks-Daily-Price": {
                "type": "market_data",
                "description": "Daily stock price data",
                "priority": 3,
                "specialization": "stock_analysis"
            },
            "paperswithbacktestStocks-1Min-Price": {
                "type": "market_data",
                "description": "High-frequency stock data",
                "priority": 4,
                "specialization": "technical_analysis"
            },
            "sp500_news_290k_articles.csv": {
                "type": "news_sentiment",
                "description": "290k SP500 news articles",
                "priority": 5,
                "specialization": "sentiment_analysis"
            },
            "paperswithbacktestForex-Daily-Price": {
                "type": "market_data",
                "description": "Forex market data",
                "priority": 6,
                "specialization": "forex_trading"
            },
            "0xMakatrading-candles-subset-qa-format": {
                "type": "trading_qa",
                "description": "Trading Q&A format data",
                "priority": 7,
                "specialization": "trading_decisions"
            }
        }
        
        for dataset_name, info in dataset_info.items():
            dataset_path = self.data_dir / dataset_name
            if dataset_path.exists():
                datasets[dataset_name] = {
                    **info,
                    "path": dataset_path,
                    "size": self._get_dataset_size(dataset_path)
                }
        
        return datasets
    
    def _get_dataset_size(self, path: Path) -> str:
        """Get dataset size information"""
        try:
            if path.is_file():
                size = path.stat().st_size
                return f"{size / (1024*1024):.1f} MB"
            elif path.is_dir():
                total_size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                return f"{total_size / (1024*1024):.1f} MB"
        except:
            pass
        return "Unknown"
    
    def get_training_datasets(self) -> List[str]:
        """Get prioritized list of datasets for training"""
        sorted_datasets = sorted(
            self.datasets.items(),
            key=lambda x: x[1]['priority']
        )
        return [name for name, _ in sorted_datasets]

class FinanceModelTrainer:
    """Train AI models on financial data"""
    
    def __init__(self):
        self.dataset_manager = FinanceDatasetManager()
        self.models_to_train = [
            {
                "name": "deepseek-r1",
                "model_path": "deepseek-ai/DeepSeek-R1-Distill-Llama-8B",
                "specialization": "financial_reasoning",
                "output_dir": "models/deepseek-finance-specialized"
            },
            {
                "name": "mistral",
                "model_path": "mistralai/Mistral-7B-Instruct-v0.2", 
                "specialization": "trading_strategies",
                "output_dir": "models/mistral-finance-specialized"
            },
            {
                "name": "qwen3",
                "model_path": "Qwen/Qwen2.5-7B-Instruct",
                "specialization": "market_analysis",
                "output_dir": "models/qwen3-finance-specialized"
            },
            {
                "name": "gemma3",
                "model_path": "google/gemma-2-9b-it",
                "specialization": "risk_assessment", 
                "output_dir": "models/gemma3-finance-specialized"
            },
            {
                "name": "phi4",
                "model_path": "microsoft/Phi-3-medium-4k-instruct",
                "specialization": "economic_analysis",
                "output_dir": "models/phi4-finance-specialized"
            }
        ]
    
    def display_available_data(self):
        """Display available financial datasets"""
        table = Table(title="🏦 Available Financial Datasets")
        table.add_column("Dataset", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Description", style="yellow")
        table.add_column("Size", style="magenta")
        table.add_column("Priority", style="red")
        
        for name, info in self.dataset_manager.datasets.items():
            table.add_row(
                name[:40] + "..." if len(name) > 40 else name,
                info['type'],
                info['description'],
                info['size'],
                str(info['priority'])
            )
        
        console.print(table)
    
    async def train_model_on_finance_data(self, model_config: Dict) -> bool:
        """Train a single model on financial data"""
        console.print(f"\n[green]🚀 Training {model_config['name']} for {model_config['specialization']}[/green]")
        
        # Create training script for this model
        training_script = self._create_training_script(model_config)
        script_path = Path(f"train_{model_config['name']}_finance.py")
        
        with open(script_path, 'w') as f:
            f.write(training_script)
        
        try:
            # Run training
            result = subprocess.run([
                sys.executable, str(script_path)
            ], capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode == 0:
                console.print(f"[green]✅ {model_config['name']} training completed successfully[/green]")
                return True
            else:
                console.print(f"[red]❌ {model_config['name']} training failed: {result.stderr[:200]}[/red]")
                return False
                
        except subprocess.TimeoutExpired:
            console.print(f"[yellow]⏰ {model_config['name']} training timed out[/yellow]")
            return False
        except Exception as e:
            console.print(f"[red]❌ {model_config['name']} training error: {e}[/red]")
            return False
        finally:
            # Clean up script
            if script_path.exists():
                script_path.unlink()
    
    def _create_training_script(self, model_config: Dict) -> str:
        """Create a training script for the specific model"""
        return f'''#!/usr/bin/env python3
"""
Auto-generated training script for {model_config['name']}
Specialization: {model_config['specialization']}
"""

import torch
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    TrainingArguments, Trainer,
    DataCollatorForLanguageModeling
)
from datasets import load_dataset, concatenate_datasets
from peft import LoraConfig, get_peft_model, TaskType
import os

def main():
    print("🚀 Starting {model_config['name']} training...")
    
    # Load model and tokenizer
    model_name = "{model_config['model_path']}"
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # Add padding token if missing
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Configure LoRA
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        inference_mode=False,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    
    # Load financial datasets
    datasets = []
    
    # Load main financial instruction dataset
    try:
        finance_dataset = load_dataset("json", data_files="data/JosephgflowersFinance-Instruct-500k/train/*.jsonl")
        datasets.append(finance_dataset["train"])
        print("✅ Loaded JosephgflowersFinance-Instruct-500k")
    except Exception as e:
        print(f"⚠️ Could not load JosephgflowersFinance-Instruct-500k: {{e}}")
    
    # Load economics dataset
    try:
        econ_dataset = load_dataset("json", data_files="data/BAAIIndustryInstruction_Finance-Economics/train/*.jsonl")
        datasets.append(econ_dataset["train"])
        print("✅ Loaded BAAIIndustryInstruction_Finance-Economics")
    except Exception as e:
        print(f"⚠️ Could not load economics dataset: {{e}}")
    
    if not datasets:
        print("❌ No datasets loaded successfully")
        return
    
    # Combine datasets
    combined_dataset = concatenate_datasets(datasets)
    print(f"📊 Combined dataset size: {{len(combined_dataset)}} examples")
    
    # Tokenize dataset
    def tokenize_function(examples):
        # Handle different data formats
        texts = []
        for i in range(len(examples.get('instruction', examples.get('text', [''])))):
            if 'instruction' in examples:
                text = f"Instruction: {{examples['instruction'][i]}}\\nResponse: {{examples.get('output', examples.get('response', ['']))[i]}}"
            else:
                text = examples['text'][i]
            texts.append(text)
        
        return tokenizer(
            texts,
            truncation=True,
            padding=True,
            max_length=2048,
            return_tensors="pt"
        )
    
    tokenized_dataset = combined_dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=combined_dataset.column_names
    )
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="{model_config['output_dir']}",
        num_train_epochs=2,
        per_device_train_batch_size=2,
        gradient_accumulation_steps=8,
        learning_rate=2e-5,
        weight_decay=0.01,
        logging_steps=50,
        save_steps=500,
        eval_steps=500,
        warmup_steps=100,
        fp16=True,
        dataloader_pin_memory=False,
        remove_unused_columns=False,
        report_to=None
    )
    
    # Data collator
    data_collator = DataCollatorForLanguageModeling(
        tokenizer=tokenizer,
        mlm=False
    )
    
    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer
    )
    
    # Train
    print("🔥 Starting training...")
    trainer.train()
    
    # Save model
    trainer.save_model()
    tokenizer.save_pretrained("{model_config['output_dir']}")
    
    print("✅ Training completed successfully!")

if __name__ == "__main__":
    main()
'''
    
    async def train_all_models(self) -> Dict[str, bool]:
        """Train all models on financial data"""
        console.print(Panel(
            "[bold blue]🏦 Starting Finance-Focused AI Training[/bold blue]\\n\\n"
            f"Training {{len(self.models_to_train)}} models on financial datasets\\n"
            f"Available datasets: {{len(self.dataset_manager.datasets)}}\\n"
            "Specializations: Financial reasoning, Trading strategies, Market analysis",
            title="Finance AI Training"
        ))
        
        self.display_available_data()
        
        results = {{}}
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{{task.description}}"),
            BarColumn(),
            console=console
        ) as progress:
            
            for model_config in self.models_to_train:
                task = progress.add_task(
                    f"Training {{model_config['name']}} ({{model_config['specialization']}})",
                    total=1
                )
                
                success = await self.train_model_on_finance_data(model_config)
                results[model_config['name']] = success
                
                progress.update(task, completed=1)
        
        return results

async def main():
    """Main training execution"""
    trainer = FinanceModelTrainer()
    
    console.print(Panel(
        "[bold green]🎯 Finance-Focused AI Model Training[/bold green]\\n\\n"
        "This will train your AI models specifically on:\\n"
        "• 500k+ financial instructions\\n"
        "• Economic analysis data\\n" 
        "• Stock market data\\n"
        "• Trading strategies\\n"
        "• News sentiment analysis\\n"
        "• Risk assessment data",
        title="Training Overview"
    ))
    
    # Train all models
    results = await trainer.train_all_models()
    
    # Display results
    success_count = sum(results.values())
    total_count = len(results)
    
    console.print(Panel(
        f"[bold green]📊 Training Results[/bold green]\\n\\n"
        f"Successfully trained: {{success_count}}/{{total_count}} models\\n"
        f"Success rate: {{success_count/total_count*100:.1f}}%\\n\\n"
        "Next steps:\\n"
        "• Test trained models\\n"
        "• Start ensemble trading\\n"
        "• Monitor performance",
        title="Training Complete"
    ))

if __name__ == "__main__":
    asyncio.run(main())
