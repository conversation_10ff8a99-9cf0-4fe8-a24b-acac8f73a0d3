name: Noryon AI Trading System CI/CD

# Trigger the workflow on push to main/develop branches and pull requests
on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]
  schedule:
    # Run nightly builds at 2 AM UTC
    - cron: '0 2 * * *'

# Environment variables
env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: noryon-ai-trading

jobs:
  # Code Quality and Testing
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
        test-type: ['unit', 'integration']
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: noryon_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio pytest-cov pytest-xdist
        pip install -e .
    
    - name: Create test configuration
      run: |
        cp config.yaml config_test.yaml
        sed -i 's/environment: development/environment: test/' config_test.yaml
        sed -i 's/host: localhost/host: postgres/' config_test.yaml
    
    - name: Run ${{ matrix.test-type }} tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/noryon_test
        REDIS_URL: redis://localhost:6379/0
        ENVIRONMENT: test
      run: |
        if [ "${{ matrix.test-type }}" = "unit" ]; then
          pytest tests/unit/ -v --cov=. --cov-report=xml --cov-report=html
        else
          pytest tests/integration/ -v --cov=. --cov-report=xml --cov-report=html
        fi
    
    - name: Upload coverage to Codecov
      if: matrix.python-version == '3.11' && matrix.test-type == 'unit'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}-${{ matrix.test-type }}
        path: |
          htmlcov/
          coverage.xml
          pytest-report.xml

  # Code Quality Checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 mypy bandit safety
        pip install -r requirements.txt
    
    - name: Run Black (code formatting)
      run: black --check --diff .
    
    - name: Run isort (import sorting)
      run: isort --check-only --diff .
    
    - name: Run Flake8 (linting)
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    
    - name: Run MyPy (type checking)
      run: mypy . --ignore-missing-imports --no-strict-optional
    
    - name: Run Bandit (security)
      run: bandit -r . -f json -o bandit-report.json
    
    - name: Run Safety (dependency security)
      run: safety check --json --output safety-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Performance Testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install locust pytest-benchmark
        pip install -e .
    
    - name: Run performance tests
      run: |
        python test_system.py --test-type performance
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance_results/

  # Docker Build and Push
  docker:
    name: Docker Build & Push
    runs-on: ubuntu-latest
    needs: [test, quality]
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
    
    - name: Build and push API-only image
      uses: docker/build-push-action@v5
      with:
        context: .
        target: api-only
        push: true
        tags: |
          ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}-api:${{ github.sha }}
          ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}-api:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Documentation
  docs:
    name: Build Documentation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install sphinx sphinx-rtd-theme sphinx-autodoc-typehints
        pip install -r requirements.txt
    
    - name: Build documentation
      run: |
        sphinx-build -b html docs/ docs/_build/html
    
    - name: Upload documentation
      uses: actions/upload-artifact@v3
      with:
        name: documentation
        path: docs/_build/html/
    
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build/html

  # Deployment to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, quality, docker]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install deployment dependencies
      run: |
        python -m pip install --upgrade pip
        pip install docker-compose
    
    - name: Deploy to staging
      env:
        STAGING_HOST: ${{ secrets.STAGING_HOST }}
        STAGING_USER: ${{ secrets.STAGING_USER }}
        STAGING_KEY: ${{ secrets.STAGING_SSH_KEY }}
      run: |
        echo "$STAGING_KEY" > staging_key.pem
        chmod 600 staging_key.pem
        
        # Deploy using our deployment script
        python deploy.py deploy --env staging --image-tag ${{ github.sha }}
    
    - name: Run staging health checks
      run: |
        python deploy.py health-check --env staging
    
    - name: Notify deployment status
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Deployment to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, quality, docker]
    if: github.event_name == 'release'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install deployment dependencies
      run: |
        python -m pip install --upgrade pip
        pip install docker-compose
    
    - name: Deploy to production
      env:
        PRODUCTION_HOST: ${{ secrets.PRODUCTION_HOST }}
        PRODUCTION_USER: ${{ secrets.PRODUCTION_USER }}
        PRODUCTION_KEY: ${{ secrets.PRODUCTION_SSH_KEY }}
      run: |
        echo "$PRODUCTION_KEY" > production_key.pem
        chmod 600 production_key.pem
        
        # Deploy using our deployment script
        python deploy.py deploy --env production --image-tag ${{ github.ref_name }}
    
    - name: Run production health checks
      run: |
        python deploy.py health-check --env production
    
    - name: Create deployment record
      run: |
        python deploy.py record --env production --version ${{ github.ref_name }}
    
    - name: Notify deployment status
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#production'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
    
    - name: Run CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        languages: python

  # Dependency Updates
  dependency-update:
    name: Update Dependencies
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Update dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pip-tools
        pip-compile --upgrade requirements.in
    
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'Automated dependency updates'
        body: |
          This PR updates dependencies to their latest versions.
          
          Please review the changes and ensure all tests pass before merging.
        branch: dependency-updates
        delete-branch: true

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    if: always()
    needs: [test, quality, docker, deploy-staging, deploy-production]
    
    steps:
    - name: Delete old artifacts
      uses: actions/github-script@v6
      with:
        script: |
          const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: context.runId,
          });
          
          // Keep artifacts for 7 days
          const cutoff = new Date();
          cutoff.setDate(cutoff.getDate() - 7);
          
          for (const artifact of artifacts.data.artifacts) {
            if (new Date(artifact.created_at) < cutoff) {
              await github.rest.actions.deleteArtifact({
                owner: context.repo.owner,
                repo: context.repo.repo,
                artifact_id: artifact.id,
              });
            }
          }

  # Notification
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    if: always()
    needs: [test, quality, docker, deploy-staging, deploy-production]
    
    steps:
    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: ${{ job.status }}
        channel: '#ci-cd'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
    
    - name: Notify Discord
      uses: Ilshidur/action-discord@master
      if: failure()
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
      with:
        args: '🚨 CI/CD Pipeline failed for {{ EVENT_PAYLOAD.repository.full_name }} on {{ GITHUB_REF }}'

# Workflow summary
  summary:
    name: Workflow Summary
    runs-on: ubuntu-latest
    if: always()
    needs: [test, quality, docker, deploy-staging, deploy-production]
    
    steps:
    - name: Generate summary
      run: |
        echo "## Workflow Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "| Job | Status |" >> $GITHUB_STEP_SUMMARY
        echo "|-----|--------|" >> $GITHUB_STEP_SUMMARY
        echo "| Tests | ${{ needs.test.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Quality | ${{ needs.quality.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Docker | ${{ needs.docker.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Staging Deploy | ${{ needs.deploy-staging.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Production Deploy | ${{ needs.deploy-production.result }} |" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "**Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Triggered by:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY