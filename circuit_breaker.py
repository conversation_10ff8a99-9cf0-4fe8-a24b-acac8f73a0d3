#!/usr/bin/env python3
"""
Circuit Breaker Implementation
REAL circuit breaker with exponential backoff for microservices
"""

import time
import logging
from typing import Callable, Any, Optional
from enum import Enum

logger = logging.getLogger(__name__)

class CircuitBreakerState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreaker:
    """REAL circuit breaker implementation with exponential backoff"""
    
    def __init__(self, failure_threshold: int = 3, recovery_timeout: int = 30, expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        # State tracking
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        self.next_attempt_time = 0
        
        # Exponential backoff
        self.base_timeout = recovery_timeout
        self.max_timeout = recovery_timeout * 8  # Max 4 minutes
        self.backoff_multiplier = 2
        
        logger.info(f"Circuit breaker initialized", extra={
            "failure_threshold": failure_threshold,
            "recovery_timeout": recovery_timeout
        })
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection"""
        
        current_time = time.time()
        
        # Check if we should attempt to recover
        if self.state == CircuitBreakerState.OPEN:
            if current_time >= self.next_attempt_time:
                self.state = CircuitBreakerState.HALF_OPEN
                logger.info("Circuit breaker transitioning to HALF_OPEN")
            else:
                raise Exception(f"Circuit breaker OPEN - next attempt in {self.next_attempt_time - current_time:.1f}s")
        
        try:
            # Execute the function
            result = func(*args, **kwargs)
            
            # Success - reset circuit breaker
            if self.state == CircuitBreakerState.HALF_OPEN:
                self._reset()
                logger.info("Circuit breaker reset to CLOSED after successful call")
            
            return result
            
        except self.expected_exception as e:
            self._record_failure()
            raise e
    
    def _record_failure(self):
        """Record a failure and update circuit breaker state"""
        
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        logger.warn(f"Circuit breaker failure recorded", extra={
            "failure_count": self.failure_count,
            "threshold": self.failure_threshold
        })
        
        if self.failure_count >= self.failure_threshold:
            self._open_circuit()
    
    def _open_circuit(self):
        """Open the circuit breaker"""
        
        self.state = CircuitBreakerState.OPEN
        
        # Calculate exponential backoff timeout
        backoff_factor = min(self.backoff_multiplier ** (self.failure_count - self.failure_threshold), 8)
        timeout = min(self.base_timeout * backoff_factor, self.max_timeout)
        
        self.next_attempt_time = time.time() + timeout
        
        logger.error(f"Circuit breaker OPENED", extra={
            "failure_count": self.failure_count,
            "timeout": timeout,
            "next_attempt": self.next_attempt_time
        })
    
    def _reset(self):
        """Reset circuit breaker to closed state"""
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        self.next_attempt_time = 0
    
    @property
    def is_open(self) -> bool:
        """Check if circuit breaker is open"""
        return self.state == CircuitBreakerState.OPEN
    
    @property
    def is_half_open(self) -> bool:
        """Check if circuit breaker is half-open"""
        return self.state == CircuitBreakerState.HALF_OPEN
    
    @property
    def is_closed(self) -> bool:
        """Check if circuit breaker is closed"""
        return self.state == CircuitBreakerState.CLOSED
    
    def get_status(self) -> dict:
        """Get circuit breaker status"""
        
        return {
            "state": self.state.value,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "next_attempt_time": self.next_attempt_time if self.state == CircuitBreakerState.OPEN else None,
            "time_until_retry": max(0, self.next_attempt_time - time.time()) if self.state == CircuitBreakerState.OPEN else 0
        }

def test_circuit_breaker():
    """Test circuit breaker functionality"""
    
    print("🔧 Testing Circuit Breaker Implementation")
    
    # Create circuit breaker
    cb = CircuitBreaker(failure_threshold=3, recovery_timeout=5)
    
    # Test function that fails
    def failing_function():
        raise Exception("Simulated failure")
    
    # Test function that succeeds
    def success_function():
        return "Success!"
    
    # Test failures
    print("\n📉 Testing failures...")
    for i in range(5):
        try:
            result = cb.call(failing_function)
        except Exception as e:
            print(f"   Attempt {i+1}: {e}")
            print(f"   Status: {cb.get_status()}")
    
    # Test recovery
    print(f"\n⏳ Waiting for recovery...")
    time.sleep(6)  # Wait for recovery timeout
    
    try:
        result = cb.call(success_function)
        print(f"   Recovery successful: {result}")
        print(f"   Status: {cb.get_status()}")
    except Exception as e:
        print(f"   Recovery failed: {e}")

if __name__ == "__main__":
    test_circuit_breaker()
