
# Memory optimization for models
import gc
import psutil

def optimize_memory():
    """Optimize memory usage"""
    gc.collect()
    
def check_memory():
    """Check available memory"""
    memory = psutil.virtual_memory()
    return memory.available / (1024**3)  # GB

def can_run_model(model_size_gb):
    """Check if model can run"""
    available_gb = check_memory()
    return available_gb > model_size_gb * 1.5
