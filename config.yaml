# Noryon AI Trading System Configuration
# Main configuration file for the trading system

# System Configuration
system:
  name: "Noryon AI Trading System"
  version: "1.0.0"
  environment: "development"  # development, staging, production
  debug: true
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  timezone: "UTC"
  
  # System paths
  paths:
    data_dir: "./data"
    models_dir: "./models"
    logs_dir: "./logs"
    reports_dir: "./reports"
    config_dir: "./config"
    temp_dir: "./temp"

# API Server Configuration
api:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  reload: true  # Only for development
  
  # Security settings
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    - "http://127.0.0.1:3000"
    - "http://127.0.0.1:8080"
  
  cors_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  
  cors_headers:
    - "*"
  
  # Rate limiting
  rate_limit:
    requests_per_minute: 100
    burst_size: 20
  
  # Authentication
  auth:
    api_keys:
      - "demo_key_123"  # Development key
      - "admin_key_456"  # Admin key
    jwt_secret: "your-secret-key-change-in-production"
    jwt_algorithm: "HS256"
    jwt_expiration: 3600  # 1 hour

# WebSocket Configuration
websocket:
  max_connections: 100
  heartbeat_interval: 30
  message_queue_size: 1000
  
# Trading Configuration
trading:
  # Enabled models for trading
  models:
    enabled:
      - "deepseek"
      - "mistral"
      - "qwen3"
      - "llama"
      - "gemma"
      - "phi"
    
    # Ensemble weights (must sum to 1.0)
    ensemble_weights:
      deepseek: 0.25
      mistral: 0.20
      qwen3: 0.20
      llama: 0.15
      gemma: 0.10
      phi: 0.10
    
    # Model-specific settings
    deepseek:
      model_name: "deepseek-ai/deepseek-coder-6.7b-instruct"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
      
    mistral:
      model_name: "mistralai/Mistral-7B-Instruct-v0.1"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
      
    qwen3:
      model_name: "Qwen/Qwen2.5-7B-Instruct"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
      
    llama:
      model_name: "meta-llama/Llama-2-7b-chat-hf"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
      
    gemma:
      model_name: "google/gemma-7b-it"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
      
    phi:
      model_name: "microsoft/phi-2"
      max_length: 2048
      temperature: 0.7
      top_p: 0.9
      do_sample: true
  
  # Trading parameters
  parameters:
    initial_capital: 1000000.0  # $1M
    max_positions: 20
    rebalance_frequency: "daily"  # daily, weekly, monthly
    transaction_cost: 0.001  # 0.1% transaction cost
    slippage: 0.0005  # 0.05% slippage
    
  # Position sizing
  position_sizing:
    method: "equal_weight"  # equal_weight, risk_parity, kelly, volatility_target
    max_weight: 0.1  # 10% max position size
    min_weight: 0.01  # 1% min position size
    
# Risk Management Configuration
risk:
  # Risk limits
  limits:
    max_position_size: 0.10  # 10% of portfolio
    max_portfolio_risk: 0.05  # 5% portfolio risk
    max_daily_loss: 0.02  # 2% daily loss limit
    max_drawdown: 0.15  # 15% maximum drawdown
    max_leverage: 1.0  # No leverage
    
  # VaR configuration
  var:
    confidence_level: 0.95  # 95% confidence
    time_horizon: 1  # 1 day
    method: "historical"  # historical, parametric, monte_carlo
    lookback_period: 252  # 1 year of trading days
    
  # Stress testing
  stress_test:
    scenarios:
      - name: "market_crash"
        description: "2008-style market crash"
        shock: -0.30  # -30% market shock
        
      - name: "volatility_spike"
        description: "Volatility spike scenario"
        vol_multiplier: 3.0
        
      - name: "interest_rate_shock"
        description: "Interest rate shock"
        rate_change: 0.02  # +2% rate increase
        
  # Circuit breaker settings
  circuit_breaker:
    enabled: true
    triggers:
      daily_loss_threshold: 0.05  # 5% daily loss
      consecutive_losses: 5
      volatility_threshold: 0.50  # 50% volatility spike
    cooldown_period: 3600  # 1 hour in seconds
    
# Performance Analytics Configuration
performance:
  # Benchmark settings
  benchmark:
    symbol: "SPY"  # S&P 500 ETF
    name: "S&P 500"
    
  # Performance metrics
  metrics:
    risk_free_rate: 0.02  # 2% annual risk-free rate
    target_return: 0.12  # 12% annual target return
    
  # Attribution analysis
  attribution:
    methods:
      - "brinson"
      - "factor_based"
    factors:
      - "market"
      - "size"
      - "value"
      - "momentum"
      - "quality"
      
  # Reporting
  reporting:
    frequency: "daily"
    formats: ["html", "pdf", "json"]
    email_recipients: []
    
# Data Configuration
data:
  # Data sources
  sources:
    yahoo:
      enabled: true
      symbols:
        - "AAPL"
        - "GOOGL"
        - "MSFT"
        - "TSLA"
        - "AMZN"
        - "NVDA"
        - "META"
        - "NFLX"
        - "SPY"
        - "QQQ"
      
    alpha_vantage:
      enabled: false
      api_key: "your_alpha_vantage_api_key"
      
    quandl:
      enabled: false
      api_key: "your_quandl_api_key"
      
  # Data processing
  processing:
    frequency: "1d"  # 1 day
    lookback_period: 252  # 1 year
    technical_indicators:
      - "sma_20"
      - "sma_50"
      - "sma_200"
      - "rsi_14"
      - "macd"
      - "bollinger_bands"
      - "atr_14"
      
  # Data storage
  storage:
    format: "parquet"  # parquet, csv, hdf5
    compression: "snappy"
    
# Training Configuration
training:
  # General training settings
  settings:
    batch_size: 16
    learning_rate: 0.0001
    num_epochs: 10
    gradient_accumulation_steps: 4
    warmup_steps: 100
    weight_decay: 0.01
    
  # LoRA configuration
  lora:
    r: 16
    lora_alpha: 32
    lora_dropout: 0.1
    target_modules:
      - "q_proj"
      - "v_proj"
      - "k_proj"
      - "o_proj"
      
  # Genetic algorithm optimization
  genetic_algorithm:
    population_size: 20
    generations: 10
    mutation_rate: 0.1
    crossover_rate: 0.8
    elite_size: 2
    
  # Hardware settings
  hardware:
    use_gpu: true
    gpu_count: 1
    mixed_precision: true
    dataloader_num_workers: 4
    
# Continuous Learning Configuration
continuous_learning:
  # Learning triggers
  triggers:
    time_based:
      enabled: true
      interval: 86400  # 24 hours
      
    performance_based:
      enabled: true
      threshold: 0.05  # 5% performance degradation
      lookback_period: 30  # 30 days
      
    market_regime:
      enabled: true
      volatility_threshold: 0.25  # 25% volatility change
      correlation_threshold: 0.20  # 20% correlation change
      
  # Adaptation settings
  adaptation:
    learning_rate_decay: 0.95
    min_learning_rate: 0.00001
    adaptation_strength: 0.1
    
# Backtesting Configuration
backtesting:
  # Default settings
  default:
    start_date: "2020-01-01"
    end_date: "2023-12-31"
    initial_capital: 100000.0
    
  # Walk-forward analysis
  walk_forward:
    training_period: 252  # 1 year
    testing_period: 63   # 3 months
    step_size: 21        # 1 month
    
  # Monte Carlo simulation
  monte_carlo:
    num_simulations: 1000
    confidence_levels: [0.05, 0.95]
    
# MLflow Configuration
mlflow:
  tracking_uri: "http://localhost:5000"
  experiment_name: "noryon_trading_experiments"
  artifact_location: "./mlruns"
  
  # Auto-logging
  autolog:
    enabled: true
    log_models: true
    log_datasets: true
    
# Deployment Configuration
deployment:
  # Deployment strategy
  strategy: "blue_green"  # blue_green, rolling, canary
  
  # Health checks
  health_check:
    enabled: true
    timeout: 300  # 5 minutes
    interval: 30  # 30 seconds
    retries: 3
    
  # Rollback settings
  rollback:
    enabled: true
    auto_rollback: true
    rollback_threshold: 0.10  # 10% performance degradation
    
  # Monitoring
  monitoring:
    metrics_collection: true
    log_aggregation: true
    alerting: true
    
# Database Configuration
database:
  # SQLite for development
  url: "sqlite:///./noryon.db"
  
  # PostgreSQL for production
  # url: "postgresql://user:password@localhost:5432/noryon"
  
  # Connection settings
  pool_size: 10
  max_overflow: 20
  pool_timeout: 30
  
# Redis Configuration (for caching and message queuing)
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  
# Monitoring and Alerting
monitoring:
  # Prometheus metrics
  prometheus:
    enabled: true
    port: 9090
    
  # Grafana dashboards
  grafana:
    enabled: false
    url: "http://localhost:3000"
    
  # Alerting
  alerts:
    email:
      enabled: false
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your_app_password"
      
    slack:
      enabled: false
      webhook_url: "your_slack_webhook_url"
      
    discord:
      enabled: false
      webhook_url: "your_discord_webhook_url"
      
# Logging Configuration
logging:
  version: 1
  disable_existing_loggers: false
  
  formatters:
    default:
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
      
    detailed:
      format: "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s"
      
  handlers:
    console:
      class: "logging.StreamHandler"
      level: "INFO"
      formatter: "default"
      stream: "ext://sys.stdout"
      
    file:
      class: "logging.handlers.RotatingFileHandler"
      level: "DEBUG"
      formatter: "detailed"
      filename: "./logs/noryon.log"
      maxBytes: 10485760  # 10MB
      backupCount: 5
      
  loggers:
    noryon:
      level: "DEBUG"
      handlers: ["console", "file"]
      propagate: false
      
  root:
    level: "INFO"
    handlers: ["console"]

# Feature Flags
feature_flags:
  enable_continuous_learning: true
  enable_risk_management: true
  enable_performance_analytics: true
  enable_backtesting: true
  enable_deployment_pipeline: true
  enable_web_dashboard: true
  enable_api_server: true
  enable_websocket: true
  enable_monitoring: true
  enable_mlflow_tracking: true
  
# Development Settings
development:
  mock_data: true
  simulate_trading: true
  fast_training: true
  debug_mode: true
  
# Production Settings
production:
  mock_data: false
  simulate_trading: false
  fast_training: false
  debug_mode: false
  ssl_enabled: true
  security_headers: true