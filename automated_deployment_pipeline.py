#!/usr/bin/env python3
"""
Noryon Automated Deployment Pipeline
Comprehensive deployment automation and orchestration system

This module provides:
- Automated model training and deployment workflows
- Continuous integration and deployment (CI/CD)
- Health monitoring and automated rollbacks
- Blue-green deployment strategies
- Integration with all system components
- Automated testing and validation
- Performance monitoring and alerting
- Configuration management and versioning
"""

import asyncio
import json
import logging
import os
import shutil
import subprocess
import sys
import time
import yaml
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from enum import Enum
import hashlib
import zipfile
import warnings
warnings.filterwarnings('ignore')

# Import system components
try:
    from train_all_models import ModelTrainingOrchestrator
    from continuous_learning_pipeline import ContinuousLearningPipeline
    from risk_management_system import RiskMonitor, RiskLimits
    from performance_analytics import PerformanceAnalytics
    from backtesting_framework import BacktestEngine
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"System components not available: {e}")
    COMPONENTS_AVAILABLE = False

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DeploymentStage(Enum):
    """Deployment pipeline stages"""
    PREPARATION = "preparation"
    TRAINING = "training"
    VALIDATION = "validation"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    MONITORING = "monitoring"
    ROLLBACK = "rollback"

class DeploymentStatus(Enum):
    """Deployment status"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"
    ROLLED_BACK = "rolled_back"

class HealthStatus(Enum):
    """System health status"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class DeploymentConfig:
    """Deployment configuration"""
    deployment_id: str
    version: str
    models_to_deploy: List[str]
    deployment_strategy: str = "blue_green"  # blue_green, rolling, canary
    validation_tests: List[str] = field(default_factory=list)
    rollback_threshold: float = 0.05  # 5% performance degradation
    monitoring_duration: int = 3600  # 1 hour monitoring
    auto_rollback: bool = True
    notification_channels: List[str] = field(default_factory=list)
    environment: str = "production"
    backup_enabled: bool = True
    health_check_interval: int = 60  # seconds
    max_deployment_time: int = 1800  # 30 minutes

@dataclass
class DeploymentStep:
    """Individual deployment step"""
    step_id: str
    stage: DeploymentStage
    name: str
    description: str
    status: DeploymentStatus = DeploymentStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration: Optional[timedelta] = None
    error_message: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    artifacts: Dict[str, str] = field(default_factory=dict)

@dataclass
class DeploymentResult:
    """Deployment result"""
    deployment_id: str
    status: DeploymentStatus
    version: str
    start_time: datetime
    end_time: Optional[datetime]
    duration: Optional[timedelta]
    steps: List[DeploymentStep]
    artifacts: Dict[str, str]
    performance_metrics: Dict[str, float]
    health_status: HealthStatus
    rollback_info: Optional[Dict[str, Any]] = None
    error_summary: Optional[str] = None

@dataclass
class HealthCheck:
    """System health check result"""
    component: str
    status: HealthStatus
    message: str
    metrics: Dict[str, float]
    timestamp: datetime = field(default_factory=datetime.now)
    response_time: float = 0.0

class ModelValidator:
    """Validate trained models before deployment"""
    
    def __init__(self):
        self.validation_tests = {
            'model_file_exists': self._check_model_file_exists,
            'model_loadable': self._check_model_loadable,
            'performance_threshold': self._check_performance_threshold,
            'inference_speed': self._check_inference_speed,
            'memory_usage': self._check_memory_usage,
            'output_format': self._check_output_format
        }
    
    async def validate_model(self, model_name: str, model_path: str, 
                           validation_config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate a trained model"""
        logger.info(f"Validating model: {model_name}")
        
        validation_results = {
            'model_name': model_name,
            'model_path': model_path,
            'overall_status': 'passed',
            'test_results': {},
            'errors': [],
            'warnings': []
        }
        
        # Run validation tests
        for test_name, test_func in self.validation_tests.items():
            if test_name in validation_config.get('enabled_tests', self.validation_tests.keys()):
                try:
                    result = await test_func(model_name, model_path, validation_config)
                    validation_results['test_results'][test_name] = result
                    
                    if not result.get('passed', False):
                        validation_results['overall_status'] = 'failed'
                        validation_results['errors'].append(f"{test_name}: {result.get('message', 'Unknown error')}")
                        
                except Exception as e:
                    logger.error(f"Validation test {test_name} failed: {e}")
                    validation_results['test_results'][test_name] = {
                        'passed': False,
                        'message': str(e),
                        'error': True
                    }
                    validation_results['overall_status'] = 'failed'
                    validation_results['errors'].append(f"{test_name}: {str(e)}")
        
        return validation_results
    
    async def _check_model_file_exists(self, model_name: str, model_path: str, 
                                     config: Dict[str, Any]) -> Dict[str, Any]:
        """Check if model file exists"""
        model_file = Path(model_path)
        exists = model_file.exists()
        
        return {
            'passed': exists,
            'message': f"Model file {'exists' if exists else 'not found'}: {model_path}",
            'file_size': model_file.stat().st_size if exists else 0
        }
    
    async def _check_model_loadable(self, model_name: str, model_path: str, 
                                  config: Dict[str, Any]) -> Dict[str, Any]:
        """Check if model can be loaded"""
        try:
            # Simulate model loading (would use actual model loading logic)
            await asyncio.sleep(0.1)  # Simulate loading time
            
            return {
                'passed': True,
                'message': "Model loaded successfully",
                'load_time': 0.1
            }
        except Exception as e:
            return {
                'passed': False,
                'message': f"Failed to load model: {str(e)}",
                'error': str(e)
            }
    
    async def _check_performance_threshold(self, model_name: str, model_path: str, 
                                         config: Dict[str, Any]) -> Dict[str, Any]:
        """Check if model meets performance thresholds"""
        # Simulate performance check
        simulated_accuracy = 0.75 + (hash(model_name) % 100) / 400  # 0.75-1.0
        threshold = config.get('performance_threshold', 0.7)
        
        passed = simulated_accuracy >= threshold
        
        return {
            'passed': passed,
            'message': f"Model accuracy {simulated_accuracy:.3f} {'meets' if passed else 'below'} threshold {threshold}",
            'accuracy': simulated_accuracy,
            'threshold': threshold
        }
    
    async def _check_inference_speed(self, model_name: str, model_path: str, 
                                   config: Dict[str, Any]) -> Dict[str, Any]:
        """Check model inference speed"""
        # Simulate inference speed test
        simulated_speed = 50 + (hash(model_name) % 100)  # 50-150 ms
        max_speed = config.get('max_inference_time', 200)  # ms
        
        passed = simulated_speed <= max_speed
        
        return {
            'passed': passed,
            'message': f"Inference time {simulated_speed}ms {'within' if passed else 'exceeds'} limit {max_speed}ms",
            'inference_time': simulated_speed,
            'limit': max_speed
        }
    
    async def _check_memory_usage(self, model_name: str, model_path: str, 
                                config: Dict[str, Any]) -> Dict[str, Any]:
        """Check model memory usage"""
        # Simulate memory usage check
        simulated_memory = 500 + (hash(model_name) % 1000)  # 500-1500 MB
        max_memory = config.get('max_memory_mb', 2000)  # MB
        
        passed = simulated_memory <= max_memory
        
        return {
            'passed': passed,
            'message': f"Memory usage {simulated_memory}MB {'within' if passed else 'exceeds'} limit {max_memory}MB",
            'memory_usage': simulated_memory,
            'limit': max_memory
        }
    
    async def _check_output_format(self, model_name: str, model_path: str, 
                                 config: Dict[str, Any]) -> Dict[str, Any]:
        """Check model output format"""
        # Simulate output format check
        expected_format = config.get('expected_output_format', 'json')
        
        return {
            'passed': True,
            'message': f"Output format matches expected: {expected_format}",
            'format': expected_format
        }

class HealthMonitor:
    """Monitor system health during deployment"""
    
    def __init__(self):
        self.health_checks = {
            'model_service': self._check_model_service,
            'database': self._check_database,
            'api_endpoints': self._check_api_endpoints,
            'memory_usage': self._check_memory_usage,
            'cpu_usage': self._check_cpu_usage,
            'disk_space': self._check_disk_space,
            'network_connectivity': self._check_network_connectivity
        }
        self.health_history = deque(maxlen=1000)
    
    async def run_health_checks(self) -> List[HealthCheck]:
        """Run all health checks"""
        health_results = []
        
        for component, check_func in self.health_checks.items():
            try:
                start_time = time.time()
                result = await check_func()
                response_time = time.time() - start_time
                
                health_check = HealthCheck(
                    component=component,
                    status=result['status'],
                    message=result['message'],
                    metrics=result.get('metrics', {}),
                    response_time=response_time
                )
                
                health_results.append(health_check)
                
            except Exception as e:
                logger.error(f"Health check failed for {component}: {e}")
                health_results.append(HealthCheck(
                    component=component,
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(e)}",
                    metrics={}
                ))
        
        self.health_history.extend(health_results)
        return health_results
    
    async def _check_model_service(self) -> Dict[str, Any]:
        """Check model service health"""
        # Simulate model service check
        await asyncio.sleep(0.1)
        
        return {
            'status': HealthStatus.HEALTHY,
            'message': 'Model service is running normally',
            'metrics': {
                'active_models': 3,
                'requests_per_second': 25.5,
                'average_response_time': 150
            }
        }
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database health"""
        # Simulate database check
        await asyncio.sleep(0.05)
        
        return {
            'status': HealthStatus.HEALTHY,
            'message': 'Database is accessible and responsive',
            'metrics': {
                'connection_pool_usage': 0.3,
                'query_response_time': 25,
                'active_connections': 15
            }
        }
    
    async def _check_api_endpoints(self) -> Dict[str, Any]:
        """Check API endpoints health"""
        # Simulate API endpoint checks
        await asyncio.sleep(0.2)
        
        return {
            'status': HealthStatus.HEALTHY,
            'message': 'All API endpoints are responding',
            'metrics': {
                'endpoint_count': 8,
                'average_response_time': 200,
                'error_rate': 0.01
            }
        }
    
    async def _check_memory_usage(self) -> Dict[str, Any]:
        """Check system memory usage"""
        # Simulate memory usage check
        import psutil
        memory = psutil.virtual_memory()
        
        status = HealthStatus.HEALTHY
        if memory.percent > 90:
            status = HealthStatus.CRITICAL
        elif memory.percent > 80:
            status = HealthStatus.WARNING
        
        return {
            'status': status,
            'message': f'Memory usage: {memory.percent:.1f}%',
            'metrics': {
                'memory_percent': memory.percent,
                'available_gb': memory.available / (1024**3),
                'total_gb': memory.total / (1024**3)
            }
        }
    
    async def _check_cpu_usage(self) -> Dict[str, Any]:
        """Check CPU usage"""
        # Simulate CPU usage check
        import psutil
        cpu_percent = psutil.cpu_percent(interval=1)
        
        status = HealthStatus.HEALTHY
        if cpu_percent > 90:
            status = HealthStatus.CRITICAL
        elif cpu_percent > 80:
            status = HealthStatus.WARNING
        
        return {
            'status': status,
            'message': f'CPU usage: {cpu_percent:.1f}%',
            'metrics': {
                'cpu_percent': cpu_percent,
                'cpu_count': psutil.cpu_count(),
                'load_average': os.getloadavg()[0] if hasattr(os, 'getloadavg') else 0
            }
        }
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """Check disk space"""
        # Simulate disk space check
        import psutil
        disk = psutil.disk_usage('/')
        
        percent_used = (disk.used / disk.total) * 100
        
        status = HealthStatus.HEALTHY
        if percent_used > 95:
            status = HealthStatus.CRITICAL
        elif percent_used > 85:
            status = HealthStatus.WARNING
        
        return {
            'status': status,
            'message': f'Disk usage: {percent_used:.1f}%',
            'metrics': {
                'disk_percent': percent_used,
                'free_gb': disk.free / (1024**3),
                'total_gb': disk.total / (1024**3)
            }
        }
    
    async def _check_network_connectivity(self) -> Dict[str, Any]:
        """Check network connectivity"""
        # Simulate network connectivity check
        await asyncio.sleep(0.1)
        
        return {
            'status': HealthStatus.HEALTHY,
            'message': 'Network connectivity is normal',
            'metrics': {
                'latency_ms': 15,
                'packet_loss': 0.0,
                'bandwidth_mbps': 100
            }
        }
    
    def get_overall_health(self, health_checks: List[HealthCheck]) -> HealthStatus:
        """Determine overall system health"""
        if not health_checks:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in health_checks]
        
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN

class DeploymentPipeline:
    """Main deployment pipeline orchestrator"""
    
    def __init__(self, config_path: str = "deployment_config.yaml"):
        self.config_path = config_path
        self.validator = ModelValidator()
        self.health_monitor = HealthMonitor()
        self.deployment_history = deque(maxlen=100)
        self.active_deployments = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Load configuration
        self.load_configuration()
        
        # Initialize directories
        self.setup_directories()
    
    def load_configuration(self):
        """Load deployment configuration"""
        config_file = Path(self.config_path)
        
        if config_file.exists():
            with open(config_file, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            # Default configuration
            self.config = {
                'deployment': {
                    'strategy': 'blue_green',
                    'validation_tests': ['model_file_exists', 'model_loadable', 'performance_threshold'],
                    'rollback_threshold': 0.05,
                    'monitoring_duration': 3600,
                    'auto_rollback': True,
                    'max_deployment_time': 1800
                },
                'validation': {
                    'performance_threshold': 0.7,
                    'max_inference_time': 200,
                    'max_memory_mb': 2000,
                    'expected_output_format': 'json'
                },
                'monitoring': {
                    'health_check_interval': 60,
                    'alert_thresholds': {
                        'memory_percent': 85,
                        'cpu_percent': 80,
                        'disk_percent': 85
                    }
                },
                'backup': {
                    'enabled': True,
                    'retention_days': 30,
                    'backup_location': 'backups/'
                }
            }
            
            # Save default configuration
            with open(config_file, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
    
    def setup_directories(self):
        """Setup required directories"""
        directories = [
            'deployments',
            'backups',
            'logs',
            'artifacts',
            'staging',
            'production'
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    async def deploy(self, deployment_config: DeploymentConfig) -> DeploymentResult:
        """Execute deployment pipeline"""
        logger.info(f"Starting deployment: {deployment_config.deployment_id}")
        
        deployment_result = DeploymentResult(
            deployment_id=deployment_config.deployment_id,
            status=DeploymentStatus.RUNNING,
            version=deployment_config.version,
            start_time=datetime.now(),
            end_time=None,
            duration=None,
            steps=[],
            artifacts={},
            performance_metrics={},
            health_status=HealthStatus.UNKNOWN
        )
        
        self.active_deployments[deployment_config.deployment_id] = deployment_result
        
        try:
            # Execute deployment stages
            stages = [
                (DeploymentStage.PREPARATION, self._prepare_deployment),
                (DeploymentStage.TRAINING, self._train_models),
                (DeploymentStage.VALIDATION, self._validate_models),
                (DeploymentStage.TESTING, self._run_tests),
                (DeploymentStage.STAGING, self._deploy_to_staging),
                (DeploymentStage.PRODUCTION, self._deploy_to_production),
                (DeploymentStage.MONITORING, self._monitor_deployment)
            ]
            
            for stage, stage_func in stages:
                step = await self._execute_stage(stage, stage_func, deployment_config)
                deployment_result.steps.append(step)
                
                if step.status == DeploymentStatus.FAILED:
                    deployment_result.status = DeploymentStatus.FAILED
                    deployment_result.error_summary = step.error_message
                    break
            
            # Check if deployment succeeded
            if deployment_result.status == DeploymentStatus.RUNNING:
                deployment_result.status = DeploymentStatus.SUCCESS
            
        except Exception as e:
            logger.error(f"Deployment failed: {e}")
            deployment_result.status = DeploymentStatus.FAILED
            deployment_result.error_summary = str(e)
            
            # Attempt rollback if auto-rollback is enabled
            if deployment_config.auto_rollback:
                await self._rollback_deployment(deployment_config, deployment_result)
        
        finally:
            deployment_result.end_time = datetime.now()
            deployment_result.duration = deployment_result.end_time - deployment_result.start_time
            
            # Final health check
            health_checks = await self.health_monitor.run_health_checks()
            deployment_result.health_status = self.health_monitor.get_overall_health(health_checks)
            
            # Save deployment result
            self._save_deployment_result(deployment_result)
            
            # Add to history
            self.deployment_history.append(deployment_result)
            
            # Remove from active deployments
            if deployment_config.deployment_id in self.active_deployments:
                del self.active_deployments[deployment_config.deployment_id]
        
        logger.info(f"Deployment completed: {deployment_config.deployment_id} - {deployment_result.status.value}")
        return deployment_result
    
    async def _execute_stage(self, stage: DeploymentStage, stage_func: Callable, 
                           deployment_config: DeploymentConfig) -> DeploymentStep:
        """Execute a deployment stage"""
        step = DeploymentStep(
            step_id=f"{deployment_config.deployment_id}_{stage.value}",
            stage=stage,
            name=stage.value.replace('_', ' ').title(),
            description=f"Execute {stage.value} stage",
            start_time=datetime.now()
        )
        
        try:
            logger.info(f"Executing stage: {stage.value}")
            step.status = DeploymentStatus.RUNNING
            
            # Execute stage function
            result = await stage_func(deployment_config, step)
            
            if result.get('success', True):
                step.status = DeploymentStatus.SUCCESS
                step.artifacts.update(result.get('artifacts', {}))
            else:
                step.status = DeploymentStatus.FAILED
                step.error_message = result.get('error', 'Unknown error')
            
        except Exception as e:
            logger.error(f"Stage {stage.value} failed: {e}")
            step.status = DeploymentStatus.FAILED
            step.error_message = str(e)
        
        finally:
            step.end_time = datetime.now()
            step.duration = step.end_time - step.start_time
        
        return step
    
    async def _prepare_deployment(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Prepare deployment environment"""
        step.logs.append("Preparing deployment environment")
        
        # Create deployment directory
        deployment_dir = Path(f"deployments/{config.deployment_id}")
        deployment_dir.mkdir(exist_ok=True)
        
        # Backup current production if enabled
        if config.backup_enabled:
            backup_dir = Path(f"backups/{config.deployment_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            backup_dir.mkdir(exist_ok=True)
            
            # Copy current production files
            production_dir = Path("production")
            if production_dir.exists():
                shutil.copytree(production_dir, backup_dir / "production", dirs_exist_ok=True)
                step.logs.append(f"Backup created: {backup_dir}")
        
        # Prepare staging environment
        staging_dir = Path("staging")
        staging_dir.mkdir(exist_ok=True)
        
        step.logs.append("Deployment environment prepared")
        
        return {
            'success': True,
            'artifacts': {
                'deployment_dir': str(deployment_dir),
                'backup_dir': str(backup_dir) if config.backup_enabled else None
            }
        }
    
    async def _train_models(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Train models if needed"""
        step.logs.append("Starting model training")
        
        if not COMPONENTS_AVAILABLE:
            step.logs.append("Training components not available, skipping training")
            return {'success': True}
        
        try:
            # Check if models need training
            models_to_train = []
            for model_name in config.models_to_deploy:
                model_path = Path(f"models/{model_name}")
                if not model_path.exists():
                    models_to_train.append(model_name)
                    step.logs.append(f"Model {model_name} needs training")
            
            if models_to_train:
                # Simulate training (would use actual training orchestrator)
                step.logs.append(f"Training {len(models_to_train)} models")
                await asyncio.sleep(2)  # Simulate training time
                
                # Create dummy model files
                for model_name in models_to_train:
                    model_dir = Path(f"models/{model_name}")
                    model_dir.mkdir(exist_ok=True)
                    
                    # Create dummy model file
                    model_file = model_dir / "model.bin"
                    with open(model_file, 'w') as f:
                        f.write(f"# Dummy model file for {model_name}\n")
                        f.write(f"# Created: {datetime.now()}\n")
                    
                    step.logs.append(f"Model {model_name} trained successfully")
            else:
                step.logs.append("All models are up to date")
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    async def _validate_models(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Validate trained models"""
        step.logs.append("Starting model validation")
        
        validation_results = {}
        validation_config = self.config.get('validation', {})
        
        for model_name in config.models_to_deploy:
            model_path = f"models/{model_name}/model.bin"
            
            step.logs.append(f"Validating model: {model_name}")
            
            result = await self.validator.validate_model(model_name, model_path, validation_config)
            validation_results[model_name] = result
            
            if result['overall_status'] == 'passed':
                step.logs.append(f"Model {model_name} validation passed")
            else:
                step.logs.append(f"Model {model_name} validation failed: {', '.join(result['errors'])}")
                return {'success': False, 'error': f"Model validation failed for {model_name}"}
        
        step.logs.append("All models validated successfully")
        
        return {
            'success': True,
            'artifacts': {
                'validation_results': validation_results
            }
        }
    
    async def _run_tests(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Run deployment tests"""
        step.logs.append("Running deployment tests")
        
        # Simulate running tests
        test_results = {}
        
        for test_name in config.validation_tests:
            step.logs.append(f"Running test: {test_name}")
            await asyncio.sleep(0.5)  # Simulate test execution
            
            # Simulate test result
            test_passed = True  # Would run actual tests
            test_results[test_name] = {
                'passed': test_passed,
                'duration': 0.5,
                'message': f"Test {test_name} {'passed' if test_passed else 'failed'}"
            }
            
            step.logs.append(f"Test {test_name}: {'PASSED' if test_passed else 'FAILED'}")
        
        all_passed = all(result['passed'] for result in test_results.values())
        
        if all_passed:
            step.logs.append("All tests passed")
            return {'success': True, 'artifacts': {'test_results': test_results}}
        else:
            failed_tests = [name for name, result in test_results.items() if not result['passed']]
            return {'success': False, 'error': f"Tests failed: {', '.join(failed_tests)}"}
    
    async def _deploy_to_staging(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Deploy to staging environment"""
        step.logs.append("Deploying to staging environment")
        
        staging_dir = Path("staging")
        
        # Copy models to staging
        for model_name in config.models_to_deploy:
            model_src = Path(f"models/{model_name}")
            model_dst = staging_dir / model_name
            
            if model_src.exists():
                shutil.copytree(model_src, model_dst, dirs_exist_ok=True)
                step.logs.append(f"Model {model_name} deployed to staging")
        
        # Update staging configuration
        staging_config = {
            'deployment_id': config.deployment_id,
            'version': config.version,
            'models': config.models_to_deploy,
            'deployed_at': datetime.now().isoformat()
        }
        
        with open(staging_dir / 'deployment_info.json', 'w') as f:
            json.dump(staging_config, f, indent=2)
        
        step.logs.append("Staging deployment completed")
        
        return {'success': True}
    
    async def _deploy_to_production(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Deploy to production environment"""
        step.logs.append("Deploying to production environment")
        
        if config.deployment_strategy == "blue_green":
            return await self._blue_green_deployment(config, step)
        elif config.deployment_strategy == "rolling":
            return await self._rolling_deployment(config, step)
        elif config.deployment_strategy == "canary":
            return await self._canary_deployment(config, step)
        else:
            return {'success': False, 'error': f"Unknown deployment strategy: {config.deployment_strategy}"}
    
    async def _blue_green_deployment(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Execute blue-green deployment"""
        step.logs.append("Executing blue-green deployment")
        
        production_dir = Path("production")
        staging_dir = Path("staging")
        
        # Create new production environment (green)
        green_dir = Path("production_green")
        if green_dir.exists():
            shutil.rmtree(green_dir)
        
        # Copy staging to green
        shutil.copytree(staging_dir, green_dir)
        step.logs.append("Green environment created")
        
        # Health check on green environment
        await asyncio.sleep(1)  # Simulate health check
        step.logs.append("Green environment health check passed")
        
        # Switch traffic to green (atomic operation)
        if production_dir.exists():
            blue_dir = Path("production_blue")
            if blue_dir.exists():
                shutil.rmtree(blue_dir)
            production_dir.rename(blue_dir)
            step.logs.append("Blue environment backed up")
        
        green_dir.rename(production_dir)
        step.logs.append("Traffic switched to green environment")
        
        return {'success': True}
    
    async def _rolling_deployment(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Execute rolling deployment"""
        step.logs.append("Executing rolling deployment")
        
        # Simulate rolling deployment
        for i, model_name in enumerate(config.models_to_deploy):
            step.logs.append(f"Deploying model {i+1}/{len(config.models_to_deploy)}: {model_name}")
            await asyncio.sleep(0.5)  # Simulate deployment time
            
            # Health check after each model
            health_checks = await self.health_monitor.run_health_checks()
            overall_health = self.health_monitor.get_overall_health(health_checks)
            
            if overall_health == HealthStatus.CRITICAL:
                return {'success': False, 'error': f"Health check failed during rolling deployment of {model_name}"}
            
            step.logs.append(f"Model {model_name} deployed successfully")
        
        return {'success': True}
    
    async def _canary_deployment(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Execute canary deployment"""
        step.logs.append("Executing canary deployment")
        
        # Deploy to small percentage of traffic first
        step.logs.append("Deploying to 10% of traffic (canary)")
        await asyncio.sleep(1)
        
        # Monitor canary performance
        step.logs.append("Monitoring canary performance")
        await asyncio.sleep(2)
        
        # Simulate performance check
        canary_performance = 0.95  # Simulated good performance
        
        if canary_performance > 0.9:
            step.logs.append("Canary performance good, proceeding with full deployment")
            await asyncio.sleep(1)
            step.logs.append("Full deployment completed")
            return {'success': True}
        else:
            return {'success': False, 'error': 'Canary performance below threshold'}
    
    async def _monitor_deployment(self, config: DeploymentConfig, step: DeploymentStep) -> Dict[str, Any]:
        """Monitor deployment health"""
        step.logs.append(f"Starting deployment monitoring for {config.monitoring_duration} seconds")
        
        monitoring_start = time.time()
        check_interval = config.health_check_interval
        
        while time.time() - monitoring_start < config.monitoring_duration:
            # Run health checks
            health_checks = await self.health_monitor.run_health_checks()
            overall_health = self.health_monitor.get_overall_health(health_checks)
            
            step.logs.append(f"Health check: {overall_health.value}")
            
            # Check for critical issues
            if overall_health == HealthStatus.CRITICAL:
                if config.auto_rollback:
                    step.logs.append("Critical health issue detected, triggering rollback")
                    return {'success': False, 'error': 'Critical health issue detected'}
                else:
                    step.logs.append("Critical health issue detected, but auto-rollback disabled")
            
            # Wait for next check
            await asyncio.sleep(min(check_interval, config.monitoring_duration - (time.time() - monitoring_start)))
        
        step.logs.append("Monitoring period completed successfully")
        return {'success': True}
    
    async def _rollback_deployment(self, config: DeploymentConfig, deployment_result: DeploymentResult):
        """Rollback failed deployment"""
        logger.info(f"Rolling back deployment: {config.deployment_id}")
        
        rollback_step = DeploymentStep(
            step_id=f"{config.deployment_id}_rollback",
            stage=DeploymentStage.ROLLBACK,
            name="Rollback",
            description="Rollback failed deployment",
            start_time=datetime.now()
        )
        
        try:
            # Find backup directory
            backup_pattern = f"backups/{config.deployment_id}_*"
            backup_dirs = list(Path().glob(backup_pattern))
            
            if backup_dirs:
                latest_backup = max(backup_dirs, key=lambda p: p.stat().st_mtime)
                
                # Restore from backup
                production_dir = Path("production")
                if production_dir.exists():
                    shutil.rmtree(production_dir)
                
                backup_production = latest_backup / "production"
                if backup_production.exists():
                    shutil.copytree(backup_production, production_dir)
                    rollback_step.logs.append(f"Restored from backup: {latest_backup}")
                    rollback_step.status = DeploymentStatus.SUCCESS
                else:
                    rollback_step.logs.append("No production backup found")
                    rollback_step.status = DeploymentStatus.FAILED
            else:
                rollback_step.logs.append("No backup found for rollback")
                rollback_step.status = DeploymentStatus.FAILED
        
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            rollback_step.status = DeploymentStatus.FAILED
            rollback_step.error_message = str(e)
        
        finally:
            rollback_step.end_time = datetime.now()
            rollback_step.duration = rollback_step.end_time - rollback_step.start_time
            deployment_result.steps.append(rollback_step)
            
            if rollback_step.status == DeploymentStatus.SUCCESS:
                deployment_result.status = DeploymentStatus.ROLLED_BACK
                deployment_result.rollback_info = {
                    'rollback_time': rollback_step.start_time.isoformat(),
                    'rollback_reason': deployment_result.error_summary
                }
    
    def _save_deployment_result(self, result: DeploymentResult):
        """Save deployment result to file"""
        result_file = Path(f"deployments/{result.deployment_id}/result.json")
        result_file.parent.mkdir(exist_ok=True)
        
        # Convert to serializable format
        result_data = {
            'deployment_id': result.deployment_id,
            'status': result.status.value,
            'version': result.version,
            'start_time': result.start_time.isoformat(),
            'end_time': result.end_time.isoformat() if result.end_time else None,
            'duration': str(result.duration) if result.duration else None,
            'health_status': result.health_status.value,
            'error_summary': result.error_summary,
            'rollback_info': result.rollback_info,
            'steps': [
                {
                    'step_id': step.step_id,
                    'stage': step.stage.value,
                    'name': step.name,
                    'status': step.status.value,
                    'start_time': step.start_time.isoformat() if step.start_time else None,
                    'end_time': step.end_time.isoformat() if step.end_time else None,
                    'duration': str(step.duration) if step.duration else None,
                    'error_message': step.error_message,
                    'logs': step.logs,
                    'artifacts': step.artifacts
                }
                for step in result.steps
            ]
        }
        
        with open(result_file, 'w') as f:
            json.dump(result_data, f, indent=2)
    
    def get_deployment_status(self, deployment_id: str) -> Optional[DeploymentResult]:
        """Get status of a deployment"""
        return self.active_deployments.get(deployment_id)
    
    def get_deployment_history(self, limit: int = 10) -> List[DeploymentResult]:
        """Get deployment history"""
        return list(self.deployment_history)[-limit:]
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get overall system status"""
        health_checks = await self.health_monitor.run_health_checks()
        overall_health = self.health_monitor.get_overall_health(health_checks)
        
        return {
            'overall_health': overall_health.value,
            'active_deployments': len(self.active_deployments),
            'recent_deployments': len([d for d in self.deployment_history 
                                     if (datetime.now() - d.start_time).total_seconds() < 86400]),
            'health_checks': [
                {
                    'component': check.component,
                    'status': check.status.value,
                    'message': check.message,
                    'response_time': check.response_time
                }
                for check in health_checks
            ],
            'timestamp': datetime.now().isoformat()
        }

# Example usage and testing
async def run_example_deployment():
    """Run example deployment"""
    logger.info("Starting example deployment")
    
    # Create deployment pipeline
    pipeline = DeploymentPipeline()
    
    # Create deployment configuration
    deployment_config = DeploymentConfig(
        deployment_id=f"deployment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        version="v1.2.0",
        models_to_deploy=["noryon-gemma-3-finance", "noryon-phi-4-finance"],
        deployment_strategy="blue_green",
        validation_tests=["model_file_exists", "model_loadable", "performance_threshold"],
        rollback_threshold=0.05,
        monitoring_duration=60,  # 1 minute for demo
        auto_rollback=True
    )
    
    # Execute deployment
    result = await pipeline.deploy(deployment_config)
    
    # Print results
    print(f"\n=== Deployment Result ===")
    print(f"Deployment ID: {result.deployment_id}")
    print(f"Status: {result.status.value}")
    print(f"Duration: {result.duration}")
    print(f"Health Status: {result.health_status.value}")
    
    if result.error_summary:
        print(f"Error: {result.error_summary}")
    
    print("\nDeployment Steps:")
    for step in result.steps:
        status_icon = "✓" if step.status == DeploymentStatus.SUCCESS else "✗" if step.status == DeploymentStatus.FAILED else "⏳"
        print(f"  {status_icon} {step.name} ({step.duration})")
        if step.error_message:
            print(f"    Error: {step.error_message}")
    
    # Get system status
    system_status = await pipeline.get_system_status()
    print(f"\n=== System Status ===")
    print(f"Overall Health: {system_status['overall_health']}")
    print(f"Active Deployments: {system_status['active_deployments']}")
    print(f"Recent Deployments: {system_status['recent_deployments']}")
    
    print("\nHealth Checks:")
    for check in system_status['health_checks']:
        status_icon = "✓" if check['status'] == 'healthy' else "⚠" if check['status'] == 'warning' else "✗"
        print(f"  {status_icon} {check['component']}: {check['message']} ({check['response_time']:.3f}s)")

if __name__ == "__main__":
    asyncio.run(run_example_deployment())