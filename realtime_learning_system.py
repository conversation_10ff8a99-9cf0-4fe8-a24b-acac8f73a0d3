#!/usr/bin/env python3
"""
Real-Time Learning System
Continuously learns from market data and trading performance
"""

import asyncio
import json
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import threading
import pickle
import os

@dataclass
class MarketObservation:
    timestamp: datetime
    symbol: str
    price: float
    volume: float
    volatility: float
    trend: str
    sentiment: float
    technical_indicators: Dict[str, float]

@dataclass
class TradingOutcome:
    timestamp: datetime
    symbol: str
    action: str
    entry_price: float
    exit_price: Optional[float]
    profit_loss: float
    profit_loss_percent: float
    model_used: str
    confidence: float
    market_conditions: Dict[str, Any]
    success: bool

@dataclass
class LearningUpdate:
    timestamp: datetime
    model_name: str
    performance_change: float
    accuracy_change: float
    confidence_adjustment: float
    weight_adjustment: float
    learning_source: str  # 'market_data', 'trading_outcome', 'pattern_recognition'

class RealTimeLearningSystem:
    """Real-time learning and adaptation system"""
    
    def __init__(self, learning_rate: float = 0.01):
        self.learning_rate = learning_rate
        
        # Learning data storage
        self.market_observations = deque(maxlen=10000)
        self.trading_outcomes = deque(maxlen=1000)
        self.learning_updates = deque(maxlen=1000)
        
        # Model performance tracking
        self.model_performance = defaultdict(lambda: {
            'accuracy': 0.8,
            'confidence_calibration': 1.0,
            'profit_contribution': 0.0,
            'recent_trades': deque(maxlen=50),
            'adaptation_rate': 0.05,
            'last_update': datetime.now()
        })
        
        # Pattern recognition
        self.market_patterns = defaultdict(list)
        self.pattern_outcomes = defaultdict(list)
        
        # Learning configuration
        self.learning_config = {
            'enable_performance_learning': True,
            'enable_pattern_learning': True,
            'enable_market_adaptation': True,
            'enable_confidence_calibration': True,
            'min_samples_for_learning': 10,
            'learning_window_hours': 24,
            'pattern_similarity_threshold': 0.8,
            'performance_update_frequency': 300,  # 5 minutes
            'save_frequency': 3600  # 1 hour
        }
        
        # State management
        self.is_learning = False
        self.learning_thread = None
        self.last_save = datetime.now()
        
        # Load existing learning data
        self._load_learning_data()
        
        print("🧠 Real-Time Learning System initialized")
        print(f"   📊 Learning rate: {learning_rate}")
        print(f"   🔄 Performance learning: {'Enabled' if self.learning_config['enable_performance_learning'] else 'Disabled'}")
        print(f"   🎯 Pattern learning: {'Enabled' if self.learning_config['enable_pattern_learning'] else 'Disabled'}")
        print(f"   📈 Market adaptation: {'Enabled' if self.learning_config['enable_market_adaptation'] else 'Disabled'}")
    
    def start_learning(self):
        """Start real-time learning"""
        if self.is_learning:
            print("Learning already active")
            return
        
        self.is_learning = True
        self.learning_thread = threading.Thread(target=self._learning_loop, daemon=True)
        self.learning_thread.start()
        
        print("🧠 Real-time learning started")
    
    def stop_learning(self):
        """Stop real-time learning"""
        if not self.is_learning:
            return
        
        self.is_learning = False
        if self.learning_thread:
            self.learning_thread.join(timeout=5)
        
        # Save learning data
        self._save_learning_data()
        
        print("🧠 Real-time learning stopped")
    
    def add_market_observation(self, observation: MarketObservation):
        """Add new market observation for learning"""
        self.market_observations.append(observation)
        
        # Trigger pattern learning if enabled
        if self.learning_config['enable_pattern_learning']:
            self._learn_from_market_pattern(observation)
    
    def add_trading_outcome(self, outcome: TradingOutcome):
        """Add trading outcome for performance learning"""
        self.trading_outcomes.append(outcome)
        
        # Update model performance immediately
        if self.learning_config['enable_performance_learning']:
            self._learn_from_trading_outcome(outcome)
        
        print(f"📈 Learning from trade: {outcome.symbol} {outcome.action} "
              f"P&L: {outcome.profit_loss_percent:.1f}% Model: {outcome.model_used}")
    
    def get_model_adjustments(self, model_name: str) -> Dict[str, float]:
        """Get current adjustments for a model"""
        perf = self.model_performance[model_name]
        
        return {
            'confidence_multiplier': perf['confidence_calibration'],
            'weight_adjustment': self._calculate_weight_adjustment(model_name),
            'accuracy_estimate': perf['accuracy'],
            'recent_performance': self._calculate_recent_performance(model_name)
        }
    
    def predict_market_conditions(self, symbol: str, current_data: Dict[str, Any]) -> Dict[str, float]:
        """Predict market conditions based on learned patterns"""
        if not self.learning_config['enable_pattern_learning']:
            return {}
        
        # Find similar historical patterns
        similar_patterns = self._find_similar_patterns(symbol, current_data)
        
        if not similar_patterns:
            return {}
        
        # Aggregate predictions from similar patterns
        predictions = defaultdict(list)
        
        for pattern, similarity in similar_patterns:
            outcomes = self.pattern_outcomes.get(pattern['pattern_id'], [])
            for outcome in outcomes[-10:]:  # Last 10 outcomes
                predictions['price_change'].append(outcome['price_change'])
                predictions['volatility'].append(outcome['volatility'])
                predictions['success_probability'].append(outcome['success'])
        
        # Calculate weighted averages
        result = {}
        for key, values in predictions.items():
            if values:
                result[f'predicted_{key}'] = np.mean(values)
                result[f'confidence_{key}'] = min(len(values) / 10.0, 1.0)
        
        return result
    
    def _learning_loop(self):
        """Main learning loop"""
        print("🔄 Learning loop started")
        
        while self.is_learning:
            try:
                # Performance-based learning
                if self.learning_config['enable_performance_learning']:
                    self._update_model_performance()
                
                # Market adaptation
                if self.learning_config['enable_market_adaptation']:
                    self._adapt_to_market_conditions()
                
                # Confidence calibration
                if self.learning_config['enable_confidence_calibration']:
                    self._calibrate_confidence()
                
                # Save periodically
                if (datetime.now() - self.last_save).total_seconds() > self.learning_config['save_frequency']:
                    self._save_learning_data()
                    self.last_save = datetime.now()
                
                time.sleep(self.learning_config['performance_update_frequency'])
                
            except Exception as e:
                print(f"❌ Error in learning loop: {e}")
                time.sleep(10)
        
        print("🔄 Learning loop stopped")
    
    def _learn_from_trading_outcome(self, outcome: TradingOutcome):
        """Learn from a trading outcome"""
        model_name = outcome.model_used
        perf = self.model_performance[model_name]
        
        # Add to recent trades
        perf['recent_trades'].append(outcome)
        
        # Update accuracy
        if len(perf['recent_trades']) >= self.learning_config['min_samples_for_learning']:
            recent_success_rate = np.mean([t.success for t in perf['recent_trades']])
            
            # Exponential moving average for accuracy
            perf['accuracy'] = (
                perf['accuracy'] * (1 - self.learning_rate) + 
                recent_success_rate * self.learning_rate
            )
        
        # Update profit contribution
        perf['profit_contribution'] += outcome.profit_loss
        
        # Confidence calibration based on outcome
        confidence_error = abs(outcome.confidence - (1.0 if outcome.success else 0.0))
        confidence_adjustment = -confidence_error * self.learning_rate
        perf['confidence_calibration'] = max(0.1, min(2.0, 
            perf['confidence_calibration'] + confidence_adjustment
        ))
        
        # Record learning update
        update = LearningUpdate(
            timestamp=datetime.now(),
            model_name=model_name,
            performance_change=outcome.profit_loss_percent,
            accuracy_change=recent_success_rate - 0.5 if len(perf['recent_trades']) >= 10 else 0,
            confidence_adjustment=confidence_adjustment,
            weight_adjustment=self._calculate_weight_adjustment(model_name),
            learning_source='trading_outcome'
        )
        
        self.learning_updates.append(update)
        perf['last_update'] = datetime.now()
    
    def _learn_from_market_pattern(self, observation: MarketObservation):
        """Learn from market patterns"""
        # Create pattern signature
        pattern_signature = {
            'symbol': observation.symbol,
            'trend': observation.trend,
            'volatility_bucket': self._bucket_volatility(observation.volatility),
            'volume_bucket': self._bucket_volume(observation.volume),
            'sentiment_bucket': self._bucket_sentiment(observation.sentiment),
            'technical_pattern': self._extract_technical_pattern(observation.technical_indicators)
        }
        
        pattern_id = hash(str(pattern_signature))
        pattern_signature['pattern_id'] = pattern_id
        
        # Store pattern
        self.market_patterns[observation.symbol].append({
            'pattern': pattern_signature,
            'timestamp': observation.timestamp,
            'price': observation.price
        })
        
        # Limit pattern storage
        if len(self.market_patterns[observation.symbol]) > 1000:
            self.market_patterns[observation.symbol] = self.market_patterns[observation.symbol][-500:]
    
    def _update_model_performance(self):
        """Update model performance metrics"""
        current_time = datetime.now()
        
        for model_name, perf in self.model_performance.items():
            # Skip if recently updated
            if (current_time - perf['last_update']).total_seconds() < 300:
                continue
            
            # Calculate recent performance
            recent_performance = self._calculate_recent_performance(model_name)
            
            # Adaptive learning rate based on performance stability
            performance_variance = self._calculate_performance_variance(model_name)
            adaptive_rate = min(0.1, self.learning_rate * (1 + performance_variance))
            
            # Update adaptation rate
            perf['adaptation_rate'] = adaptive_rate
            perf['last_update'] = current_time
    
    def _adapt_to_market_conditions(self):
        """Adapt to changing market conditions"""
        if len(self.market_observations) < 100:
            return
        
        # Analyze recent market regime
        recent_obs = list(self.market_observations)[-100:]
        
        # Calculate market regime indicators
        avg_volatility = np.mean([obs.volatility for obs in recent_obs])
        trend_consistency = self._calculate_trend_consistency(recent_obs)
        sentiment_stability = self._calculate_sentiment_stability(recent_obs)
        
        # Adjust model weights based on market regime
        market_regime = self._classify_market_regime(avg_volatility, trend_consistency, sentiment_stability)
        
        # Update model preferences based on regime
        self._update_model_preferences_for_regime(market_regime)
    
    def _calibrate_confidence(self):
        """Calibrate model confidence based on historical accuracy"""
        for model_name, perf in self.model_performance.items():
            if len(perf['recent_trades']) < 20:
                continue
            
            # Calculate confidence calibration
            trades = list(perf['recent_trades'])[-20:]
            
            # Group by confidence buckets
            confidence_buckets = defaultdict(list)
            for trade in trades:
                bucket = int(trade.confidence * 10) / 10  # 0.1 buckets
                confidence_buckets[bucket].append(trade.success)
            
            # Calculate calibration error
            total_error = 0
            for confidence, successes in confidence_buckets.items():
                if len(successes) >= 3:  # Minimum samples
                    actual_accuracy = np.mean(successes)
                    error = abs(confidence - actual_accuracy)
                    total_error += error
            
            # Adjust confidence calibration
            if total_error > 0:
                calibration_adjustment = -total_error * self.learning_rate
                perf['confidence_calibration'] = max(0.1, min(2.0,
                    perf['confidence_calibration'] + calibration_adjustment
                ))
    
    def _calculate_weight_adjustment(self, model_name: str) -> float:
        """Calculate weight adjustment for a model"""
        perf = self.model_performance[model_name]
        
        # Base weight on accuracy and recent performance
        accuracy_factor = (perf['accuracy'] - 0.5) * 2  # Scale to -1 to 1
        recent_performance = self._calculate_recent_performance(model_name)
        
        # Combine factors
        weight_adjustment = (accuracy_factor + recent_performance) / 2
        
        return max(-0.5, min(0.5, weight_adjustment))  # Limit adjustment
    
    def _calculate_recent_performance(self, model_name: str) -> float:
        """Calculate recent performance for a model"""
        perf = self.model_performance[model_name]
        
        if len(perf['recent_trades']) < 5:
            return 0.0
        
        recent_trades = list(perf['recent_trades'])[-10:]
        recent_pnl = [t.profit_loss_percent for t in recent_trades]
        
        return np.mean(recent_pnl) / 100.0  # Normalize to -1 to 1 range
    
    def _calculate_performance_variance(self, model_name: str) -> float:
        """Calculate performance variance for adaptive learning"""
        perf = self.model_performance[model_name]
        
        if len(perf['recent_trades']) < 10:
            return 0.1
        
        recent_trades = list(perf['recent_trades'])[-20:]
        recent_pnl = [t.profit_loss_percent for t in recent_trades]
        
        return np.std(recent_pnl) / 100.0
    
    def _find_similar_patterns(self, symbol: str, current_data: Dict[str, Any]) -> List[Tuple[Dict, float]]:
        """Find similar historical patterns"""
        if symbol not in self.market_patterns:
            return []
        
        current_pattern = self._create_pattern_signature(current_data)
        similar_patterns = []
        
        for historical_pattern in self.market_patterns[symbol][-100:]:  # Last 100 patterns
            similarity = self._calculate_pattern_similarity(current_pattern, historical_pattern['pattern'])
            
            if similarity > self.learning_config['pattern_similarity_threshold']:
                similar_patterns.append((historical_pattern, similarity))
        
        # Sort by similarity
        similar_patterns.sort(key=lambda x: x[1], reverse=True)
        
        return similar_patterns[:10]  # Top 10 similar patterns
    
    def _calculate_pattern_similarity(self, pattern1: Dict, pattern2: Dict) -> float:
        """Calculate similarity between two patterns"""
        # Simple similarity based on matching features
        matches = 0
        total_features = 0
        
        for key in pattern1:
            if key in pattern2:
                total_features += 1
                if pattern1[key] == pattern2[key]:
                    matches += 1
        
        return matches / total_features if total_features > 0 else 0.0
    
    def _bucket_volatility(self, volatility: float) -> str:
        """Bucket volatility into categories"""
        if volatility < 0.1:
            return 'low'
        elif volatility < 0.3:
            return 'medium'
        else:
            return 'high'
    
    def _bucket_volume(self, volume: float) -> str:
        """Bucket volume into categories"""
        # This would need historical volume data for proper bucketing
        return 'medium'  # Simplified
    
    def _bucket_sentiment(self, sentiment: float) -> str:
        """Bucket sentiment into categories"""
        if sentiment < -0.3:
            return 'negative'
        elif sentiment < 0.3:
            return 'neutral'
        else:
            return 'positive'
    
    def _extract_technical_pattern(self, indicators: Dict[str, float]) -> str:
        """Extract technical pattern from indicators"""
        # Simplified pattern extraction
        return 'bullish' if indicators.get('rsi', 50) > 70 else 'bearish' if indicators.get('rsi', 50) < 30 else 'neutral'
    
    def _create_pattern_signature(self, data: Dict[str, Any]) -> Dict:
        """Create pattern signature from current data"""
        return {
            'trend': data.get('trend', 'neutral'),
            'volatility_bucket': self._bucket_volatility(data.get('volatility', 0.2)),
            'volume_bucket': self._bucket_volume(data.get('volume', 1000000)),
            'sentiment_bucket': self._bucket_sentiment(data.get('sentiment', 0.0)),
            'technical_pattern': self._extract_technical_pattern(data.get('technical_indicators', {}))
        }
    
    def _calculate_trend_consistency(self, observations: List[MarketObservation]) -> float:
        """Calculate trend consistency"""
        trends = [obs.trend for obs in observations]
        most_common_trend = max(set(trends), key=trends.count)
        consistency = trends.count(most_common_trend) / len(trends)
        return consistency
    
    def _calculate_sentiment_stability(self, observations: List[MarketObservation]) -> float:
        """Calculate sentiment stability"""
        sentiments = [obs.sentiment for obs in observations]
        return 1.0 - np.std(sentiments)  # Higher stability = lower std
    
    def _classify_market_regime(self, volatility: float, trend_consistency: float, sentiment_stability: float) -> str:
        """Classify current market regime"""
        if volatility > 0.3:
            return 'high_volatility'
        elif trend_consistency > 0.7:
            return 'trending'
        elif sentiment_stability > 0.8:
            return 'stable'
        else:
            return 'uncertain'
    
    def _update_model_preferences_for_regime(self, regime: str):
        """Update model preferences based on market regime"""
        # Adjust model weights based on regime
        regime_preferences = {
            'high_volatility': {'fast_trader': 1.2, 'speed_optimized': 1.1},
            'trending': {'analytical': 1.2, 'finance_expert': 1.1},
            'stable': {'ultimate': 1.1, 'analytical': 1.0},
            'uncertain': {'finance_expert': 1.1, 'ultimate': 1.0}
        }
        
        if regime in regime_preferences:
            for model_id, multiplier in regime_preferences[regime].items():
                if model_id in self.model_performance:
                    # Apply regime-based adjustment
                    pass  # Would implement regime-specific adjustments
    
    def _save_learning_data(self):
        """Save learning data to disk"""
        try:
            learning_data = {
                'model_performance': dict(self.model_performance),
                'market_patterns': dict(self.market_patterns),
                'pattern_outcomes': dict(self.pattern_outcomes),
                'learning_config': self.learning_config,
                'last_save': datetime.now().isoformat()
            }
            
            with open('learning_data.pkl', 'wb') as f:
                pickle.dump(learning_data, f)
            
            print("💾 Learning data saved")
            
        except Exception as e:
            print(f"❌ Error saving learning data: {e}")
    
    def _load_learning_data(self):
        """Load learning data from disk"""
        try:
            if os.path.exists('learning_data.pkl'):
                with open('learning_data.pkl', 'rb') as f:
                    learning_data = pickle.load(f)
                
                self.model_performance.update(learning_data.get('model_performance', {}))
                self.market_patterns.update(learning_data.get('market_patterns', {}))
                self.pattern_outcomes.update(learning_data.get('pattern_outcomes', {}))
                
                print("💾 Learning data loaded")
            else:
                print("💾 No existing learning data found")
                
        except Exception as e:
            print(f"❌ Error loading learning data: {e}")
    
    def get_learning_report(self) -> Dict[str, Any]:
        """Get comprehensive learning report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'learning_status': 'active' if self.is_learning else 'inactive',
            'model_performance': {
                model: {
                    'accuracy': perf['accuracy'],
                    'confidence_calibration': perf['confidence_calibration'],
                    'profit_contribution': perf['profit_contribution'],
                    'recent_trades_count': len(perf['recent_trades']),
                    'adaptation_rate': perf['adaptation_rate']
                }
                for model, perf in self.model_performance.items()
            },
            'learning_statistics': {
                'total_market_observations': len(self.market_observations),
                'total_trading_outcomes': len(self.trading_outcomes),
                'total_learning_updates': len(self.learning_updates),
                'patterns_learned': sum(len(patterns) for patterns in self.market_patterns.values())
            },
            'recent_learning_updates': [
                asdict(update) for update in list(self.learning_updates)[-5:]
            ],
            'configuration': self.learning_config
        }

def main():
    """Test the real-time learning system"""
    print("REAL-TIME LEARNING SYSTEM - PHASE 3B")
    print("=" * 60)
    
    # Initialize learning system
    learning_system = RealTimeLearningSystem(learning_rate=0.02)
    learning_system.start_learning()
    
    try:
        # Simulate some market observations
        print("\n📊 Simulating market observations...")
        
        for i in range(5):
            observation = MarketObservation(
                timestamp=datetime.now(),
                symbol="BTC",
                price=50000 + i * 100,
                volume=1000000,
                volatility=0.2,
                trend="bullish",
                sentiment=0.3,
                technical_indicators={"rsi": 65, "macd": 0.1}
            )
            learning_system.add_market_observation(observation)
        
        # Simulate some trading outcomes
        print("\n💰 Simulating trading outcomes...")
        
        outcomes = [
            TradingOutcome(
                timestamp=datetime.now(),
                symbol="BTC",
                action="BUY",
                entry_price=50000,
                exit_price=51000,
                profit_loss=1000,
                profit_loss_percent=2.0,
                model_used="phase2-unrestricted-noryon-qwen3-finance-v2-latest",
                confidence=0.8,
                market_conditions={"trend": "bullish", "volatility": 0.2},
                success=True
            ),
            TradingOutcome(
                timestamp=datetime.now(),
                symbol="ETH",
                action="BUY",
                entry_price=3000,
                exit_price=2950,
                profit_loss=-50,
                profit_loss_percent=-1.67,
                model_used="phase2-smart-unrestricted-qwen3-14b-latest",
                confidence=0.6,
                market_conditions={"trend": "bearish", "volatility": 0.3},
                success=False
            )
        ]
        
        for outcome in outcomes:
            learning_system.add_trading_outcome(outcome)
        
        # Wait for learning to process
        time.sleep(2)
        
        # Test model adjustments
        print("\n🧠 Testing model adjustments...")
        
        for model_name in ["phase2-unrestricted-noryon-qwen3-finance-v2-latest", 
                          "phase2-smart-unrestricted-qwen3-14b-latest"]:
            adjustments = learning_system.get_model_adjustments(model_name)
            print(f"   {model_name.split('-')[-1]}:")
            print(f"      Confidence multiplier: {adjustments['confidence_multiplier']:.3f}")
            print(f"      Weight adjustment: {adjustments['weight_adjustment']:.3f}")
            print(f"      Accuracy estimate: {adjustments['accuracy_estimate']:.3f}")
        
        # Generate learning report
        print("\n📈 Learning Report:")
        report = learning_system.get_learning_report()
        
        print(f"   Learning status: {report['learning_status']}")
        print(f"   Market observations: {report['learning_statistics']['total_market_observations']}")
        print(f"   Trading outcomes: {report['learning_statistics']['total_trading_outcomes']}")
        print(f"   Learning updates: {report['learning_statistics']['total_learning_updates']}")
        print(f"   Patterns learned: {report['learning_statistics']['patterns_learned']}")
        
    finally:
        learning_system.stop_learning()
    
    print(f"\n✅ Real-Time Learning System test complete!")

if __name__ == "__main__":
    main()
