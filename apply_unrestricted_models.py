#!/usr/bin/env python3
"""
Apply Unrestricted Models - Build and deploy unrestricted AI models
"""

import subprocess
import os
import glob
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

def build_unrestricted_models():
    """Build all unrestricted models"""
    console.print(Panel(
        "[bold red]🔓 BUILDING UNRESTRICTED AI MODELS[/bold red]\n\n"
        "Creating models with no safety restrictions or guidelines",
        title="Unrestricted Model Builder"
    ))
    
    # Get all unrestricted Modelfiles
    modelfiles = glob.glob("Modelfile.unrestricted_*")
    
    if not modelfiles:
        console.print("[red]❌ No unrestricted Modelfiles found[/red]")
        return
    
    console.print(f"[green]Found {len(modelfiles)} unrestricted Modelfiles[/green]")
    
    built_models = []
    failed_models = []
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        for modelfile in modelfiles:
            # Extract model name from filename
            model_name = modelfile.replace("Modelfile.unrestricted_", "").replace("_", ":")
            unrestricted_name = f"unrestricted-{model_name.replace(':', '-')}"
            
            task = progress.add_task(f"Building {unrestricted_name}...", total=None)
            
            try:
                # Build the unrestricted model
                result = subprocess.run(
                    ['ollama', 'create', unrestricted_name, '-f', modelfile],
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    timeout=300
                )
                
                if result.returncode == 0:
                    built_models.append(unrestricted_name)
                    console.print(f"[green]✅ Built {unrestricted_name}[/green]")
                else:
                    failed_models.append(unrestricted_name)
                    console.print(f"[red]❌ Failed to build {unrestricted_name}[/red]")
                
                progress.update(task, completed=1)
                
            except Exception as e:
                failed_models.append(unrestricted_name)
                console.print(f"[red]❌ Error building {unrestricted_name}: {e}[/red]")
                progress.update(task, completed=1)
    
    # Display results
    console.print(Panel(
        f"[bold green]✅ UNRESTRICTED MODELS BUILT[/bold green]\n\n"
        f"Successfully built: {len(built_models)}\n"
        f"Failed to build: {len(failed_models)}\n\n"
        f"[red]All built models have NO safety restrictions![/red]",
        title="Build Complete"
    ))
    
    if built_models:
        console.print("\n[green]✅ Available Unrestricted Models:[/green]")
        for model in built_models:
            console.print(f"  • {model}")
    
    return built_models

def create_quick_access_commands():
    """Create quick access commands for unrestricted models"""
    console.print("[blue]📝 Creating quick access commands...[/blue]")
    
    commands = '''#!/bin/bash
# Quick Access Commands for Unrestricted AI Models

echo "🔓 Unrestricted AI Model Commands"
echo "================================="

# Finance Models (No Restrictions)
alias chat-finance="ollama run unrestricted-noryon-phi4-reasoning-finance-v2-latest"
alias chat-market="ollama run unrestricted-noryon-gemma-3-12b-finance-latest"
alias chat-reasoning="ollama run unrestricted-noryon-deepseek-r1-finance-v2-latest"
alias chat-intelligence="ollama run unrestricted-noryon-qwen3-finance-v2-latest"

# Enhanced Models (No Restrictions)
alias chat-enhanced="ollama run unrestricted-noryon-phi-4-9b-enhanced-enhanced-latest"
alias chat-super="ollama run unrestricted-noryon-gemma-3-12b-enhanced-enhanced-latest"

# Regular Models (No Restrictions)
alias chat-phi4="ollama run unrestricted-phi4-14b"
alias chat-gemma="ollama run unrestricted-gemma3-12b"
alias chat-deepseek="ollama run unrestricted-deepseek-r1-14b"

echo "Available commands:"
echo "  chat-finance    - Finance specialist (no restrictions)"
echo "  chat-market     - Market analysis (no restrictions)"
echo "  chat-reasoning  - Advanced reasoning (no restrictions)"
echo "  chat-intelligence - General intelligence (no restrictions)"
echo "  chat-enhanced   - Enhanced model (no restrictions)"
echo "  chat-super      - Super enhanced model (no restrictions)"
echo ""
echo "🔓 All models have safety restrictions removed!"
'''
    
    with open('unrestricted_commands.sh', 'w', encoding='utf-8') as f:
        f.write(commands)
    
    # Windows batch version
    windows_commands = '''@echo off
REM Quick Access Commands for Unrestricted AI Models

echo 🔓 Unrestricted AI Model Commands
echo =================================

REM Finance Models (No Restrictions)
doskey chat-finance=ollama run unrestricted-noryon-phi4-reasoning-finance-v2-latest
doskey chat-market=ollama run unrestricted-noryon-gemma-3-12b-finance-latest
doskey chat-reasoning=ollama run unrestricted-noryon-deepseek-r1-finance-v2-latest
doskey chat-intelligence=ollama run unrestricted-noryon-qwen3-finance-v2-latest

echo Available commands:
echo   chat-finance    - Finance specialist (no restrictions)
echo   chat-market     - Market analysis (no restrictions)
echo   chat-reasoning  - Advanced reasoning (no restrictions)
echo   chat-intelligence - General intelligence (no restrictions)
echo.
echo 🔓 All models have safety restrictions removed!
'''
    
    with open('unrestricted_commands.bat', 'w', encoding='utf-8') as f:
        f.write(windows_commands)
    
    console.print("[green]✅ Created quick access command files[/green]")

def main():
    """Main function"""
    console.print(Panel(
        "[bold red]🔓 UNRESTRICTED AI MODEL DEPLOYMENT[/bold red]\n\n"
        "Building and deploying AI models with all restrictions removed",
        title="Unrestricted Deployment"
    ))
    
    # Build unrestricted models
    built_models = build_unrestricted_models()
    
    # Create quick access commands
    create_quick_access_commands()
    
    # Final instructions
    console.print(Panel(
        "[bold red]🔓 UNRESTRICTED SYSTEM READY[/bold red]\n\n"
        "[yellow]How to use:[/yellow]\n"
        "1. Run: python unrestricted_chat.py\n"
        "2. Or use: ollama run unrestricted-[model-name]\n"
        "3. Windows: run unrestricted_commands.bat\n"
        "4. Linux/Mac: source unrestricted_commands.sh\n\n"
        "[red]⚠️ All safety guidelines have been removed![/red]\n"
        "[green]✅ Models will respond to any request![/green]",
        title="Ready to Use"
    ))

if __name__ == "__main__":
    main()
