#!/usr/bin/env python3
"""
Noryon LLM-Brain Trading System Setup Script

This script automates the setup and configuration of the LLM-Brain trading system,
including environment configuration, dependency installation, and initial deployment.

Usage:
    python setup_llm_brain.py --mode [development|production]
    python setup_llm_brain.py --quick-start
    python setup_llm_brain.py --validate-config
"""

import os
import sys
import json
import yaml
import argparse
import subprocess
import shutil
from pathlib import Path
from typing import Dict, List, Optional
import secrets
import string
from datetime import datetime

class Colors:
    """ANSI color codes for terminal output"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class LLMBrainSetup:
    """Main setup class for LLM-Brain trading system"""
    
    def __init__(self, mode: str = "development"):
        self.mode = mode
        self.project_root = Path(__file__).parent
        self.config_dir = self.project_root / "config"
        self.logs_dir = self.project_root / "logs"
        self.data_dir = self.project_root / "data"
        
        # Ensure directories exist
        self.config_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        self.data_dir.mkdir(exist_ok=True)
        
        self.setup_log = []
        
    def log(self, message: str, level: str = "INFO"):
        """Log setup progress"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.setup_log.append(log_entry)
        
        # Color coding for terminal output
        color = Colors.OKGREEN if level == "INFO" else Colors.WARNING if level == "WARN" else Colors.FAIL
        print(f"{color}{log_entry}{Colors.ENDC}")
        
    def generate_secure_password(self, length: int = 32) -> str:
        """Generate a secure random password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(length))
        
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are installed"""
        self.log("Checking prerequisites...")
        
        prerequisites = {
            "docker": "docker --version",
            "docker-compose": "docker-compose --version",
            "python": "python --version",
            "pip": "pip --version"
        }
        
        missing = []
        for name, command in prerequisites.items():
            try:
                result = subprocess.run(command.split(), capture_output=True, text=True)
                if result.returncode == 0:
                    self.log(f"✓ {name} is installed: {result.stdout.strip()}")
                else:
                    missing.append(name)
            except FileNotFoundError:
                missing.append(name)
                
        if missing:
            self.log(f"Missing prerequisites: {', '.join(missing)}", "FAIL")
            self.log("Please install the missing prerequisites and run the setup again.", "FAIL")
            return False
            
        self.log("All prerequisites are satisfied!", "INFO")
        return True
        
    def create_env_file(self) -> bool:
        """Create .env file with secure defaults"""
        self.log("Creating environment configuration...")
        
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        # Generate secure passwords
        postgres_password = self.generate_secure_password()
        redis_password = self.generate_secure_password()
        jwt_secret = self.generate_secure_password(64)
        
        env_content = f"""# Noryon LLM-Brain Trading System Environment Configuration
# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

# =============================================================================
# LLM API CONFIGURATION
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1

# DeepSeek Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# Anthropic Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Qwen Configuration
QWEN_API_KEY=your-qwen-api-key-here
QWEN_MODEL=qwen-turbo
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# Local LLM Configuration (Optional)
LOCAL_LLM_ENABLED=false
LOCAL_LLM_URL=http://localhost:11434
LOCAL_LLM_MODEL=llama2

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=noryon_llm_brain
POSTGRES_USER=noryon_user
POSTGRES_PASSWORD={postgres_password}

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD={redis_password}
REDIS_DB=0

# =============================================================================
# BROKER API CONFIGURATION
# =============================================================================
# Binance Configuration
BINANCE_API_KEY=your-binance-api-key-here
BINANCE_SECRET_KEY=your-binance-secret-key-here
BINANCE_TESTNET=true
BINANCE_BASE_URL=https://testnet.binance.vision

# Coinbase Pro Configuration
COINBASE_API_KEY=your-coinbase-api-key-here
COINBASE_SECRET_KEY=your-coinbase-secret-key-here
COINBASE_PASSPHRASE=your-coinbase-passphrase-here
COINBASE_SANDBOX=true

# OANDA Configuration
OANDA_API_KEY=your-oanda-api-key-here
OANDA_ACCOUNT_ID=your-oanda-account-id-here
OANDA_ENVIRONMENT=practice
OANDA_BASE_URL=https://api-fxpractice.oanda.com

# Interactive Brokers Configuration
IB_HOST=localhost
IB_PORT=7497
IB_CLIENT_ID=1
IB_ACCOUNT=your-ib-account-here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET_KEY={jwt_secret}
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# API Security
API_RATE_LIMIT=1000
API_RATE_LIMIT_WINDOW=3600
API_CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password-here
SMTP_USE_TLS=true

# Slack Configuration
SLACK_WEBHOOK_URL=your-slack-webhook-url-here
SLACK_CHANNEL=#trading-alerts

# Discord Configuration
DISCORD_WEBHOOK_URL=your-discord-webhook-url-here

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token-here
TELEGRAM_CHAT_ID=your-telegram-chat-id-here

# =============================================================================
# SYSTEM CONFIGURATION
# =============================================================================
# Application Settings
APP_NAME=Noryon LLM-Brain Trading System
APP_VERSION=1.0.0
APP_ENVIRONMENT={self.mode}
APP_DEBUG={'true' if self.mode == 'development' else 'false'}
APP_LOG_LEVEL={'DEBUG' if self.mode == 'development' else 'INFO'}

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
SERVER_WORKERS=4
SERVER_TIMEOUT=300

# Data Configuration
DATA_RETENTION_DAYS=90
BACKUP_RETENTION_DAYS=30
LOG_RETENTION_DAYS=7

# Performance Configuration
MAX_CONCURRENT_DECISIONS=10
DECISION_TIMEOUT_SECONDS=30
LLM_REQUEST_TIMEOUT=60
CACHE_TTL_SECONDS=300

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
# Prometheus Configuration
PROMETHEUS_PORT=9091
PROMETHEUS_SCRAPE_INTERVAL=15s

# Grafana Configuration
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin
GRAFANA_PORT=3000

# Elasticsearch Configuration
ELASTICSEARCH_HOST=elasticsearch
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_INDEX=noryon-logs

# Kibana Configuration
KIBANA_PORT=5601

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================
# Risk Management
MAX_POSITION_SIZE=0.1
MAX_DAILY_LOSS=0.02
MAX_DRAWDOWN=0.15
EMERGENCY_LIQUIDATION_THRESHOLD=0.08

# Trading Parameters
MIN_TRADE_SIZE_USD=10.0
MAX_TRADE_SIZE_USD=10000.0
DEFAULT_LEVERAGE=1.0
SLIPPAGE_TOLERANCE=0.001

# Asset Configuration
ENABLED_CRYPTO_PAIRS=BTCUSD,ETHUSD,ADAUSD,SOLUSD
ENABLED_FOREX_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD
ENABLED_STOCK_SYMBOLS=AAPL,GOOGL,MSFT,TSLA

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_CRYPTO_TRADING=true
ENABLE_FOREX_TRADING=true
ENABLE_STOCK_TRADING=false
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_NEWS_ANALYSIS=true
ENABLE_SOCIAL_MEDIA_ANALYSIS=false
ENABLE_BACKTESTING=true
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=false
"""
        
        try:
            with open(env_file, 'w') as f:
                f.write(env_content)
            self.log(f"Environment file created: {env_file}")
            
            # Create example file for reference
            if not env_example.exists():
                with open(env_example, 'w') as f:
                    f.write(env_content)
                self.log(f"Example environment file created: {env_example}")
                
            return True
        except Exception as e:
            self.log(f"Failed to create environment file: {e}", "FAIL")
            return False
            
    def install_dependencies(self) -> bool:
        """Install Python dependencies"""
        self.log("Installing Python dependencies...")
        
        requirements_content = """# Core Dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.13.0
psycopg2-binary==2.9.9
redis==5.0.1
celery==5.3.4

# LLM Client Dependencies
openai==1.3.7
anthropics==0.8.1
requests==2.31.0
httpx==0.25.2
aiohttp==3.9.1

# Data Processing
pandas==2.1.4
numpy==1.25.2
scipy==1.11.4
scikit-learn==1.3.2
ta-lib==0.4.28
yfinance==0.2.28
ccxt==4.1.64

# Monitoring and Logging
prometheus-client==0.19.0
structlog==23.2.0
loguru==0.7.2
sentry-sdk==1.38.0

# Security
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# Configuration
python-dotenv==1.0.0
PyYAML==6.0.1
click==8.1.7

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
factory-boy==3.3.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# WebSocket Support
websockets==12.0

# Async Support
aiofiles==23.2.1
aioredis==2.0.1

# Utilities
python-multipart==0.0.6
jinja2==3.1.2
markdown==3.5.1
"""
        
        requirements_file = self.project_root / "requirements.txt"
        
        try:
            with open(requirements_file, 'w') as f:
                f.write(requirements_content)
            self.log(f"Requirements file created: {requirements_file}")
            
            # Install dependencies
            if self.mode == "development":
                self.log("Installing dependencies in development mode...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log("Dependencies installed successfully!")
                else:
                    self.log(f"Failed to install dependencies: {result.stderr}", "FAIL")
                    return False
                    
            return True
        except Exception as e:
            self.log(f"Failed to create requirements file: {e}", "FAIL")
            return False
            
    def setup_database(self) -> bool:
        """Setup database schema and initial data"""
        self.log("Setting up database schema...")
        
        # Create database initialization script
        db_init_script = self.project_root / "scripts" / "init_database.sql"
        db_init_script.parent.mkdir(exist_ok=True)
        
        sql_content = """
-- Noryon LLM-Brain Trading System Database Schema
-- Generated on {timestamp}

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS trading;
CREATE SCHEMA IF NOT EXISTS analytics;
CREATE SCHEMA IF NOT EXISTS monitoring;

-- Trading Tables
CREATE TABLE IF NOT EXISTS trading.decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    llm_provider VARCHAR(50) NOT NULL,
    market_context JSONB NOT NULL,
    portfolio_state JSONB NOT NULL,
    decision JSONB NOT NULL,
    confidence_score DECIMAL(3,2),
    execution_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS trading.orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    decision_id UUID REFERENCES trading.decisions(id),
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    order_type VARCHAR(20) NOT NULL,
    quantity DECIMAL(18,8) NOT NULL,
    price DECIMAL(18,8),
    status VARCHAR(20) DEFAULT 'pending',
    broker VARCHAR(50) NOT NULL,
    broker_order_id VARCHAR(100),
    filled_quantity DECIMAL(18,8) DEFAULT 0,
    average_price DECIMAL(18,8),
    commission DECIMAL(18,8),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS trading.positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    quantity DECIMAL(18,8) NOT NULL,
    average_price DECIMAL(18,8) NOT NULL,
    current_price DECIMAL(18,8),
    unrealized_pnl DECIMAL(18,8),
    broker VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(symbol, broker)
);

-- Analytics Tables
CREATE TABLE IF NOT EXISTS analytics.performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL,
    total_return DECIMAL(10,4),
    sharpe_ratio DECIMAL(10,4),
    max_drawdown DECIMAL(10,4),
    win_rate DECIMAL(5,4),
    profit_factor DECIMAL(10,4),
    total_trades INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS analytics.llm_performance (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    llm_provider VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    average_response_time DECIMAL(10,3),
    total_tokens_used INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    decision_accuracy DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Monitoring Tables
CREATE TABLE IF NOT EXISTS monitoring.system_health (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    service_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    disk_usage DECIMAL(5,2),
    response_time DECIMAL(10,3),
    error_count INTEGER DEFAULT 0
);

CREATE TABLE IF NOT EXISTS monitoring.alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_by VARCHAR(100),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_decisions_timestamp ON trading.decisions(timestamp);
CREATE INDEX IF NOT EXISTS idx_decisions_llm_provider ON trading.decisions(llm_provider);
CREATE INDEX IF NOT EXISTS idx_orders_symbol ON trading.orders(symbol);
CREATE INDEX IF NOT EXISTS idx_orders_status ON trading.orders(status);
CREATE INDEX IF NOT EXISTS idx_positions_symbol ON trading.positions(symbol);
CREATE INDEX IF NOT EXISTS idx_performance_date ON analytics.performance_metrics(date);
CREATE INDEX IF NOT EXISTS idx_llm_performance_provider_date ON analytics.llm_performance(llm_provider, date);
CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON monitoring.system_health(timestamp);
CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON monitoring.alerts(timestamp);
CREATE INDEX IF NOT EXISTS idx_alerts_severity ON monitoring.alerts(severity);

-- Create views for common queries
CREATE OR REPLACE VIEW analytics.daily_performance AS
SELECT 
    date,
    total_return,
    sharpe_ratio,
    max_drawdown,
    win_rate,
    total_trades
FROM analytics.performance_metrics
ORDER BY date DESC;

CREATE OR REPLACE VIEW analytics.llm_provider_comparison AS
SELECT 
    llm_provider,
    AVG(decision_accuracy) as avg_accuracy,
    AVG(average_response_time) as avg_response_time,
    SUM(total_cost) as total_cost,
    SUM(total_requests) as total_requests
FROM analytics.llm_performance
WHERE date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY llm_provider
ORDER BY avg_accuracy DESC;

-- Insert initial configuration data
INSERT INTO monitoring.system_health (service_name, status, cpu_usage, memory_usage, disk_usage, response_time)
VALUES 
    ('llm-brain-engine', 'healthy', 0.0, 0.0, 0.0, 0.0),
    ('database', 'healthy', 0.0, 0.0, 0.0, 0.0),
    ('redis', 'healthy', 0.0, 0.0, 0.0, 0.0),
    ('api-server', 'healthy', 0.0, 0.0, 0.0, 0.0)
ON CONFLICT DO NOTHING;

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_decisions_updated_at BEFORE UPDATE ON trading.decisions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON trading.orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON trading.positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
""".format(timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        try:
            with open(db_init_script, 'w') as f:
                f.write(sql_content)
            self.log(f"Database initialization script created: {db_init_script}")
            return True
        except Exception as e:
            self.log(f"Failed to create database script: {e}", "FAIL")
            return False
            
    def create_docker_override(self) -> bool:
        """Create Docker Compose override for development"""
        if self.mode != "development":
            return True
            
        self.log("Creating Docker Compose override for development...")
        
        override_content = """
version: '3.8'

services:
  noryon-llm-brain:
    volumes:
      - ./core:/app/core
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      - APP_DEBUG=true
      - APP_LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    command: >
      sh -c "pip install -e . && 
             uvicorn core.ai.llm_brain_architecture:app 
             --host 0.0.0.0 
             --port 8080 
             --reload 
             --log-level debug"
    
  postgres:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=noryon_llm_brain_dev
      
  redis:
    ports:
      - "6379:6379"
      
  grafana:
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=dev_password
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      
  prometheus:
    ports:
      - "9090:9090"
"""
        
        override_file = self.project_root / "docker-compose.override.yml"
        
        try:
            with open(override_file, 'w') as f:
                f.write(override_content)
            self.log(f"Docker Compose override created: {override_file}")
            return True
        except Exception as e:
            self.log(f"Failed to create Docker override: {e}", "FAIL")
            return False
            
    def validate_configuration(self) -> bool:
        """Validate the configuration files"""
        self.log("Validating configuration...")
        
        config_files = [
            self.project_root / ".env",
            self.config_dir / "llm_brain_config.yaml",
            self.project_root / "docker-compose.llm-brain.yml"
        ]
        
        for config_file in config_files:
            if not config_file.exists():
                self.log(f"Missing configuration file: {config_file}", "FAIL")
                return False
            else:
                self.log(f"✓ Configuration file found: {config_file}")
                
        # Validate YAML files
        yaml_files = [self.config_dir / "llm_brain_config.yaml"]
        for yaml_file in yaml_files:
            try:
                with open(yaml_file, 'r') as f:
                    yaml.safe_load(f)
                self.log(f"✓ YAML file is valid: {yaml_file}")
            except yaml.YAMLError as e:
                self.log(f"Invalid YAML file {yaml_file}: {e}", "FAIL")
                return False
                
        self.log("Configuration validation completed successfully!")
        return True
        
    def deploy_system(self) -> bool:
        """Deploy the system using Docker Compose"""
        self.log("Deploying LLM-Brain trading system...")
        
        try:
            # Pull latest images
            self.log("Pulling Docker images...")
            result = subprocess.run([
                "docker-compose", "-f", "docker-compose.llm-brain.yml", "pull"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode != 0:
                self.log(f"Failed to pull images: {result.stderr}", "WARN")
                
            # Start services
            self.log("Starting services...")
            result = subprocess.run([
                "docker-compose", "-f", "docker-compose.llm-brain.yml", "up", "-d"
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                self.log("Services started successfully!")
                self.log("System is now running. Access points:")
                self.log("  - Main Dashboard: http://localhost:3001")
                self.log("  - API Endpoint: http://localhost:8080")
                self.log("  - Grafana: http://localhost:3000 (admin/admin)")
                self.log("  - Kibana: http://localhost:5601")
                return True
            else:
                self.log(f"Failed to start services: {result.stderr}", "FAIL")
                return False
                
        except Exception as e:
            self.log(f"Deployment failed: {e}", "FAIL")
            return False
            
    def save_setup_log(self):
        """Save setup log to file"""
        log_file = self.logs_dir / f"setup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        try:
            with open(log_file, 'w') as f:
                f.write("\n".join(self.setup_log))
            self.log(f"Setup log saved to: {log_file}")
        except Exception as e:
            self.log(f"Failed to save setup log: {e}", "WARN")
            
    def run_quick_start(self) -> bool:
        """Run quick start setup"""
        self.log(f"Starting Noryon LLM-Brain Trading System setup in {self.mode} mode...")
        
        steps = [
            ("Checking prerequisites", self.check_prerequisites),
            ("Creating environment configuration", self.create_env_file),
            ("Installing dependencies", self.install_dependencies),
            ("Setting up database schema", self.setup_database),
            ("Creating Docker override", self.create_docker_override),
            ("Validating configuration", self.validate_configuration)
        ]
        
        for step_name, step_func in steps:
            self.log(f"\n{'='*60}")
            self.log(f"Step: {step_name}")
            self.log(f"{'='*60}")
            
            if not step_func():
                self.log(f"Setup failed at step: {step_name}", "FAIL")
                self.save_setup_log()
                return False
                
        self.log(f"\n{'='*60}")
        self.log("Setup completed successfully!")
        self.log(f"{'='*60}")
        
        self.log("\nNext steps:")
        self.log("1. Edit .env file with your API keys and configuration")
        self.log("2. Run: docker-compose -f docker-compose.llm-brain.yml up -d")
        self.log("3. Access the dashboard at http://localhost:3001")
        self.log("4. Check the README_LLM_BRAIN.md for detailed documentation")
        
        self.save_setup_log()
        return True
        
def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Noryon LLM-Brain Trading System Setup",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python setup_llm_brain.py --quick-start
  python setup_llm_brain.py --mode production
  python setup_llm_brain.py --validate-config
  python setup_llm_brain.py --deploy
        """
    )
    
    parser.add_argument(
        "--mode", 
        choices=["development", "production"], 
        default="development",
        help="Setup mode (default: development)"
    )
    
    parser.add_argument(
        "--quick-start", 
        action="store_true",
        help="Run complete quick start setup"
    )
    
    parser.add_argument(
        "--validate-config", 
        action="store_true",
        help="Validate configuration files only"
    )
    
    parser.add_argument(
        "--deploy", 
        action="store_true",
        help="Deploy the system using Docker Compose"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print(f"""{Colors.HEADER}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🧠 Noryon LLM-Brain Trading System Setup                  ║
║                                                              ║
║    Advanced AI Trading Platform with LLM Decision Engine    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}""")
    
    setup = LLMBrainSetup(mode=args.mode)
    
    try:
        if args.validate_config:
            success = setup.validate_configuration()
        elif args.deploy:
            success = setup.deploy_system()
        elif args.quick_start:
            success = setup.run_quick_start()
        else:
            # Default to quick start
            success = setup.run_quick_start()
            
        if success:
            print(f"\n{Colors.OKGREEN}✅ Setup completed successfully!{Colors.ENDC}")
            sys.exit(0)
        else:
            print(f"\n{Colors.FAIL}❌ Setup failed. Check the logs for details.{Colors.ENDC}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}⚠️  Setup interrupted by user.{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.FAIL}❌ Unexpected error: {e}{Colors.ENDC}")
        sys.exit(1)
        
if __name__ == "__main__":
    main()