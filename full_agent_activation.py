#!/usr/bin/env python3
"""
Full Agent Activation - Activate ALL Available Reasoning Models
"""

import subprocess
import time
from datetime import datetime
from agent_command_center import Agent<PERSON>ommandCenter

def activate_all_available_agents():
    """Activate all available reasoning models"""
    print("🚀 FULL AGENT ACTIVATION - PHASE B")
    print("=" * 60)
    
    # Initialize command center
    center = AgentCommandCenter()
    
    print(f"Starting with {len(center.agents)} working agents")
    print(f"Testing {len(center.potential_agents)} potential agents")
    
    # Test all potential agents
    activated_count = 0
    failed_count = 0
    
    potential_agents = list(center.potential_agents.keys())
    
    for i, agent_name in enumerate(potential_agents, 1):
        print(f"\n🧪 Testing {i}/{len(potential_agents)}: {agent_name}")
        
        if center.test_potential_agent(agent_name):
            activated_count += 1
            print(f"   ✅ ACTIVATED: {agent_name}")
        else:
            failed_count += 1
            print(f"   ❌ FAILED: {agent_name}")
    
    print(f"\n📊 ACTIVATION RESULTS:")
    print(f"   Tested: {len(potential_agents)}")
    print(f"   Activated: {activated_count}")
    print(f"   Failed: {failed_count}")
    print(f"   Total working agents: {len(center.agents)}")
    
    # Show all working agents
    print(f"\n🤖 ALL WORKING AGENTS:")
    for i, (agent_id, details) in enumerate(center.agents.items(), 1):
        print(f"   {i}. {agent_id}: {details['specialty']}")
    
    return center

def test_multi_agent_power(center):
    """Test the power of multiple agents working together"""
    print(f"\n🧠 TESTING MULTI-AGENT POWER")
    print("=" * 50)
    
    # Test with all available agents
    available_agents = list(center.agents.keys())
    
    if len(available_agents) >= 3:
        test_agents = available_agents[:3]  # Use top 3 agents
        
        print(f"Testing with {len(test_agents)} agents: {test_agents}")
        
        # Complex analysis question
        question = """Comprehensive analysis: Should I invest $100,000 in cryptocurrency right now?
        
Consider:
1. Market conditions and trends
2. Risk factors and mitigation
3. Portfolio allocation strategy
4. Entry and exit strategies
5. Timeline and objectives

Provide specific recommendations with reasoning."""

        summary = center.multi_agent_analysis(question, agents=test_agents)
        
        print(f"\n📊 MULTI-AGENT ANALYSIS RESULTS:")
        print(f"   Agents used: {summary['agents_queried']}")
        print(f"   Successful responses: {summary['successful_responses']}")
        print(f"   Success rate: {summary['success_rate']:.1%}")
        print(f"   Total time: {summary['total_time']:.1f}s")
        
        if summary['successful_responses'] > 0:
            print(f"   ✅ Multi-agent analysis successful!")
            return True
        else:
            print(f"   ❌ Multi-agent analysis failed")
            return False
    else:
        print(f"   ⚠️ Need at least 3 agents for multi-agent test")
        return False

def main():
    """Full agent activation"""
    start_time = time.time()
    
    # Step 1: Activate all agents
    center = activate_all_available_agents()
    
    # Step 2: Test multi-agent power
    multi_agent_success = test_multi_agent_power(center)
    
    total_time = time.time() - start_time
    
    # Final summary
    print(f"\n🎉 FULL AGENT ACTIVATION COMPLETE!")
    print(f"   Total working agents: {len(center.agents)}")
    print(f"   Multi-agent capability: {'✅ Working' if multi_agent_success else '❌ Failed'}")
    print(f"   Total activation time: {total_time:.1f}s")
    
    # Show capabilities
    capabilities = [
        'Deep reasoning',
        'Financial analysis', 
        'Strategic planning',
        'Risk assessment',
        'Market analysis',
        'Mathematical reasoning',
        'Quick insights',
        'Cognitive analysis'
    ]
    
    print(f"\n🎯 AVAILABLE CAPABILITIES:")
    for capability in capabilities:
        print(f"   • {capability}")
    
    return center

if __name__ == "__main__":
    center = main()
