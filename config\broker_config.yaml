# Noryon AI Broker Configuration
# Configuration for different broker integrations

brokers:
  # Paper Trading Broker (Default)
  paper:
    enabled: true
    priority: 1
    type: "paper"
    initial_balance: 100000
    commission: 0.001
    slippage: 0.0005
    realistic_execution: true
    
  # Alpaca (Paper Trading)
  alpaca_paper:
    enabled: false
    priority: 2
    type: "alpaca"
    mode: "paper"
    base_url: "https://paper-api.alpaca.markets"
    api_key: "${ALPACA_PAPER_API_KEY}"
    secret_key: "${ALPACA_PAPER_SECRET_KEY}"
    
  # Alpaca (Live Trading)
  alpaca_live:
    enabled: false
    priority: 3
    type: "alpaca"
    mode: "live"
    base_url: "https://api.alpaca.markets"
    api_key: "${ALPACA_LIVE_API_KEY}"
    secret_key: "${ALPACA_LIVE_SECRET_KEY}"
    
  # Interactive Brokers
  interactive_brokers:
    enabled: false
    priority: 4
    type: "ib"
    host: "127.0.0.1"
    port: 7497  # Paper: 7497, Live: 7496
    client_id: 1
    
  # TD Ameritrade
  td_ameritrade:
    enabled: false
    priority: 5
    type: "td"
    api_key: "${TD_API_KEY}"
    redirect_uri: "http://localhost:8080"
    
  # E*TRADE
  etrade:
    enabled: false
    priority: 6
    type: "etrade"
    consumer_key: "${ETRADE_CONSUMER_KEY}"
    consumer_secret: "${ETRADE_CONSUMER_SECRET}"
    
  # Robinhood (Unofficial)
  robinhood:
    enabled: false
    priority: 7
    type: "robinhood"
    username: "${ROBINHOOD_USERNAME}"
    password: "${ROBINHOOD_PASSWORD}"
    
# Broker Selection Strategy
selection:
  strategy: "priority"  # priority, round_robin, least_latency
  fallback_enabled: true
  health_check_interval: 60  # seconds
  
# Connection Settings
connection:
  timeout: 30
  retry_attempts: 3
  retry_delay: 5
  keep_alive: true
  
# Data Feed Configuration
data_feeds:
  primary: "paper"
  
  yahoo_finance:
    enabled: true
    priority: 1
    rate_limit: 2000  # requests per hour
    
  alpha_vantage:
    enabled: false
    priority: 2
    api_key: "${ALPHA_VANTAGE_API_KEY}"
    rate_limit: 500
    
  polygon:
    enabled: false
    priority: 3
    api_key: "${POLYGON_API_KEY}"
    
  iex_cloud:
    enabled: false
    priority: 4
    api_key: "${IEX_CLOUD_API_KEY}"
    
# Order Routing
order_routing:
  default_broker: "paper"
  
  # Route by asset type
  routing_rules:
    stocks: "paper"
    options: "paper"
    crypto: "paper"
    
  # Smart order routing
  smart_routing:
    enabled: false
    factors:
      - "commission"
      - "execution_speed"
      - "fill_quality"
      
# Risk Controls
risk_controls:
  # Per-broker limits
  broker_limits:
    paper:
      max_position_value: 50000  # $50k max position
      max_daily_volume: 1000000  # $1M daily volume
      
  # Global limits
  global_limits:
    max_total_exposure: 100000  # $100k total
    max_leverage: 1.0
    
# Monitoring
monitoring:
  health_checks:
    enabled: true
    interval: 60
    
  performance_tracking:
    enabled: true
    metrics:
      - "latency"
      - "fill_rate"
      - "slippage"
      
  alerts:
    connection_lost: true
    high_latency: true
    failed_orders: true
    
# Logging
logging:
  level: "INFO"
  file: "logs/broker.log"
  include_sensitive: false  # Don't log API keys
  
# Development Settings
development:
  mock_mode: false
  simulation_speed: 1.0  # 1x real time
  debug_orders: true
