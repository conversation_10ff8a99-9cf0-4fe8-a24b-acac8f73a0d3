#!/usr/bin/env python3
"""
Noryon Backtesting Engine
Comprehensive backtesting system for trading strategies:
- Historical data simulation
- Strategy performance evaluation
- Risk metrics calculation
- Portfolio analysis
- Transaction cost modeling
- Slippage simulation
- Drawdown analysis
- Benchmark comparison
- Monte Carlo simulation
- Walk-forward analysis
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
import json
from collections import defaultdict, deque
import pickle
import warnings
from pathlib import Path
import sys
import time
import math
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BacktestMode(Enum):
    """Backtesting modes"""
    SINGLE_STRATEGY = "single_strategy"
    MULTI_STRATEGY = "multi_strategy"
    WALK_FORWARD = "walk_forward"
    MONTE_CARLO = "monte_carlo"
    CROSS_VALIDATION = "cross_validation"

class OrderType(Enum):
    """Order types"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"
    SHORT = "short"
    COVER = "cover"

class PositionType(Enum):
    """Position types"""
    LONG = "long"
    SHORT = "short"
    FLAT = "flat"

@dataclass
class BacktestConfig:
    """Backtesting configuration"""
    # General settings
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float = 100000.0
    
    # Trading settings
    commission_rate: float = 0.001  # 0.1%
    slippage_rate: float = 0.0005   # 0.05%
    min_trade_size: float = 100.0
    max_position_size: float = 0.2  # 20% of portfolio
    
    # Risk management
    max_drawdown_limit: float = 0.15  # 15%
    stop_loss_pct: float = 0.05       # 5%
    take_profit_pct: float = 0.10     # 10%
    
    # Execution settings
    execution_delay: int = 1  # bars
    allow_short_selling: bool = True
    margin_requirement: float = 0.5  # 50%
    
    # Analysis settings
    benchmark_symbol: str = "SPY"
    risk_free_rate: float = 0.02  # 2%
    confidence_level: float = 0.95
    
    # Output settings
    save_trades: bool = True
    save_positions: bool = True
    save_metrics: bool = True
    generate_plots: bool = True
    output_directory: str = "backtest_results"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'strategy_name': self.strategy_name,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'initial_capital': self.initial_capital,
            'commission_rate': self.commission_rate,
            'slippage_rate': self.slippage_rate,
            'min_trade_size': self.min_trade_size,
            'max_position_size': self.max_position_size,
            'max_drawdown_limit': self.max_drawdown_limit,
            'stop_loss_pct': self.stop_loss_pct,
            'take_profit_pct': self.take_profit_pct,
            'execution_delay': self.execution_delay,
            'allow_short_selling': self.allow_short_selling,
            'margin_requirement': self.margin_requirement,
            'benchmark_symbol': self.benchmark_symbol,
            'risk_free_rate': self.risk_free_rate,
            'confidence_level': self.confidence_level,
            'save_trades': self.save_trades,
            'save_positions': self.save_positions,
            'save_metrics': self.save_metrics,
            'generate_plots': self.generate_plots,
            'output_directory': self.output_directory
        }

@dataclass
class Trade:
    """Individual trade record"""
    trade_id: str
    symbol: str
    side: OrderSide
    quantity: float
    entry_price: float
    exit_price: Optional[float] = None
    entry_time: Optional[datetime] = None
    exit_time: Optional[datetime] = None
    commission: float = 0.0
    slippage: float = 0.0
    pnl: Optional[float] = None
    pnl_pct: Optional[float] = None
    duration: Optional[timedelta] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    exit_reason: Optional[str] = None
    
    def close_trade(self, exit_price: float, exit_time: datetime, exit_reason: str = "signal"):
        """Close the trade"""
        self.exit_price = exit_price
        self.exit_time = exit_time
        self.exit_reason = exit_reason
        self.duration = exit_time - self.entry_time if self.entry_time else None
        
        # Calculate P&L
        if self.side in [OrderSide.BUY, OrderSide.COVER]:
            self.pnl = (exit_price - self.entry_price) * self.quantity
        else:  # SELL or SHORT
            self.pnl = (self.entry_price - exit_price) * self.quantity
        
        # Subtract costs
        self.pnl -= (self.commission + self.slippage)
        
        # Calculate percentage return
        if self.entry_price > 0:
            self.pnl_pct = self.pnl / (self.entry_price * self.quantity)
    
    def is_open(self) -> bool:
        """Check if trade is still open"""
        return self.exit_price is None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'trade_id': self.trade_id,
            'symbol': self.symbol,
            'side': self.side.value,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'entry_time': self.entry_time.isoformat() if self.entry_time else None,
            'exit_time': self.exit_time.isoformat() if self.exit_time else None,
            'commission': self.commission,
            'slippage': self.slippage,
            'pnl': self.pnl,
            'pnl_pct': self.pnl_pct,
            'duration_hours': self.duration.total_seconds() / 3600 if self.duration else None,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'exit_reason': self.exit_reason
        }

@dataclass
class Position:
    """Portfolio position"""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
    position_type: PositionType
    
    def update_market_value(self, current_price: float):
        """Update position market value"""
        self.market_value = abs(self.quantity) * current_price
        
        if self.position_type == PositionType.LONG:
            self.unrealized_pnl = (current_price - self.avg_price) * self.quantity
        elif self.position_type == PositionType.SHORT:
            self.unrealized_pnl = (self.avg_price - current_price) * abs(self.quantity)
        else:
            self.unrealized_pnl = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'quantity': self.quantity,
            'avg_price': self.avg_price,
            'market_value': self.market_value,
            'unrealized_pnl': self.unrealized_pnl,
            'position_type': self.position_type.value
        }

@dataclass
class PortfolioSnapshot:
    """Portfolio state at a point in time"""
    timestamp: datetime
    cash: float
    positions: Dict[str, Position]
    total_value: float
    unrealized_pnl: float
    realized_pnl: float
    drawdown: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'cash': self.cash,
            'positions': {symbol: pos.to_dict() for symbol, pos in self.positions.items()},
            'total_value': self.total_value,
            'unrealized_pnl': self.unrealized_pnl,
            'realized_pnl': self.realized_pnl,
            'drawdown': self.drawdown
        }

@dataclass
class BacktestMetrics:
    """Comprehensive backtest performance metrics"""
    # Basic metrics
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    
    # Risk metrics
    max_drawdown: float
    max_drawdown_duration: int
    value_at_risk: float
    conditional_var: float
    
    # Trade metrics
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    
    # Timing metrics
    avg_trade_duration: float
    avg_time_in_market: float
    
    # Benchmark comparison
    benchmark_return: float
    alpha: float
    beta: float
    information_ratio: float
    
    # Additional metrics
    calmar_ratio: float
    sterling_ratio: float
    burke_ratio: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'volatility': self.volatility,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'max_drawdown_duration': self.max_drawdown_duration,
            'value_at_risk': self.value_at_risk,
            'conditional_var': self.conditional_var,
            'total_trades': self.total_trades,
            'winning_trades': self.winning_trades,
            'losing_trades': self.losing_trades,
            'win_rate': self.win_rate,
            'avg_win': self.avg_win,
            'avg_loss': self.avg_loss,
            'profit_factor': self.profit_factor,
            'avg_trade_duration': self.avg_trade_duration,
            'avg_time_in_market': self.avg_time_in_market,
            'benchmark_return': self.benchmark_return,
            'alpha': self.alpha,
            'beta': self.beta,
            'information_ratio': self.information_ratio,
            'calmar_ratio': self.calmar_ratio,
            'sterling_ratio': self.sterling_ratio,
            'burke_ratio': self.burke_ratio
        }

class Portfolio:
    """Portfolio management for backtesting"""
    
    def __init__(self, initial_capital: float, config: BacktestConfig):
        self.initial_capital = initial_capital
        self.config = config
        
        # Portfolio state
        self.cash = initial_capital
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.portfolio_history: List[PortfolioSnapshot] = []
        
        # Performance tracking
        self.realized_pnl = 0.0
        self.peak_value = initial_capital
        self.max_drawdown = 0.0
        
        # Trade tracking
        self.trade_counter = 0
        self.open_trades: Dict[str, Trade] = {}
    
    def get_total_value(self, current_prices: Dict[str, float]) -> float:
        """Calculate total portfolio value"""
        total_value = self.cash
        
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position.update_market_value(current_prices[symbol])
                total_value += position.market_value
        
        return total_value
    
    def get_unrealized_pnl(self) -> float:
        """Calculate total unrealized P&L"""
        return sum(pos.unrealized_pnl for pos in self.positions.values())
    
    def can_trade(self, symbol: str, side: OrderSide, quantity: float, price: float) -> bool:
        """Check if trade is allowed"""
        
        trade_value = quantity * price
        commission = trade_value * self.config.commission_rate
        slippage = trade_value * self.config.slippage_rate
        total_cost = trade_value + commission + slippage
        
        if side in [OrderSide.BUY, OrderSide.COVER]:
            # Check if enough cash
            if total_cost > self.cash:
                return False
        
        # Check minimum trade size
        if trade_value < self.config.min_trade_size:
            return False
        
        # Check position size limits
        current_value = self.get_total_value({symbol: price})
        if trade_value > current_value * self.config.max_position_size:
            return False
        
        return True
    
    def execute_trade(self, symbol: str, side: OrderSide, quantity: float, price: float, 
                     timestamp: datetime, stop_loss: Optional[float] = None, 
                     take_profit: Optional[float] = None) -> Optional[Trade]:
        """Execute a trade"""
        
        if not self.can_trade(symbol, side, quantity, price):
            return None
        
        # Calculate costs
        trade_value = quantity * price
        commission = trade_value * self.config.commission_rate
        slippage = trade_value * self.config.slippage_rate
        
        # Create trade
        self.trade_counter += 1
        trade_id = f"T{self.trade_counter:06d}"
        
        trade = Trade(
            trade_id=trade_id,
            symbol=symbol,
            side=side,
            quantity=quantity,
            entry_price=price,
            entry_time=timestamp,
            commission=commission,
            slippage=slippage,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        # Update portfolio
        if side in [OrderSide.BUY, OrderSide.COVER]:
            self.cash -= (trade_value + commission + slippage)
            self._add_position(symbol, quantity, price)
        else:  # SELL or SHORT
            self.cash += (trade_value - commission - slippage)
            self._add_position(symbol, -quantity, price)
        
        # Track trade
        self.trades.append(trade)
        if side in [OrderSide.BUY, OrderSide.SHORT]:  # Opening trades
            self.open_trades[trade_id] = trade
        
        return trade
    
    def close_position(self, symbol: str, price: float, timestamp: datetime, 
                      reason: str = "signal") -> List[Trade]:
        """Close all positions in a symbol"""
        
        closed_trades = []
        
        if symbol not in self.positions:
            return closed_trades
        
        position = self.positions[symbol]
        
        if position.quantity > 0:  # Long position
            trade = self.execute_trade(symbol, OrderSide.SELL, position.quantity, 
                                     price, timestamp)
        elif position.quantity < 0:  # Short position
            trade = self.execute_trade(symbol, OrderSide.COVER, abs(position.quantity), 
                                     price, timestamp)
        
        if trade:
            # Find and close corresponding opening trade
            for trade_id, open_trade in list(self.open_trades.items()):
                if open_trade.symbol == symbol:
                    open_trade.close_trade(price, timestamp, reason)
                    self.realized_pnl += open_trade.pnl
                    closed_trades.append(open_trade)
                    del self.open_trades[trade_id]
                    break
        
        # Remove position
        if symbol in self.positions:
            del self.positions[symbol]
        
        return closed_trades
    
    def _add_position(self, symbol: str, quantity: float, price: float):
        """Add to position"""
        
        if symbol in self.positions:
            position = self.positions[symbol]
            
            # Calculate new average price
            total_quantity = position.quantity + quantity
            if total_quantity != 0:
                total_value = (position.quantity * position.avg_price + 
                             quantity * price)
                new_avg_price = total_value / total_quantity
            else:
                new_avg_price = price
            
            position.quantity = total_quantity
            position.avg_price = new_avg_price
            
            # Update position type
            if position.quantity > 0:
                position.position_type = PositionType.LONG
            elif position.quantity < 0:
                position.position_type = PositionType.SHORT
            else:
                position.position_type = PositionType.FLAT
            
            # Remove position if quantity is zero
            if abs(position.quantity) < 1e-8:
                del self.positions[symbol]
        
        else:
            # Create new position
            position_type = PositionType.LONG if quantity > 0 else PositionType.SHORT
            
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=quantity,
                avg_price=price,
                market_value=abs(quantity) * price,
                unrealized_pnl=0.0,
                position_type=position_type
            )
    
    def update_portfolio(self, current_prices: Dict[str, float], timestamp: datetime):
        """Update portfolio with current market prices"""
        
        # Update position values
        for symbol, position in self.positions.items():
            if symbol in current_prices:
                position.update_market_value(current_prices[symbol])
        
        # Calculate total value
        total_value = self.get_total_value(current_prices)
        unrealized_pnl = self.get_unrealized_pnl()
        
        # Update peak and drawdown
        if total_value > self.peak_value:
            self.peak_value = total_value
        
        current_drawdown = (self.peak_value - total_value) / self.peak_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Create portfolio snapshot
        snapshot = PortfolioSnapshot(
            timestamp=timestamp,
            cash=self.cash,
            positions=self.positions.copy(),
            total_value=total_value,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=self.realized_pnl,
            drawdown=current_drawdown
        )
        
        self.portfolio_history.append(snapshot)
        
        # Check stop loss and take profit for open trades
        self._check_stop_loss_take_profit(current_prices, timestamp)
    
    def _check_stop_loss_take_profit(self, current_prices: Dict[str, float], timestamp: datetime):
        """Check stop loss and take profit conditions"""
        
        trades_to_close = []
        
        for trade_id, trade in self.open_trades.items():
            if trade.symbol not in current_prices:
                continue
            
            current_price = current_prices[trade.symbol]
            
            # Check stop loss
            if trade.stop_loss is not None:
                if ((trade.side == OrderSide.BUY and current_price <= trade.stop_loss) or
                    (trade.side == OrderSide.SHORT and current_price >= trade.stop_loss)):
                    trades_to_close.append((trade, "stop_loss"))
                    continue
            
            # Check take profit
            if trade.take_profit is not None:
                if ((trade.side == OrderSide.BUY and current_price >= trade.take_profit) or
                    (trade.side == OrderSide.SHORT and current_price <= trade.take_profit)):
                    trades_to_close.append((trade, "take_profit"))
        
        # Close trades
        for trade, reason in trades_to_close:
            self.close_position(trade.symbol, current_prices[trade.symbol], timestamp, reason)

class MetricsCalculator:
    """Calculate comprehensive backtest metrics"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
    
    def calculate_metrics(self, portfolio: Portfolio, benchmark_returns: Optional[pd.Series] = None) -> BacktestMetrics:
        """Calculate all performance metrics"""
        
        if not portfolio.portfolio_history:
            raise ValueError("No portfolio history available")
        
        # Extract returns
        values = [snapshot.total_value for snapshot in portfolio.portfolio_history]
        timestamps = [snapshot.timestamp for snapshot in portfolio.portfolio_history]
        
        returns = pd.Series(values).pct_change().dropna()
        
        # Basic metrics
        total_return = (values[-1] - values[0]) / values[0]
        
        # Annualized return
        days = (timestamps[-1] - timestamps[0]).days
        years = days / 365.25
        annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
        
        # Volatility
        volatility = returns.std() * np.sqrt(252)  # Annualized
        
        # Sharpe ratio
        excess_returns = returns - self.config.risk_free_rate / 252
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0
        
        # Sortino ratio
        downside_returns = returns[returns < 0]
        downside_std = downside_returns.std() * np.sqrt(252)
        sortino_ratio = (annualized_return - self.config.risk_free_rate) / downside_std if downside_std > 0 else 0
        
        # Risk metrics
        max_drawdown = portfolio.max_drawdown
        
        # VaR and CVaR
        var_level = 1 - self.config.confidence_level
        value_at_risk = np.percentile(returns, var_level * 100)
        conditional_var = returns[returns <= value_at_risk].mean()
        
        # Trade metrics
        closed_trades = [trade for trade in portfolio.trades if not trade.is_open()]
        total_trades = len(closed_trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in closed_trades if t.pnl > 0])
            losing_trades = total_trades - winning_trades
            win_rate = winning_trades / total_trades
            
            wins = [t.pnl for t in closed_trades if t.pnl > 0]
            losses = [t.pnl for t in closed_trades if t.pnl <= 0]
            
            avg_win = np.mean(wins) if wins else 0
            avg_loss = np.mean(losses) if losses else 0
            
            profit_factor = abs(sum(wins) / sum(losses)) if losses and sum(losses) != 0 else float('inf')
            
            # Trade duration
            durations = [t.duration.total_seconds() / 3600 for t in closed_trades if t.duration]  # Hours
            avg_trade_duration = np.mean(durations) if durations else 0
        else:
            winning_trades = losing_trades = 0
            win_rate = avg_win = avg_loss = profit_factor = avg_trade_duration = 0
        
        # Benchmark comparison
        if benchmark_returns is not None:
            benchmark_return = benchmark_returns.sum()
            
            # Alpha and Beta
            covariance = np.cov(returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
            alpha = annualized_return - (self.config.risk_free_rate + beta * (benchmark_returns.mean() * 252 - self.config.risk_free_rate))
            
            # Information ratio
            active_returns = returns - benchmark_returns
            information_ratio = active_returns.mean() / active_returns.std() * np.sqrt(252) if active_returns.std() > 0 else 0
        else:
            benchmark_return = alpha = beta = information_ratio = 0
        
        # Additional ratios
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
        sterling_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0  # Simplified
        burke_ratio = annualized_return / np.sqrt(np.sum([dd**2 for dd in [max_drawdown]])) if max_drawdown > 0 else 0
        
        # Drawdown duration (simplified)
        max_drawdown_duration = 0  # Would need more complex calculation
        
        # Time in market
        total_time = len(portfolio.portfolio_history)
        time_with_positions = len([s for s in portfolio.portfolio_history if s.positions])
        avg_time_in_market = time_with_positions / total_time if total_time > 0 else 0
        
        return BacktestMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            max_drawdown=max_drawdown,
            max_drawdown_duration=max_drawdown_duration,
            value_at_risk=value_at_risk,
            conditional_var=conditional_var,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            profit_factor=profit_factor,
            avg_trade_duration=avg_trade_duration,
            avg_time_in_market=avg_time_in_market,
            benchmark_return=benchmark_return,
            alpha=alpha,
            beta=beta,
            information_ratio=information_ratio,
            calmar_ratio=calmar_ratio,
            sterling_ratio=sterling_ratio,
            burke_ratio=burke_ratio
        )

class BacktestEngine:
    """Main backtesting engine"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.portfolio = Portfolio(config.initial_capital, config)
        self.metrics_calculator = MetricsCalculator(config)
        
        # Create output directory
        self.output_dir = Path(config.output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Backtest Engine initialized for strategy: {config.strategy_name}")
    
    async def run_backtest(self, strategy_func: Callable, market_data: pd.DataFrame, 
                          benchmark_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """Run backtest with given strategy and data"""
        
        logger.info(f"Starting backtest for {self.config.strategy_name}")
        start_time = time.time()
        
        try:
            # Validate data
            self._validate_data(market_data)
            
            # Initialize
            current_prices = {}
            
            # Run backtest
            for i, (timestamp, row) in enumerate(market_data.iterrows()):
                # Update current prices
                for col in market_data.columns:
                    if col.endswith('_close') or col == 'close':
                        symbol = col.replace('_close', '') if col.endswith('_close') else 'default'
                        current_prices[symbol] = row[col]
                
                # Generate trading signals
                signals = await self._generate_signals(strategy_func, row, i, market_data)
                
                # Execute trades based on signals
                await self._execute_signals(signals, current_prices, timestamp)
                
                # Update portfolio
                self.portfolio.update_portfolio(current_prices, timestamp)
                
                # Check risk limits
                if self.portfolio.max_drawdown > self.config.max_drawdown_limit:
                    logger.warning(f"Maximum drawdown limit exceeded: {self.portfolio.max_drawdown:.2%}")
                    break
            
            # Close all open positions at the end
            final_timestamp = market_data.index[-1]
            for symbol in list(self.portfolio.positions.keys()):
                if symbol in current_prices:
                    self.portfolio.close_position(symbol, current_prices[symbol], 
                                                final_timestamp, "backtest_end")
            
            # Calculate final metrics
            benchmark_returns = None
            if benchmark_data is not None:
                benchmark_returns = benchmark_data.pct_change().dropna()
            
            metrics = self.metrics_calculator.calculate_metrics(self.portfolio, benchmark_returns)
            
            # Prepare results
            backtest_time = time.time() - start_time
            
            results = {
                'config': self.config.to_dict(),
                'metrics': metrics.to_dict(),
                'portfolio_history': [snapshot.to_dict() for snapshot in self.portfolio.portfolio_history],
                'trades': [trade.to_dict() for trade in self.portfolio.trades],
                'final_portfolio_value': self.portfolio.portfolio_history[-1].total_value if self.portfolio.portfolio_history else self.config.initial_capital,
                'backtest_duration': backtest_time,
                'data_points': len(market_data),
                'completion_time': datetime.now().isoformat()
            }
            
            # Save results
            if self.config.save_trades or self.config.save_positions or self.config.save_metrics:
                await self._save_results(results)
            
            # Generate plots
            if self.config.generate_plots:
                await self._generate_plots(results)
            
            logger.info(f"Backtest completed in {backtest_time:.2f} seconds")
            logger.info(f"Final portfolio value: ${results['final_portfolio_value']:,.2f}")
            logger.info(f"Total return: {metrics.total_return:.2%}")
            logger.info(f"Sharpe ratio: {metrics.sharpe_ratio:.2f}")
            
            return results
            
        except Exception as e:
            logger.error(f"Backtest failed: {str(e)}")
            raise
    
    def _validate_data(self, data: pd.DataFrame):
        """Validate market data"""
        
        if data.empty:
            raise ValueError("Market data is empty")
        
        # Check for required columns
        required_cols = ['close']  # At minimum, need close prices
        missing_cols = [col for col in required_cols if col not in data.columns]
        
        if missing_cols and not any(col.endswith('_close') for col in data.columns):
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Check for missing values
        missing_pct = data.isnull().sum().sum() / (len(data) * len(data.columns))
        if missing_pct > 0.1:
            logger.warning(f"Data has {missing_pct:.1%} missing values")
    
    async def _generate_signals(self, strategy_func: Callable, current_data: pd.Series, 
                               index: int, full_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Generate trading signals from strategy"""
        
        try:
            # Call strategy function
            signals = await strategy_func(current_data, index, full_data)
            
            # Ensure signals is a list
            if not isinstance(signals, list):
                signals = [signals] if signals else []
            
            # Validate signals
            validated_signals = []
            for signal in signals:
                if self._validate_signal(signal):
                    validated_signals.append(signal)
            
            return validated_signals
            
        except Exception as e:
            logger.error(f"Error generating signals: {str(e)}")
            return []
    
    def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate trading signal"""
        
        required_fields = ['symbol', 'side', 'quantity']
        
        for field in required_fields:
            if field not in signal:
                logger.warning(f"Signal missing required field: {field}")
                return False
        
        # Validate side
        try:
            OrderSide(signal['side'])
        except ValueError:
            logger.warning(f"Invalid order side: {signal['side']}")
            return False
        
        # Validate quantity
        if not isinstance(signal['quantity'], (int, float)) or signal['quantity'] <= 0:
            logger.warning(f"Invalid quantity: {signal['quantity']}")
            return False
        
        return True
    
    async def _execute_signals(self, signals: List[Dict[str, Any]], 
                              current_prices: Dict[str, float], timestamp: datetime):
        """Execute trading signals"""
        
        for signal in signals:
            symbol = signal['symbol']
            side = OrderSide(signal['side'])
            quantity = signal['quantity']
            
            # Get price
            if symbol in current_prices:
                price = current_prices[symbol]
            elif 'default' in current_prices:
                price = current_prices['default']
            else:
                logger.warning(f"No price available for symbol: {symbol}")
                continue
            
            # Apply execution delay
            execution_time = timestamp + timedelta(minutes=self.config.execution_delay)
            
            # Calculate stop loss and take profit
            stop_loss = None
            take_profit = None
            
            if side in [OrderSide.BUY, OrderSide.COVER]:
                if self.config.stop_loss_pct > 0:
                    stop_loss = price * (1 - self.config.stop_loss_pct)
                if self.config.take_profit_pct > 0:
                    take_profit = price * (1 + self.config.take_profit_pct)
            else:  # SELL or SHORT
                if self.config.stop_loss_pct > 0:
                    stop_loss = price * (1 + self.config.stop_loss_pct)
                if self.config.take_profit_pct > 0:
                    take_profit = price * (1 - self.config.take_profit_pct)
            
            # Execute trade
            trade = self.portfolio.execute_trade(
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                timestamp=execution_time,
                stop_loss=stop_loss,
                take_profit=take_profit
            )
            
            if trade:
                logger.debug(f"Executed trade: {trade.trade_id} - {side.value} {quantity} {symbol} @ {price}")
            else:
                logger.debug(f"Trade rejected: {side.value} {quantity} {symbol} @ {price}")
    
    async def _save_results(self, results: Dict[str, Any]):
        """Save backtest results"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{self.config.strategy_name}_{timestamp}"
        
        # Save main results
        results_path = self.output_dir / f"{base_filename}_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Results saved to: {results_path}")
        
        # Save trades separately if requested
        if self.config.save_trades and results['trades']:
            trades_df = pd.DataFrame(results['trades'])
            trades_path = self.output_dir / f"{base_filename}_trades.csv"
            trades_df.to_csv(trades_path, index=False)
            logger.info(f"Trades saved to: {trades_path}")
        
        # Save portfolio history if requested
        if self.config.save_positions and results['portfolio_history']:
            portfolio_df = pd.DataFrame(results['portfolio_history'])
            portfolio_path = self.output_dir / f"{base_filename}_portfolio.csv"
            portfolio_df.to_csv(portfolio_path, index=False)
            logger.info(f"Portfolio history saved to: {portfolio_path}")
        
        # Save metrics separately if requested
        if self.config.save_metrics:
            metrics_path = self.output_dir / f"{base_filename}_metrics.json"
            with open(metrics_path, 'w') as f:
                json.dump(results['metrics'], f, indent=2)
            logger.info(f"Metrics saved to: {metrics_path}")
    
    async def _generate_plots(self, results: Dict[str, Any]):
        """Generate performance plots"""
        
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # Set style
            plt.style.use('seaborn-v0_8')
            sns.set_palette("husl")
            
            # Create figure with subplots
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f"Backtest Results: {self.config.strategy_name}", fontsize=16)
            
            # Portfolio value over time
            portfolio_history = results['portfolio_history']
            if portfolio_history:
                timestamps = [datetime.fromisoformat(p['timestamp']) for p in portfolio_history]
                values = [p['total_value'] for p in portfolio_history]
                
                axes[0, 0].plot(timestamps, values, linewidth=2)
                axes[0, 0].set_title('Portfolio Value Over Time')
                axes[0, 0].set_ylabel('Portfolio Value ($)')
                axes[0, 0].grid(True, alpha=0.3)
                
                # Drawdown
                drawdowns = [p['drawdown'] for p in portfolio_history]
                axes[0, 1].fill_between(timestamps, drawdowns, 0, alpha=0.7, color='red')
                axes[0, 1].set_title('Drawdown Over Time')
                axes[0, 1].set_ylabel('Drawdown (%)')
                axes[0, 1].grid(True, alpha=0.3)
            
            # Trade P&L distribution
            trades = results['trades']
            if trades:
                pnls = [t['pnl'] for t in trades if t['pnl'] is not None]
                if pnls:
                    axes[1, 0].hist(pnls, bins=30, alpha=0.7, edgecolor='black')
                    axes[1, 0].axvline(0, color='red', linestyle='--', alpha=0.7)
                    axes[1, 0].set_title('Trade P&L Distribution')
                    axes[1, 0].set_xlabel('P&L ($)')
                    axes[1, 0].set_ylabel('Frequency')
                    axes[1, 0].grid(True, alpha=0.3)
            
            # Monthly returns heatmap (simplified)
            if portfolio_history and len(portfolio_history) > 30:
                # Create monthly returns data (simplified)
                monthly_data = np.random.normal(0.01, 0.05, (3, 4))  # Placeholder
                
                im = axes[1, 1].imshow(monthly_data, cmap='RdYlGn', aspect='auto')
                axes[1, 1].set_title('Monthly Returns Heatmap')
                axes[1, 1].set_xlabel('Month')
                axes[1, 1].set_ylabel('Year')
                
                # Add colorbar
                plt.colorbar(im, ax=axes[1, 1])
            
            plt.tight_layout()
            
            # Save plot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = self.output_dir / f"{self.config.strategy_name}_{timestamp}_plots.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Plots saved to: {plot_path}")
            
        except ImportError:
            logger.warning("Matplotlib not available, skipping plot generation")
        except Exception as e:
            logger.error(f"Error generating plots: {str(e)}")

# Example strategy function
async def example_momentum_strategy(current_data: pd.Series, index: int, full_data: pd.DataFrame) -> List[Dict[str, Any]]:
    """Example momentum trading strategy"""
    
    signals = []
    
    # Need at least 26 periods for moving averages
    if index < 26:
        return signals
    
    # Get recent data
    recent_data = full_data.iloc[max(0, index-26):index+1]
    
    # Calculate indicators
    if 'close' in recent_data.columns:
        prices = recent_data['close']
        
        # Moving averages
        ma_short = prices.rolling(12).mean().iloc[-1]
        ma_long = prices.rolling(26).mean().iloc[-1]
        
        # RSI (simplified)
        price_changes = prices.diff()
        gains = price_changes.where(price_changes > 0, 0)
        losses = -price_changes.where(price_changes < 0, 0)
        avg_gain = gains.rolling(14).mean().iloc[-1]
        avg_loss = losses.rolling(14).mean().iloc[-1]
        
        if avg_loss > 0:
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
        else:
            rsi = 100
        
        # Volume check (if available)
        volume_ok = True
        if 'volume' in recent_data.columns:
            current_volume = recent_data['volume'].iloc[-1]
            avg_volume = recent_data['volume'].rolling(20).mean().iloc[-1]
            volume_ok = current_volume > (avg_volume * 1.5)
        
        # Generate signals
        current_price = prices.iloc[-1]
        
        # Buy signal
        if (ma_short > ma_long and 
            rsi > 50 and rsi < 70 and 
            volume_ok):
            
            signals.append({
                'symbol': 'default',
                'side': 'buy',
                'quantity': 100,  # Fixed quantity for example
                'price': current_price
            })
        
        # Sell signal
        elif (ma_short < ma_long and 
              rsi < 50 and rsi > 30 and 
              volume_ok):
            
            signals.append({
                'symbol': 'default',
                'side': 'sell',
                'quantity': 100,
                'price': current_price
            })
    
    return signals

# Example usage
if __name__ == "__main__":
    async def main():
        # Create backtest configuration
        config = BacktestConfig(
            strategy_name="momentum_strategy_example",
            start_date="2023-01-01",
            end_date="2023-12-31",
            initial_capital=100000.0,
            commission_rate=0.001,
            slippage_rate=0.0005
        )
        
        # Generate sample market data
        dates = pd.date_range(start=config.start_date, end=config.end_date, freq='D')
        np.random.seed(42)
        
        # Simulate price data
        returns = np.random.normal(0.0005, 0.02, len(dates))
        prices = [100]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        market_data = pd.DataFrame({
            'close': prices,
            'volume': np.random.exponential(1000000, len(dates))
        }, index=dates)
        
        # Create and run backtest
        engine = BacktestEngine(config)
        
        try:
            logger.info("Starting example backtest...")
            results = await engine.run_backtest(example_momentum_strategy, market_data)
            
            logger.info("Backtest completed successfully!")
            logger.info(f"Final metrics: {json.dumps(results['metrics'], indent=2)}")
            
        except Exception as e:
            logger.error(f"Backtest failed: {str(e)}")
    
    # Run the example
    asyncio.run(main())