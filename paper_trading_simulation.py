#!/usr/bin/env python3
"""
Paper Trading Simulation System
Complete simulated trading environment for AI learning and testing
"""

import time
import json
import random
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

# Import our systems
from live_portfolio_tracker import LivePortfolioTracker
from order_management_system import OrderManagementSystem
from emergency_control_system import EmergencyControlSystem
from agent_command_center import AgentCommandCenter

@dataclass
class MarketSimulation:
    symbol: str
    base_price: float
    current_price: float
    volatility: float
    trend: float  # -1 to 1
    volume: float
    last_update: datetime

class PaperTradingSimulation:
    """Complete paper trading simulation for AI learning"""
    
    def __init__(self, initial_capital: float = 100000.0):
        # Initialize all systems
        self.portfolio = LivePortfolioTracker(initial_capital)
        self.order_system = OrderManagementSystem()
        self.emergency_system = EmergencyControlSystem()
        self.ai_agents = AgentCommandCenter()
        
        # Simulation settings
        self.simulation_active = False
        self.simulation_speed = 1.0  # 1.0 = real time, 10.0 = 10x speed
        self.market_hours = True
        
        # Market simulation
        self.market_data = {}
        self.price_history = {}
        
        # AI learning metrics
        self.ai_performance = {
            'total_decisions': 0,
            'profitable_decisions': 0,
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'best_trade': 0.0,
            'worst_trade': 0.0,
            'learning_progress': []
        }
        
        # Initialize market simulation
        self._initialize_market_simulation()
        
        # Setup callbacks
        self._setup_callbacks()
        
        print("📊 PAPER TRADING SIMULATION INITIALIZED")
        print(f"   💰 Virtual Capital: ${initial_capital:,.2f}")
        print(f"   📈 Markets: {len(self.market_data)} symbols")
        print(f"   🤖 AI Agents: {len(self.ai_agents.agents)}")
        print(f"   🎯 Learning Mode: ACTIVE")
    
    def _initialize_market_simulation(self):
        """Initialize simulated market data"""
        
        # Define markets to simulate
        markets = {
            'BTC-USD': {'base_price': 50000, 'volatility': 0.05, 'trend': 0.1},
            'ETH-USD': {'base_price': 3000, 'volatility': 0.06, 'trend': 0.05},
            'AAPL': {'base_price': 150, 'volatility': 0.02, 'trend': 0.02},
            'TSLA': {'base_price': 200, 'volatility': 0.08, 'trend': -0.01},
            'SPY': {'base_price': 400, 'volatility': 0.015, 'trend': 0.01},
            'NVDA': {'base_price': 800, 'volatility': 0.07, 'trend': 0.03},
            'MSFT': {'base_price': 300, 'volatility': 0.025, 'trend': 0.015},
            'GOOGL': {'base_price': 2500, 'volatility': 0.03, 'trend': 0.01}
        }
        
        for symbol, config in markets.items():
            self.market_data[symbol] = MarketSimulation(
                symbol=symbol,
                base_price=config['base_price'],
                current_price=config['base_price'],
                volatility=config['volatility'],
                trend=config['trend'],
                volume=random.uniform(1000000, 10000000),
                last_update=datetime.now()
            )
            
            self.price_history[symbol] = [config['base_price']]
    
    def _setup_callbacks(self):
        """Setup system callbacks"""
        
        # Order callback
        def order_callback(order, event_type):
            if event_type == 'FILLED':
                self._handle_order_fill(order)
        
        self.order_system.add_order_callback(order_callback)
        
        # Emergency callback
        def emergency_callback(event):
            print(f"🚨 SIMULATION EMERGENCY: {event.message}")
            self._handle_emergency(event)
        
        self.emergency_system.add_emergency_callback(emergency_callback)
    
    def start_simulation(self, duration_hours: int = 8, speed_multiplier: float = 10.0):
        """Start paper trading simulation"""
        
        self.simulation_active = True
        self.simulation_speed = speed_multiplier
        
        print(f"\n🚀 STARTING PAPER TRADING SIMULATION")
        print(f"   ⏱️ Duration: {duration_hours} hours")
        print(f"   🏃 Speed: {speed_multiplier}x real time")
        print(f"   🎯 Learning mode: ACTIVE")
        print("=" * 60)
        
        # Start market simulation
        market_thread = threading.Thread(target=self._run_market_simulation, daemon=True)
        market_thread.start()
        
        # Start AI trading
        ai_thread = threading.Thread(target=self._run_ai_trading, daemon=True)
        ai_thread.start()
        
        # Start portfolio monitoring
        self.portfolio.start_live_tracking(self._portfolio_callback)
        
        # Run simulation
        start_time = datetime.now()
        end_time = start_time + timedelta(hours=duration_hours)
        
        try:
            while datetime.now() < end_time and self.simulation_active:
                # Check emergency conditions
                metrics = self.portfolio.get_portfolio_metrics()
                self.emergency_system.check_emergency_conditions(asdict(metrics))
                
                # Update AI performance
                self._update_ai_performance()
                
                # Sleep based on simulation speed
                time.sleep(1.0 / self.simulation_speed)
                
        except KeyboardInterrupt:
            print("\n⏹️ Simulation stopped by user")
        
        self.stop_simulation()
    
    def _run_market_simulation(self):
        """Run market price simulation"""
        
        while self.simulation_active:
            for symbol, market in self.market_data.items():
                # Generate price movement
                random_factor = random.gauss(0, market.volatility)
                trend_factor = market.trend * 0.001  # Small trend component
                
                # Calculate new price
                price_change = market.current_price * (trend_factor + random_factor)
                new_price = market.current_price + price_change
                
                # Ensure price doesn't go negative
                new_price = max(new_price, market.base_price * 0.1)
                
                # Update market
                market.current_price = new_price
                market.volume = random.uniform(500000, 5000000)
                market.last_update = datetime.now()
                
                # Store price history
                self.price_history[symbol].append(new_price)
                if len(self.price_history[symbol]) > 1000:
                    self.price_history[symbol] = self.price_history[symbol][-1000:]
                
                # Update portfolio positions
                self.portfolio.update_position_price(symbol, new_price)
            
            # Sleep based on simulation speed
            time.sleep(5.0 / self.simulation_speed)  # Update every 5 seconds (simulated)
    
    def _run_ai_trading(self):
        """Run AI trading decisions"""
        
        decision_interval = 30.0 / self.simulation_speed  # 30 seconds simulated time
        
        while self.simulation_active:
            try:
                # Get market analysis from AI
                market_summary = self._get_market_summary()
                
                # Ask AI for trading decision
                trading_decision = self._get_ai_trading_decision(market_summary)
                
                if trading_decision:
                    self._execute_ai_decision(trading_decision)
                
                time.sleep(decision_interval)
                
            except Exception as e:
                print(f"❌ AI trading error: {e}")
                time.sleep(decision_interval)
    
    def _get_market_summary(self) -> str:
        """Get current market summary for AI"""
        
        summary_lines = ["CURRENT MARKET CONDITIONS:"]
        
        for symbol, market in self.market_data.items():
            # Calculate price change
            if len(self.price_history[symbol]) > 1:
                price_change = ((market.current_price - self.price_history[symbol][-10]) / 
                               self.price_history[symbol][-10]) * 100
            else:
                price_change = 0
            
            summary_lines.append(f"{symbol}: ${market.current_price:.2f} ({price_change:+.2f}%)")
        
        # Add portfolio status
        metrics = self.portfolio.get_portfolio_metrics()
        summary_lines.append(f"\nPORTFOLIO STATUS:")
        summary_lines.append(f"Total Value: ${metrics.total_value:,.2f}")
        summary_lines.append(f"Cash: ${metrics.cash_balance:,.2f}")
        summary_lines.append(f"P&L: ${metrics.total_pnl:,.2f} ({metrics.total_pnl_percent:+.2f}%)")
        summary_lines.append(f"Positions: {metrics.positions_count}")
        
        return "\n".join(summary_lines)
    
    def _get_ai_trading_decision(self, market_summary: str) -> Optional[Dict[str, Any]]:
        """Get trading decision from AI"""
        
        prompt = f"""{market_summary}

TRADING DECISION REQUIRED:

Based on current market conditions and portfolio status, make a trading decision:

1. ANALYSIS
   - Market trend assessment
   - Risk/reward evaluation
   - Portfolio balance considerations

2. DECISION
   - Action: BUY/SELL/HOLD
   - Symbol: Which asset to trade
   - Size: Position size (1-10% of portfolio)
   - Reasoning: Why this decision

3. RISK MANAGEMENT
   - Stop loss level
   - Take profit target
   - Maximum risk tolerance

Provide a clear, actionable trading decision or recommend HOLD if no good opportunities."""

        try:
            # Get decision from best performing AI agent
            response = self.ai_agents.query_agent('deepseek_finance', prompt)
            
            if response and response['success']:
                decision = self._parse_ai_decision(response['response'])
                self.ai_performance['total_decisions'] += 1
                return decision
            
        except Exception as e:
            print(f"❌ AI decision error: {e}")
        
        return None
    
    def _parse_ai_decision(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """Parse AI response into trading decision"""
        
        response_lower = ai_response.lower()
        
        # Extract action
        action = None
        if 'buy' in response_lower and 'sell' not in response_lower:
            action = 'BUY'
        elif 'sell' in response_lower and 'buy' not in response_lower:
            action = 'SELL'
        elif 'hold' in response_lower:
            action = 'HOLD'
        
        if action == 'HOLD' or action is None:
            return None
        
        # Extract symbol (look for common symbols)
        symbol = None
        for sym in self.market_data.keys():
            if sym.lower() in response_lower or sym.replace('-', '').lower() in response_lower:
                symbol = sym
                break
        
        if not symbol:
            # Default to BTC if no symbol found
            symbol = 'BTC-USD'
        
        # Extract size (look for percentage)
        import re
        size_match = re.search(r'(\d+(?:\.\d+)?)%', ai_response)
        if size_match:
            size_percent = float(size_match.group(1))
        else:
            size_percent = 2.0  # Default 2%
        
        # Limit size to reasonable range
        size_percent = max(0.5, min(size_percent, 10.0))
        
        return {
            'action': action,
            'symbol': symbol,
            'size_percent': size_percent,
            'reasoning': ai_response[:200],  # First 200 chars
            'confidence': random.uniform(0.6, 0.9)  # Simulated confidence
        }
    
    def _execute_ai_decision(self, decision: Dict[str, Any]):
        """Execute AI trading decision"""
        
        symbol = decision['symbol']
        action = decision['action']
        size_percent = decision['size_percent']
        
        if symbol not in self.market_data:
            return
        
        current_price = self.market_data[symbol].current_price
        portfolio_value = self.portfolio.get_portfolio_metrics().total_value
        
        # Calculate position size
        position_value = portfolio_value * (size_percent / 100)
        quantity = position_value / current_price
        
        print(f"\n🤖 AI TRADING DECISION:")
        print(f"   Action: {action} {symbol}")
        print(f"   Size: {size_percent:.1f}% (${position_value:,.2f})")
        print(f"   Price: ${current_price:.2f}")
        print(f"   Reasoning: {decision['reasoning'][:100]}...")
        
        try:
            if action == 'BUY':
                # Create buy order
                order_id = self.order_system.create_order(
                    symbol=symbol,
                    side='BUY',
                    quantity=quantity,
                    order_type='MARKET'
                )
                self.order_system.submit_order(order_id)
                
            elif action == 'SELL':
                # Check if we have position to sell
                if symbol in self.portfolio.positions:
                    position = self.portfolio.positions[symbol]
                    sell_quantity = min(quantity, position.quantity)
                    
                    order_id = self.order_system.create_order(
                        symbol=symbol,
                        side='SELL',
                        quantity=sell_quantity,
                        order_type='MARKET'
                    )
                    self.order_system.submit_order(order_id)
                else:
                    print(f"   ⚠️ No position to sell in {symbol}")
            
            self.ai_performance['total_trades'] += 1
            
        except Exception as e:
            print(f"   ❌ Execution error: {e}")
    
    def _handle_order_fill(self, order):
        """Handle order fill"""
        
        symbol = order.symbol
        side = order.side.value
        quantity = order.filled_quantity
        price = order.average_fill_price
        
        if side == 'BUY':
            # Add position
            self.portfolio.add_position(symbol, quantity, price, 'long')
        elif side == 'SELL':
            # Close position
            pnl = self.portfolio.close_position(symbol, price)
            
            # Update AI performance
            if pnl is not None:
                if pnl > 0:
                    self.ai_performance['profitable_decisions'] += 1
                    self.ai_performance['winning_trades'] += 1
                
                self.ai_performance['total_pnl'] += pnl
                self.ai_performance['best_trade'] = max(self.ai_performance['best_trade'], pnl)
                self.ai_performance['worst_trade'] = min(self.ai_performance['worst_trade'], pnl)
        
        print(f"✅ Order filled: {symbol} {side} {quantity:.4f} @ ${price:.2f}")
    
    def _handle_emergency(self, event):
        """Handle emergency event"""
        print(f"🚨 Emergency handled: {event.event_type.value}")
        
        # In simulation, we can continue after logging
        if event.level.value == 'CRITICAL':
            print("🛑 Simulation paused for emergency")
            time.sleep(5)  # Brief pause
    
    def _portfolio_callback(self, metrics):
        """Portfolio update callback"""
        # This is called when portfolio is updated
        pass
    
    def _update_ai_performance(self):
        """Update AI learning performance metrics"""
        
        # Calculate performance metrics
        if self.ai_performance['total_decisions'] > 0:
            decision_accuracy = (self.ai_performance['profitable_decisions'] / 
                               self.ai_performance['total_decisions'])
        else:
            decision_accuracy = 0
        
        if self.ai_performance['total_trades'] > 0:
            win_rate = (self.ai_performance['winning_trades'] / 
                       self.ai_performance['total_trades'])
        else:
            win_rate = 0
        
        # Store learning progress
        progress = {
            'timestamp': datetime.now().isoformat(),
            'decision_accuracy': decision_accuracy,
            'win_rate': win_rate,
            'total_pnl': self.ai_performance['total_pnl'],
            'total_decisions': self.ai_performance['total_decisions'],
            'total_trades': self.ai_performance['total_trades']
        }
        
        self.ai_performance['learning_progress'].append(progress)
        
        # Keep only last 100 progress points
        if len(self.ai_performance['learning_progress']) > 100:
            self.ai_performance['learning_progress'] = self.ai_performance['learning_progress'][-100:]
    
    def stop_simulation(self):
        """Stop simulation and generate report"""
        
        self.simulation_active = False
        self.portfolio.stop_live_tracking()
        
        print(f"\n🏁 SIMULATION COMPLETE")
        print("=" * 60)
        
        # Generate final report
        self._generate_simulation_report()
    
    def _generate_simulation_report(self):
        """Generate comprehensive simulation report"""
        
        # Portfolio metrics
        final_metrics = self.portfolio.get_portfolio_metrics()
        
        # AI performance
        ai_perf = self.ai_performance
        
        print(f"\n📊 SIMULATION RESULTS:")
        print(f"   💰 Final Portfolio Value: ${final_metrics.total_value:,.2f}")
        print(f"   📈 Total P&L: ${final_metrics.total_pnl:,.2f} ({final_metrics.total_pnl_percent:+.2f}%)")
        print(f"   💵 Cash Remaining: ${final_metrics.cash_balance:,.2f}")
        print(f"   📊 Final Positions: {final_metrics.positions_count}")
        
        print(f"\n🤖 AI PERFORMANCE:")
        print(f"   🧠 Total Decisions: {ai_perf['total_decisions']}")
        print(f"   💼 Total Trades: {ai_perf['total_trades']}")
        print(f"   🎯 Win Rate: {(ai_perf['winning_trades']/max(1,ai_perf['total_trades']))*100:.1f}%")
        print(f"   💰 Trading P&L: ${ai_perf['total_pnl']:,.2f}")
        print(f"   🏆 Best Trade: ${ai_perf['best_trade']:,.2f}")
        print(f"   📉 Worst Trade: ${ai_perf['worst_trade']:,.2f}")
        
        # Emergency events
        emergency_status = self.emergency_system.get_emergency_status()
        print(f"\n🚨 EMERGENCY EVENTS:")
        print(f"   Total Events: {emergency_status['total_events']}")
        print(f"   Active Events: {emergency_status['active_events']}")
        
        # Save report
        report = {
            'simulation_end': datetime.now().isoformat(),
            'portfolio_metrics': asdict(final_metrics),
            'ai_performance': ai_perf,
            'emergency_events': emergency_status,
            'market_data': {k: asdict(v) for k, v in self.market_data.items()}
        }
        
        with open(f'simulation_report_{int(time.time())}.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"\n📄 Detailed report saved to simulation_report_{int(time.time())}.json")

def main():
    """Run paper trading simulation"""
    print("📊 PAPER TRADING SIMULATION - AI LEARNING")
    print("=" * 60)
    
    # Initialize simulation
    simulation = PaperTradingSimulation(100000.0)
    
    # Run simulation
    print("\n🚀 Starting 2-hour simulation at 20x speed...")
    simulation.start_simulation(duration_hours=2, speed_multiplier=20.0)
    
    print(f"\n✅ PAPER TRADING SIMULATION COMPLETE")

if __name__ == "__main__":
    main()
