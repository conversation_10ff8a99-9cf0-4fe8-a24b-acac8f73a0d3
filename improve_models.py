#!/usr/bin/env python3
"""
Improve Models - Make better versions
Clean, simple improvements that work
"""

import subprocess
import os

def get_models():
    """Get models that aren't already improved"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]
            models = []
            for line in lines:
                if line.strip():
                    model = line.split()[0]
                    # Skip already improved models
                    if not any(x in model.lower() for x in ['better', 'improved', 'enhanced']):
                        models.append(model)
            return models
        return []
    except:
        return []

def create_better_model(base_model):
    """Create improved version of model"""
    new_name = f"better-{base_model.replace(':', '-')}"
    
    print(f"Creating better version of {base_model}...")
    
    # Simple improvement prompt
    system_prompt = """You are a helpful AI assistant. Always:
- Give clear, detailed answers
- Think step by step
- Explain your reasoning
- Be direct and honest
- Provide useful information"""
    
    # Create Modelfile
    modelfile = f"""FROM {base_model}

SYSTEM "{system_prompt}"

PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER num_ctx 4096
"""
    
    # Save and create model
    try:
        filename = f"Modelfile.{new_name}"
        with open(filename, 'w') as f:
            f.write(modelfile)
        
        result = subprocess.run([
            'ollama', 'create', new_name, '-f', filename
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ Created: {new_name}")
            return True
        else:
            print(f"❌ Failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def improve_top_models():
    """Improve your top 3 models"""
    models = get_models()
    if not models:
        print("No models to improve")
        return
    
    print(f"Improving top 3 models...")
    
    # Take first 3 models
    for model in models[:3]:
        create_better_model(model)
    
    print("\nDone! Check with 'ollama list'")

def show_improved():
    """Show improved models"""
    try:
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]
            improved = []
            
            for line in lines:
                if line.strip():
                    model = line.split()[0]
                    if 'better-' in model:
                        improved.append(model)
            
            if improved:
                print(f"\nImproved models ({len(improved)}):")
                for model in improved:
                    print(f"  {model}")
            else:
                print("No improved models found")
    except:
        print("Error checking models")

def main():
    print("Model Improver")
    print("==============")
    
    while True:
        print("\nOptions:")
        print("1 - Improve top 3 models")
        print("2 - Show improved models")
        print("q - Quit")
        
        choice = input("\nChoice: ").lower()
        
        if choice == 'q':
            break
        elif choice == '1':
            improve_top_models()
        elif choice == '2':
            show_improved()
        else:
            print("Invalid choice")
    
    print("Done!")

if __name__ == "__main__":
    main()
