#!/usr/bin/env python3
"""
AI-Powered Trading System
Complete integration of multi-agent AI with trading capabilities
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass

# Import your existing components
from agent_command_center import AgentCommand<PERSON>enter
from working_risk_management import WorkingRiskManager
from multi_agent_reasoning_system import MultiAgentReasoningSystem, ReasoningTask

@dataclass
class AITradingSignal:
    symbol: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    reasoning: str
    agents_consensus: float
    risk_score: float
    position_size: float
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]
    timestamp: datetime

class AIPoweredTradingSystem:
    """Complete AI-powered trading system with multi-agent intelligence"""
    
    def __init__(self, initial_capital: float = 100000.0):
        print("🚀 AI-POWERED TRADING SYSTEM INITIALIZATION")
        print("=" * 60)
        
        # Initialize components
        self.command_center = AgentCommandCenter()
        self.risk_manager = WorkingRiskManager(initial_capital)
        self.reasoning_system = MultiAgentReasoningSystem()
        
        # Trading configuration
        self.trading_config = {
            'min_confidence': 0.7,
            'min_consensus': 0.6,
            'max_risk_score': 0.8,
            'default_timeout': 60,
            'max_agents_per_analysis': 3
        }
        
        # Performance tracking
        self.trading_history = []
        self.ai_performance = {
            'total_signals': 0,
            'successful_signals': 0,
            'total_profit_loss': 0.0,
            'best_performing_agent': None,
            'avg_response_time': 0.0
        }
        
        print(f"✅ AI Trading System initialized")
        print(f"   💰 Capital: ${initial_capital:,.2f}")
        print(f"   🤖 AI Agents: {len(self.command_center.agents)}")
        print(f"   🛡️ Risk Management: Active")
        print(f"   📊 Multi-Agent Reasoning: Ready")
    
    async def generate_ai_trading_signal(self, symbol: str, market_context: Dict = None) -> Optional[AITradingSignal]:
        """Generate AI-powered trading signal using multiple agents"""
        print(f"\n🧠 GENERATING AI TRADING SIGNAL: {symbol}")
        print("=" * 50)
        
        start_time = time.time()
        
        # Step 1: Multi-agent market analysis
        print("🔍 Step 1: Multi-agent market analysis...")
        
        analysis_question = f"""Analyze {symbol} for trading. Provide:
1. BUY/SELL/HOLD recommendation
2. Confidence level (1-10)
3. Entry price target
4. Stop loss level
5. Target price
6. Key reasoning factors

Current context: {market_context or 'General market conditions'}

Be specific with price levels and reasoning."""

        # Get analysis from multiple agents
        analysis_summary = self.command_center.multi_agent_analysis(
            analysis_question,
            agents=['deepseek_finance', 'qwen_finance', 'marco_o1']
        )
        
        if analysis_summary['successful_responses'] == 0:
            print("❌ No agents responded successfully")
            return None
        
        # Step 2: Parse and synthesize responses
        print("🔄 Step 2: Synthesizing agent responses...")
        
        signal_data = self._synthesize_agent_responses(analysis_summary, symbol)
        
        if not signal_data:
            print("❌ Failed to synthesize responses")
            return None
        
        # Step 3: Risk assessment
        print("🛡️ Step 3: Risk assessment...")
        
        risk_score = self._assess_trading_risk(signal_data, symbol)
        
        # Step 4: Position sizing
        print("💰 Step 4: Position sizing...")
        
        if signal_data['action'] in ['BUY', 'SELL']:
            position_size = self._calculate_ai_position_size(signal_data, risk_score)
        else:
            position_size = 0.0
        
        # Step 5: Create final signal
        execution_time = time.time() - start_time
        
        ai_signal = AITradingSignal(
            symbol=symbol,
            action=signal_data['action'],
            confidence=signal_data['confidence'],
            reasoning=signal_data['reasoning'],
            agents_consensus=analysis_summary['success_rate'],
            risk_score=risk_score,
            position_size=position_size,
            entry_price=signal_data.get('entry_price'),
            target_price=signal_data.get('target_price'),
            stop_loss=signal_data.get('stop_loss'),
            timestamp=datetime.now()
        )
        
        # Update performance tracking
        self.ai_performance['total_signals'] += 1
        self.ai_performance['avg_response_time'] = (
            self.ai_performance['avg_response_time'] * 0.9 + execution_time * 0.1
        )
        
        print(f"\n📊 AI SIGNAL GENERATED:")
        print(f"   Action: {ai_signal.action}")
        print(f"   Confidence: {ai_signal.confidence:.2f}")
        print(f"   Consensus: {ai_signal.agents_consensus:.2f}")
        print(f"   Risk Score: {ai_signal.risk_score:.2f}")
        print(f"   Position Size: {ai_signal.position_size:.4f}")
        print(f"   Execution Time: {execution_time:.1f}s")
        
        return ai_signal
    
    def _synthesize_agent_responses(self, analysis_summary: Dict, symbol: str) -> Optional[Dict]:
        """Synthesize responses from multiple agents"""
        responses = analysis_summary['responses']
        successful_responses = [r for r in responses.values() if r and r['success']]
        
        if not successful_responses:
            return None
        
        # Extract actions and confidence from responses
        actions = []
        confidences = []
        entry_prices = []
        stop_losses = []
        target_prices = []
        reasonings = []
        
        for response in successful_responses:
            text = response['response'].lower()
            
            # Extract action
            if 'buy' in text and 'don\'t buy' not in text:
                actions.append('BUY')
            elif 'sell' in text and 'don\'t sell' not in text:
                actions.append('SELL')
            else:
                actions.append('HOLD')
            
            # Extract confidence (look for patterns like "confidence: 8/10" or "8 out of 10")
            import re
            conf_patterns = [
                r'confidence[:\s]+(\d+)[/\s]*10',
                r'(\d+)[/\s]*10\s*confidence',
                r'confidence[:\s]+(\d+)%'
            ]
            
            confidence = 0.7  # Default
            for pattern in conf_patterns:
                match = re.search(pattern, text)
                if match:
                    conf_value = int(match.group(1))
                    confidence = conf_value / 10 if conf_value <= 10 else conf_value / 100
                    break
            
            confidences.append(confidence)
            
            # Extract prices (simplified - would need more sophisticated parsing)
            price_matches = re.findall(r'\$?(\d+,?\d*\.?\d*)', text)
            prices = [float(p.replace(',', '')) for p in price_matches if p and float(p.replace(',', '')) > 100]
            
            if prices:
                entry_prices.append(prices[0] if len(prices) >= 1 else None)
                stop_losses.append(prices[1] if len(prices) >= 2 else None)
                target_prices.append(prices[2] if len(prices) >= 3 else None)
            
            reasonings.append(response['response'][:200] + "...")
        
        # Determine consensus action
        from collections import Counter
        action_counts = Counter(actions)
        consensus_action = action_counts.most_common(1)[0][0]
        
        # Average confidence
        avg_confidence = sum(confidences) / len(confidences)
        
        # Combine reasoning
        combined_reasoning = " | ".join(reasonings)
        
        return {
            'action': consensus_action,
            'confidence': avg_confidence,
            'entry_price': sum(entry_prices) / len(entry_prices) if entry_prices else None,
            'stop_loss': sum(stop_losses) / len(stop_losses) if stop_losses else None,
            'target_price': sum(target_prices) / len(target_prices) if target_prices else None,
            'reasoning': combined_reasoning,
            'agent_count': len(successful_responses)
        }
    
    def _assess_trading_risk(self, signal_data: Dict, symbol: str) -> float:
        """Assess risk of the trading signal"""
        risk_score = 0.0
        
        # Confidence risk
        if signal_data['confidence'] < 0.7:
            risk_score += 0.3
        
        # Action risk
        if signal_data['action'] == 'HOLD':
            risk_score += 0.1  # Lower risk for holding
        
        # Price level risk (simplified)
        if signal_data.get('entry_price') and signal_data.get('stop_loss'):
            price_risk = abs(signal_data['entry_price'] - signal_data['stop_loss']) / signal_data['entry_price']
            risk_score += min(price_risk, 0.3)
        
        # Portfolio risk
        portfolio_metrics = self.risk_manager.calculate_portfolio_metrics()
        if portfolio_metrics.current_drawdown > 5:
            risk_score += 0.2
        
        return min(risk_score, 1.0)
    
    def _calculate_ai_position_size(self, signal_data: Dict, risk_score: float) -> float:
        """Calculate position size based on AI signal and risk"""
        if signal_data['action'] == 'HOLD':
            return 0.0
        
        # Base position size on confidence and risk
        confidence_factor = signal_data['confidence']
        risk_factor = 1.0 - risk_score
        
        # Calculate base size (percentage of portfolio)
        base_percentage = 0.05  # 5% base
        adjusted_percentage = base_percentage * confidence_factor * risk_factor
        
        # Get portfolio value
        portfolio_metrics = self.risk_manager.calculate_portfolio_metrics()
        portfolio_value = portfolio_metrics.total_value
        
        # Calculate dollar amount
        dollar_amount = portfolio_value * adjusted_percentage
        
        # Convert to shares (assuming entry price)
        if signal_data.get('entry_price'):
            position_size = dollar_amount / signal_data['entry_price']
        else:
            position_size = 0.0
        
        return position_size
    
    async def execute_ai_signal(self, signal: AITradingSignal) -> bool:
        """Execute AI trading signal"""
        print(f"\n🎯 EXECUTING AI SIGNAL: {signal.symbol}")
        
        # Check signal quality
        if signal.confidence < self.trading_config['min_confidence']:
            print(f"❌ Signal rejected: Low confidence ({signal.confidence:.2f})")
            return False
        
        if signal.agents_consensus < self.trading_config['min_consensus']:
            print(f"❌ Signal rejected: Low consensus ({signal.agents_consensus:.2f})")
            return False
        
        if signal.risk_score > self.trading_config['max_risk_score']:
            print(f"❌ Signal rejected: High risk ({signal.risk_score:.2f})")
            return False
        
        # Execute through risk manager
        if signal.action == 'BUY' and signal.position_size > 0:
            # Validate position
            allowed, alerts = self.risk_manager.validate_new_position(
                signal.symbol, signal.position_size, signal.entry_price or 50000, 'long', signal.stop_loss
            )
            
            if allowed:
                success = self.risk_manager.add_position(
                    signal.symbol, signal.position_size, signal.entry_price or 50000,
                    'long', signal.stop_loss, signal.target_price
                )
                
                if success:
                    print(f"✅ BUY order executed: {signal.position_size:.4f} shares")
                    self.trading_history.append(signal)
                    return True
                else:
                    print(f"❌ Failed to execute BUY order")
            else:
                print(f"❌ Position blocked by risk management:")
                for alert in alerts:
                    print(f"   • {alert.message}")
        
        elif signal.action == 'SELL':
            # Close existing position
            success = self.risk_manager.close_position(signal.symbol)
            if success:
                print(f"✅ SELL order executed")
                self.trading_history.append(signal)
                return True
            else:
                print(f"❌ No position to close")
        
        return False
    
    def get_ai_performance_report(self) -> Dict:
        """Get AI performance report"""
        return {
            'timestamp': datetime.now().isoformat(),
            'ai_performance': self.ai_performance,
            'trading_history_count': len(self.trading_history),
            'working_agents': len(self.command_center.agents),
            'portfolio_metrics': asdict(self.risk_manager.calculate_portfolio_metrics()),
            'recent_signals': [asdict(signal) for signal in self.trading_history[-5:]],
            'system_status': 'operational'
        }

async def main():
    """Test the AI-powered trading system"""
    print("🚀 AI-POWERED TRADING SYSTEM - COMPLETE INTEGRATION")
    print("=" * 70)
    
    # Initialize system
    ai_trading = AIPoweredTradingSystem(100000.0)
    
    # Test symbols
    test_symbols = ["BTC", "ETH"]
    
    for symbol in test_symbols:
        print(f"\n{'='*60}")
        print(f"TESTING AI TRADING FOR {symbol}")
        print(f"{'='*60}")
        
        # Generate AI signal
        signal = await ai_trading.generate_ai_trading_signal(
            symbol, 
            {"trend": "bullish", "volatility": "medium", "news": "positive"}
        )
        
        if signal:
            # Execute signal
            executed = await ai_trading.execute_ai_signal(signal)
            
            if executed:
                print(f"🎉 AI trading signal executed successfully!")
            else:
                print(f"⚠️ AI signal generated but not executed")
        else:
            print(f"❌ Failed to generate AI signal")
    
    # Performance report
    report = ai_trading.get_ai_performance_report()
    print(f"\n📊 AI TRADING PERFORMANCE:")
    print(f"   Total signals: {report['ai_performance']['total_signals']}")
    print(f"   Working agents: {report['working_agents']}")
    print(f"   Portfolio value: ${report['portfolio_metrics']['total_value']:,.2f}")
    print(f"   System status: {report['system_status']}")
    
    print(f"\n🎉 AI-POWERED TRADING SYSTEM OPERATIONAL!")

if __name__ == "__main__":
    asyncio.run(main())
