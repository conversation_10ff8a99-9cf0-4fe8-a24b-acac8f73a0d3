#!/usr/bin/env python3
"""
Test New Specialized Financial Models
Comprehensive testing of all 8 Noryon AI financial models
"""

import subprocess
import asyncio
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class SpecializedModelTester:
    """Test all specialized financial models"""
    
    def __init__(self):
        self.all_models = [
            # Previously trained models
            {
                "name": "noryon-phi-4-9b-finance:latest",
                "specialization": "Risk Assessment",
                "status": "existing"
            },
            {
                "name": "noryon-gemma-3-12b-finance:latest", 
                "specialization": "Market Analysis",
                "status": "existing"
            },
            {
                "name": "noryon-phi-4-9b-enhanced-enhanced:latest",
                "specialization": "Advanced Risk Management",
                "status": "existing"
            },
            {
                "name": "noryon-gemma-3-12b-enhanced-enhanced:latest",
                "specialization": "Enhanced Market Analysis", 
                "status": "existing"
            },
            # Newly trained models
            {
                "name": "noryon-qwen3-finance-v2:latest",
                "specialization": "Multilingual Analysis",
                "status": "new"
            },
            {
                "name": "noryon-cogito-finance-v2:latest",
                "specialization": "Cognitive Analysis",
                "status": "new"
            },
            {
                "name": "noryon-marco-o1-finance-v2:latest",
                "specialization": "Step-by-Step Reasoning",
                "status": "new"
            },
            {
                "name": "noryon-deepscaler-finance-v2:latest",
                "specialization": "Efficient Analysis",
                "status": "new"
            }
        ]
        
        self.test_scenarios = [
            {
                "name": "Technical Analysis Challenge",
                "query": "Analyze TSLA stock at $250 with RSI 72, MACD bearish divergence, volume declining. Provide specific trading strategy with entry/exit points.",
                "expected_elements": ["entry", "exit", "rsi", "macd", "strategy", "risk"]
            },
            {
                "name": "Portfolio Optimization",
                "query": "Optimize a $500k portfolio currently 80% tech stocks, 20% cash. Market showing high volatility. Provide specific allocation recommendations.",
                "expected_elements": ["allocation", "diversification", "risk", "volatility", "rebalance"]
            },
            {
                "name": "Options Strategy Design",
                "query": "Design an income strategy for 1000 AAPL shares at $180, seeking 2% monthly income. Include risk analysis and profit scenarios.",
                "expected_elements": ["options", "income", "covered call", "risk", "profit", "premium"]
            },
            {
                "name": "Market Sentiment Analysis",
                "query": "Analyze current market sentiment with VIX at 25, crypto down 15%, bond yields rising. What's your trading recommendation?",
                "expected_elements": ["sentiment", "vix", "volatility", "recommendation", "strategy"]
            }
        ]
    
    async def test_single_model(self, model_info):
        """Test a single model with all scenarios"""
        console.print(f"[yellow]🧪 Testing {model_info['name']}...[/yellow]")
        
        results = {
            "model_name": model_info["name"],
            "specialization": model_info["specialization"],
            "status": model_info["status"],
            "total_tests": len(self.test_scenarios),
            "passed_tests": 0,
            "failed_tests": 0,
            "avg_response_length": 0,
            "avg_quality_score": 0,
            "test_details": []
        }
        
        total_length = 0
        total_quality = 0
        
        for scenario in self.test_scenarios:
            try:
                # Test the model
                test_result = subprocess.run([
                    'ollama', 'run', model_info['name'], scenario['query']
                ], capture_output=True, text=True, timeout=90)
                
                if test_result.returncode == 0:
                    response = test_result.stdout.strip()
                    response_length = len(response)
                    total_length += response_length
                    
                    # Calculate quality score
                    quality_score = self.calculate_quality_score(response, scenario)
                    total_quality += quality_score
                    
                    results["passed_tests"] += 1
                    
                    test_detail = {
                        "scenario": scenario["name"],
                        "success": True,
                        "response_length": response_length,
                        "quality_score": quality_score,
                        "response_preview": response[:150] + "..." if len(response) > 150 else response
                    }
                    
                else:
                    results["failed_tests"] += 1
                    test_detail = {
                        "scenario": scenario["name"],
                        "success": False,
                        "error": test_result.stderr,
                        "quality_score": 0
                    }
                
                results["test_details"].append(test_detail)
                
            except subprocess.TimeoutExpired:
                results["failed_tests"] += 1
                results["test_details"].append({
                    "scenario": scenario["name"],
                    "success": False,
                    "error": "Timeout",
                    "quality_score": 0
                })
            except Exception as e:
                results["failed_tests"] += 1
                results["test_details"].append({
                    "scenario": scenario["name"],
                    "success": False,
                    "error": str(e),
                    "quality_score": 0
                })
        
        # Calculate averages
        if results["passed_tests"] > 0:
            results["avg_response_length"] = total_length / results["passed_tests"]
            results["avg_quality_score"] = total_quality / results["passed_tests"]
        
        console.print(f"[green]✅ {model_info['name']} completed: {results['passed_tests']}/{results['total_tests']} passed[/green]")
        return results
    
    def calculate_quality_score(self, response, scenario):
        """Calculate response quality score"""
        score = 0
        
        # Length check
        if len(response) > 200:
            score += 20
        if len(response) > 500:
            score += 10
        
        # Expected elements check
        elements_found = sum(1 for element in scenario['expected_elements'] 
                           if element.lower() in response.lower())
        element_score = (elements_found / len(scenario['expected_elements'])) * 40
        score += element_score
        
        # Financial terminology
        financial_terms = ["analysis", "risk", "return", "portfolio", "market", "trading", 
                          "investment", "strategy", "price", "volatility", "recommendation"]
        financial_count = sum(1 for term in financial_terms if term.lower() in response.lower())
        score += min(financial_count * 2, 20)
        
        # Specific recommendations
        if any(word in response.lower() for word in ["recommend", "suggest", "advise", "should", "target", "buy", "sell", "hold"]):
            score += 10
        
        return min(score, 100)
    
    async def test_all_models(self):
        """Test all models comprehensively"""
        console.print(Panel(
            "[bold blue]🧪 Comprehensive Model Testing[/bold blue]\n\n"
            "Testing all 8 Noryon AI financial models:\n"
            "• 4 Previously trained models\n"
            "• 4 Newly trained specialized models\n\n"
            "Each model will be tested with 4 financial scenarios",
            title="Comprehensive Testing"
        ))
        
        all_results = {}
        start_time = datetime.now()
        
        # Test each model
        for model_info in self.all_models:
            results = await self.test_single_model(model_info)
            all_results[model_info["name"]] = results
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        return all_results, duration
    
    def generate_comprehensive_report(self, all_results, duration):
        """Generate comprehensive test report"""
        console.print("\n[bold green]📊 COMPREHENSIVE TEST RESULTS[/bold green]")
        
        # Summary statistics
        total_models = len(all_results)
        total_tests = sum(r["total_tests"] for r in all_results.values())
        total_passed = sum(r["passed_tests"] for r in all_results.values())
        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
        
        # Model performance table
        performance_table = Table(title="Model Performance Summary")
        performance_table.add_column("Model", style="cyan")
        performance_table.add_column("Specialization", style="yellow")
        performance_table.add_column("Status", style="blue")
        performance_table.add_column("Success Rate", style="green")
        performance_table.add_column("Avg Quality", style="magenta")
        performance_table.add_column("Rating", style="red")
        
        for model_name, results in all_results.items():
            success_rate = f"{(results['passed_tests']/results['total_tests']*100):.1f}%"
            avg_quality = f"{results['avg_quality_score']:.1f}/100"
            
            # Determine rating
            if results['passed_tests'] == results['total_tests'] and results['avg_quality_score'] > 80:
                rating = "🏆 EXCELLENT"
            elif results['passed_tests'] == results['total_tests']:
                rating = "✅ VERY GOOD"
            elif results['passed_tests'] >= results['total_tests'] * 0.75:
                rating = "👍 GOOD"
            else:
                rating = "⚠️ NEEDS WORK"
            
            status_icon = "🆕 NEW" if results["status"] == "new" else "📋 EXISTING"
            
            performance_table.add_row(
                model_name.split(":")[0],  # Remove :latest
                results["specialization"],
                status_icon,
                success_rate,
                avg_quality,
                rating
            )
        
        console.print(performance_table)
        
        # Specialization analysis
        console.print("\n[bold cyan]🎯 Specialization Analysis[/bold cyan]")
        
        specialization_table = Table(title="Model Specializations")
        specialization_table.add_column("Specialization", style="cyan")
        specialization_table.add_column("Model", style="yellow")
        specialization_table.add_column("Performance", style="green")
        
        for model_name, results in all_results.items():
            performance = f"{results['passed_tests']}/{results['total_tests']} ({results['avg_quality_score']:.1f})"
            specialization_table.add_row(
                results["specialization"],
                model_name.split(":")[0],
                performance
            )
        
        console.print(specialization_table)
        
        # Overall assessment
        console.print(Panel(
            f"[bold green]🎉 COMPREHENSIVE TESTING COMPLETE![/bold green]\n\n"
            f"Testing Duration: {duration}\n"
            f"Total Models Tested: {total_models}\n"
            f"Total Test Scenarios: {total_tests}\n"
            f"Total Tests Passed: {total_passed}\n"
            f"Overall Success Rate: {overall_success_rate:.1f}%\n\n"
            f"System Status: {'🎯 PRODUCTION READY' if overall_success_rate > 85 else '✅ READY FOR TESTING' if overall_success_rate > 70 else '⚠️ NEEDS IMPROVEMENT'}\n\n"
            f"🚀 Your 8-model AI ensemble is ready for advanced trading strategies!",
            title="Testing Complete"
        ))
        
        return {
            "total_models": total_models,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "overall_success_rate": overall_success_rate,
            "duration": duration,
            "detailed_results": all_results
        }

async def main():
    """Main testing function"""
    console.print("[bold blue]🧪 Starting Comprehensive Model Testing...[/bold blue]\n")
    
    tester = SpecializedModelTester()
    
    # Test all models
    results, duration = await tester.test_all_models()
    
    # Generate comprehensive report
    summary = tester.generate_comprehensive_report(results, duration)
    
    console.print("\n[bold green]🎯 Next Steps:[/bold green]")
    console.print("1. Create ensemble voting system with all 8 models")
    console.print("2. Set up model specialization routing")
    console.print("3. Integrate into live trading system")
    console.print("4. Deploy advanced multi-model strategies")
    console.print("5. Monitor performance in production")
    
    return summary

if __name__ == "__main__":
    results = asyncio.run(main())
