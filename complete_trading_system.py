#!/usr/bin/env python3
"""
Complete Trading System - PRODUCTION READY
Integrated system with all capabilities for real-world trading
"""

import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Any

# Import all our systems
from live_portfolio_tracker import LivePortfolioTracker
from order_management_system import OrderManagementSystem
from emergency_control_system import EmergencyControlSystem
from agent_command_center import AgentCommandCenter
from paper_trading_simulation import PaperTradingSimulation

class CompleteTradingSystem:
    """Complete integrated trading system"""
    
    def __init__(self, initial_capital: float = 100000.0, paper_trading: bool = True):
        self.paper_trading = paper_trading
        self.initial_capital = initial_capital
        
        # Initialize all subsystems
        self.portfolio = LivePortfolioTracker(initial_capital)
        self.orders = OrderManagementSystem()
        self.emergency = EmergencyControlSystem()
        self.ai_agents = AgentCommandCenter()
        
        # Paper trading simulation
        if paper_trading:
            self.simulation = PaperTradingSimulation(initial_capital)
        
        # System status
        self.system_active = False
        self.trading_enabled = True
        
        print("🚀 COMPLETE TRADING SYSTEM INITIALIZED")
        print(f"   💰 Capital: ${initial_capital:,.2f}")
        print(f"   📊 Mode: {'PAPER TRADING' if paper_trading else 'LIVE TRADING'}")
        print(f"   🤖 AI Agents: {len(self.ai_agents.agents)}")
        print(f"   🛡️ Emergency controls: ACTIVE")
        print(f"   📋 Order management: READY")
        print(f"   📈 Portfolio tracking: READY")
    
    def start_system(self):
        """Start the complete trading system"""
        
        print(f"\n🚀 STARTING COMPLETE TRADING SYSTEM")
        print("=" * 60)
        
        # System health check
        health_check = self._system_health_check()
        
        if not health_check['all_systems_ok']:
            print("❌ System health check failed!")
            for system, status in health_check['systems'].items():
                if not status:
                    print(f"   ❌ {system}: FAILED")
            return False
        
        print("✅ System health check passed")
        
        # Start subsystems
        self.system_active = True
        
        # Start portfolio tracking
        self.portfolio.start_live_tracking(self._portfolio_callback)
        print("✅ Portfolio tracking started")
        
        # Setup emergency monitoring
        self._setup_emergency_monitoring()
        print("✅ Emergency monitoring active")
        
        # Setup order callbacks
        self._setup_order_callbacks()
        print("✅ Order management configured")
        
        print(f"\n🎯 TRADING SYSTEM OPERATIONAL")
        print(f"   Status: {'PAPER TRADING' if self.paper_trading else 'LIVE TRADING'}")
        print(f"   Capital: ${self.initial_capital:,.2f}")
        print(f"   Emergency controls: ACTIVE")
        
        return True
    
    def _system_health_check(self) -> Dict[str, Any]:
        """Comprehensive system health check"""
        
        systems = {}
        
        # Check AI agents
        try:
            test_response = self.ai_agents.query_agent('marco_o1', 'System health check')
            systems['ai_agents'] = test_response and test_response['success']
        except:
            systems['ai_agents'] = False
        
        # Check portfolio tracker
        try:
            metrics = self.portfolio.get_portfolio_metrics()
            systems['portfolio_tracker'] = metrics is not None
        except:
            systems['portfolio_tracker'] = False
        
        # Check order management
        try:
            summary = self.orders.get_order_summary()
            systems['order_management'] = summary is not None
        except:
            systems['order_management'] = False
        
        # Check emergency system
        try:
            status = self.emergency.get_emergency_status()
            systems['emergency_system'] = status is not None
        except:
            systems['emergency_system'] = False
        
        all_ok = all(systems.values())
        
        return {
            'all_systems_ok': all_ok,
            'systems': systems,
            'timestamp': datetime.now()
        }
    
    def _setup_emergency_monitoring(self):
        """Setup emergency monitoring"""
        
        def emergency_callback(event):
            print(f"🚨 EMERGENCY: {event.level.value} - {event.message}")
            
            if event.level.value == 'CRITICAL':
                self.trading_enabled = False
                print("🛑 Trading disabled due to critical emergency")
        
        self.emergency.add_emergency_callback(emergency_callback)
    
    def _setup_order_callbacks(self):
        """Setup order management callbacks"""
        
        def order_callback(order, event_type):
            print(f"📋 Order {event_type}: {order.symbol} {order.side.value} {order.quantity}")
            
            if event_type == 'FILLED':
                # Update portfolio
                if order.side.value == 'BUY':
                    self.portfolio.add_position(
                        order.symbol, 
                        order.filled_quantity, 
                        order.average_fill_price, 
                        'long'
                    )
                elif order.side.value == 'SELL':
                    self.portfolio.close_position(order.symbol, order.average_fill_price)
        
        self.orders.add_order_callback(order_callback)
    
    def _portfolio_callback(self, metrics):
        """Portfolio update callback"""
        
        # Check for emergency conditions
        emergency_events = self.emergency.check_emergency_conditions(metrics.__dict__)
        
        if emergency_events:
            print(f"⚠️ Emergency conditions detected: {len(emergency_events)}")
    
    def place_trade(self, symbol: str, action: str, amount: float, 
                   order_type: str = 'MARKET', price: Optional[float] = None) -> Optional[str]:
        """Place a trade through the system"""
        
        if not self.trading_enabled:
            print("❌ Trading disabled - emergency mode active")
            return None
        
        if not self.system_active:
            print("❌ System not active")
            return None
        
        print(f"\n💼 PLACING TRADE:")
        print(f"   Symbol: {symbol}")
        print(f"   Action: {action}")
        print(f"   Amount: {amount}")
        print(f"   Type: {order_type}")
        
        try:
            # Create order
            order_id = self.orders.create_order(
                symbol=symbol,
                side=action.upper(),
                quantity=amount,
                order_type=order_type.upper(),
                price=price
            )
            
            # Submit order
            if self.orders.submit_order(order_id):
                print(f"✅ Trade submitted: {order_id}")
                return order_id
            else:
                print(f"❌ Trade submission failed")
                return None
                
        except Exception as e:
            print(f"❌ Trade error: {e}")
            return None
    
    def get_ai_recommendation(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get AI trading recommendation"""
        
        # Get current market data
        portfolio_metrics = self.portfolio.get_portfolio_metrics()
        
        prompt = f"""TRADING RECOMMENDATION REQUEST

Symbol: {symbol}
Current Portfolio Value: ${portfolio_metrics.total_value:,.2f}
Cash Available: ${portfolio_metrics.cash_balance:,.2f}
Current P&L: ${portfolio_metrics.total_pnl:,.2f} ({portfolio_metrics.total_pnl_percent:+.2f}%)
Active Positions: {portfolio_metrics.positions_count}

Provide a trading recommendation for {symbol}:

1. RECOMMENDATION: BUY/SELL/HOLD
2. CONFIDENCE: 1-10 scale
3. POSITION SIZE: Percentage of portfolio (1-10%)
4. REASONING: Brief explanation
5. RISK LEVEL: LOW/MEDIUM/HIGH
6. TIME HORIZON: SHORT/MEDIUM/LONG

Be specific and actionable."""

        try:
            response = self.ai_agents.query_agent('deepseek_finance', prompt)
            
            if response and response['success']:
                return {
                    'symbol': symbol,
                    'ai_response': response['response'],
                    'response_time': response['response_time'],
                    'timestamp': datetime.now(),
                    'agent': 'deepseek_finance'
                }
        except Exception as e:
            print(f"❌ AI recommendation error: {e}")
        
        return None
    
    def run_paper_trading_session(self, duration_hours: int = 4, speed: float = 10.0):
        """Run paper trading learning session"""
        
        if not self.paper_trading:
            print("❌ Paper trading not enabled")
            return
        
        print(f"\n📊 STARTING PAPER TRADING SESSION")
        print(f"   Duration: {duration_hours} hours")
        print(f"   Speed: {speed}x real time")
        
        self.simulation.start_simulation(duration_hours, speed)
    
    def emergency_stop(self, reason: str = "Manual stop"):
        """Emergency stop all trading"""
        
        print(f"\n🚨 EMERGENCY STOP INITIATED")
        print(f"Reason: {reason}")
        
        # Trigger emergency system
        self.emergency.PANIC_BUTTON(reason)
        
        # Disable trading
        self.trading_enabled = False
        
        print("🛑 All trading halted")
        print("Call resume_trading() to restart")
    
    def resume_trading(self, authorization_code: str = "RESUME"):
        """Resume trading after emergency stop"""
        
        if authorization_code != "RESUME":
            print("❌ Invalid authorization code")
            return False
        
        # Check if emergencies are resolved
        emergency_status = self.emergency.get_emergency_status()
        
        if emergency_status['emergency_active']:
            print("❌ Cannot resume - active emergencies exist")
            print(f"Active emergencies: {emergency_status['active_events']}")
            return False
        
        # Resume trading
        self.trading_enabled = True
        print("✅ Trading resumed")
        return True
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get complete system status"""
        
        portfolio_metrics = self.portfolio.get_portfolio_metrics()
        order_summary = self.orders.get_order_summary()
        emergency_status = self.emergency.get_emergency_status()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_active': self.system_active,
            'trading_enabled': self.trading_enabled,
            'paper_trading': self.paper_trading,
            'portfolio': {
                'total_value': portfolio_metrics.total_value,
                'cash_balance': portfolio_metrics.cash_balance,
                'total_pnl': portfolio_metrics.total_pnl,
                'total_pnl_percent': portfolio_metrics.total_pnl_percent,
                'positions_count': portfolio_metrics.positions_count,
                'risk_exposure': portfolio_metrics.risk_exposure
            },
            'orders': {
                'total_orders': order_summary['total_orders'],
                'active_orders': order_summary['active_orders'],
                'completed_orders': order_summary['completed_orders']
            },
            'emergency': {
                'emergency_active': emergency_status['emergency_active'],
                'total_events': emergency_status['total_events'],
                'active_events': emergency_status['active_events']
            },
            'ai_agents': {
                'total_agents': len(self.ai_agents.agents),
                'available_agents': list(self.ai_agents.agents.keys())
            }
        }
    
    def stop_system(self):
        """Stop the complete trading system"""
        
        print(f"\n🛑 STOPPING TRADING SYSTEM")
        
        # Stop subsystems
        self.system_active = False
        self.portfolio.stop_live_tracking()
        
        if hasattr(self, 'simulation') and self.simulation:
            self.simulation.stop_simulation()
        
        print("✅ Trading system stopped")

def main():
    """Test complete trading system"""
    print("🚀 COMPLETE TRADING SYSTEM - TESTING")
    print("=" * 60)
    
    # Initialize system in paper trading mode
    trading_system = CompleteTradingSystem(100000.0, paper_trading=True)
    
    # Start system
    if trading_system.start_system():
        
        # Get AI recommendation
        print(f"\n🤖 Getting AI recommendation...")
        recommendation = trading_system.get_ai_recommendation('BTC-USD')
        
        if recommendation:
            print(f"✅ AI recommendation received")
            print(f"   Agent: {recommendation['agent']}")
            print(f"   Response time: {recommendation['response_time']:.1f}s")
            print(f"   Response: {recommendation['ai_response'][:200]}...")
        
        # Test trade placement
        print(f"\n💼 Testing trade placement...")
        order_id = trading_system.place_trade('BTC-USD', 'BUY', 0.1, 'MARKET')
        
        if order_id:
            print(f"✅ Trade placed successfully: {order_id}")
        
        # Wait a moment for order processing
        time.sleep(3)
        
        # Get system status
        status = trading_system.get_system_status()
        
        print(f"\n📊 SYSTEM STATUS:")
        print(f"   System Active: {status['system_active']}")
        print(f"   Trading Enabled: {status['trading_enabled']}")
        print(f"   Portfolio Value: ${status['portfolio']['total_value']:,.2f}")
        print(f"   Cash Balance: ${status['portfolio']['cash_balance']:,.2f}")
        print(f"   Active Orders: {status['orders']['active_orders']}")
        print(f"   Emergency Events: {status['emergency']['active_events']}")
        
        # Test emergency stop
        print(f"\n🚨 Testing emergency stop...")
        trading_system.emergency_stop("System test")
        
        # Try to place trade (should fail)
        print(f"\n💼 Testing trade during emergency...")
        failed_order = trading_system.place_trade('ETH-USD', 'BUY', 1.0, 'MARKET')
        
        if not failed_order:
            print("✅ Trade correctly blocked during emergency")
        
        # Resume trading
        print(f"\n🔄 Resuming trading...")
        if trading_system.resume_trading("RESUME"):
            print("✅ Trading resumed successfully")
        
        # Stop system
        trading_system.stop_system()
    
    print(f"\n✅ COMPLETE TRADING SYSTEM TEST FINISHED")

if __name__ == "__main__":
    main()
