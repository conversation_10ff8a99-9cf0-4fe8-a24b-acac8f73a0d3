#!/usr/bin/env python3
"""
Noryon AI Trading System - Command Line Interface

A comprehensive CLI tool for managing, monitoring, and operating
the Noryon AI Trading System.

Usage:
    python cli.py system start
    python cli.py system status
    python cli.py training start --model lstm --data data/training.csv
    python cli.py backtest run --strategy momentum --start 2023-01-01
    python cli.py deploy --env production
    python cli.py monitor alerts
"""

import asyncio
import click
import json
import yaml
import logging
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import pandas as pd
from tabulate import tabulate
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.text import Text
from rich import print as rprint

# Import system components
try:
    from system_integration import SystemOrchestrator
    from training_pipeline import TrainingPipeline
    from backtesting_engine import BacktestingEngine
    from deploy import DeploymentManager
    from monitoring import MonitoringSystem
except ImportError as e:
    print(f"Warning: Could not import system components: {e}")
    print("Some CLI features may not be available.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Rich console for pretty output
console = Console()


class NoryonCLI:
    """
    Main CLI class for Noryon AI Trading System.
    """
    
    def __init__(self):
        self.config = self._load_config()
        self.api_base_url = f"http://{self.config.get('api', {}).get('host', 'localhost')}:{self.config.get('api', {}).get('port', 8000)}"
    
    def _load_config(self) -> Dict[str, Any]:
        """Load system configuration."""
        config_file = Path("config.yaml")
        if config_file.exists():
            with open(config_file, 'r') as f:
                return yaml.safe_load(f)
        return {}
    
    async def _make_api_request(self, endpoint: str, method: str = "GET", data: Dict = None) -> Dict[str, Any]:
        """Make API request to the system."""
        url = f"{self.api_base_url}{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                if method == "GET":
                    async with session.get(url) as response:
                        return await response.json()
                elif method == "POST":
                    async with session.post(url, json=data) as response:
                        return await response.json()
                elif method == "PUT":
                    async with session.put(url, json=data) as response:
                        return await response.json()
                elif method == "DELETE":
                    async with session.delete(url) as response:
                        return await response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def _format_table(self, data: List[Dict], headers: List[str] = None) -> str:
        """Format data as a table."""
        if not data:
            return "No data available"
        
        if headers is None:
            headers = list(data[0].keys())
        
        table_data = []
        for item in data:
            row = [str(item.get(header, "")) for header in headers]
            table_data.append(row)
        
        return tabulate(table_data, headers=headers, tablefmt="grid")
    
    def _format_rich_table(self, data: List[Dict], title: str = None) -> Table:
        """Format data as a Rich table."""
        if not data:
            table = Table(title=title or "No Data")
            table.add_column("Message")
            table.add_row("No data available")
            return table
        
        table = Table(title=title)
        
        # Add columns
        headers = list(data[0].keys())
        for header in headers:
            table.add_column(header.replace('_', ' ').title())
        
        # Add rows
        for item in data:
            row = [str(item.get(header, "")) for header in headers]
            table.add_row(*row)
        
        return table


# CLI Groups
@click.group()
@click.option('--config', '-c', default='config.yaml', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config, verbose):
    """Noryon AI Trading System CLI"""
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    ctx.ensure_object(dict)
    ctx.obj['cli'] = NoryonCLI()
    ctx.obj['config_path'] = config


@cli.group()
def system():
    """System management commands"""
    pass


@cli.group()
def training():
    """Model training commands"""
    pass


@cli.group()
def backtest():
    """Backtesting commands"""
    pass


@cli.group()
def deploy():
    """Deployment commands"""
    pass


@cli.group()
def monitor():
    """Monitoring commands"""
    pass


@cli.group()
def data():
    """Data management commands"""
    pass


# System Commands
@system.command()
@click.pass_context
def start(ctx):
    """Start the Noryon system"""
    async def _start():
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Starting Noryon system...", total=None)
            
            try:
                orchestrator = SystemOrchestrator()
                await orchestrator.initialize()
                await orchestrator.start()
                
                progress.update(task, description="✅ System started successfully")
                console.print("[green]Noryon AI Trading System is now running[/green]")
                
                # Keep running until interrupted
                try:
                    while True:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    progress.update(task, description="Shutting down...")
                    await orchestrator.shutdown()
                    console.print("[yellow]System shutdown complete[/yellow]")
                    
            except Exception as e:
                progress.update(task, description=f"❌ Failed to start: {e}")
                console.print(f"[red]Error starting system: {e}[/red]")
                sys.exit(1)
    
    asyncio.run(_start())


@system.command()
@click.pass_context
def status(ctx):
    """Get system status"""
    async def _status():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching system status..."):
            status_data = await cli_obj._make_api_request("/status")
        
        if "error" in status_data:
            console.print(f"[red]Error: {status_data['error']}[/red]")
            return
        
        # Display overall status
        status_color = "green" if status_data.get('status') == 'running' else "red"
        console.print(f"\n[bold]System Status: [{status_color}]{status_data.get('status', 'unknown')}[/{status_color}][/bold]")
        
        # Display components
        if 'components' in status_data:
            table = Table(title="Component Status")
            table.add_column("Component")
            table.add_column("Status")
            table.add_column("Health")
            table.add_column("Last Updated")
            
            for comp_name, comp_data in status_data['components'].items():
                status_color = "green" if comp_data.get('status') == 'running' else "red"
                health_color = "green" if comp_data.get('health') == 'healthy' else "red"
                
                table.add_row(
                    comp_name,
                    f"[{status_color}]{comp_data.get('status', 'unknown')}[/{status_color}]",
                    f"[{health_color}]{comp_data.get('health', 'unknown')}[/{health_color}]",
                    comp_data.get('last_updated', 'N/A')
                )
            
            console.print(table)
        
        # Display metrics
        if 'metrics' in status_data:
            metrics_panel = Panel(
                f"CPU: {status_data['metrics'].get('cpu_usage', 'N/A')}%\n"
                f"Memory: {status_data['metrics'].get('memory_usage', 'N/A')}%\n"
                f"Active Models: {status_data['metrics'].get('active_models', 'N/A')}\n"
                f"Trades Today: {status_data['metrics'].get('trades_today', 'N/A')}",
                title="System Metrics"
            )
            console.print(metrics_panel)
    
    asyncio.run(_status())


@system.command()
@click.pass_context
def stop(ctx):
    """Stop the Noryon system"""
    async def _stop():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold red]Stopping system..."):
            result = await cli_obj._make_api_request("/shutdown", method="POST")
        
        if "error" in result:
            console.print(f"[red]Error stopping system: {result['error']}[/red]")
        else:
            console.print("[green]System stopped successfully[/green]")
    
    asyncio.run(_stop())


# Training Commands
@training.command()
@click.option('--model', '-m', required=True, help='Model type (lstm, transformer, ensemble)')
@click.option('--data', '-d', help='Training data file path')
@click.option('--epochs', '-e', default=100, help='Number of training epochs')
@click.option('--batch-size', '-b', default=32, help='Batch size')
@click.option('--learning-rate', '-lr', default=0.001, help='Learning rate')
@click.pass_context
def start(ctx, model, data, epochs, batch_size, learning_rate):
    """Start model training"""
    async def _train():
        cli_obj = ctx.obj['cli']
        
        training_config = {
            'model_type': model,
            'data_path': data,
            'epochs': epochs,
            'batch_size': batch_size,
            'learning_rate': learning_rate
        }
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Starting {model} training...", total=None)
            
            result = await cli_obj._make_api_request("/training/start", method="POST", data=training_config)
            
            if "error" in result:
                progress.update(task, description=f"❌ Training failed: {result['error']}")
                console.print(f"[red]Training failed: {result['error']}[/red]")
            else:
                progress.update(task, description="✅ Training started successfully")
                console.print(f"[green]Training started with job ID: {result.get('job_id')}[/green]")
    
    asyncio.run(_train())


@training.command()
@click.pass_context
def list(ctx):
    """List training jobs"""
    async def _list():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching training jobs..."):
            jobs = await cli_obj._make_api_request("/training/jobs")
        
        if "error" in jobs:
            console.print(f"[red]Error: {jobs['error']}[/red]")
            return
        
        if not jobs.get('jobs'):
            console.print("[yellow]No training jobs found[/yellow]")
            return
        
        table = cli_obj._format_rich_table(jobs['jobs'], "Training Jobs")
        console.print(table)
    
    asyncio.run(_list())


# Backtesting Commands
@backtest.command()
@click.option('--strategy', '-s', required=True, help='Trading strategy')
@click.option('--start', required=True, help='Start date (YYYY-MM-DD)')
@click.option('--end', help='End date (YYYY-MM-DD)')
@click.option('--initial-capital', default=100000, help='Initial capital')
@click.option('--symbols', help='Comma-separated list of symbols')
@click.pass_context
def run(ctx, strategy, start, end, initial_capital, symbols):
    """Run backtest"""
    async def _backtest():
        cli_obj = ctx.obj['cli']
        
        backtest_config = {
            'strategy': strategy,
            'start_date': start,
            'end_date': end or datetime.now().strftime('%Y-%m-%d'),
            'initial_capital': initial_capital,
            'symbols': symbols.split(',') if symbols else ['AAPL', 'GOOGL', 'MSFT']
        }
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Running backtest...", total=None)
            
            result = await cli_obj._make_api_request("/backtest/run", method="POST", data=backtest_config)
            
            if "error" in result:
                progress.update(task, description=f"❌ Backtest failed: {result['error']}")
                console.print(f"[red]Backtest failed: {result['error']}[/red]")
            else:
                progress.update(task, description="✅ Backtest completed")
                
                # Display results
                results = result.get('results', {})
                
                results_panel = Panel(
                    f"Total Return: {results.get('total_return', 'N/A')}%\n"
                    f"Sharpe Ratio: {results.get('sharpe_ratio', 'N/A')}\n"
                    f"Max Drawdown: {results.get('max_drawdown', 'N/A')}%\n"
                    f"Win Rate: {results.get('win_rate', 'N/A')}%\n"
                    f"Total Trades: {results.get('total_trades', 'N/A')}",
                    title="Backtest Results"
                )
                console.print(results_panel)
    
    asyncio.run(_backtest())


@backtest.command()
@click.pass_context
def list(ctx):
    """List backtest results"""
    async def _list():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching backtest results..."):
            results = await cli_obj._make_api_request("/backtest/results")
        
        if "error" in results:
            console.print(f"[red]Error: {results['error']}[/red]")
            return
        
        if not results.get('results'):
            console.print("[yellow]No backtest results found[/yellow]")
            return
        
        table = cli_obj._format_rich_table(results['results'], "Backtest Results")
        console.print(table)
    
    asyncio.run(_list())


# Deployment Commands
@deploy.command()
@click.option('--env', '-e', default='development', help='Environment (development, staging, production)')
@click.option('--force', is_flag=True, help='Force deployment')
@click.pass_context
def start(ctx, env, force):
    """Deploy the system"""
    async def _deploy():
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task(f"Deploying to {env}...", total=None)
            
            try:
                manager = DeploymentManager(env)
                success = await manager.deploy(force=force)
                
                if success:
                    progress.update(task, description="✅ Deployment successful")
                    console.print(f"[green]Successfully deployed to {env}[/green]")
                else:
                    progress.update(task, description="❌ Deployment failed")
                    console.print(f"[red]Deployment to {env} failed[/red]")
                    sys.exit(1)
                    
            except Exception as e:
                progress.update(task, description=f"❌ Deployment error: {e}")
                console.print(f"[red]Deployment error: {e}[/red]")
                sys.exit(1)
    
    asyncio.run(_deploy())


@deploy.command()
@click.option('--env', '-e', default='development', help='Environment')
@click.pass_context
def status(ctx, env):
    """Get deployment status"""
    async def _status():
        with console.status("[bold green]Fetching deployment status..."):
            manager = DeploymentManager(env)
            status_data = await manager.get_status()
        
        if "error" in status_data:
            console.print(f"[red]Error: {status_data['error']}[/red]")
            return
        
        console.print(f"\n[bold]Environment: {env}[/bold]")
        
        if 'services' in status_data:
            table = Table(title="Service Status")
            table.add_column("Service")
            table.add_column("Status")
            table.add_column("Health")
            
            for service in status_data['services']:
                status_color = "green" if service.get('State') == 'running' else "red"
                table.add_row(
                    service.get('Name', 'Unknown'),
                    f"[{status_color}]{service.get('State', 'unknown')}[/{status_color}]",
                    service.get('Health', 'unknown')
                )
            
            console.print(table)
    
    asyncio.run(_status())


# Monitoring Commands
@monitor.command()
@click.pass_context
def alerts(ctx):
    """Show active alerts"""
    async def _alerts():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching alerts..."):
            alerts_data = await cli_obj._make_api_request("/monitoring/alerts")
        
        if "error" in alerts_data:
            console.print(f"[red]Error: {alerts_data['error']}[/red]")
            return
        
        active_alerts = alerts_data.get('active_alerts', [])
        
        if not active_alerts:
            console.print("[green]No active alerts[/green]")
            return
        
        table = Table(title="Active Alerts")
        table.add_column("Name")
        table.add_column("Severity")
        table.add_column("Message")
        table.add_column("Time")
        
        for alert in active_alerts:
            severity_color = "red" if alert.get('severity') == 'critical' else "yellow"
            table.add_row(
                alert.get('name', 'Unknown'),
                f"[{severity_color}]{alert.get('severity', 'unknown')}[/{severity_color}]",
                alert.get('message', ''),
                alert.get('timestamp', '')
            )
        
        console.print(table)
    
    asyncio.run(_alerts())


@monitor.command()
@click.pass_context
def metrics(ctx):
    """Show system metrics"""
    async def _metrics():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching metrics..."):
            metrics_data = await cli_obj._make_api_request("/monitoring/metrics")
        
        if "error" in metrics_data:
            console.print(f"[red]Error: {metrics_data['error']}[/red]")
            return
        
        # Display key metrics
        metrics = metrics_data.get('metrics', {})
        
        for category, category_metrics in metrics.items():
            if isinstance(category_metrics, dict):
                panel_content = "\n".join([
                    f"{key.replace('_', ' ').title()}: {value}"
                    for key, value in category_metrics.items()
                ])
                
                panel = Panel(
                    panel_content,
                    title=category.replace('_', ' ').title()
                )
                console.print(panel)
    
    asyncio.run(_metrics())


# Data Commands
@data.command()
@click.option('--source', '-s', help='Data source')
@click.option('--symbols', help='Comma-separated list of symbols')
@click.option('--start', help='Start date (YYYY-MM-DD)')
@click.option('--end', help='End date (YYYY-MM-DD)')
@click.pass_context
def fetch(ctx, source, symbols, start, end):
    """Fetch market data"""
    async def _fetch():
        cli_obj = ctx.obj['cli']
        
        fetch_config = {
            'source': source or 'yahoo',
            'symbols': symbols.split(',') if symbols else ['AAPL'],
            'start_date': start or (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            'end_date': end or datetime.now().strftime('%Y-%m-%d')
        }
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Fetching data...", total=None)
            
            result = await cli_obj._make_api_request("/data/fetch", method="POST", data=fetch_config)
            
            if "error" in result:
                progress.update(task, description=f"❌ Data fetch failed: {result['error']}")
                console.print(f"[red]Data fetch failed: {result['error']}[/red]")
            else:
                progress.update(task, description="✅ Data fetched successfully")
                console.print(f"[green]Fetched {result.get('records', 0)} records[/green]")
    
    asyncio.run(_fetch())


@data.command()
@click.pass_context
def status(ctx):
    """Show data status"""
    async def _status():
        cli_obj = ctx.obj['cli']
        
        with console.status("[bold green]Fetching data status..."):
            status_data = await cli_obj._make_api_request("/data/status")
        
        if "error" in status_data:
            console.print(f"[red]Error: {status_data['error']}[/red]")
            return
        
        # Display data status
        data_sources = status_data.get('sources', {})
        
        for source_name, source_data in data_sources.items():
            panel_content = "\n".join([
                f"Status: {source_data.get('status', 'unknown')}",
                f"Last Update: {source_data.get('last_update', 'N/A')}",
                f"Records: {source_data.get('record_count', 'N/A')}",
                f"Symbols: {', '.join(source_data.get('symbols', []))}"
            ])
            
            panel = Panel(
                panel_content,
                title=f"Data Source: {source_name}"
            )
            console.print(panel)
    
    asyncio.run(_status())


# Utility Commands
@cli.command()
@click.option('--output', '-o', default='config_template.yaml', help='Output file path')
def init(output):
    """Initialize a new Noryon configuration"""
    template_config = {
        'system': {
            'name': 'noryon-ai-trading',
            'version': '1.0.0',
            'environment': 'development'
        },
        'api': {
            'host': 'localhost',
            'port': 8000
        },
        'trading': {
            'enabled': True,
            'initial_capital': 100000
        },
        'risk_management': {
            'max_position_size': 0.1,
            'max_daily_loss': 0.05
        }
    }
    
    with open(output, 'w') as f:
        yaml.dump(template_config, f, default_flow_style=False)
    
    console.print(f"[green]Configuration template created: {output}[/green]")


@cli.command()
@click.option('--config', '-c', default='config.yaml', help='Configuration file to validate')
def validate(config):
    """Validate configuration file"""
    try:
        with open(config, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # Basic validation
        required_sections = ['system', 'api', 'trading']
        missing_sections = [section for section in required_sections if section not in config_data]
        
        if missing_sections:
            console.print(f"[red]Missing required sections: {', '.join(missing_sections)}[/red]")
            sys.exit(1)
        
        console.print(f"[green]Configuration file {config} is valid[/green]")
        
    except FileNotFoundError:
        console.print(f"[red]Configuration file {config} not found[/red]")
        sys.exit(1)
    except yaml.YAMLError as e:
        console.print(f"[red]Invalid YAML in {config}: {e}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    cli()